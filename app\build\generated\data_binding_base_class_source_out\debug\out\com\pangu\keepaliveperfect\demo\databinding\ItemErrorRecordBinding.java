// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemErrorRecordBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final LinearLayout errorDataContainer;

  @NonNull
  public final TextView loginTypeTextView;

  @NonNull
  public final TextView timeTextView;

  private ItemErrorRecordBinding(@NonNull CardView rootView,
      @NonNull LinearLayout errorDataContainer, @NonNull TextView loginTypeTextView,
      @NonNull TextView timeTextView) {
    this.rootView = rootView;
    this.errorDataContainer = errorDataContainer;
    this.loginTypeTextView = loginTypeTextView;
    this.timeTextView = timeTextView;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemErrorRecordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemErrorRecordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_error_record, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemErrorRecordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.errorDataContainer;
      LinearLayout errorDataContainer = ViewBindings.findChildViewById(rootView, id);
      if (errorDataContainer == null) {
        break missingId;
      }

      id = R.id.loginTypeTextView;
      TextView loginTypeTextView = ViewBindings.findChildViewById(rootView, id);
      if (loginTypeTextView == null) {
        break missingId;
      }

      id = R.id.timeTextView;
      TextView timeTextView = ViewBindings.findChildViewById(rootView, id);
      if (timeTextView == null) {
        break missingId;
      }

      return new ItemErrorRecordBinding((CardView) rootView, errorDataContainer, loginTypeTextView,
          timeTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
