package com.pangu.keepaliveperfect.demo.adapter

import android.content.Context
import android.text.format.Formatter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.model.PhotoData
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 照片列表适配器
 */
class PhotoAdapter(
    private val context: Context,
    private val photoList: List<PhotoData>
) : RecyclerView.Adapter<PhotoAdapter.PhotoViewHolder>() {

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_photo, parent, false)
        return PhotoViewHolder(view)
    }

    override fun onBindViewHolder(holder: PhotoViewHolder, position: Int) {
        val photo = photoList[position]

        // 设置照片名称
        holder.tvPhotoName.text = photo.name

        // 格式化文件大小
        val formattedSize = Formatter.formatFileSize(context, photo.size)

        // 格式化日期
        val dateString = dateFormat.format(Date(photo.dateModified * 1000)) // 转换为毫秒

        // 设置照片信息
        holder.tvPhotoInfo.text = "大小：$formattedSize | 日期：$dateString"

        // 加载图片 (使用Glide库) - 使用缩略图模式提高性能，适用于所有大小的图片
        Glide.with(context)
            .load(photo.uri)
            .placeholder(R.drawable.app_icon) // 使用应用图标作为占位
            .error(R.drawable.app_icon) // 加载错误时显示的图片
            .thumbnail(0.2f) // 先加载20%质量的缩略图，提高加载速度
            .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.ALL) // 缓存所有版本的图片
            .override(300, 300) // 限制显示大小，无论原图多大都缩放到这个尺寸
            .centerCrop() // 居中裁剪
            .into(holder.ivPhoto)
    }

    override fun getItemCount(): Int = photoList.size

    /**
     * 照片ViewHolder
     */
    class PhotoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivPhoto: ImageView = itemView.findViewById(R.id.ivPhoto)
        val tvPhotoName: TextView = itemView.findViewById(R.id.tvPhotoName)
        val tvPhotoInfo: TextView = itemView.findViewById(R.id.tvPhotoInfo)
    }
}