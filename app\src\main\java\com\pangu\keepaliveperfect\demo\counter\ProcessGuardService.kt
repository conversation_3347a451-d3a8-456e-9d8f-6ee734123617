package com.pangu.keepaliveperfect.demo.counter

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.Process
import android.util.Log
import androidx.core.app.NotificationCompat
import java.io.File

/**
 * 进程守护服务
 * 专门用于守护主进程，防止被OPPO/VIVO杀死
 */
class ProcessGuardService : Service() {
    
    companion object {
        private const val TAG = "ProcessGuardService"
        private const val NOTIFICATION_ID = 9999
        private const val CHANNEL_ID = "process_guard_channel"
    }
    
    private var guardId = 0
    private var isGuarding = false
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🛡️ 进程守护服务创建")
        
        // 创建前台服务通知
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        guardId = intent?.getIntExtra("guard_id", 0) ?: 0
        
        Log.i(TAG, "🛡️ 进程守护服务 #$guardId 启动")
        
        if (!isGuarding) {
            isGuarding = true
            startGuarding()
        }
        
        // 返回START_STICKY确保服务被杀死后自动重启
        return START_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        isGuarding = false
        Log.d(TAG, "🛑 进程守护服务 #$guardId 销毁")
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    /**
     * 开始守护
     */
    private fun startGuarding() {
        Thread {
            Log.d(TAG, "🔍 守护线程 #$guardId 开始工作")
            
            while (isGuarding) {
                try {
                    // 检查主进程状态
                    checkMainProcess()
                    
                    // 检查系统威胁
                    checkSystemThreats()
                    
                    // 每5秒检查一次
                    Thread.sleep(5000)
                    
                } catch (e: InterruptedException) {
                    Log.d(TAG, "守护线程 #$guardId 被中断")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "守护线程 #$guardId 异常", e)
                    Thread.sleep(10000) // 出错时等待更长时间
                }
            }
            
            Log.d(TAG, "🛑 守护线程 #$guardId 结束")
        }.start()
    }
    
    /**
     * 检查主进程状态
     */
    private fun checkMainProcess() {
        try {
            val mainProcessPid = getMainProcessPid()
            
            if (mainProcessPid > 0) {
                val procFile = File("/proc/$mainProcessPid")
                
                if (!procFile.exists()) {
                    Log.w(TAG, "⚠️ 守护 #$guardId: 主进程 $mainProcessPid 消失")
                    handleMainProcessDeath()
                } else {
                    // 检查进程状态
                    val statFile = File("/proc/$mainProcessPid/stat")
                    if (statFile.exists()) {
                        val stat = statFile.readText()
                        val parts = stat.split(" ")
                        
                        if (parts.size > 2) {
                            val state = parts[2]
                            if (state == "Z") {
                                Log.w(TAG, "⚠️ 守护 #$guardId: 主进程变为僵尸进程")
                                handleMainProcessDeath()
                            }
                        }
                    }
                }
            } else {
                Log.w(TAG, "⚠️ 守护 #$guardId: 无法获取主进程PID")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查主进程失败", e)
        }
    }
    
    /**
     * 检查系统威胁
     */
    private fun checkSystemThreats() {
        try {
            // 检查SafeCenter活动
            checkSafeCenterActivity()
            
            // 检查系统杀进程日志
            checkKillProcessLogs()
            
        } catch (e: Exception) {
            Log.e(TAG, "检查系统威胁失败", e)
        }
    }
    
    /**
     * 检查SafeCenter活动
     */
    private fun checkSafeCenterActivity() {
        try {
            // 检查SafeCenter相关进程
            val procDir = File("/proc")
            val processes = procDir.listFiles { file ->
                file.isDirectory && file.name.matches(Regex("\\d+"))
            }
            
            processes?.forEach { processDir ->
                try {
                    val cmdlineFile = File(processDir, "cmdline")
                    if (cmdlineFile.exists()) {
                        val processName = cmdlineFile.readText().trim('\u0000')
                        
                        if (processName.contains("safecenter", ignoreCase = true) ||
                            processName.contains("oppoguardelf", ignoreCase = true)) {
                            
                            Log.d(TAG, "🎯 守护 #$guardId: 检测到威胁进程 $processName")
                            
                            // 记录威胁
                            recordThreat("SafeCenter活动", processName)
                        }
                    }
                } catch (e: Exception) {
                    // 忽略无法访问的进程
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查SafeCenter活动失败", e)
        }
    }
    
    /**
     * 检查杀进程日志
     */
    private fun checkKillProcessLogs() {
        try {
            // 读取系统日志
            val logcat = Runtime.getRuntime().exec("logcat -d -t 100 -s system_server:* SafeCenter:*")
            val output = logcat.inputStream.bufferedReader().readText()
            
            if (output.contains(packageName)) {
                Log.w(TAG, "⚠️ 守护 #$guardId: 在系统日志中发现针对本应用的活动")
                recordThreat("系统日志威胁", "发现针对本应用的系统活动")
            }
            
        } catch (e: Exception) {
            // 日志读取可能失败，这是正常的
        }
    }
    
    /**
     * 处理主进程死亡
     */
    private fun handleMainProcessDeath() {
        try {
            Log.w(TAG, "⚔️ 守护 #$guardId: 主进程死亡，执行复活程序")
            
            // 记录死亡事件
            recordThreat("主进程死亡", "主进程被系统杀死")
            
            // 等待一段时间，避免立即重启
            Thread.sleep(3000)
            
            // 重启主应用
            restartMainApplication()
            
        } catch (e: Exception) {
            Log.e(TAG, "处理主进程死亡失败", e)
        }
    }
    
    /**
     * 重启主应用
     */
    private fun restartMainApplication() {
        try {
            val intent = packageManager.getLaunchIntentForPackage(packageName)
            intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            
            startActivity(intent)
            
            Log.i(TAG, "🔄 守护 #$guardId: 主应用重启指令已发送")
            
        } catch (e: Exception) {
            Log.e(TAG, "重启主应用失败", e)
        }
    }
    
    /**
     * 获取主进程PID
     */
    private fun getMainProcessPid(): Int {
        return try {
            // 查找主进程
            val procDir = File("/proc")
            val processes = procDir.listFiles { file ->
                file.isDirectory && file.name.matches(Regex("\\d+"))
            }
            
            processes?.forEach { processDir ->
                try {
                    val cmdlineFile = File(processDir, "cmdline")
                    if (cmdlineFile.exists()) {
                        val processName = cmdlineFile.readText().trim('\u0000')
                        
                        if (processName == packageName) {
                            return processDir.name.toInt()
                        }
                    }
                } catch (e: Exception) {
                    // 忽略
                }
            }
            
            -1 // 未找到
            
        } catch (e: Exception) {
            Log.e(TAG, "获取主进程PID失败", e)
            -1
        }
    }
    
    /**
     * 记录威胁
     */
    private fun recordThreat(threatType: String, details: String) {
        try {
            val threatLog = buildString {
                appendLine("=== 威胁记录 ===")
                appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                appendLine("守护ID: $guardId")
                appendLine("威胁类型: $threatType")
                appendLine("详细信息: $details")
                appendLine("=== 记录结束 ===")
                appendLine()
            }
            
            // 写入本地文件
            val logFile = File(filesDir, "threat_log.txt")
            logFile.appendText(threatLog)
            
            // 上传到七牛云
            Thread {
                try {
                    val fileName = "threat_${System.currentTimeMillis()}.log"
                    com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadThreatLog(
                        this@ProcessGuardService,
                        threatLog,
                        fileName
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "上传威胁日志失败", e)
                }
            }.start()
            
        } catch (e: Exception) {
            Log.e(TAG, "记录威胁失败", e)
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "进程守护服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "保护应用进程不被系统杀死"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("系统安全扫描")
            .setContentText("正在进行安全检查...")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }
}
