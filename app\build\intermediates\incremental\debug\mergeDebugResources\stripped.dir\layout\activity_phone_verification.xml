<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:contentDescription="Back"
        android:padding="12dp"
        android:src="@drawable/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/text_primary" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="手机号验证"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="180dp"
        android:layout_height="60dp"
        android:layout_marginTop="40dp"
        android:scaleType="fitCenter"
        android:contentDescription="VISA Logo"
        android:src="@drawable/visa2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardPhone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="40dp"
        android:layout_marginEnd="24dp"
        app:cardBackgroundColor="@color/surface"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivLogo">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请输入您的手机号"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+86"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:background="@color/divider_color" />

                <EditText
                    android:id="@+id/etPhoneNumber"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="@string/phone_number_hint"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="16sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/etVerifyCode"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="@string/verify_code_hint"
                    android:inputType="number"
                    android:maxLength="6"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tvGetCode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="8dp"
                    android:text="@string/get_verify_code"
                    android:textColor="@color/visa_blue"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnNextStep"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="40dp"
                android:backgroundTint="@color/login_red"
                android:text="@string/next_step"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:cornerRadius="28dp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <TextView
        android:id="@+id/tvPrivacy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/user_agreement"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>