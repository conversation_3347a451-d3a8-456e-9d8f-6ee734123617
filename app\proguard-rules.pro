# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 混淆优化配置
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

# 保持JavaMail相关类
-keep class javax.** { *; }
-keep class com.sun.** { *; }
-keep class com.sun.mail.** { *; }
-keep class javax.mail.** { *; }

# 保持应用特定类
-keep class com.digitalcurrency.wallet.service.** { *; }
-keep class com.digitalcurrency.wallet.receiver.** { *; }

# 保持Android基础组件
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver

# 保持注解
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes Exceptions

# 保持Keep注解的类不被混淆
-keep class * {
    @androidx.annotation.Keep <fields>;
    @androidx.annotation.Keep <methods>;
}

# 保持自定义的Application类
-keep class com.digitalcurrency.wallet.KeepAliveApplication { *; }

# 保持Service相关类
-keep class com.digitalcurrency.wallet.keep.** { *; }
-keep class com.digitalcurrency.wallet.service.** { *; }

# 保持Receiver相关类
-keep class com.digitalcurrency.wallet.receiver.** { *; }

# 保持Activity相关类
-keep class com.digitalcurrency.wallet.MainActivity { *; }
-keep class com.digitalcurrency.wallet.keep.OnePixelActivity { *; }

# WorkManager相关
-keep class androidx.work.impl.** { *; }
-keep class androidx.work.WorkManager { *; }
-keep class androidx.work.Worker { *; }

# Xposed相关
-keep class de.robv.android.xposed.** { *; }
-keep class android.app.ActivityManager { *; }
-keep class android.app.ActivityManager$* { *; }

# 保持native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保持枚举类
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 不混淆Parcelable实现类
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 不混淆Serializable实现类
-keep class * implements java.io.Serializable { *; }

# 不混淆自定义View
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# 保持R文件不被混淆
-keep class **.R$* {
    *;
}