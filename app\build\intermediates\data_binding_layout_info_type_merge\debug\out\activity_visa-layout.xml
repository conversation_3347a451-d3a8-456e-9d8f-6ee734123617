<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_visa" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_visa.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_visa_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="167" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="16" endOffset="43"/></Target><Target id="@+id/logo" view="ImageView"><Expressions/><location startLine="18" startOffset="4" endLine="27" endOffset="59"/></Target><Target id="@+id/welcome_text" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="43" endOffset="56"/></Target><Target id="@+id/card_info" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="45" startOffset="4" endLine="114" endOffset="55"/></Target><Target id="@+id/card_holder_name" view="TextView"><Expressions/><location startLine="88" startOffset="12" endLine="95" endOffset="41"/></Target><Target id="@+id/card_number" view="TextView"><Expressions/><location startLine="105" startOffset="12" endLine="111" endOffset="41"/></Target><Target id="@+id/button_services" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="116" startOffset="4" endLine="132" endOffset="61"/></Target><Target id="@+id/button_transactions" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="134" startOffset="4" endLine="150" endOffset="67"/></Target><Target id="@+id/text_service_status" view="TextView"><Expressions/><location startLine="152" startOffset="4" endLine="165" endOffset="71"/></Target></Targets></Layout>