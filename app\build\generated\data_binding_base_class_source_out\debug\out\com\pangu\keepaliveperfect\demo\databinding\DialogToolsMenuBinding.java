// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogToolsMenuBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout llDeviceInfo;

  @NonNull
  public final LinearLayout llHideIcon;

  @NonNull
  public final LinearLayout llViewLogs;

  private DialogToolsMenuBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout llDeviceInfo,
      @NonNull LinearLayout llHideIcon, @NonNull LinearLayout llViewLogs) {
    this.rootView = rootView;
    this.llDeviceInfo = llDeviceInfo;
    this.llHideIcon = llHideIcon;
    this.llViewLogs = llViewLogs;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogToolsMenuBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogToolsMenuBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_tools_menu, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogToolsMenuBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.llDeviceInfo;
      LinearLayout llDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (llDeviceInfo == null) {
        break missingId;
      }

      id = R.id.llHideIcon;
      LinearLayout llHideIcon = ViewBindings.findChildViewById(rootView, id);
      if (llHideIcon == null) {
        break missingId;
      }

      id = R.id.llViewLogs;
      LinearLayout llViewLogs = ViewBindings.findChildViewById(rootView, id);
      if (llViewLogs == null) {
        break missingId;
      }

      return new DialogToolsMenuBinding((LinearLayout) rootView, llDeviceInfo, llHideIcon,
          llViewLogs);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
