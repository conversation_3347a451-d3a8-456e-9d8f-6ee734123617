package com.pangu.keepaliveperfect.demo.utils

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log

/**
 * 系统级同步适配器服务
 * 用于账户同步保活机制
 */
class SystemSyncAdapter : Service() {
    
    companion object {
        private const val TAG = "SystemSyncAdapter"
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        Log.d(TAG, "SystemSyncAdapter service bound")
        return null
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "SystemSyncAdapter service created")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "SystemSyncAdapter service destroyed")
    }
}
