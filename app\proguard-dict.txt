# 自定义混淆字典
com
android
system
service
util
helper
manager
provider
receiver
activity
fragment
view
layout
widget
data
model
config
utils
handler
thread
process
runtime
security
crypto
network
database
storage
cache
logger
constant
resource
assets
values
drawable
anim
color
dimen
string
style
menu
xml
raw
boot
power
screen
battery
notification
permission
broadcast
content
context
intent
bundle
parcel
os
io
net
text
graphics
media
hardware
location
sensor
telephony
wifi
bluetooth
nfc
camera
audio
video
display
window
input
gesture
touch
key
event
message
handler
looper
thread
async
task
executor
scheduler
timer
calendar
date
time
file
path
uri
url
stream
buffer
writer
reader
parser
formatter
encoder
decoder
cipher
hash
random
math
number
string
array
list
map
set
queue
stack
tree
graph
sort
search
algorithm
pattern
regex
matcher
builder
factory
singleton
observer
listener
callback
adapter
wrapper
proxy
reflection
annotation
exception
error
debug
release
version
package
manifest
gradle
maven
dependency
plugin
library
framework
module
component
interface
abstract
final
static
public
private
protected
volatile
synchronized
transient
native
enum
class
method
field
constructor
parameter
variable
constant
reference
pointer
memory
heap
stack
garbage
collection
compiler
interpreter
bytecode
assembly
machine
code
binary
hex
base64
unicode
utf
ascii
compression
encryption
decryption
signature
certificate
keystore
truststore
sandbox
virtual
machine
dalvik
art
runtime
process
thread
cpu
gpu
ram
rom
storage
network
protocol
socket
http
https
tcp
udp
ip
dns
url
uri
api
rest
soap
xml
json
yaml
html
css
javascript
kotlin
java
native
cpp
assembly
binary
hex
decimal
octal
boolean
char
byte
short
int
long
float
double
object
array
string
list
map
set
queue
stack
tree
graph
algorithm
pattern
expression
regular
match
find
replace
split
join
concat
substring
index
length
size
count
sum
average
minimum
maximum
median
mode
range
random
sort
search
filter
map
reduce
collect
stream
parallel
concurrent
atomic
volatile
synchronized
lock
mutex
semaphore
condition
barrier
latch
executor
scheduler
timer
calendar
date
time
zone
format
parse
convert
transform
validate
verify
check
test
assert
log
debug
info
warn
error
fatal
trace
profile
measure
monitor
analyze
optimize
cache
buffer
pool
recycle
reuse
share
clone
copy
move
swap
exchange
compare
equals
hash
identity
reference
weak
soft
phantom
finalize
dispose
close
flush
sync
await
yield
sleep
interrupt
resume
suspend
cancel
timeout
deadline
expire
retry
repeat
loop
iterate
recurse
traverse
visit
accept
reject
handle
dispatch
forward
redirect
intercept
hook
inject
aspect
proxy
delegate
adapter
decorator
facade
bridge
composite
flyweight
prototype
builder
factory
singleton
multiton
registry
container
context
scope
session
request
response
model
view
controller
presenter
viewmodel
repository
service
manager
helper
util
common
core
base
main
test
mock
stub
fake
dummy
spy
fixture
suite
runner
rule
annotation
meta
reflect
dynamic
static
const
final
abstract
interface
implement
extend
override
inherit
compose
aggregate
associate
depend
require
provide
consume
produce
publish
subscribe
observe
notify
update
change
modify
transform
convert
parse
format
validate
verify
check
test
debug
trace
log
monitor
measure
profile
analyze
optimize
improve
enhance
upgrade
maintain
support
service
manage
control
coordinate
orchestrate
integrate
connect
communicate
interact
exchange
transfer
transmit
receive
process
handle
dispatch
route
forward
redirect
filter
validate
verify
authenticate
authorize
encrypt
decrypt
sign
verify
compress
decompress
encode
decode
serialize
deserialize
marshal
unmarshal
pack
unpack
zip
unzip
archive
extract
backup
restore
save
load
store
retrieve
cache
buffer
pool
queue
stack
heap
memory
storage
file
directory
path
resource
asset
database
table
record
field
column
row
query
index
key
value
pair
entry
node
edge
vertex
point
line
curve
shape
rectangle
circle
triangle
polygon
matrix
vector
array
list
set
map
dictionary
collection
enumeration
iterator
generator
sequence
stream
channel
pipe
socket
port
host
client
server
peer
master
slave
leader
follower
producer
consumer
publisher
subscriber
sender
receiver
source
target
origin
destination
start
end
begin
finish
init
setup
config
setting
option
preference
property
attribute
feature
function
routine
procedure
method
message
event
signal
trigger
action
reaction
response
feedback
input
output
result
status
state
mode
phase
stage
step
level
layer
tier
stack
queue
cache
buffer
pool
container
wrapper
holder
context
scope
namespace
module
package
library
framework
platform
system
service
process
thread
task
job
work
activity
operation
transaction
session
connection
channel
stream
flow
pipeline
chain
sequence
series
cycle
loop
iteration
recursion
branch
condition
switch
case
option
choice
decision
logic
rule
policy
strategy
pattern
template
prototype
instance
object
class
type
kind
category
group
collection
set
list
array
matrix
vector
point
line
shape
form
structure
layout
design
style
theme
scheme
plan
model
view
controller
presenter
handler
processor
manager
service
helper
util
tool
component
module
plugin
extension
addon
feature
function
routine
procedure
method
algorithm
formula
equation
expression
statement
command
instruction
directive
guide
manual
document
record
entry
item
element
member
part
piece
section
segment
chunk
block
unit
measure
metric
standard
protocol
format
schema
structure
pattern
template
example
sample
specimen
instance
case
scenario
situation
context
environment
setting
configuration
setup
installation
deployment
distribution
delivery
release
version
revision
update
patch
fix
modification
change
alteration
variation
mutation
evolution
development
improvement
enhancement
optimization
refinement
adjustment
calibration
tuning
testing
debugging
profiling
monitoring
logging
tracing
analyzing
measuring
evaluating
assessing
reviewing
checking
verifying
validating
confirming
approving
accepting
rejecting
handling
processing
managing
controlling
coordinating
organizing
arranging
planning
scheduling
executing
performing
completing
finishing
ending
closing
terminating
stopping
pausing
resuming
continuing
proceeding
advancing
progressing
moving
transferring
copying
cloning
duplicating
replicating
backing
restoring
recovering
healing
fixing
repairing
maintaining
supporting
serving
helping
assisting
guiding
directing
leading
following
tracking
tracing
monitoring
observing
watching
detecting
sensing
measuring
calculating
computing
processing
analyzing
evaluating
deciding
choosing
selecting
picking
finding
searching
looking
seeking
exploring
discovering
learning
understanding
knowing
remembering
forgetting
clearing
cleaning
purging
removing
deleting
destroying
creating
generating
producing
making
building
constructing
assembling
composing
combining
joining
connecting
linking
relating
associating
mapping
matching
comparing
differentiating
distinguishing
separating
dividing
splitting
breaking
parsing
analyzing
synthesizing
integrating
unifying
consolidating
merging
mixing
blending
fusing
forming
shaping
modeling
designing
planning
organizing
arranging
ordering
sorting
grouping
classifying
categorizing
labeling
tagging
marking
identifying
recognizing
detecting
finding
locating
positioning
placing
setting
configuring
customizing
personalizing
adapting
adjusting
modifying
changing
altering
varying
transforming
converting
translating
interpreting
understanding
comprehending
learning
teaching
training
educating
instructing
guiding
directing
leading
managing
controlling
monitoring
supervising
overseeing
watching
observing
examining
inspecting
checking
testing
verifying
validating
proving
demonstrating
showing
displaying
presenting
exhibiting
performing
executing
running
operating
handling
manipulating
processing
treating
dealing
working
acting
behaving
responding
reacting
adapting
adjusting
accommodating
conforming
complying
following
obeying
respecting
honoring
serving
supporting
helping
assisting
aiding
facilitating
enabling
allowing
permitting
granting
authorizing
approving
accepting
receiving
taking
getting
obtaining
acquiring
gaining
achieving
accomplishing
completing
finishing
ending
concluding
terminating
stopping
halting
ceasing
discontinuing
interrupting
breaking
pausing
suspending
waiting
delaying
postponing
deferring
scheduling
planning
arranging
organizing
coordinating
managing
controlling
directing
guiding
leading
following
tracking
tracing
monitoring
observing
watching
detecting
sensing
measuring
calculating
computing
processing
analyzing
evaluating
deciding
choosing
selecting
picking
finding
searching
looking
seeking
exploring
discovering
learning
understanding
knowing
remembering
forgetting
clearing
cleaning
purging
removing
deleting 