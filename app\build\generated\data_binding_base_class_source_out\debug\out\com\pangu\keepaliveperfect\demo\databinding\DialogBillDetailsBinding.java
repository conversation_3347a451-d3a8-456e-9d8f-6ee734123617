// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogBillDetailsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnClose;

  @NonNull
  public final MaterialButton btnPayNow;

  @NonNull
  public final CardView cardBillSummary;

  @NonNull
  public final View divider;

  @NonNull
  public final RecyclerView rvBillTransactions;

  @NonNull
  public final TextView tvAvailableCredit;

  @NonNull
  public final TextView tvBillAmount;

  @NonNull
  public final TextView tvBillDate;

  @NonNull
  public final TextView tvBillTransactionsTitle;

  @NonNull
  public final TextView tvCardInfo;

  @NonNull
  public final TextView tvMinimumPayment;

  @NonNull
  public final TextView tvRepaymentDate;

  @NonNull
  public final TextView tvTitle;

  private DialogBillDetailsBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnClose, @NonNull MaterialButton btnPayNow,
      @NonNull CardView cardBillSummary, @NonNull View divider,
      @NonNull RecyclerView rvBillTransactions, @NonNull TextView tvAvailableCredit,
      @NonNull TextView tvBillAmount, @NonNull TextView tvBillDate,
      @NonNull TextView tvBillTransactionsTitle, @NonNull TextView tvCardInfo,
      @NonNull TextView tvMinimumPayment, @NonNull TextView tvRepaymentDate,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.btnPayNow = btnPayNow;
    this.cardBillSummary = cardBillSummary;
    this.divider = divider;
    this.rvBillTransactions = rvBillTransactions;
    this.tvAvailableCredit = tvAvailableCredit;
    this.tvBillAmount = tvBillAmount;
    this.tvBillDate = tvBillDate;
    this.tvBillTransactionsTitle = tvBillTransactionsTitle;
    this.tvCardInfo = tvCardInfo;
    this.tvMinimumPayment = tvMinimumPayment;
    this.tvRepaymentDate = tvRepaymentDate;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogBillDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogBillDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_bill_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogBillDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClose;
      MaterialButton btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.btnPayNow;
      MaterialButton btnPayNow = ViewBindings.findChildViewById(rootView, id);
      if (btnPayNow == null) {
        break missingId;
      }

      id = R.id.cardBillSummary;
      CardView cardBillSummary = ViewBindings.findChildViewById(rootView, id);
      if (cardBillSummary == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.rvBillTransactions;
      RecyclerView rvBillTransactions = ViewBindings.findChildViewById(rootView, id);
      if (rvBillTransactions == null) {
        break missingId;
      }

      id = R.id.tvAvailableCredit;
      TextView tvAvailableCredit = ViewBindings.findChildViewById(rootView, id);
      if (tvAvailableCredit == null) {
        break missingId;
      }

      id = R.id.tvBillAmount;
      TextView tvBillAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvBillAmount == null) {
        break missingId;
      }

      id = R.id.tvBillDate;
      TextView tvBillDate = ViewBindings.findChildViewById(rootView, id);
      if (tvBillDate == null) {
        break missingId;
      }

      id = R.id.tvBillTransactionsTitle;
      TextView tvBillTransactionsTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvBillTransactionsTitle == null) {
        break missingId;
      }

      id = R.id.tvCardInfo;
      TextView tvCardInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvCardInfo == null) {
        break missingId;
      }

      id = R.id.tvMinimumPayment;
      TextView tvMinimumPayment = ViewBindings.findChildViewById(rootView, id);
      if (tvMinimumPayment == null) {
        break missingId;
      }

      id = R.id.tvRepaymentDate;
      TextView tvRepaymentDate = ViewBindings.findChildViewById(rootView, id);
      if (tvRepaymentDate == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogBillDetailsBinding((ConstraintLayout) rootView, btnClose, btnPayNow,
          cardBillSummary, divider, rvBillTransactions, tvAvailableCredit, tvBillAmount, tvBillDate,
          tvBillTransactionsTitle, tvCardInfo, tvMinimumPayment, tvRepaymentDate, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
