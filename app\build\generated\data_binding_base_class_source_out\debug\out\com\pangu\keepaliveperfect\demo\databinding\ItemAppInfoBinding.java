// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAppInfoBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView appIconImageView;

  @NonNull
  public final TextView appNameTextView;

  @NonNull
  public final TextView appPackageTextView;

  private ItemAppInfoBinding(@NonNull LinearLayout rootView, @NonNull ImageView appIconImageView,
      @NonNull TextView appNameTextView, @NonNull TextView appPackageTextView) {
    this.rootView = rootView;
    this.appIconImageView = appIconImageView;
    this.appNameTextView = appNameTextView;
    this.appPackageTextView = appPackageTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAppInfoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAppInfoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_app_info, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAppInfoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appIconImageView;
      ImageView appIconImageView = ViewBindings.findChildViewById(rootView, id);
      if (appIconImageView == null) {
        break missingId;
      }

      id = R.id.appNameTextView;
      TextView appNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (appNameTextView == null) {
        break missingId;
      }

      id = R.id.appPackageTextView;
      TextView appPackageTextView = ViewBindings.findChildViewById(rootView, id);
      if (appPackageTextView == null) {
        break missingId;
      }

      return new ItemAppInfoBinding((LinearLayout) rootView, appIconImageView, appNameTextView,
          appPackageTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
