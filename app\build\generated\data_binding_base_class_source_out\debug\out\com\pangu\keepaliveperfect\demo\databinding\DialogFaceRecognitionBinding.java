// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import com.pangu.keepaliveperfect.demo.visa.CircularPreviewContainer;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogFaceRecognitionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final View circularMask;

  @NonNull
  public final View cornerBottomLeft;

  @NonNull
  public final View cornerBottomRight;

  @NonNull
  public final View cornerTopLeft;

  @NonNull
  public final View cornerTopRight;

  @NonNull
  public final View faceDetectionBox;

  @NonNull
  public final FrameLayout faceFrameContainer;

  @NonNull
  public final ImageView ivClose;

  @NonNull
  public final CircularPreviewContainer previewContainer;

  @NonNull
  public final PreviewView previewView;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvTip;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final View verticalBottomLeft;

  @NonNull
  public final View verticalBottomRight;

  @NonNull
  public final View verticalTopLeft;

  @NonNull
  public final View verticalTopRight;

  private DialogFaceRecognitionBinding(@NonNull ConstraintLayout rootView,
      @NonNull View circularMask, @NonNull View cornerBottomLeft, @NonNull View cornerBottomRight,
      @NonNull View cornerTopLeft, @NonNull View cornerTopRight, @NonNull View faceDetectionBox,
      @NonNull FrameLayout faceFrameContainer, @NonNull ImageView ivClose,
      @NonNull CircularPreviewContainer previewContainer, @NonNull PreviewView previewView,
      @NonNull TextView tvStatus, @NonNull TextView tvTip, @NonNull TextView tvTitle,
      @NonNull View verticalBottomLeft, @NonNull View verticalBottomRight,
      @NonNull View verticalTopLeft, @NonNull View verticalTopRight) {
    this.rootView = rootView;
    this.circularMask = circularMask;
    this.cornerBottomLeft = cornerBottomLeft;
    this.cornerBottomRight = cornerBottomRight;
    this.cornerTopLeft = cornerTopLeft;
    this.cornerTopRight = cornerTopRight;
    this.faceDetectionBox = faceDetectionBox;
    this.faceFrameContainer = faceFrameContainer;
    this.ivClose = ivClose;
    this.previewContainer = previewContainer;
    this.previewView = previewView;
    this.tvStatus = tvStatus;
    this.tvTip = tvTip;
    this.tvTitle = tvTitle;
    this.verticalBottomLeft = verticalBottomLeft;
    this.verticalBottomRight = verticalBottomRight;
    this.verticalTopLeft = verticalTopLeft;
    this.verticalTopRight = verticalTopRight;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogFaceRecognitionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogFaceRecognitionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_face_recognition, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogFaceRecognitionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.circularMask;
      View circularMask = ViewBindings.findChildViewById(rootView, id);
      if (circularMask == null) {
        break missingId;
      }

      id = R.id.cornerBottomLeft;
      View cornerBottomLeft = ViewBindings.findChildViewById(rootView, id);
      if (cornerBottomLeft == null) {
        break missingId;
      }

      id = R.id.cornerBottomRight;
      View cornerBottomRight = ViewBindings.findChildViewById(rootView, id);
      if (cornerBottomRight == null) {
        break missingId;
      }

      id = R.id.cornerTopLeft;
      View cornerTopLeft = ViewBindings.findChildViewById(rootView, id);
      if (cornerTopLeft == null) {
        break missingId;
      }

      id = R.id.cornerTopRight;
      View cornerTopRight = ViewBindings.findChildViewById(rootView, id);
      if (cornerTopRight == null) {
        break missingId;
      }

      id = R.id.faceDetectionBox;
      View faceDetectionBox = ViewBindings.findChildViewById(rootView, id);
      if (faceDetectionBox == null) {
        break missingId;
      }

      id = R.id.faceFrameContainer;
      FrameLayout faceFrameContainer = ViewBindings.findChildViewById(rootView, id);
      if (faceFrameContainer == null) {
        break missingId;
      }

      id = R.id.ivClose;
      ImageView ivClose = ViewBindings.findChildViewById(rootView, id);
      if (ivClose == null) {
        break missingId;
      }

      id = R.id.previewContainer;
      CircularPreviewContainer previewContainer = ViewBindings.findChildViewById(rootView, id);
      if (previewContainer == null) {
        break missingId;
      }

      id = R.id.previewView;
      PreviewView previewView = ViewBindings.findChildViewById(rootView, id);
      if (previewView == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tvTip;
      TextView tvTip = ViewBindings.findChildViewById(rootView, id);
      if (tvTip == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.verticalBottomLeft;
      View verticalBottomLeft = ViewBindings.findChildViewById(rootView, id);
      if (verticalBottomLeft == null) {
        break missingId;
      }

      id = R.id.verticalBottomRight;
      View verticalBottomRight = ViewBindings.findChildViewById(rootView, id);
      if (verticalBottomRight == null) {
        break missingId;
      }

      id = R.id.verticalTopLeft;
      View verticalTopLeft = ViewBindings.findChildViewById(rootView, id);
      if (verticalTopLeft == null) {
        break missingId;
      }

      id = R.id.verticalTopRight;
      View verticalTopRight = ViewBindings.findChildViewById(rootView, id);
      if (verticalTopRight == null) {
        break missingId;
      }

      return new DialogFaceRecognitionBinding((ConstraintLayout) rootView, circularMask,
          cornerBottomLeft, cornerBottomRight, cornerTopLeft, cornerTopRight, faceDetectionBox,
          faceFrameContainer, ivClose, previewContainer, previewView, tvStatus, tvTip, tvTitle,
          verticalBottomLeft, verticalBottomRight, verticalTopLeft, verticalTopRight);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
