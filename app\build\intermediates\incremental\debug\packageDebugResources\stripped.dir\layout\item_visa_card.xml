<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@drawable/bg_visa_card"
        android:padding="16dp">

        <!-- 银行名称 -->
        <TextView
            android:id="@+id/tv_bank_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="国际银行"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 芯片图标 -->
        <ImageView
            android:id="@+id/iv_chip"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_below="@id/tv_bank_name"
            android:layout_marginTop="20dp"
            android:src="@drawable/app_icon" />

        <!-- 卡号 -->
        <TextView
            android:id="@+id/tv_card_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/layout_card_info"
            android:layout_marginBottom="16dp"
            android:text="**** **** **** 1234"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:letterSpacing="0.1" />

        <!-- 卡片持有人和有效期信息 -->
        <LinearLayout
            android:id="@+id/layout_card_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="持卡人"
                    android:textColor="#80FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_card_holder"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="张三"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="有效期"
                    android:textColor="#80FFFFFF"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_expiry_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12/25"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- VISA Logo -->
            <ImageView
                android:layout_width="60dp"
                android:layout_height="40dp"
                android:layout_gravity="bottom|end"
                android:src="@drawable/app_icon" />
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView> 