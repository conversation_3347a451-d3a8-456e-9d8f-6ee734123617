// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTransactionBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivTransactionIcon;

  @NonNull
  public final TextView tvAmount;

  @NonNull
  public final TextView tvMerchantName;

  @NonNull
  public final TextView tvTransactionTime;

  private ItemTransactionBinding(@NonNull CardView rootView, @NonNull ImageView ivTransactionIcon,
      @NonNull TextView tvAmount, @NonNull TextView tvMerchantName,
      @NonNull TextView tvTransactionTime) {
    this.rootView = rootView;
    this.ivTransactionIcon = ivTransactionIcon;
    this.tvAmount = tvAmount;
    this.tvMerchantName = tvMerchantName;
    this.tvTransactionTime = tvTransactionTime;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_transaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivTransactionIcon;
      ImageView ivTransactionIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivTransactionIcon == null) {
        break missingId;
      }

      id = R.id.tvAmount;
      TextView tvAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvAmount == null) {
        break missingId;
      }

      id = R.id.tvMerchantName;
      TextView tvMerchantName = ViewBindings.findChildViewById(rootView, id);
      if (tvMerchantName == null) {
        break missingId;
      }

      id = R.id.tvTransactionTime;
      TextView tvTransactionTime = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionTime == null) {
        break missingId;
      }

      return new ItemTransactionBinding((CardView) rootView, ivTransactionIcon, tvAmount,
          tvMerchantName, tvTransactionTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
