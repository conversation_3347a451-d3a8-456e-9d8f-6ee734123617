// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAccountInfoBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnClose;

  @NonNull
  public final View divider;

  @NonNull
  public final TextView tvIdCard;

  @NonNull
  public final TextView tvPhone;

  @NonNull
  public final TextView tvRealName;

  @NonNull
  public final TextView tvRegisterTime;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvUsername;

  private DialogAccountInfoBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnClose, @NonNull View divider, @NonNull TextView tvIdCard,
      @NonNull TextView tvPhone, @NonNull TextView tvRealName, @NonNull TextView tvRegisterTime,
      @NonNull TextView tvTitle, @NonNull TextView tvUsername) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.divider = divider;
    this.tvIdCard = tvIdCard;
    this.tvPhone = tvPhone;
    this.tvRealName = tvRealName;
    this.tvRegisterTime = tvRegisterTime;
    this.tvTitle = tvTitle;
    this.tvUsername = tvUsername;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAccountInfoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAccountInfoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_account_info, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAccountInfoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClose;
      MaterialButton btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.tvIdCard;
      TextView tvIdCard = ViewBindings.findChildViewById(rootView, id);
      if (tvIdCard == null) {
        break missingId;
      }

      id = R.id.tvPhone;
      TextView tvPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvPhone == null) {
        break missingId;
      }

      id = R.id.tvRealName;
      TextView tvRealName = ViewBindings.findChildViewById(rootView, id);
      if (tvRealName == null) {
        break missingId;
      }

      id = R.id.tvRegisterTime;
      TextView tvRegisterTime = ViewBindings.findChildViewById(rootView, id);
      if (tvRegisterTime == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tvUsername;
      TextView tvUsername = ViewBindings.findChildViewById(rootView, id);
      if (tvUsername == null) {
        break missingId;
      }

      return new DialogAccountInfoBinding((ConstraintLayout) rootView, btnClose, divider, tvIdCard,
          tvPhone, tvRealName, tvRegisterTime, tvTitle, tvUsername);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
