package com.pangu.keepaliveperfect.demo.receiver

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.service.DaemonService
import com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper

/**
 * 增强版服务重启广播接收器
 * 实现多进程保活和服务相互唤醒
 */
class ServiceRestartReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "ServiceRestartReceiver"
        private const val MAX_RESTART_ATTEMPTS = 3
        private var restartAttempts = 0
        private var lastRestartTime = 0L
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "收到服务重启广播: ${intent.action}")
        
        try {
            val action = intent.action ?: return
            
            if (action == "com.pangu.keepaliveperfect.demo.action.START_SERVICE" || 
                action == "com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE") {
                
                // 检查重启次数限制
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastRestartTime > 60000) { // 1分钟重置计数
                    restartAttempts = 0
                }
                
                if (restartAttempts < MAX_RESTART_ATTEMPTS) {
                    restartAttempts++
                    lastRestartTime = currentTime
                    
                    // 启动所有保活服务
                    startAllServices(context)
                    
                    // 刷新通知监听服务
                    NotificationAccessHelper.refreshNotificationListenerConnection(context)
                    
                    Log.d(TAG, "已尝试重启所有服务，当前尝试次数: $restartAttempts")
                } else {
                    Log.d(TAG, "已达到最大重启次数限制，等待冷却时间")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理服务重启广播失败: ${e.message}")
        }
    }
    
    /**
     * 启动所有保活相关服务
     */
    private fun startAllServices(context: Context) {
        try {
            // 1. 启动主保活服务
            startKeepAliveService(context)
            
            // 2. 启动守护服务
            startDaemonService(context)
            
            // 3. 启动其他相关服务
            startOtherServices(context)
            
            // 4. 检查服务是否成功启动
            checkServicesStatus(context)
            
        } catch (e: Exception) {
            Log.e(TAG, "启动服务组失败: ${e.message}")
        }
    }
    
    /**
     * 启动主保活服务
     */
    private fun startKeepAliveService(context: Context) {
        try {
            val serviceIntent = Intent(context, KeepAliveService::class.java)
            serviceIntent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
            Log.d(TAG, "已启动主保活服务")
        } catch (e: Exception) {
            Log.e(TAG, "启动主保活服务失败: ${e.message}")
        }
    }
    
    /**
     * 启动守护服务
     */
    private fun startDaemonService(context: Context) {
        try {
            val daemonIntent = Intent(context, DaemonService::class.java)
            daemonIntent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(daemonIntent)
            } else {
                context.startService(daemonIntent)
            }
            
            Log.d(TAG, "已启动守护服务")
        } catch (e: Exception) {
            Log.e(TAG, "启动守护服务失败: ${e.message}")
        }
    }
    
    /**
     * 启动其他相关服务
     */
    private fun startOtherServices(context: Context) {
        try {
            // 可以在这里添加其他服务的启动代码
            // 例如JobService、SyncService等
            
            // 尝试激活通知监听服务
            if (NotificationAccessHelper.isNotificationAccessEnabled(context)) {
                NotificationAccessHelper.refreshNotificationListenerConnection(context)
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动其他服务失败: ${e.message}")
        }
    }
    
    /**
     * 检查服务状态
     */
    private fun checkServicesStatus(context: Context) {
        try {
            val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = am.getRunningServices(100)
            
            val mainServiceRunning = runningServices.any { 
                it.service.className == KeepAliveService::class.java.name 
            }
            val daemonServiceRunning = runningServices.any { 
                it.service.className == DaemonService::class.java.name 
            }
            
            Log.d(TAG, "服务状态检查 - 主服务: $mainServiceRunning, 守护服务: $daemonServiceRunning")
            
            // 如果有服务未运行，触发重启
            if (!mainServiceRunning || !daemonServiceRunning) {
                startAllServices(context)
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查服务状态失败: ${e.message}")
        }
    }
} 