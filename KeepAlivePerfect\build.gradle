// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.pangu.keepaliveperfect'
    compileSdk 33

    defaultConfig {
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0"
        
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
        
        manifestPlaceholders = [
            PACKAGE_NAME: 'com.pangu.keepaliveperfect'
        ]
        
        multiDexEnabled true
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            proguardFile 'proguard-rules.pro'
            
            debuggable false
            jniDebuggable false
        }
        
        debug {
            minifyEnabled false
            debuggable true
            jniDebuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs += [
            "-Xopt-in=kotlin.RequiresOptIn",
            "-Xjvm-default=all"
        ]
    }

    buildFeatures {
        aidl true
        buildConfig true
        viewBinding true
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
        disable 'MissingTranslation', 'ExtraTranslation'
    }

    sourceSets {
        main {
            res.srcDirs = ['src/main/res']
            java.srcDirs = ['src/main/java']
            manifest.srcFile 'src/main/AndroidManifest.xml'
            jniLibs.srcDirs = ['libs']
        }
    }
    
    packagingOptions {
        resources {
            excludes += [
                'META-INF/DEPENDENCIES',
                'META-INF/LICENSE',
                'META-INF/LICENSE.txt',
                'META-INF/license.txt',
                'META-INF/NOTICE',
                'META-INF/NOTICE.txt',
                'META-INF/notice.txt',
                'META-INF/ASL2.0',
                'META-INF/*.kotlin_module'
            ]
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    
    implementation 'androidx.work:work-runtime-ktx:2.8.1'
    implementation 'androidx.lifecycle:lifecycle-process:2.6.1'
    
    implementation 'androidx.lifecycle:lifecycle-service:2.6.1'
    
    implementation 'org.jetbrains.kotlin:kotlin-reflect:1.8.20'
    
    implementation 'androidx.multidex:multidex:2.0.1'
    
    compileOnly files('libs/hidden-api-bypass.jar')
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}

kotlin {
    experimental {
        coroutines 'enable'
    }
}
