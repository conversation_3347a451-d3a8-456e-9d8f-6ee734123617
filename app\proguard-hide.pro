# 保持注解
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable

# 保持自定义组件不被混淆
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# 保持 Xposed 相关类
-keep class de.robv.android.xposed.** { *; }
-keep class android.** { *; }
-keep class com.android.** { *; }

# 保持 MMKV
-keep class com.tencent.mmkv.** { *; }

# 保持 WorkManager
-keep class androidx.work.** { *; }

# 自定义混淆字典
-obfuscationdictionary proguard-dict.txt
-classobfuscationdictionary proguard-dict.txt
-packageobfuscationdictionary proguard-dict.txt

# 优化配置
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# 混淆时不生成大小写混合的类名
-dontusemixedcaseclassnames

# 不忽略非公共库的类
-dontskipnonpubliclibraryclasses

# 不预校验
-dontpreverify

# 保持自定义控件类不被混淆
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public void set*(...);
    *** get*();
}

# 保持 Parcelable 序列化的类不被混淆
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保持 Serializable 序列化的类不被混淆
-keep class * implements java.io.Serializable { *; }

# 保持 native 方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保持自定义控件类不被混淆
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# 保持枚举类不被混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 不混淆 R 文件中的所有静态字段
-keepclassmembers class **.R$* {
    public static <fields>;
} 