package com.pangu.keepaliveperfect.demo.visa

import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout

import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.utils.UserDataManager
import java.util.regex.Pattern

class RegisterActivity : AppCompatActivity() {

    // 当前步骤
    private var currentStep = 1

    // 步骤指示器
    private lateinit var tvStep1: TextView
    private lateinit var tvStep2: TextView
    private lateinit var tvStep3: TextView
    private lateinit var tvStep4: TextView
    private lateinit var tvStepTitle: TextView

    // 步骤布局
    private lateinit var clStep1: ConstraintLayout
    private lateinit var clStep2: ConstraintLayout
    private lateinit var clStep3: ConstraintLayout
    private lateinit var clStep4: ConstraintLayout

    // 步骤1：基本信息
    private lateinit var etPhoneNumber: TextInputEditText
    private lateinit var etPassword: TextInputEditText
    private lateinit var etConfirmPassword: TextInputEditText
    private lateinit var tvPasswordStrength: TextView

    // 步骤2：支付密码
    private lateinit var etPaymentPassword: TextInputEditText
    private lateinit var etConfirmPaymentPassword: TextInputEditText

    // 步骤3：交易密码
    private lateinit var etTransactionPassword: TextInputEditText
    private lateinit var etConfirmTransactionPassword: TextInputEditText

    // 步骤4：身份信息
    private lateinit var etRealName: TextInputEditText
    private lateinit var etIdNumber: TextInputEditText
    private lateinit var tvFaceRecognitionHint: TextView

    // 下一步按钮
    private lateinit var btnNext: MaterialButton

    // 存储密码
    private var paymentPassword = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_register)

        // 设置窗口软键盘模式，确保输入法弹出时布局调整
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        initViews()
        setupListeners()

        // 直接隐藏VISA图标，简化处理
        findViewById<ImageView>(R.id.ivLogo).visibility = View.GONE

        // 调整ScrollView的顶部约束
        adjustScrollViewTopConstraint()
    }

    private fun initViews() {
        // 步骤指示器
        tvStep1 = findViewById(R.id.tvStep1)
        tvStep2 = findViewById(R.id.tvStep2)
        tvStep3 = findViewById(R.id.tvStep3)
        tvStep4 = findViewById(R.id.tvStep4)
        tvStepTitle = findViewById(R.id.tvStepTitle)

        // 步骤布局
        clStep1 = findViewById(R.id.clStep1)
        clStep2 = findViewById(R.id.clStep2)
        clStep3 = findViewById(R.id.clStep3)
        clStep4 = findViewById(R.id.clStep4)

        // 步骤1：基本信息
        etPhoneNumber = findViewById(R.id.etPhoneNumber)
        etPassword = findViewById(R.id.etPassword)
        etConfirmPassword = findViewById(R.id.etConfirmPassword)
        tvPasswordStrength = findViewById(R.id.tvPasswordStrength)

        // 步骤2：支付密码
        etPaymentPassword = findViewById(R.id.etPaymentPassword)
        etConfirmPaymentPassword = findViewById(R.id.etConfirmPaymentPassword)

        // 步骤3：交易密码
        etTransactionPassword = findViewById(R.id.etTransactionPassword)
        etConfirmTransactionPassword = findViewById(R.id.etConfirmTransactionPassword)

        // 步骤4：身份信息
        etRealName = findViewById(R.id.etRealName)
        etIdNumber = findViewById(R.id.etIdNumber)
        tvFaceRecognitionHint = findViewById(R.id.tvFaceRecognitionHint)

        // 下一步按钮
        btnNext = findViewById(R.id.btnNext)

        // 返回按钮
        val ivBack = findViewById<ImageView>(R.id.ivBack)
        ivBack.setOnClickListener {
            finish()
        }


    }

    private fun setupListeners() {
        // 密码强度检查
        etPassword.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                checkPasswordStrength(s.toString())
            }
        })

        // 身份信息完整性检查
        val identityTextWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                checkIdentityInfo()
            }
        }
        etRealName.addTextChangedListener(identityTextWatcher)
        etIdNumber.addTextChangedListener(identityTextWatcher)

        // 为所有输入框添加焦点变化监听，确保在输入法弹出时自动滚动
        setupFocusChangeListeners()

        // 下一步按钮
        btnNext.setOnClickListener {
            when (currentStep) {
                1 -> validateAndProceedToStep2()
                2 -> validateAndProceedToStep3()
                3 -> validateAndProceedToStep4()
                4 -> {
                    // 最后一步，直接进行人脸识别
                    val name = etRealName.text.toString().trim()
                    val idNumber = etIdNumber.text.toString().trim()

                    if (name.isNotEmpty() && idNumber.length == 18) {
                        simulateFaceRecognition()
                    } else {
                        Toast.makeText(this, "请先填写完整的身份信息", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }
    }

    private fun checkPasswordStrength(password: String) {
        if (password.isEmpty()) {
            tvPasswordStrength.text = "密码强度：弱"
            tvPasswordStrength.setTextColor(resources.getColor(R.color.error_red, null))
            return
        }

        // 检查密码是否包含数字和字母
        val hasDigit = password.any { it.isDigit() }
        val hasLetter = password.any { it.isLetter() }
        val isLongEnough = password.length >= 6

        if (hasDigit && hasLetter && isLongEnough) {
            if (password.length >= 8) {
                tvPasswordStrength.text = "密码强度：强"
                tvPasswordStrength.setTextColor(resources.getColor(R.color.success_green, null))
            } else {
                tvPasswordStrength.text = "密码强度：中"
                tvPasswordStrength.setTextColor(resources.getColor(R.color.warning_yellow, null))
            }
        } else {
            tvPasswordStrength.text = "密码强度：弱"
            tvPasswordStrength.setTextColor(resources.getColor(R.color.error_red, null))
        }
    }

    private fun checkIdentityInfo() {
        val name = etRealName.text.toString().trim()
        val idNumber = etIdNumber.text.toString().trim().uppercase() // 转为大写，确保X是大写

        if (name.isNotEmpty() && isValidIdNumber(idNumber)) {
            btnNext.backgroundTintList = resources.getColorStateList(R.color.login_red, null)
            tvFaceRecognitionHint.text = "请点击下方\"完成注册\"按钮进行人脸识别"
        } else {
            btnNext.backgroundTintList = resources.getColorStateList(R.color.visa_blue, null)
            tvFaceRecognitionHint.text = "填写完整信息后，点击下方\"完成注册\"按钮进行人脸识别"
        }
    }

    /**
     * 验证身份证号码是否有效
     * 简单验证：长度为18位，最后一位可以是数字或X
     */
    private fun isValidIdNumber(idNumber: String): Boolean {
        // 验证长度
        if (idNumber.length != 18) {
            return false
        }

        // 验证前17位是否都是数字
        val regex = Regex("^[0-9]{17}[0-9X]$")
        return regex.matches(idNumber)
    }

    private fun validateAndProceedToStep2() {
        val phone = etPhoneNumber.text.toString().trim()
        val password = etPassword.text.toString()
        val confirmPassword = etConfirmPassword.text.toString()

        // 验证手机号
        if (phone.length != 11 || !Pattern.matches("^1[3-9]\\d{9}$", phone)) {
            Toast.makeText(this, "请输入有效的手机号码", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证密码
        if (password.length < 6) {
            Toast.makeText(this, "密码长度至少为6位", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证密码是否包含数字和字母
        val hasDigit = password.any { it.isDigit() }
        val hasLetter = password.any { it.isLetter() }
        if (!hasDigit || !hasLetter) {
            Toast.makeText(this, "密码必须包含数字和字母", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证两次密码是否一致
        if (password != confirmPassword) {
            Toast.makeText(this, "两次输入的密码不一致", Toast.LENGTH_SHORT).show()
            return
        }

        // 切换到步骤2
        currentStep = 2
        updateStepIndicator()
        switchToStep(2)
    }

    private fun validateAndProceedToStep3() {
        val paymentPwd = etPaymentPassword.text.toString()
        val confirmPaymentPwd = etConfirmPaymentPassword.text.toString()

        // 验证支付密码
        if (paymentPwd.length != 6) {
            Toast.makeText(this, "支付密码必须为6位数字", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证支付密码是否为简单密码
        if (isSimplePassword(paymentPwd)) {
            Toast.makeText(this, "请勿使用简单密码，如连续数字或重复数字", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证两次支付密码是否一致
        if (paymentPwd != confirmPaymentPwd) {
            Toast.makeText(this, "两次输入的支付密码不一致", Toast.LENGTH_SHORT).show()
            return
        }

        // 保存支付密码，用于后续验证交易密码
        paymentPassword = paymentPwd

        // 切换到步骤3
        currentStep = 3
        updateStepIndicator()
        switchToStep(3)
    }

    private fun validateAndProceedToStep4() {
        val transactionPwd = etTransactionPassword.text.toString()
        val confirmTransactionPwd = etConfirmTransactionPassword.text.toString()

        // 验证交易密码
        if (transactionPwd.length != 6) {
            Toast.makeText(this, "交易密码必须为6位数字", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证交易密码是否为简单密码
        if (isSimplePassword(transactionPwd)) {
            Toast.makeText(this, "请勿使用简单密码，如连续数字或重复数字", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证交易密码是否与支付密码相同
        if (transactionPwd == paymentPassword) {
            Toast.makeText(this, "交易密码不能与支付密码相同", Toast.LENGTH_SHORT).show()
            return
        }

        // 验证两次交易密码是否一致
        if (transactionPwd != confirmTransactionPwd) {
            Toast.makeText(this, "两次输入的交易密码不一致", Toast.LENGTH_SHORT).show()
            return
        }

        // 切换到步骤4
        currentStep = 4
        updateStepIndicator()
        switchToStep(4)
        btnNext.text = "完成注册"
    }

    private fun isSimplePassword(password: String): Boolean {
        // 检查是否为连续数字 (如 123456, 654321)
        var isSequential = true
        for (i in 1 until password.length) {
            if (password[i] - password[i-1] != 1 && password[i] - password[i-1] != -1) {
                isSequential = false
                break
            }
        }

        // 检查是否为重复数字 (如 111111, 222222)
        val isRepeated = password.all { it == password[0] }

        // 检查是否为其他简单组合 (如 123123, 456456)
        val isPatternRepeated = password.length == 6 &&
                password.substring(0, 3) == password.substring(3, 6)

        return isSequential || isRepeated || isPatternRepeated
    }

    private fun updateStepIndicator() {
        // 重置所有步骤指示器
        tvStep1.background = resources.getDrawable(R.drawable.circle_outline, null)
        tvStep1.setTextColor(resources.getColor(R.color.text_secondary, null))
        tvStep2.background = resources.getDrawable(R.drawable.circle_outline, null)
        tvStep2.setTextColor(resources.getColor(R.color.text_secondary, null))
        tvStep3.background = resources.getDrawable(R.drawable.circle_outline, null)
        tvStep3.setTextColor(resources.getColor(R.color.text_secondary, null))
        tvStep4.background = resources.getDrawable(R.drawable.circle_outline, null)
        tvStep4.setTextColor(resources.getColor(R.color.text_secondary, null))

        // 设置当前步骤指示器
        when (currentStep) {
            1 -> {
                tvStep1.background = resources.getDrawable(R.drawable.circle_primary, null)
                tvStep1.setTextColor(resources.getColor(R.color.white, null))
                tvStepTitle.text = "基本信息"
            }
            2 -> {
                tvStep2.background = resources.getDrawable(R.drawable.circle_primary, null)
                tvStep2.setTextColor(resources.getColor(R.color.white, null))
                tvStepTitle.text = "支付密码"
            }
            3 -> {
                tvStep3.background = resources.getDrawable(R.drawable.circle_primary, null)
                tvStep3.setTextColor(resources.getColor(R.color.white, null))
                tvStepTitle.text = "交易密码"
            }
            4 -> {
                tvStep4.background = resources.getDrawable(R.drawable.circle_primary, null)
                tvStep4.setTextColor(resources.getColor(R.color.white, null))
                tvStepTitle.text = "身份验证"
            }
        }
    }

    private fun switchToStep(step: Int) {
        // 隐藏所有步骤
        clStep1.visibility = View.GONE
        clStep2.visibility = View.GONE
        clStep3.visibility = View.GONE
        clStep4.visibility = View.GONE

        // 显示当前步骤
        when (step) {
            1 -> clStep1.visibility = View.VISIBLE
            2 -> clStep2.visibility = View.VISIBLE
            3 -> clStep3.visibility = View.VISIBLE
            4 -> clStep4.visibility = View.VISIBLE
        }

        // 更新按钮文本
        btnNext.text = if (step < 4) "下一步" else "完成注册"
    }

    private fun simulateFaceRecognition() {
        // 获取用户输入的身份信息
        val realName = etRealName.text.toString().trim()
        val idNumber = etIdNumber.text.toString().trim().uppercase() // 转为大写，确保X是大写

        // 验证身份证号码
        if (!isValidIdNumber(idNumber)) {
            Toast.makeText(this, "请输入正确的18位身份证号码", Toast.LENGTH_SHORT).show()
            return
        }

        // 禁用按钮，防止重复点击
        btnNext.isEnabled = false
        btnNext.text = "准备识别..."

        // 显示人脸识别弹窗 (传入Activity上下文)
        val faceRecognitionDialog = FaceRecognitionDialog(this@RegisterActivity) { success ->
            if (success) {
                // 识别成功，完成注册
                btnNext.text = "识别成功"
                btnNext.backgroundTintList = resources.getColorStateList(R.color.success_green, null)

                // 延迟一小段时间后完成注册，让用户看到成功状态
                btnNext.postDelayed({
                    completeRegistration()
                }, 500)
            } else {
                // 用户取消了识别
                btnNext.isEnabled = true
                btnNext.text = "完成注册"
                Toast.makeText(this, "人脸识别已取消", Toast.LENGTH_SHORT).show()
            }
        }

        // 显示弹窗并开始识别
        faceRecognitionDialog.show()
        faceRecognitionDialog.startRecognition()
    }

    /**
     * 为所有输入框添加焦点变化监听，确保在输入法弹出时自动滚动到当前输入框
     */
    private fun setupFocusChangeListeners() {
        // 获取ScrollView
        val scrollView = findViewById<ScrollView>(R.id.scrollView)

        // 步骤1的输入框
        setupFocusChangeListener(etPhoneNumber, scrollView)
        setupFocusChangeListener(etPassword, scrollView)
        setupFocusChangeListener(etConfirmPassword, scrollView)

        // 步骤2的输入框
        setupFocusChangeListener(etPaymentPassword, scrollView)
        setupFocusChangeListener(etConfirmPaymentPassword, scrollView)

        // 步骤3的输入框
        setupFocusChangeListener(etTransactionPassword, scrollView)
        setupFocusChangeListener(etConfirmTransactionPassword, scrollView)

        // 步骤4的输入框
        setupFocusChangeListener(etRealName, scrollView)
        setupFocusChangeListener(etIdNumber, scrollView)
    }

    /**
     * 为单个输入框设置焦点变化监听
     */
    private fun setupFocusChangeListener(editText: TextInputEditText, scrollView: ScrollView) {
        editText.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                // 延迟滚动，确保输入法已经显示
                scrollView.postDelayed({
                    // 获取输入框在父容器中的位置
                    val location = IntArray(2)
                    v.getLocationInWindow(location)

                    // 获取输入框在ScrollView中的相对位置
                    val scrollViewLocation = IntArray(2)
                    scrollView.getLocationInWindow(scrollViewLocation)

                    // 计算相对位置
                    val relativeY = location[1] - scrollViewLocation[1]

                    // 滚动到输入框位置，确保它在可见区域内
                    scrollView.smoothScrollTo(0, relativeY - 100) // 减去一些偏移量，确保输入框在可见区域的上部
                }, 500) // 增加延迟时间，确保输入法完全显示
            }
        }
    }

    /**
     * 调整ScrollView的顶部约束，使其直接位于标题下方
     */
    private fun adjustScrollViewTopConstraint() {
        // 获取ScrollView
        val scrollView = findViewById<ScrollView>(R.id.scrollView)

        // 调整ScrollView的上边距，使其直接位于步骤标题下方
        val params = scrollView.layoutParams as ConstraintLayout.LayoutParams
        params.topToBottom = R.id.tvTitle
        params.topMargin = resources.getDimensionPixelSize(R.dimen.margin_medium)
        scrollView.layoutParams = params

        // 设置焦点变化监听，确保输入框获取焦点时自动滚动
        setupFocusChangeListeners()
    }



    private fun completeRegistration() {
        // 收集所有用户输入的数据
        val phoneNumber = etPhoneNumber.text.toString().trim()
        val password = etPassword.text.toString()
        val paymentPassword = etPaymentPassword.text.toString()
        val transactionPassword = etTransactionPassword.text.toString()
        val realName = etRealName.text.toString().trim()
        val idNumber = etIdNumber.text.toString().trim().uppercase() // 转为大写，确保X是大写

        // 保存用户数据
        UserDataManager.savePhoneNumber(this, phoneNumber)
        UserDataManager.savePassword(this, password)
        UserDataManager.savePaymentPassword(this, paymentPassword)
        UserDataManager.saveTransactionPassword(this, transactionPassword)
        UserDataManager.saveRealName(this, realName)
        UserDataManager.saveIdNumber(this, idNumber)
        UserDataManager.saveLoginType(this, UserDataManager.LOGIN_TYPE_REGISTER)
        UserDataManager.setRegistered(this, true) // 设置用户已注册状态

        // 生成随机VISA卡信息
        UserDataManager.getVisaCardNumber(this) // 这会自动生成并保存
        UserDataManager.getVisaCardBalance(this) // 这会自动生成并保存
        UserDataManager.getVisaCreditLimit(this) // 这会自动设置并保存

        // 注册成功，跳转到主界面
        Toast.makeText(this, "注册成功！", Toast.LENGTH_SHORT).show()
        startActivity(Intent(this, DashboardActivity::class.java))
        finish()
    }


}
