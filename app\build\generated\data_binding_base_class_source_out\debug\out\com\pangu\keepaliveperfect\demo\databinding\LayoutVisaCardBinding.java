// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutVisaCardBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView background;

  @NonNull
  public final TextView bankName;

  @NonNull
  public final TextView cardHolder;

  @NonNull
  public final TextView cardNumber;

  @NonNull
  public final View cardOuterGlow;

  @NonNull
  public final TextView cardValid;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final RelativeLayout cardVisaDesign;

  @NonNull
  public final TextView labelHolder;

  @NonNull
  public final TextView labelValid;

  @NonNull
  public final View shineEffect;

  @NonNull
  public final ImageView visaLogo;

  private LayoutVisaCardBinding(@NonNull RelativeLayout rootView, @NonNull ImageView background,
      @NonNull TextView bankName, @NonNull TextView cardHolder, @NonNull TextView cardNumber,
      @NonNull View cardOuterGlow, @NonNull TextView cardValid, @NonNull CardView cardView,
      @NonNull RelativeLayout cardVisaDesign, @NonNull TextView labelHolder,
      @NonNull TextView labelValid, @NonNull View shineEffect, @NonNull ImageView visaLogo) {
    this.rootView = rootView;
    this.background = background;
    this.bankName = bankName;
    this.cardHolder = cardHolder;
    this.cardNumber = cardNumber;
    this.cardOuterGlow = cardOuterGlow;
    this.cardValid = cardValid;
    this.cardView = cardView;
    this.cardVisaDesign = cardVisaDesign;
    this.labelHolder = labelHolder;
    this.labelValid = labelValid;
    this.shineEffect = shineEffect;
    this.visaLogo = visaLogo;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutVisaCardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutVisaCardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_visa_card, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutVisaCardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.background;
      ImageView background = ViewBindings.findChildViewById(rootView, id);
      if (background == null) {
        break missingId;
      }

      id = R.id.bank_name;
      TextView bankName = ViewBindings.findChildViewById(rootView, id);
      if (bankName == null) {
        break missingId;
      }

      id = R.id.card_holder;
      TextView cardHolder = ViewBindings.findChildViewById(rootView, id);
      if (cardHolder == null) {
        break missingId;
      }

      id = R.id.card_number;
      TextView cardNumber = ViewBindings.findChildViewById(rootView, id);
      if (cardNumber == null) {
        break missingId;
      }

      id = R.id.card_outer_glow;
      View cardOuterGlow = ViewBindings.findChildViewById(rootView, id);
      if (cardOuterGlow == null) {
        break missingId;
      }

      id = R.id.card_valid;
      TextView cardValid = ViewBindings.findChildViewById(rootView, id);
      if (cardValid == null) {
        break missingId;
      }

      id = R.id.card_view;
      CardView cardView = ViewBindings.findChildViewById(rootView, id);
      if (cardView == null) {
        break missingId;
      }

      RelativeLayout cardVisaDesign = (RelativeLayout) rootView;

      id = R.id.label_holder;
      TextView labelHolder = ViewBindings.findChildViewById(rootView, id);
      if (labelHolder == null) {
        break missingId;
      }

      id = R.id.label_valid;
      TextView labelValid = ViewBindings.findChildViewById(rootView, id);
      if (labelValid == null) {
        break missingId;
      }

      id = R.id.shine_effect;
      View shineEffect = ViewBindings.findChildViewById(rootView, id);
      if (shineEffect == null) {
        break missingId;
      }

      id = R.id.visa_logo;
      ImageView visaLogo = ViewBindings.findChildViewById(rootView, id);
      if (visaLogo == null) {
        break missingId;
      }

      return new LayoutVisaCardBinding((RelativeLayout) rootView, background, bankName, cardHolder,
          cardNumber, cardOuterGlow, cardValid, cardView, cardVisaDesign, labelHolder, labelValid,
          shineEffect, visaLogo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
