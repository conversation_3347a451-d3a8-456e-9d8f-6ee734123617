package com.pangu.keepaliveperfect.demo.visa

import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.button.MaterialButton

import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.utils.UserDataManager

/**
 * 账号密码登录界面
 */
class AccountLoginActivity : AppCompatActivity() {

    private lateinit var etUsername: EditText
    private lateinit var etPassword: EditText
    private lateinit var ivTogglePassword: ImageView
    private lateinit var btnLogin: MaterialButton
    private var isPasswordVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_account_login)

        initViews()
    }

    private fun initViews() {
        etUsername = findViewById(R.id.etUsername)
        etPassword = findViewById(R.id.etPassword)
        ivTogglePassword = findViewById(R.id.ivTogglePassword)
        btnLogin = findViewById(R.id.btnLogin)

        // 返回按钮
        val ivBack = findViewById<ImageView>(R.id.ivBack)
        ivBack.setOnClickListener {
            finish()
        }

        // 密码可见性切换
        ivTogglePassword.setOnClickListener {
            togglePasswordVisibility()
        }

        // 登录按钮
        btnLogin.setOnClickListener {
            attemptLogin()
        }

        // 忘记密码 - 跳转到注册界面
        val tvForgotPassword = findViewById<TextView>(R.id.tvForgotPassword)
        tvForgotPassword.setOnClickListener {
            // 跳转到注册界面
            startActivity(Intent(this, RegisterActivity::class.java))
            finish()
        }

        // 注册按钮 - 跳转到注册界面
        val tvRegister = findViewById<TextView>(R.id.tvRegister)
        tvRegister.setOnClickListener {
            // 跳转到注册界面
            startActivity(Intent(this, RegisterActivity::class.java))
            finish()
        }

        // 其他登录方式
        val ivPhoneLogin = findViewById<ImageView>(R.id.ivPhoneLogin)
        ivPhoneLogin.setOnClickListener {
            startActivity(Intent(this, PhoneVerificationActivity::class.java))
            finish()
        }

        val ivWechatLogin = findViewById<ImageView>(R.id.ivWechatLogin)
        ivWechatLogin.setOnClickListener {
            startActivity(Intent(this, WechatLoginActivity::class.java))
            finish()
        }

        val ivQQLogin = findViewById<ImageView>(R.id.ivQQLogin)
        ivQQLogin.setOnClickListener {
            startActivity(Intent(this, QQLoginActivity::class.java))
            finish()
        }


    }

    private fun togglePasswordVisibility() {
        isPasswordVisible = !isPasswordVisible
        if (isPasswordVisible) {
            // 显示密码
            etPassword.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            ivTogglePassword.setImageResource(R.drawable.ic_visibility)
        } else {
            // 隐藏密码
            etPassword.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
            ivTogglePassword.setImageResource(R.drawable.ic_visibility_off)
        }
        // 将光标移到文本末尾
        etPassword.setSelection(etPassword.text.length)
    }

    private fun attemptLogin() {
        val username = etUsername.text.toString().trim()
        val password = etPassword.text.toString().trim()

        // 简单的输入验证
        when {
            username.isEmpty() -> {
                Toast.makeText(this, "请输入账号", Toast.LENGTH_SHORT).show()
                etUsername.requestFocus()
                return
            }
            password.isEmpty() -> {
                Toast.makeText(this, "请输入密码", Toast.LENGTH_SHORT).show()
                etPassword.requestFocus()
                return
            }
        }

        // 模拟登录过程
        Toast.makeText(this, "登录中...", Toast.LENGTH_SHORT).show()

        // 这里应该有实际的登录逻辑，现在只是模拟成功
        // 延迟2秒模拟网络请求
        btnLogin.isEnabled = false
        btnLogin.text = "登录中..."

        btnLogin.postDelayed({
            btnLogin.isEnabled = true
            btnLogin.text = "登录"

            // 验证用户输入
            val isValid = UserDataManager.verifyAccountLogin(this, username, password)

            if (isValid) {
                // 登录成功，设置登录类型
                UserDataManager.saveLoginType(this, UserDataManager.LOGIN_TYPE_ACCOUNT)

                // 生成随机VISA卡信息
                UserDataManager.getVisaCardNumber(this) // 这会自动生成并保存
                UserDataManager.getVisaCardBalance(this) // 这会自动生成并保存
                UserDataManager.getVisaCreditLimit(this) // 这会自动设置并保存

                // 跳转到主界面
                startActivity(Intent(this, DashboardActivity::class.java))
                finish()
            } else {
                // 检查用户是否已注册
                if (!UserDataManager.hasRegistered(this)) {
                    // 未注册用户
                    Toast.makeText(this, "您尚未注册，请先注册账号", Toast.LENGTH_LONG).show()

                    // 提示用户前往注册页面
                    androidx.appcompat.app.AlertDialog.Builder(this)
                        .setTitle("未注册账号")
                        .setMessage("您输入的账号尚未注册，是否立即前往注册页面？")
                        .setPositiveButton("立即注册") { dialog, which ->
                            startActivity(Intent(this, RegisterActivity::class.java))
                            finish()
                        }
                        .setNegativeButton("取消") { dialog, which ->
                            // 不做任何操作，关闭对话框
                        }
                        .show()
                } else {
                    // 已注册但密码错误
                    Toast.makeText(this, "账号或密码错误", Toast.LENGTH_SHORT).show()
                }
            }
        }, 2000)
    }


}
