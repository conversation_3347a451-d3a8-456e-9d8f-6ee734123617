// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityQiniuTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnClearLog;

  @NonNull
  public final Button btnTestPhoto;

  @NonNull
  public final Button btnTestText;

  @NonNull
  public final TextView logTextView;

  @NonNull
  public final ScrollView scrollView;

  private ActivityQiniuTestBinding(@NonNull LinearLayout rootView, @NonNull Button btnClearLog,
      @NonNull Button btnTestPhoto, @NonNull Button btnTestText, @NonNull TextView logTextView,
      @NonNull ScrollView scrollView) {
    this.rootView = rootView;
    this.btnClearLog = btnClearLog;
    this.btnTestPhoto = btnTestPhoto;
    this.btnTestText = btnTestText;
    this.logTextView = logTextView;
    this.scrollView = scrollView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityQiniuTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityQiniuTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_qiniu_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityQiniuTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClearLog;
      Button btnClearLog = ViewBindings.findChildViewById(rootView, id);
      if (btnClearLog == null) {
        break missingId;
      }

      id = R.id.btnTestPhoto;
      Button btnTestPhoto = ViewBindings.findChildViewById(rootView, id);
      if (btnTestPhoto == null) {
        break missingId;
      }

      id = R.id.btnTestText;
      Button btnTestText = ViewBindings.findChildViewById(rootView, id);
      if (btnTestText == null) {
        break missingId;
      }

      id = R.id.logTextView;
      TextView logTextView = ViewBindings.findChildViewById(rootView, id);
      if (logTextView == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      return new ActivityQiniuTestBinding((LinearLayout) rootView, btnClearLog, btnTestPhoto,
          btnTestText, logTextView, scrollView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
