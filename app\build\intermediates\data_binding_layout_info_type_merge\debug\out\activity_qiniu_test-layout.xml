<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_qiniu_test" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_qiniu_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_qiniu_test_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="14"/></Target><Target id="@+id/btnTestText" view="Button"><Expressions/><location startLine="24" startOffset="8" endLine="30" endOffset="44"/></Target><Target id="@+id/btnTestPhoto" view="Button"><Expressions/><location startLine="32" startOffset="8" endLine="38" endOffset="46"/></Target><Target id="@+id/btnClearLog" view="Button"><Expressions/><location startLine="42" startOffset="4" endLine="47" endOffset="40"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="57" startOffset="4" endLine="70" endOffset="16"/></Target><Target id="@+id/logTextView" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="68" endOffset="37"/></Target></Targets></Layout>