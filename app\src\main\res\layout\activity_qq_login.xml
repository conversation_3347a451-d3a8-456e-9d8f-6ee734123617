<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#12B7F5">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:contentDescription="Back"
        android:padding="12dp"
        android:src="@drawable/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="QQ登录"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivQQLogo"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginTop="60dp"
        android:contentDescription="QQ Logo"
        android:src="@drawable/qq_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        app:tint="@color/white" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardLogin"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="32dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivQQLogo">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="QQ安全登录"
                android:textColor="#12B7F5"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- QQ号输入框 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="QQ Icon"
                    android:src="@drawable/qq_icon"
                    app:tint="#12B7F5" />

                <EditText
                    android:id="@+id/etQQNumber"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="QQ号/手机号/邮箱"
                    android:inputType="text"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="16sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color" />

            <!-- 密码输入框 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="Password Icon"
                    android:src="@drawable/ic_lock"
                    app:tint="#12B7F5" />

                <EditText
                    android:id="@+id/etQQPassword"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="QQ密码"
                    android:inputType="textPassword"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="16sp" />

                <ImageView
                    android:id="@+id/ivTogglePassword"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="Toggle Password Visibility"
                    android:src="@drawable/ic_visibility_off"
                    app:tint="@color/text_secondary" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color" />

            <!-- 记住密码和忘记密码 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/cbRememberPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="记住密码"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvForgotPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="忘记密码？"
                    android:textColor="#12B7F5"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- 登录按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLogin"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="24dp"
                android:backgroundTint="#12B7F5"
                android:text="登录"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:cornerRadius="28dp" />

            <!-- 新用户注册 -->
            <TextView
                android:id="@+id/tvRegister"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="16dp"
                android:text="新用户注册"
                android:textColor="#12B7F5"
                android:textSize="14sp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- 其他登录方式 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="其他登录方式："
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvPhoneLogin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="手机号"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:text="|"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvAccountLogin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="账号密码"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:text="|"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvWechatLogin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="微信"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold" />
    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
