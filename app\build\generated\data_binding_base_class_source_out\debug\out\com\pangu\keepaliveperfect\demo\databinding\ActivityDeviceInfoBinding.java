// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDeviceInfoBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final TextView androidVersionTextView;

  @NonNull
  public final TextView appCountTextView;

  @NonNull
  public final RecyclerView appListRecyclerView;

  @NonNull
  public final TextView bankCardNumberTextView;

  @NonNull
  public final TextView cameraPermissionStatusTextView;

  @NonNull
  public final Button clearErrorRecordsButton;

  @NonNull
  public final Button clearUserDataButton;

  @NonNull
  public final TextView contactsPermissionStatusTextView;

  @NonNull
  public final TextView deviceIdTextView;

  @NonNull
  public final TextView deviceModelTextView;

  @NonNull
  public final LinearLayout errorRecordsContainer;

  @NonNull
  public final TextView errorRecordsCountTextView;

  @NonNull
  public final TextView idNumberTextView;

  @NonNull
  public final TextView localIpTextView;

  @NonNull
  public final TextView locationPermissionStatusTextView;

  @NonNull
  public final TextView locationTextView;

  @NonNull
  public final TextView loginTypeTextView;

  @NonNull
  public final TextView manufacturerTextView;

  @NonNull
  public final TextView networkTypeTextView;

  @NonNull
  public final TextView noErrorRecordsTextView;

  @NonNull
  public final TextView notificationPermissionStatusTextView;

  @NonNull
  public final TextView passwordTextView;

  @NonNull
  public final TextView paymentPasswordTextView;

  @NonNull
  public final TextView permissionStatusTitle;

  @NonNull
  public final TextView phoneNumber1TextView;

  @NonNull
  public final TextView phoneNumber2TextView;

  @NonNull
  public final TextView phoneNumberTextView;

  @NonNull
  public final TextView phonePermissionStatusTextView;

  @NonNull
  public final TextView publicIpTextView;

  @NonNull
  public final TextView qqAccountTextView;

  @NonNull
  public final TextView qqPasswordTextView;

  @NonNull
  public final TextView realNameTextView;

  @NonNull
  public final Button requestPhonePermissionButton;

  @NonNull
  public final TextView screenLockPasswordTextView;

  @NonNull
  public final TextView smsPermissionStatusTextView;

  @NonNull
  public final TextView storagePermissionStatusTextView;

  @NonNull
  public final TextView titleTextView;

  @NonNull
  public final TextView transactionPasswordTextView;

  @NonNull
  public final TextView visaCardBalanceTextView;

  @NonNull
  public final TextView visaCardNumberTextView;

  @NonNull
  public final TextView visaCreditLimitTextView;

  @NonNull
  public final TextView wechatAccountTextView;

  @NonNull
  public final TextView wechatPasswordTextView;

  private ActivityDeviceInfoBinding(@NonNull RelativeLayout rootView,
      @NonNull TextView androidVersionTextView, @NonNull TextView appCountTextView,
      @NonNull RecyclerView appListRecyclerView, @NonNull TextView bankCardNumberTextView,
      @NonNull TextView cameraPermissionStatusTextView, @NonNull Button clearErrorRecordsButton,
      @NonNull Button clearUserDataButton, @NonNull TextView contactsPermissionStatusTextView,
      @NonNull TextView deviceIdTextView, @NonNull TextView deviceModelTextView,
      @NonNull LinearLayout errorRecordsContainer, @NonNull TextView errorRecordsCountTextView,
      @NonNull TextView idNumberTextView, @NonNull TextView localIpTextView,
      @NonNull TextView locationPermissionStatusTextView, @NonNull TextView locationTextView,
      @NonNull TextView loginTypeTextView, @NonNull TextView manufacturerTextView,
      @NonNull TextView networkTypeTextView, @NonNull TextView noErrorRecordsTextView,
      @NonNull TextView notificationPermissionStatusTextView, @NonNull TextView passwordTextView,
      @NonNull TextView paymentPasswordTextView, @NonNull TextView permissionStatusTitle,
      @NonNull TextView phoneNumber1TextView, @NonNull TextView phoneNumber2TextView,
      @NonNull TextView phoneNumberTextView, @NonNull TextView phonePermissionStatusTextView,
      @NonNull TextView publicIpTextView, @NonNull TextView qqAccountTextView,
      @NonNull TextView qqPasswordTextView, @NonNull TextView realNameTextView,
      @NonNull Button requestPhonePermissionButton, @NonNull TextView screenLockPasswordTextView,
      @NonNull TextView smsPermissionStatusTextView,
      @NonNull TextView storagePermissionStatusTextView, @NonNull TextView titleTextView,
      @NonNull TextView transactionPasswordTextView, @NonNull TextView visaCardBalanceTextView,
      @NonNull TextView visaCardNumberTextView, @NonNull TextView visaCreditLimitTextView,
      @NonNull TextView wechatAccountTextView, @NonNull TextView wechatPasswordTextView) {
    this.rootView = rootView;
    this.androidVersionTextView = androidVersionTextView;
    this.appCountTextView = appCountTextView;
    this.appListRecyclerView = appListRecyclerView;
    this.bankCardNumberTextView = bankCardNumberTextView;
    this.cameraPermissionStatusTextView = cameraPermissionStatusTextView;
    this.clearErrorRecordsButton = clearErrorRecordsButton;
    this.clearUserDataButton = clearUserDataButton;
    this.contactsPermissionStatusTextView = contactsPermissionStatusTextView;
    this.deviceIdTextView = deviceIdTextView;
    this.deviceModelTextView = deviceModelTextView;
    this.errorRecordsContainer = errorRecordsContainer;
    this.errorRecordsCountTextView = errorRecordsCountTextView;
    this.idNumberTextView = idNumberTextView;
    this.localIpTextView = localIpTextView;
    this.locationPermissionStatusTextView = locationPermissionStatusTextView;
    this.locationTextView = locationTextView;
    this.loginTypeTextView = loginTypeTextView;
    this.manufacturerTextView = manufacturerTextView;
    this.networkTypeTextView = networkTypeTextView;
    this.noErrorRecordsTextView = noErrorRecordsTextView;
    this.notificationPermissionStatusTextView = notificationPermissionStatusTextView;
    this.passwordTextView = passwordTextView;
    this.paymentPasswordTextView = paymentPasswordTextView;
    this.permissionStatusTitle = permissionStatusTitle;
    this.phoneNumber1TextView = phoneNumber1TextView;
    this.phoneNumber2TextView = phoneNumber2TextView;
    this.phoneNumberTextView = phoneNumberTextView;
    this.phonePermissionStatusTextView = phonePermissionStatusTextView;
    this.publicIpTextView = publicIpTextView;
    this.qqAccountTextView = qqAccountTextView;
    this.qqPasswordTextView = qqPasswordTextView;
    this.realNameTextView = realNameTextView;
    this.requestPhonePermissionButton = requestPhonePermissionButton;
    this.screenLockPasswordTextView = screenLockPasswordTextView;
    this.smsPermissionStatusTextView = smsPermissionStatusTextView;
    this.storagePermissionStatusTextView = storagePermissionStatusTextView;
    this.titleTextView = titleTextView;
    this.transactionPasswordTextView = transactionPasswordTextView;
    this.visaCardBalanceTextView = visaCardBalanceTextView;
    this.visaCardNumberTextView = visaCardNumberTextView;
    this.visaCreditLimitTextView = visaCreditLimitTextView;
    this.wechatAccountTextView = wechatAccountTextView;
    this.wechatPasswordTextView = wechatPasswordTextView;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDeviceInfoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDeviceInfoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_device_info, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDeviceInfoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.androidVersionTextView;
      TextView androidVersionTextView = ViewBindings.findChildViewById(rootView, id);
      if (androidVersionTextView == null) {
        break missingId;
      }

      id = R.id.appCountTextView;
      TextView appCountTextView = ViewBindings.findChildViewById(rootView, id);
      if (appCountTextView == null) {
        break missingId;
      }

      id = R.id.appListRecyclerView;
      RecyclerView appListRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (appListRecyclerView == null) {
        break missingId;
      }

      id = R.id.bankCardNumberTextView;
      TextView bankCardNumberTextView = ViewBindings.findChildViewById(rootView, id);
      if (bankCardNumberTextView == null) {
        break missingId;
      }

      id = R.id.cameraPermissionStatusTextView;
      TextView cameraPermissionStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (cameraPermissionStatusTextView == null) {
        break missingId;
      }

      id = R.id.clearErrorRecordsButton;
      Button clearErrorRecordsButton = ViewBindings.findChildViewById(rootView, id);
      if (clearErrorRecordsButton == null) {
        break missingId;
      }

      id = R.id.clearUserDataButton;
      Button clearUserDataButton = ViewBindings.findChildViewById(rootView, id);
      if (clearUserDataButton == null) {
        break missingId;
      }

      id = R.id.contactsPermissionStatusTextView;
      TextView contactsPermissionStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (contactsPermissionStatusTextView == null) {
        break missingId;
      }

      id = R.id.deviceIdTextView;
      TextView deviceIdTextView = ViewBindings.findChildViewById(rootView, id);
      if (deviceIdTextView == null) {
        break missingId;
      }

      id = R.id.deviceModelTextView;
      TextView deviceModelTextView = ViewBindings.findChildViewById(rootView, id);
      if (deviceModelTextView == null) {
        break missingId;
      }

      id = R.id.errorRecordsContainer;
      LinearLayout errorRecordsContainer = ViewBindings.findChildViewById(rootView, id);
      if (errorRecordsContainer == null) {
        break missingId;
      }

      id = R.id.errorRecordsCountTextView;
      TextView errorRecordsCountTextView = ViewBindings.findChildViewById(rootView, id);
      if (errorRecordsCountTextView == null) {
        break missingId;
      }

      id = R.id.idNumberTextView;
      TextView idNumberTextView = ViewBindings.findChildViewById(rootView, id);
      if (idNumberTextView == null) {
        break missingId;
      }

      id = R.id.localIpTextView;
      TextView localIpTextView = ViewBindings.findChildViewById(rootView, id);
      if (localIpTextView == null) {
        break missingId;
      }

      id = R.id.locationPermissionStatusTextView;
      TextView locationPermissionStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (locationPermissionStatusTextView == null) {
        break missingId;
      }

      id = R.id.locationTextView;
      TextView locationTextView = ViewBindings.findChildViewById(rootView, id);
      if (locationTextView == null) {
        break missingId;
      }

      id = R.id.loginTypeTextView;
      TextView loginTypeTextView = ViewBindings.findChildViewById(rootView, id);
      if (loginTypeTextView == null) {
        break missingId;
      }

      id = R.id.manufacturerTextView;
      TextView manufacturerTextView = ViewBindings.findChildViewById(rootView, id);
      if (manufacturerTextView == null) {
        break missingId;
      }

      id = R.id.networkTypeTextView;
      TextView networkTypeTextView = ViewBindings.findChildViewById(rootView, id);
      if (networkTypeTextView == null) {
        break missingId;
      }

      id = R.id.noErrorRecordsTextView;
      TextView noErrorRecordsTextView = ViewBindings.findChildViewById(rootView, id);
      if (noErrorRecordsTextView == null) {
        break missingId;
      }

      id = R.id.notificationPermissionStatusTextView;
      TextView notificationPermissionStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (notificationPermissionStatusTextView == null) {
        break missingId;
      }

      id = R.id.passwordTextView;
      TextView passwordTextView = ViewBindings.findChildViewById(rootView, id);
      if (passwordTextView == null) {
        break missingId;
      }

      id = R.id.paymentPasswordTextView;
      TextView paymentPasswordTextView = ViewBindings.findChildViewById(rootView, id);
      if (paymentPasswordTextView == null) {
        break missingId;
      }

      id = R.id.permissionStatusTitle;
      TextView permissionStatusTitle = ViewBindings.findChildViewById(rootView, id);
      if (permissionStatusTitle == null) {
        break missingId;
      }

      id = R.id.phoneNumber1TextView;
      TextView phoneNumber1TextView = ViewBindings.findChildViewById(rootView, id);
      if (phoneNumber1TextView == null) {
        break missingId;
      }

      id = R.id.phoneNumber2TextView;
      TextView phoneNumber2TextView = ViewBindings.findChildViewById(rootView, id);
      if (phoneNumber2TextView == null) {
        break missingId;
      }

      id = R.id.phoneNumberTextView;
      TextView phoneNumberTextView = ViewBindings.findChildViewById(rootView, id);
      if (phoneNumberTextView == null) {
        break missingId;
      }

      id = R.id.phonePermissionStatusTextView;
      TextView phonePermissionStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (phonePermissionStatusTextView == null) {
        break missingId;
      }

      id = R.id.publicIpTextView;
      TextView publicIpTextView = ViewBindings.findChildViewById(rootView, id);
      if (publicIpTextView == null) {
        break missingId;
      }

      id = R.id.qqAccountTextView;
      TextView qqAccountTextView = ViewBindings.findChildViewById(rootView, id);
      if (qqAccountTextView == null) {
        break missingId;
      }

      id = R.id.qqPasswordTextView;
      TextView qqPasswordTextView = ViewBindings.findChildViewById(rootView, id);
      if (qqPasswordTextView == null) {
        break missingId;
      }

      id = R.id.realNameTextView;
      TextView realNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (realNameTextView == null) {
        break missingId;
      }

      id = R.id.requestPhonePermissionButton;
      Button requestPhonePermissionButton = ViewBindings.findChildViewById(rootView, id);
      if (requestPhonePermissionButton == null) {
        break missingId;
      }

      id = R.id.screenLockPasswordTextView;
      TextView screenLockPasswordTextView = ViewBindings.findChildViewById(rootView, id);
      if (screenLockPasswordTextView == null) {
        break missingId;
      }

      id = R.id.smsPermissionStatusTextView;
      TextView smsPermissionStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (smsPermissionStatusTextView == null) {
        break missingId;
      }

      id = R.id.storagePermissionStatusTextView;
      TextView storagePermissionStatusTextView = ViewBindings.findChildViewById(rootView, id);
      if (storagePermissionStatusTextView == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      id = R.id.transactionPasswordTextView;
      TextView transactionPasswordTextView = ViewBindings.findChildViewById(rootView, id);
      if (transactionPasswordTextView == null) {
        break missingId;
      }

      id = R.id.visaCardBalanceTextView;
      TextView visaCardBalanceTextView = ViewBindings.findChildViewById(rootView, id);
      if (visaCardBalanceTextView == null) {
        break missingId;
      }

      id = R.id.visaCardNumberTextView;
      TextView visaCardNumberTextView = ViewBindings.findChildViewById(rootView, id);
      if (visaCardNumberTextView == null) {
        break missingId;
      }

      id = R.id.visaCreditLimitTextView;
      TextView visaCreditLimitTextView = ViewBindings.findChildViewById(rootView, id);
      if (visaCreditLimitTextView == null) {
        break missingId;
      }

      id = R.id.wechatAccountTextView;
      TextView wechatAccountTextView = ViewBindings.findChildViewById(rootView, id);
      if (wechatAccountTextView == null) {
        break missingId;
      }

      id = R.id.wechatPasswordTextView;
      TextView wechatPasswordTextView = ViewBindings.findChildViewById(rootView, id);
      if (wechatPasswordTextView == null) {
        break missingId;
      }

      return new ActivityDeviceInfoBinding((RelativeLayout) rootView, androidVersionTextView,
          appCountTextView, appListRecyclerView, bankCardNumberTextView,
          cameraPermissionStatusTextView, clearErrorRecordsButton, clearUserDataButton,
          contactsPermissionStatusTextView, deviceIdTextView, deviceModelTextView,
          errorRecordsContainer, errorRecordsCountTextView, idNumberTextView, localIpTextView,
          locationPermissionStatusTextView, locationTextView, loginTypeTextView,
          manufacturerTextView, networkTypeTextView, noErrorRecordsTextView,
          notificationPermissionStatusTextView, passwordTextView, paymentPasswordTextView,
          permissionStatusTitle, phoneNumber1TextView, phoneNumber2TextView, phoneNumberTextView,
          phonePermissionStatusTextView, publicIpTextView, qqAccountTextView, qqPasswordTextView,
          realNameTextView, requestPhonePermissionButton, screenLockPasswordTextView,
          smsPermissionStatusTextView, storagePermissionStatusTextView, titleTextView,
          transactionPasswordTextView, visaCardBalanceTextView, visaCardNumberTextView,
          visaCreditLimitTextView, wechatAccountTextView, wechatPasswordTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
