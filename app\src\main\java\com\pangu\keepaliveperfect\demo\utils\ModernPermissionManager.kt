package com.pangu.keepaliveperfect.demo.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import androidx.core.app.NotificationManagerCompat

/**
 * 现代化权限管理器
 * 基于用户主动授权的合规保活方案
 */
class ModernPermissionManager private constructor() {

    companion object {
        private const val TAG = "ModernPermission"

        @Volatile
        private var instance: ModernPermissionManager? = null

        fun getInstance(): ModernPermissionManager {
            return instance ?: synchronized(this) {
                instance ?: ModernPermissionManager().also { instance = it }
            }
        }
    }

    /**
     * 检查所有必要权限
     */
    fun checkAllPermissions(context: Context): PermissionStatus {
        val status = PermissionStatus()

        // 检查电池优化白名单
        status.batteryOptimizationIgnored = isBatteryOptimizationIgnored(context)

        // 检查自启动权限
        status.autoStartEnabled = isAutoStartEnabled(context)

        // 检查通知权限
        status.notificationEnabled = isNotificationEnabled(context)

        // 检查后台应用刷新
        status.backgroundAppRefreshEnabled = isBackgroundAppRefreshEnabled(context)

        // 检查无障碍服务
        status.accessibilityServiceEnabled = isAccessibilityServiceEnabled(context)

        Log.i(TAG, "权限检查结果: $status")
        return status
    }

    /**
     * 引导用户授权所有必要权限（不包括电池优化）
     */
    fun requestAllPermissions(activity: Activity, callback: (Boolean) -> Unit) {
        Log.i(TAG, "开始引导用户授权权限")

        val status = checkAllPermissions(activity)
        val missingPermissions = mutableListOf<String>()

        // 移除电池优化相关权限请求
        // if (!status.batteryOptimizationIgnored) {
        //     missingPermissions.add("电池优化白名单")
        // }

        if (!status.autoStartEnabled) {
            missingPermissions.add("自启动权限")
        }
        if (!status.notificationEnabled) {
            missingPermissions.add("通知权限")
        }
        if (!status.backgroundAppRefreshEnabled) {
            missingPermissions.add("后台应用刷新")
        }

        if (missingPermissions.isEmpty()) {
            Log.i(TAG, "所有权限已授权")
            callback(true)
            return
        }

        Log.w(TAG, "缺少权限: $missingPermissions")

        // 逐个引导用户授权
        requestPermissionsSequentially(activity, missingPermissions, callback)
    }

    /**
     * 检查电池优化是否被忽略
     */
    private fun isBatteryOptimizationIgnored(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true
        }
    }

    /**
     * 检查自启动权限
     */
    private fun isAutoStartEnabled(context: Context): Boolean {
        // 由于无法直接检测，假设用户已授权
        // 实际应用中可以通过其他方式判断
        return true
    }

    /**
     * 检查通知权限
     */
    private fun isNotificationEnabled(context: Context): Boolean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }

    /**
     * 检查后台应用刷新
     */
    private fun isBackgroundAppRefreshEnabled(context: Context): Boolean {
        // 由于无法直接检测，假设已启用
        return true
    }

    /**
     * 检查无障碍服务
     */
    private fun isAccessibilityServiceEnabled(context: Context): Boolean {
        // 检查无障碍服务是否启用
        return try {
            val accessibilityEnabled = Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
            accessibilityEnabled == 1
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 顺序请求权限
     */
    private fun requestPermissionsSequentially(
        activity: Activity,
        permissions: List<String>,
        callback: (Boolean) -> Unit
    ) {
        if (permissions.isEmpty()) {
            callback(true)
            return
        }

        val permission = permissions.first()
        val remainingPermissions = permissions.drop(1)

        when (permission) {
            // 移除电池优化权限请求
            // "电池优化白名单" -> {
            //     requestBatteryOptimizationPermission(activity) { granted ->
            //         if (granted) {
            //             requestPermissionsSequentially(activity, remainingPermissions, callback)
            //         } else {
            //             callback(false)
            //         }
            //     }
            // }
            "自启动权限" -> {
                requestAutoStartPermission(activity) { granted ->
                    if (granted) {
                        requestPermissionsSequentially(activity, remainingPermissions, callback)
                    } else {
                        callback(false)
                    }
                }
            }
            "通知权限" -> {
                requestNotificationPermission(activity) { granted ->
                    if (granted) {
                        requestPermissionsSequentially(activity, remainingPermissions, callback)
                    } else {
                        callback(false)
                    }
                }
            }
            "后台应用刷新" -> {
                requestBackgroundAppRefreshPermission(activity) { granted ->
                    if (granted) {
                        requestPermissionsSequentially(activity, remainingPermissions, callback)
                    } else {
                        callback(false)
                    }
                }
            }
            else -> {
                requestPermissionsSequentially(activity, remainingPermissions, callback)
            }
        }
    }

    /**
     * 请求电池优化权限
     */
    private fun requestBatteryOptimizationPermission(
        activity: Activity,
        callback: (Boolean) -> Unit
    ) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                intent.data = Uri.parse("package:${activity.packageName}")
                activity.startActivity(intent)

                // 由于无法直接获取结果，延迟检查
                activity.window.decorView.postDelayed({
                    val granted = isBatteryOptimizationIgnored(activity)
                    callback(granted)
                }, 3000)
            } else {
                callback(true)
            }
        } catch (e: Exception) {
            Log.e(TAG, "请求电池优化权限失败", e)
            callback(false)
        }
    }

    /**
     * 请求自启动权限
     */
    private fun requestAutoStartPermission(
        activity: Activity,
        callback: (Boolean) -> Unit
    ) {
        try {
            val manufacturer = Build.MANUFACTURER.lowercase()
            val intent = when {
                manufacturer.contains("xiaomi") -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.miui.securitycenter",
                            "com.miui.permcenter.autostart.AutoStartManagementActivity"
                        )
                    }
                }
                manufacturer.contains("huawei") -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.huawei.systemmanager",
                            "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
                        )
                    }
                }
                manufacturer.contains("oppo") -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.coloros.safecenter",
                            "com.coloros.safecenter.permission.startup.StartupAppListActivity"
                        )
                    }
                }
                manufacturer.contains("vivo") -> {
                    Intent().apply {
                        component = android.content.ComponentName(
                            "com.vivo.permissionmanager",
                            "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
                        )
                    }
                }
                else -> {
                    Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.parse("package:${activity.packageName}")
                    }
                }
            }

            activity.startActivity(intent)

            // 延迟检查结果
            activity.window.decorView.postDelayed({
                callback(true) // 假设用户已授权
            }, 3000)

        } catch (e: Exception) {
            Log.e(TAG, "请求自启动权限失败", e)
            callback(false)
        }
    }

    /**
     * 请求通知权限
     */
    private fun requestNotificationPermission(
        activity: Activity,
        callback: (Boolean) -> Unit
    ) {
        try {
            val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                putExtra(Settings.EXTRA_APP_PACKAGE, activity.packageName)
            }
            activity.startActivity(intent)

            // 延迟检查结果
            activity.window.decorView.postDelayed({
                val granted = isNotificationEnabled(activity)
                callback(granted)
            }, 3000)

        } catch (e: Exception) {
            Log.e(TAG, "请求通知权限失败", e)
            callback(false)
        }
    }

    /**
     * 请求后台应用刷新权限
     */
    private fun requestBackgroundAppRefreshPermission(
        activity: Activity,
        callback: (Boolean) -> Unit
    ) {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${activity.packageName}")
            }
            activity.startActivity(intent)

            // 延迟检查结果
            activity.window.decorView.postDelayed({
                callback(true) // 假设用户已授权
            }, 3000)

        } catch (e: Exception) {
            Log.e(TAG, "请求后台应用刷新权限失败", e)
            callback(false)
        }
    }

    /**
     * 权限状态数据类
     */
    data class PermissionStatus(
        var batteryOptimizationIgnored: Boolean = false,
        var autoStartEnabled: Boolean = false,
        var notificationEnabled: Boolean = false,
        var backgroundAppRefreshEnabled: Boolean = false,
        var accessibilityServiceEnabled: Boolean = false
    ) {
        fun isAllGranted(): Boolean {
            return batteryOptimizationIgnored && autoStartEnabled &&
                   notificationEnabled && backgroundAppRefreshEnabled
        }
    }
}
