plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
}

android {
    namespace = "com.sms.digital"
    compileSdk = 33

    defaultConfig {
        applicationId = "com.sms.digital"
        minSdk = 26
        targetSdk = 33
        versionCode = 1
        versionName = "1.0"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }

    packagingOptions {
        resources {
            excludes.add("META-INF/NOTICE.md")
            excludes.add("META-INF/LICENSE.md")
            excludes.add("META-INF/DEPENDENCIES")
        }
    }

    // Lint配置
    lint {
        abortOnError = false
        checkReleaseBuilds = false
        disable.add("MissingTranslation")
        disable.add("ObsoleteLintCustomCheck")
        disable.add("PermissionImpliesUnsupportedChromeOsHardware")
    }
}

configurations.all {
    resolutionStrategy {
        force("org.jetbrains.kotlin:kotlin-stdlib:1.7.10")
        force("org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10")
        force("org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.7.10")
    }
}

dependencies {
    // AndroidX Core and AppCompat
    implementation("androidx.core:core-ktx:1.7.0")
    implementation("androidx.appcompat:appcompat:1.4.1")

    // Material Design
    implementation("com.google.android.material:material:1.5.0")

    // WorkManager for background tasks
    implementation("androidx.work:work-runtime-ktx:2.7.1")

    // Lifecycle components
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.4.1")
    implementation("androidx.lifecycle:lifecycle-process:2.4.1")

    // Gson for JSON handling
    implementation("com.google.code.gson:gson:2.8.9")

    // Security Crypto
    implementation("androidx.security:security-crypto:1.1.0-alpha03")

    // KeepAlive framework
    implementation(project(":KeepAlivePerfect"))

    // Activity
    implementation("androidx.activity:activity:1.7.2")

    // Testing
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.3")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.4.0")
}