<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_identity_verification" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_identity_verification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_identity_verification_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="118" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="7" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/tvSubtitle" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="35" endOffset="60"/></Target><Target id="@+id/tilRealName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="37" startOffset="4" endLine="61" endOffset="59"/></Target><Target id="@+id/etRealName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="54" startOffset="8" endLine="60" endOffset="34"/></Target><Target id="@+id/tilIdNumber" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="63" startOffset="4" endLine="89" endOffset="59"/></Target><Target id="@+id/etIdNumber" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="80" startOffset="8" endLine="88" endOffset="34"/></Target><Target id="@+id/btnFaceRecognition" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="91" startOffset="4" endLine="108" endOffset="64"/></Target></Targets></Layout>