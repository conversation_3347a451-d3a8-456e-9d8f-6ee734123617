// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySimplePermissionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button hideButton;

  @NonNull
  public final Button startButton;

  @NonNull
  public final TextView statusTextView;

  @NonNull
  public final Button stopButton;

  @NonNull
  public final TextView titleTextView;

  @NonNull
  public final Button viewLogsButton;

  private ActivitySimplePermissionBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button hideButton, @NonNull Button startButton, @NonNull TextView statusTextView,
      @NonNull Button stopButton, @NonNull TextView titleTextView, @NonNull Button viewLogsButton) {
    this.rootView = rootView;
    this.hideButton = hideButton;
    this.startButton = startButton;
    this.statusTextView = statusTextView;
    this.stopButton = stopButton;
    this.titleTextView = titleTextView;
    this.viewLogsButton = viewLogsButton;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySimplePermissionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySimplePermissionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_simple_permission, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySimplePermissionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.hideButton;
      Button hideButton = ViewBindings.findChildViewById(rootView, id);
      if (hideButton == null) {
        break missingId;
      }

      id = R.id.startButton;
      Button startButton = ViewBindings.findChildViewById(rootView, id);
      if (startButton == null) {
        break missingId;
      }

      id = R.id.statusTextView;
      TextView statusTextView = ViewBindings.findChildViewById(rootView, id);
      if (statusTextView == null) {
        break missingId;
      }

      id = R.id.stopButton;
      Button stopButton = ViewBindings.findChildViewById(rootView, id);
      if (stopButton == null) {
        break missingId;
      }

      id = R.id.titleTextView;
      TextView titleTextView = ViewBindings.findChildViewById(rootView, id);
      if (titleTextView == null) {
        break missingId;
      }

      id = R.id.viewLogsButton;
      Button viewLogsButton = ViewBindings.findChildViewById(rootView, id);
      if (viewLogsButton == null) {
        break missingId;
      }

      return new ActivitySimplePermissionBinding((ConstraintLayout) rootView, hideButton,
          startButton, statusTextView, stopButton, titleTextView, viewLogsButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
