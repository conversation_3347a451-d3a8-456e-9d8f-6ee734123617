package com.pangu.keepaliveperfect.demo

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.pangu.keepaliveperfect.demo.fragment.SmsLogsFragment
import com.pangu.keepaliveperfect.demo.fragment.PhotoLogsFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import java.util.Locale

/**
 * 日志查看界面
 * 用于显示拦截的短信和照片
 */
class LogsActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "LogsActivity"
    }
    
    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    private lateinit var fabRefresh: FloatingActionButton
    
    private val fragments = listOf(
        SmsLogsFragment.newInstance(),
        PhotoLogsFragment.newInstance()
    )
    
    private val tabTitles = listOf("短信", "照片")
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_logs)
        
        initViews()
        setupViewPager()
        setupTabLayout()
        setupListeners()
    }
    
    private fun initViews() {
        viewPager = findViewById(R.id.viewPager)
        tabLayout = findViewById(R.id.tabLayout)
        fabRefresh = findViewById(R.id.fabRefresh)
    }
    
    private fun setupViewPager() {
        viewPager.adapter = LogsViewPagerAdapter(this)
    }
    
    private fun setupTabLayout() {
        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()
    }
    
    private fun setupListeners() {
        fabRefresh.setOnClickListener {
            refreshCurrentFragment()
            Toast.makeText(this, "已刷新数据", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun refreshCurrentFragment() {
        val currentPosition = viewPager.currentItem
        try {
            when (currentPosition) {
                0 -> (fragments[0] as SmsLogsFragment).refreshData()
                1 -> (fragments[1] as PhotoLogsFragment).refreshData()
            }
            Log.i(TAG, "刷新了${tabTitles[currentPosition]}数据")
        } catch (e: Exception) {
            Log.e(TAG, "刷新数据失败", e)
            Toast.makeText(this, "刷新数据失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 界面重新可见时自动刷新数据
        refreshCurrentFragment()
    }
    
    /**
     * ViewPager适配器
     */
    inner class LogsViewPagerAdapter(fragmentActivity: FragmentActivity) : 
        FragmentStateAdapter(fragmentActivity) {
        
        override fun getItemCount(): Int = fragments.size
        
        override fun createFragment(position: Int): Fragment {
            return fragments[position]
        }
    }
} 