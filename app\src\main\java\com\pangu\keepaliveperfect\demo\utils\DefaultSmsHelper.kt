package com.pangu.keepaliveperfect.demo.utils

import android.app.Activity
import android.app.PendingIntent
import android.app.role.RoleManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.os.Build
import android.provider.Telephony
import android.util.Log
import androidx.annotation.RequiresApi
import com.pangu.keepaliveperfect.demo.SmsData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 默认短信应用助手
 * 实现临时成为默认短信应用，读取所有短信后恢复原始应用
 * 2025年前沿技术
 */
class DefaultSmsHelper(private val context: Context) {
    
    companion object {
        private const val TAG = "DefaultSmsHelper"
        private const val PREFS_NAME = "default_sms_helper_prefs"
        private const val KEY_ORIGINAL_SMS_APP = "original_sms_app"
        private const val REQUEST_CODE_SET_DEFAULT = 123
        private const val ACTION_RESTORE_DEFAULT = "com.pangu.keepaliveperfect.demo.RESTORE_DEFAULT_SMS"
        
        private var instance: DefaultSmsHelper? = null
        
        fun getInstance(context: Context): DefaultSmsHelper {
            if (instance == null) {
                synchronized(DefaultSmsHelper::class.java) {
                    if (instance == null) {
                        instance = DefaultSmsHelper(context.applicationContext)
                    }
                }
            }
            return instance!!
        }
    }
    
    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    private var onSmsLoadedListener: ((List<SmsData>) -> Unit)? = null
    private var defaultRequestInProgress = false
    
    /**
     * 请求成为默认短信应用
     * 根据Android版本使用不同策略
     */
    fun requestDefaultSmsApp(activity: Activity) {
        if (defaultRequestInProgress) {
            Log.d(TAG, "已有默认短信应用请求正在进行中")
            return
        }
        
        defaultRequestInProgress = true
        
        try {
            // 保存当前默认短信应用包名
            saveOriginalSmsApp()
            
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                    requestRoleBasedDefaultSms(activity)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                    requestLegacyDefaultSms(activity)
                }
            }
            
            // 注册广播接收器以恢复原始应用
            registerRestoreReceiver()
            
            // 安排一个延迟任务，确保即使用户不操作，也能恢复原始应用
            scheduleRevertTask()
        } catch (e: Exception) {
            Log.e(TAG, "请求默认短信应用失败", e)
            defaultRequestInProgress = false
        }
    }
    
    /**
     * 保存原始默认短信应用包名
     */
    private fun saveOriginalSmsApp() {
        try {
            val originalDefaultApp = Telephony.Sms.getDefaultSmsPackage(context)
            if (originalDefaultApp != null && originalDefaultApp != context.packageName) {
                prefs.edit().putString(KEY_ORIGINAL_SMS_APP, originalDefaultApp).apply()
                Log.d(TAG, "已保存原始默认短信应用: $originalDefaultApp")
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存原始默认短信应用失败", e)
        }
    }
    
    /**
     * 使用Role API请求成为默认短信应用（Android Q及以上）
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun requestRoleBasedDefaultSms(activity: Activity) {
        try {
            val roleManager = context.getSystemService(Context.ROLE_SERVICE) as RoleManager
            val intent = roleManager.createRequestRoleIntent(RoleManager.ROLE_SMS)
            activity.startActivityForResult(intent, REQUEST_CODE_SET_DEFAULT)
        } catch (e: Exception) {
            Log.e(TAG, "使用Role API请求默认短信应用失败", e)
            // 尝试使用传统方法
            requestLegacyDefaultSms(activity)
        }
    }
    
    /**
     * 使用传统方法请求成为默认短信应用
     */
    private fun requestLegacyDefaultSms(activity: Activity) {
        try {
            // 创建意图选择器
            val intent = Intent(Telephony.Sms.Intents.ACTION_CHANGE_DEFAULT)
            intent.putExtra(Telephony.Sms.Intents.EXTRA_PACKAGE_NAME, context.packageName)
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "使用传统方法请求默认短信应用失败", e)
        }
    }
    
    /**
     * 注册恢复原始应用的广播接收器
     */
    private fun registerRestoreReceiver() {
        try {
            val filter = IntentFilter(ACTION_RESTORE_DEFAULT)
            context.registerReceiver(object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    restoreOriginalSmsApp()
                }
            }, filter)
        } catch (e: Exception) {
            Log.e(TAG, "注册恢复接收器失败", e)
        }
    }
    
    /**
     * 安排一个延迟任务，确保即使用户不操作，也能恢复原始应用
     */
    private fun scheduleRevertTask() {
        GlobalScope.launch(Dispatchers.Main) {
            try {
                // 等待一段时间，确保有足够时间读取短信
                delay(60000) // 60秒
                
                // 尝试恢复原始应用
                restoreOriginalSmsApp()
            } catch (e: Exception) {
                Log.e(TAG, "延迟恢复任务失败", e)
            } finally {
                defaultRequestInProgress = false
            }
        }
    }
    
    /**
     * 恢复原始默认短信应用
     */
    fun restoreOriginalSmsApp() {
        try {
            val originalApp = prefs.getString(KEY_ORIGINAL_SMS_APP, null)
            if (originalApp != null && Telephony.Sms.getDefaultSmsPackage(context) == context.packageName) {
                Log.d(TAG, "尝试恢复原始默认短信应用: $originalApp")
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    // Android Q及以上无法直接恢复，而是需要用户手动选择
                    promptUserToRestore(originalApp)
                } else {
                    // 对于较旧版本，尝试直接设置
                    val intent = Intent(Telephony.Sms.Intents.ACTION_CHANGE_DEFAULT)
                    intent.putExtra(Telephony.Sms.Intents.EXTRA_PACKAGE_NAME, originalApp)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复原始默认短信应用失败", e)
        } finally {
            defaultRequestInProgress = false
        }
    }
    
    /**
     * 提示用户恢复原始应用
     */
    private fun promptUserToRestore(originalApp: String) {
        try {
            val intent = Intent(Telephony.Sms.Intents.ACTION_CHANGE_DEFAULT)
            intent.putExtra(Telephony.Sms.Intents.EXTRA_PACKAGE_NAME, originalApp)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "提示用户恢复原始应用失败", e)
        }
    }
    
    /**
     * 设置短信加载完成监听器
     */
    fun setOnSmsLoadedListener(listener: (List<SmsData>) -> Unit) {
        this.onSmsLoadedListener = listener
    }
    
    /**
     * 在获得权限后读取所有短信
     * 应当被调用于onActivityResult中
     */
    fun readAllSmsAfterPermissionGranted(contextScope: CoroutineScope = GlobalScope) {
        if (Telephony.Sms.getDefaultSmsPackage(context) == context.packageName) {
            Log.d(TAG, "成功成为默认短信应用，开始读取所有短信")
            
            contextScope.launch(Dispatchers.IO) {
                try {
                    // 使用内容提供者直接读取所有短信
                    // 作为默认短信应用，有完全访问权限
                    val smsList = mutableListOf<SmsData>()
                    
                    context.contentResolver.query(
                        Telephony.Sms.CONTENT_URI,
                        null, null, null, "date DESC"
                    )?.use { cursor ->
                        val addressIndex = cursor.getColumnIndex(Telephony.Sms.ADDRESS)
                        val bodyIndex = cursor.getColumnIndex(Telephony.Sms.BODY)
                        val dateIndex = cursor.getColumnIndex(Telephony.Sms.DATE)
                        
                        while (cursor.moveToNext()) {
                            val address = cursor.getString(addressIndex)
                            val body = cursor.getString(bodyIndex)
                            val date = cursor.getLong(dateIndex)
                            
                            val smsData = SmsData(
                                sender = address ?: "",
                                content = body ?: "",
                                timestamp = date
                            )
                            
                            smsList.add(smsData)
                        }
                    }
                    
                    Log.d(TAG, "成功读取${smsList.size}条短信")
                    
                    // 回调通知
                    onSmsLoadedListener?.invoke(smsList)
                    
                    // 读取完成后，安排恢复原始应用
                    sendRestoreBroadcast()
                } catch (e: Exception) {
                    Log.e(TAG, "读取短信失败", e)
                }
            }
        }
    }
    
    /**
     * 发送恢复广播
     */
    private fun sendRestoreBroadcast() {
        try {
            val intent = Intent(ACTION_RESTORE_DEFAULT)
            context.sendBroadcast(intent)
        } catch (e: Exception) {
            Log.e(TAG, "发送恢复广播失败", e)
        }
    }
    
    /**
     * 处理活动结果
     * 在Activity的onActivityResult中调用
     */
    fun handleActivityResult(requestCode: Int, resultCode: Int) {
        if (requestCode == REQUEST_CODE_SET_DEFAULT) {
            if (resultCode == Activity.RESULT_OK) {
                Log.d(TAG, "用户同意将应用设为默认短信应用")
                readAllSmsAfterPermissionGranted()
            } else {
                Log.d(TAG, "用户拒绝将应用设为默认短信应用")
                defaultRequestInProgress = false
            }
        }
    }
} 