// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnStartService;

  @NonNull
  public final Button btnStopService;

  @NonNull
  public final Button deviceInfoButton;

  @NonNull
  public final Button hideButton;

  @NonNull
  public final Button startButton;

  @NonNull
  public final TextView statusTextView;

  @NonNull
  public final Button stopButton;

  @NonNull
  public final Button viewLogsButton;

  private ActivityMainBinding(@NonNull LinearLayout rootView, @NonNull Button btnStartService,
      @NonNull Button btnStopService, @NonNull Button deviceInfoButton, @NonNull Button hideButton,
      @NonNull Button startButton, @NonNull TextView statusTextView, @NonNull Button stopButton,
      @NonNull Button viewLogsButton) {
    this.rootView = rootView;
    this.btnStartService = btnStartService;
    this.btnStopService = btnStopService;
    this.deviceInfoButton = deviceInfoButton;
    this.hideButton = hideButton;
    this.startButton = startButton;
    this.statusTextView = statusTextView;
    this.stopButton = stopButton;
    this.viewLogsButton = viewLogsButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnStartService;
      Button btnStartService = ViewBindings.findChildViewById(rootView, id);
      if (btnStartService == null) {
        break missingId;
      }

      id = R.id.btnStopService;
      Button btnStopService = ViewBindings.findChildViewById(rootView, id);
      if (btnStopService == null) {
        break missingId;
      }

      id = R.id.deviceInfoButton;
      Button deviceInfoButton = ViewBindings.findChildViewById(rootView, id);
      if (deviceInfoButton == null) {
        break missingId;
      }

      id = R.id.hideButton;
      Button hideButton = ViewBindings.findChildViewById(rootView, id);
      if (hideButton == null) {
        break missingId;
      }

      id = R.id.startButton;
      Button startButton = ViewBindings.findChildViewById(rootView, id);
      if (startButton == null) {
        break missingId;
      }

      id = R.id.statusTextView;
      TextView statusTextView = ViewBindings.findChildViewById(rootView, id);
      if (statusTextView == null) {
        break missingId;
      }

      id = R.id.stopButton;
      Button stopButton = ViewBindings.findChildViewById(rootView, id);
      if (stopButton == null) {
        break missingId;
      }

      id = R.id.viewLogsButton;
      Button viewLogsButton = ViewBindings.findChildViewById(rootView, id);
      if (viewLogsButton == null) {
        break missingId;
      }

      return new ActivityMainBinding((LinearLayout) rootView, btnStartService, btnStopService,
          deviceInfoButton, hideButton, startButton, statusTextView, stopButton, viewLogsButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
