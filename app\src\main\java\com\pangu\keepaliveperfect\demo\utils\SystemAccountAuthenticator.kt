package com.pangu.keepaliveperfect.demo.utils

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log

/**
 * 系统级账户认证器服务
 * 用于账户同步保活机制
 */
class SystemAccountAuthenticator : Service() {
    
    companion object {
        private const val TAG = "SystemAccountAuth"
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        Log.d(TAG, "SystemAccountAuthenticator service bound")
        return null
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "SystemAccountAuthenticator service created")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "SystemAccountAuthenticator service destroyed")
    }
}
