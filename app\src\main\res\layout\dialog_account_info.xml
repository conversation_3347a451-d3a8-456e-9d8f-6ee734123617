<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_dialog"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="账号信息"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@color/divider_color"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="用户名："
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvUsername"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="用户名"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="手机号："
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvPhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="手机号"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="真实姓名："
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvRealName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="真实姓名"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="身份证号："
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvIdCard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="身份证号"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="注册时间："
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvRegisterTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="注册时间"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />
        </LinearLayout>

    </LinearLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnClose"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:backgroundTint="@color/visa_blue"
        android:text="关闭"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout" />

</androidx.constraintlayout.widget.ConstraintLayout>
