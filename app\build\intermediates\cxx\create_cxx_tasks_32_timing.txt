# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 44ms
  [gap of 13ms]
create_cxx_tasks completed in 58ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 36ms]
    create-variant-model 13ms
    [gap of 21ms]
  create-initial-cxx-model completed in 81ms
  [gap of 16ms]
create_cxx_tasks completed in 98ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 40ms
  [gap of 15ms]
create_cxx_tasks completed in 55ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
  [gap of 13ms]
create_cxx_tasks completed in 53ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 69ms
create_cxx_tasks completed in 71ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 53ms
create_cxx_tasks completed in 54ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
create_cxx_tasks completed in 40ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 40ms
create_cxx_tasks completed in 41ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 29ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 24ms
create_cxx_tasks completed in 25ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 22ms
create_cxx_tasks completed in 23ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 22ms
create_cxx_tasks completed in 22ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 36ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 51ms
  [gap of 10ms]
create_cxx_tasks completed in 61ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 43ms
  [gap of 13ms]
create_cxx_tasks completed in 56ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
create_cxx_tasks completed in 41ms

