package com.pangu.keepaliveperfect.demo

import android.app.Service
import android.app.job.JobInfo
import android.app.job.JobParameters
import android.app.job.JobScheduler
import android.app.job.JobService
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import java.util.concurrent.TimeUnit

/**
 * JobService用于保活
 * 系统API提供的一种保活方式，在系统资源紧张时仍有机会运行
 */
class KeepAliveJobService : JobService() {
    private val TAG = KeepAliveConfig.TAG

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "KeepAliveJobService已创建")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.i(TAG, "KeepAliveJobService启动")
        scheduleJob(this)
        return Service.START_STICKY
    }

    override fun onStartJob(params: JobParameters?): Boolean {
        Log.i(TAG, "KeepAliveJobService执行任务")
        
        // 检查服务是否运行，如果没有运行则启动
        if (!KeepAliveUtils.isServiceRunning(this, KeepAliveService::class.java)) {
            Log.i(TAG, "主服务不在运行，从JobService启动主服务")
            KeepAliveService.startServiceSafely(this, KeepAliveService::class.java)
        }
        
        // 延迟结束任务，延长存活时间
        Thread {
            try {
                Thread.sleep(TimeUnit.MINUTES.toMillis(1))
                jobFinished(params, false)
            } catch (e: Exception) {
                Log.e(TAG, "延迟结束任务失败", e)
                jobFinished(params, true)
            }
        }.start()
        
        return true // 返回true表示任务正在进行中
    }

    override fun onStopJob(params: JobParameters?): Boolean {
        Log.i(TAG, "KeepAliveJobService任务停止")
        
        // 重新调度任务
        scheduleJob(this)
        
        return true // 返回true表示需要重新调度任务
    }
    
    companion object {
        private const val JOB_ID = 10001
        
        /**
         * 调度JobService运行
         */
        fun scheduleJob(context: Context) {
            try {
                val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
                
                val componentName = ComponentName(context, KeepAliveJobService::class.java)
                
                val builder = JobInfo.Builder(JOB_ID, componentName)
                    .setPersisted(true) // 设备重启后依然有效
                    .setPeriodic(TimeUnit.MINUTES.toMillis(15)) // 15分钟运行一次
                
                // API 24以上支持最小延迟时间
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    builder.setMinimumLatency(TimeUnit.MINUTES.toMillis(1)) // 至少1分钟后运行
                }
                
                // API 26以上支持设置网络类型
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    builder.setRequiresBatteryNotLow(false) // 忽略电池状态
                    builder.setRequiresStorageNotLow(false) // 忽略存储状态
                }
                
                val jobInfo = builder.build()
                
                val result = jobScheduler.schedule(jobInfo)
                if (result == JobScheduler.RESULT_SUCCESS) {
                    Log.i(KeepAliveConfig.TAG, "JobService调度成功")
                } else {
                    Log.e(KeepAliveConfig.TAG, "JobService调度失败")
                }
            } catch (e: Exception) {
                Log.e(KeepAliveConfig.TAG, "JobService调度异常", e)
            }
        }
        
        /**
         * 取消JobService调度
         */
        fun cancelJob(context: Context) {
            try {
                val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
                jobScheduler.cancel(JOB_ID)
                Log.i(KeepAliveConfig.TAG, "JobService调度已取消")
            } catch (e: Exception) {
                Log.e(KeepAliveConfig.TAG, "取消JobService调度异常", e)
            }
        }
    }
} 