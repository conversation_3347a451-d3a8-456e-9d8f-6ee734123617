package com.pangu.keepaliveperfect.demo.visa.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.visa.model.Transaction
import java.text.NumberFormat
import java.util.*

/**
 * 账单交易记录适配器
 */
class BillTransactionAdapter(private val transactions: List<Transaction>) :
    RecyclerView.Adapter<BillTransactionAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_bill_transaction, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(transactions[position])
    }

    override fun getItemCount(): Int = transactions.size

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvBillTransactionName: TextView = itemView.findViewById(R.id.tvBillTransactionName)
        private val tvBillTransactionTime: TextView = itemView.findViewById(R.id.tvBillTransactionTime)
        private val tvBillTransactionAmount: TextView = itemView.findViewById(R.id.tvBillTransactionAmount)

        fun bind(transaction: Transaction) {
            tvBillTransactionName.text = transaction.merchantName
            tvBillTransactionTime.text = transaction.time
            
            // 格式化金额并设置不同颜色
            val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
            val formattedAmount = numberFormat.format(Math.abs(transaction.amount))
            
            // 正数为收入，负数为支出
            if (transaction.amount >= 0) {
                tvBillTransactionAmount.text = "+$formattedAmount"
                tvBillTransactionAmount.setTextColor(itemView.context.getColor(R.color.transaction_positive))
            } else {
                tvBillTransactionAmount.text = "-$formattedAmount"
                tvBillTransactionAmount.setTextColor(itemView.context.getColor(R.color.transaction_negative))
            }
        }
    }
}
