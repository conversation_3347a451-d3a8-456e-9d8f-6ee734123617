<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 7.4.2" type="baseline" client="gradle" dependencies="false" name="AGP (7.4.2)" variant="all" version="7.4.2">

    <issue
        id="MissingPermission"
        message="Setting Exact alarms with `setExactAndAllowWhileIdle` requires the `SCHEDULE_EXACT_ALARM` permission or power exemption from user; it is intended for applications where the user knowingly schedules actions to happen at a precise time such as alarms, clocks, calendars, etc. Check out the javadoc on this permission to make sure your use case is valid."
        errorLine1="                alarmManager.setExactAndAllowWhileIdle("
        errorLine2="                ^">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="547"
            column="17"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Setting Exact alarms with `setExact` requires the `SCHEDULE_EXACT_ALARM` permission or power exemption from user; it is intended for applications where the user knowingly schedules actions to happen at a precise time such as alarms, clocks, calendars, etc. Check out the javadoc on this permission to make sure your use case is valid."
        errorLine1="                alarmManager.setExact("
        errorLine2="                ^">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="553"
            column="17"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Setting Exact alarms with `setExactAndAllowWhileIdle` requires the `SCHEDULE_EXACT_ALARM` permission or power exemption from user; it is intended for applications where the user knowingly schedules actions to happen at a precise time such as alarms, clocks, calendars, etc. Check out the javadoc on this permission to make sure your use case is valid."
        errorLine1="                alarmManager.setExactAndAllowWhileIdle("
        errorLine2="                ^">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="265"
            column="17"/>
    </issue>

    <issue
        id="MissingPermission"
        message="Setting Exact alarms with `setExact` requires the `SCHEDULE_EXACT_ALARM` permission or power exemption from user; it is intended for applications where the user knowingly schedules actions to happen at a precise time such as alarms, clocks, calendars, etc. Check out the javadoc on this permission to make sure your use case is valid."
        errorLine1="                alarmManager.setExact("
        errorLine2="                ^">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="272"
            column="17"/>
    </issue>

    <issue
        id="ScopedStorage"
        message="WRITE_EXTERNAL_STORAGE no longer provides write access when targeting Android 10, unless you use `requestLegacyExternalStorage`"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot; "
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="23"
            column="36"/>
    </issue>

    <issue
        id="OldTargetApi"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        errorLine1="        targetSdk 33"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="16"
            column="9"/>
    </issue>

    <issue
        id="OldTargetApi"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        errorLine1="        targetSdk = 33"
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="PrivateApi"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future"
        errorLine1="            val clazz = Class.forName(&quot;android.os.SystemProperties&quot;)"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/InjectHelper.kt"
            line="182"
            column="25"/>
    </issue>

    <issue
        id="PrivateApi"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future"
        errorLine1="            val clazz = Class.forName(&quot;android.os.SystemProperties&quot;)"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="435"
            column="25"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="                &quot;/data/data/com.android.providers.telephony/databases/mmssms.db&quot;,"
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/AdvancedSmsExtractor.kt"
            line="367"
            column="18"/>
    </issue>

    <issue
        id="SdCardPath"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead"
        errorLine1="                &quot;/data/user/0/com.android.providers.telephony/databases/mmssms.db&quot;,"
        errorLine2="                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/AdvancedSmsExtractor.kt"
            line="368"
            column="18"/>
    </issue>

    <issue
        id="BatteryLife"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        errorLine1="                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="308"
            column="46"/>
    </issue>

    <issue
        id="BatteryLife"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        errorLine1="                        val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/SimplePermissionActivity.kt"
            line="503"
            column="54"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        message="Permission is only granted to system apps"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.BIND_NOTIFICATION_LISTENER_SERVICE&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="49"
            column="22"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.9.0 is available: 1.16.0"
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="70"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.0"
        errorLine1="    implementation &apos;androidx.appcompat:appcompat:1.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="71"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.9.0 is available: 1.12.0"
        errorLine1="    implementation &apos;com.google.android.material:material:1.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="73"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="75"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="76"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.core:core-ktx than 1.7.0 is available: 1.16.0"
        errorLine1="    implementation(&quot;androidx.core:core-ktx:1.7.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="63"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.appcompat:appcompat than 1.4.1 is available: 1.7.0"
        errorLine1="    implementation(&quot;androidx.appcompat:appcompat:1.4.1&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="64"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.android.material:material than 1.5.0 is available: 1.12.0"
        errorLine1="    implementation(&quot;com.google.android.material:material:1.5.0&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="67"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.work:work-runtime-ktx than 2.7.1 is available: 2.9.0"
        errorLine1="    implementation(&quot;androidx.work:work-runtime-ktx:2.7.1&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="74"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.4.1 is available: 2.7.0"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-runtime-ktx:2.4.1&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="77"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.lifecycle:lifecycle-process than 2.4.1 is available: 2.7.0"
        errorLine1="    implementation(&quot;androidx.lifecycle:lifecycle-process:2.4.1&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="78"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.code.gson:gson than 2.8.9 is available: 2.10.1"
        errorLine1="    implementation(&quot;com.google.code.gson:gson:2.8.9&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="81"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha03 is available: 1.1.0-alpha06"
        errorLine1="    implementation(&quot;androidx.security:security-crypto:1.1.0-alpha03&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="84"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.activity:activity than 1.7.2 is available: 1.8.0"
        errorLine1="    implementation(&quot;androidx.activity:activity:1.7.2&quot;)"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="90"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.ext:junit than 1.1.3 is available: 1.2.1"
        errorLine1="    androidTestImplementation(&quot;androidx.test.ext:junit:1.1.3&quot;)"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="94"
            column="32"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of androidx.test.espresso:espresso-core than 3.4.0 is available: 3.6.1"
        errorLine1="    androidTestImplementation(&quot;androidx.test.espresso:espresso-core:3.4.0&quot;)"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="95"
            column="32"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `SERIAL` to get device identifiers is not recommended"
        errorLine1="                    put(&quot;device_id&quot;, android.os.Build.SERIAL)"
        errorLine2="                                                      ~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/DataForwarder.kt"
            line="94"
            column="55"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `SERIAL` to get device identifiers is not recommended"
        errorLine1="                    put(&quot;device_id&quot;, android.os.Build.SERIAL)"
        errorLine2="                                                      ~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/DataForwarder.kt"
            line="347"
            column="55"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `getLine1Number` to get device identifiers is not recommended"
        errorLine1="                simInfo.put(&quot;line1_number&quot;, telephonyManager.line1Number ?: &quot;&quot;)"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/DeviceInfoCollector.kt"
            line="316"
            column="45"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `SERIAL` to get device identifiers is not recommended"
        errorLine1="            info[&quot;serial&quot;] = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) &quot;unknown&quot; else Build.SERIAL"
        errorLine2="                                                                                                      ~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/utils/DeviceUtils.kt"
            line="205"
            column="103"/>
    </issue>

    <issue
        id="ExportedReceiver"
        message="Exported receiver does not require permission"
        errorLine1="        &lt;receiver"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="201"
            column="10"/>
    </issue>

    <issue
        id="SystemPermissionTypo"
        message="Did you mean `android.permission.FOREGROUND_SERVICE`?"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.FOREGROUND_SERVICE_DATA_SYNC&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="37"
            column="36"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="            photoAdapter.notifyDataSetChanged()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/fragment/PhotoLogsFragment.kt"
            line="372"
            column="13"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="            smsAdapter.notifyDataSetChanged()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/fragment/SmsLogsFragment.kt"
            line="400"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/service/DaemonService.kt"
            line="79"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/service/DaemonService.kt"
            line="100"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/service/DaemonService.kt"
            line="163"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/DefaultSmsHelper.kt"
            line="77"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/DeviceInfoCollector.kt"
            line="158"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            info[&quot;serial&quot;] = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) &quot;unknown&quot; else Build.SERIAL"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/utils/DeviceUtils.kt"
            line="205"
            column="34"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            val imei = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/utils/DeviceUtils.kt"
            line="269"
            column="28"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/utils/DeviceUtils.kt"
            line="341"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/HeartbeatMonitorService.kt"
            line="92"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/HeartbeatMonitorService.kt"
            line="273"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/HeartbeatMonitorService.kt"
            line="287"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/InjectHelper.kt"
            line="566"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveApplication.kt"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveApplication.kt"
            line="83"
            column="25"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveConfig.kt"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveJobService.kt"
            line="82"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveJobService.kt"
            line="87"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="181"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="181"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="309"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="546"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="1039"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="1043"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveService.kt"
            line="1086"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="263"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="270"
            column="24"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="306"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/KeepAliveUtils.kt"
            line="324"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/NotificationAccessHelper.kt"
            line="417"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/NotificationAccessHelper.kt"
            line="430"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/OnePixelActivity.kt"
            line="111"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/OnePixelActivity.kt"
            line="119"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/OnePixelActivity.kt"
            line="184"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/OnePixelActivity.kt"
            line="225"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/OnePixelActivity.kt"
            line="263"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/ProcessLifecycleObserver.kt"
            line="180"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/receiver/ServiceRestartReceiver.kt"
            line="91"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/receiver/ServiceRestartReceiver.kt"
            line="111"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/SmsBroadcastReceiver.kt"
            line="65"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/SmsBroadcastReceiver.kt"
            line="101"
            column="25"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                        val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M &amp;&amp; format != null) {"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/SmsBroadcastReceiver.kt"
            line="141"
            column="46"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/SmsBroadcastReceiver.kt"
            line="222"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/SmsReceiver.kt"
            line="91"
            column="29"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/receiver/SmsReceiver.kt"
            line="113"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/SmsReceiver.kt"
            line="130"
            column="25"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                            val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/receiver/SmsReceiver.kt"
            line="134"
            column="50"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                        val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/receiver/SmsReceiver.kt"
            line="158"
            column="46"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/receiver/SmsReceiver.kt"
            line="204"
            column="21"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="            val subId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/utils/SmsUtils.kt"
            line="159"
            column="29"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="                val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/utils/SmsUtils.kt"
            line="169"
            column="38"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        message="Unnecessary; SDK_INT is always >= 26"
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/utils/SmsUtils.kt"
            line="638"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `DefaultSmsHelper` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="        private var instance: DefaultSmsHelper? = null"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/DefaultSmsHelper.kt"
            line="36"
            column="9"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `InjectHelper` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="        private var instance: InjectHelper? = null"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/InjectHelper.kt"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `ProcessLifecycleObserver` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="        private var INSTANCE: ProcessLifecycleObserver? = null"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/utils/ProcessLifecycleObserver.kt"
            line="31"
            column="9"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.activity_config` appears to be unused"
        errorLine1="&lt;ScrollView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.activity_simple_permission` appears to be unused"
        errorLine1="&lt;androidx.constraintlayout.widget.ConstraintLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/activity_simple_permission.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.xml.backup_rules` appears to be unused"
        errorLine1="&lt;full-backup-content>"
        errorLine2="^">
        <location
            file="src/main/res/xml/backup_rules.xml"
            line="8"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.xml.data_extraction_rules` appears to be unused"
        errorLine1="&lt;data-extraction-rules>"
        errorLine2="^">
        <location
            file="src/main/res/xml/data_extraction_rules.xml"
            line="6"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_digital_currency` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_digital_currency.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_name_wechat` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_name_wechat&quot;>VISA&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_name_system` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_name_system&quot;>系统服务&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_name_service` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_name_service&quot;>微信服务&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.config_title` appears to be unused"
        errorLine1="    &lt;string name=&quot;config_title&quot;>系统配置&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.smtp_host` appears to be unused"
        errorLine1="    &lt;string name=&quot;smtp_host&quot;>SMTP服务器&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.smtp_port` appears to be unused"
        errorLine1="    &lt;string name=&quot;smtp_port&quot;>端口&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sender_email` appears to be unused"
        errorLine1="    &lt;string name=&quot;sender_email&quot;>发件人邮箱&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sender_password` appears to be unused"
        errorLine1="    &lt;string name=&quot;sender_password&quot;>发件人密码&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.recipient_email` appears to be unused"
        errorLine1="    &lt;string name=&quot;recipient_email&quot;>收件人邮箱&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.use_ssl` appears to be unused"
        errorLine1="    &lt;string name=&quot;use_ssl&quot;>使用SSL/TLS&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.save_config` appears to be unused"
        errorLine1="    &lt;string name=&quot;save_config&quot;>保存配置&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.email_provider` appears to be unused"
        errorLine1="    &lt;string name=&quot;email_provider&quot;>邮件服务商&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.service_running` appears to be unused"
        errorLine1="    &lt;string name=&quot;service_running&quot;>系统服务正在运行&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.OnePixelTheme` appears to be unused"
        errorLine1="    &lt;style name=&quot;OnePixelTheme&quot; parent=&quot;Theme.AppCompat.Light.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/styles.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_MyApplication` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.MyApplication&quot; parent=&quot;Theme.MaterialComponents.DayNight.DarkActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values-night/themes.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_DigitalCurrencyWallet` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.DigitalCurrencyWallet&quot; parent=&quot;Theme.MaterialComponents.DayNight.DarkActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_DigitalCurrencyWallet_NoActionBar` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.DigitalCurrencyWallet.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_KeepAliveDemo_NoActionBar` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.KeepAliveDemo.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="42"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="55"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="77"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="90"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="103"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="42"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="55"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="77"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="90"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_config.xml"
            line="103"
            column="10"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        holder.tvPhotoInfo.text = &quot;大小：$formattedSize | 日期：$dateString&quot;"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/adapter/PhotoAdapter.kt"
            line="47"
            column="35"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            binding.tvNoPhotoData.text = &quot;加载照片失败: ${e.message}&quot;"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/pangu/keepaliveperfect/demo/fragment/PhotoLogsFragment.kt"
            line="104"
            column="42"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;短信&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;短信&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_logs.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;照片&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;照片&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_logs.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;刷新&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;刷新&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_logs.xml"
            line="49"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;短信拦截服务&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;短信拦截服务&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;服务状态：未运行&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;服务状态：未运行&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="22"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;启动服务&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;启动服务&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;停止服务&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;停止服务&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="39"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;隐藏应用&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;隐藏应用&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="46"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;开始监听短信&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;开始监听短信&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;停止监听短信&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;停止监听短信&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="60"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;查看数据日志&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;查看数据日志&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="67"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;服务说明：\n此服务将在后台运行，自动拦截短信并提取验证码。服务启动后，即使应用被关闭也会持续运行。\n\n注意：隐藏应用后，需要通过特殊方式重新打开应用。&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;服务说明：\n此服务将在后台运行，自动拦截短信并提取验证码。服务启动后，即使应用被关闭也会持续运行。\n\n注意：隐藏应用后，需要通过特殊方式重新打开应用。&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="75"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;服务管理&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;服务管理&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_simple_permission.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;服务状态：未运行&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;服务状态：未运行&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_simple_permission.xml"
            line="27"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;启动服务&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;启动服务&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_simple_permission.xml"
            line="38"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;停止服务&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;停止服务&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_simple_permission.xml"
            line="48"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;隐藏应用图标&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;隐藏应用图标&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_simple_permission.xml"
            line="58"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;查看日志记录&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;查看日志记录&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_simple_permission.xml"
            line="68"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;暂无照片数据&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;暂无照片数据&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_photo_logs.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;暂无短信数据&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;暂无短信数据&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_sms_logs.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;照片&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;照片&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_photo.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;照片名称&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;照片名称&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_photo.xml"
            line="36"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;大小：2.5MB | 日期：2025-05-01&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;大小：2.5MB | 日期：2025-05-01&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_photo.xml"
            line="45"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;类型&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;类型&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_sms.xml"
            line="28"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;发送人&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;发送人&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_sms.xml"
            line="43"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;时间&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;时间&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_sms.xml"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;短信内容&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;短信内容&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_sms.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;验证码：123456&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;验证码：123456&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_sms.xml"
            line="72"
            column="13"/>
    </issue>

</issues>
