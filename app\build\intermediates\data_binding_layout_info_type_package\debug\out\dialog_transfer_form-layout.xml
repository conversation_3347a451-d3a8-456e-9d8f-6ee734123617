<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_transfer_form" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_transfer_form.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_transfer_form_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="178" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="21" startOffset="4" endLine="27" endOffset="60"/></Target><Target id="@+id/tilReceiverName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="29" startOffset="4" endLine="50" endOffset="59"/></Target><Target id="@+id/etReceiverName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="8" endLine="49" endOffset="34"/></Target><Target id="@+id/tilBankCardNumber" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="52" startOffset="4" endLine="73" endOffset="59"/></Target><Target id="@+id/etBankCardNumber" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="67" startOffset="8" endLine="72" endOffset="34"/></Target><Target id="@+id/tilBankName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="75" startOffset="4" endLine="96" endOffset="59"/></Target><Target id="@+id/etBankName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="90" startOffset="8" endLine="95" endOffset="34"/></Target><Target id="@+id/tilBankAddress" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="98" startOffset="4" endLine="119" endOffset="59"/></Target><Target id="@+id/etBankAddress" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="113" startOffset="8" endLine="118" endOffset="34"/></Target><Target id="@+id/tilAmount" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="121" startOffset="4" endLine="143" endOffset="59"/></Target><Target id="@+id/etAmount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="137" startOffset="8" endLine="142" endOffset="34"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="145" startOffset="4" endLine="160" endOffset="44"/></Target><Target id="@+id/btnConfirm" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="162" startOffset="4" endLine="176" endOffset="62"/></Target></Targets></Layout>