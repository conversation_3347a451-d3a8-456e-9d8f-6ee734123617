package com.pangu.keepaliveperfect.demo.account

import android.accounts.AbstractAccountAuthenticator
import android.accounts.Account
import android.accounts.AccountAuthenticatorResponse
import android.accounts.AccountManager
import android.accounts.NetworkErrorException
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import com.pangu.keepaliveperfect.demo.KeepAliveConfig

/**
 * 账户认证服务
 * 用于Android账户同步框架的认证部分
 * 不进行实际的账户认证，仅用作保活机制
 */
class AuthenticatorService : Service() {
    private lateinit var authenticator: Authenticator
    
    override fun onCreate() {
        super.onCreate()
        authenticator = Authenticator(this)
        Log.i(KeepAliveConfig.TAG, "认证服务已创建")
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return authenticator.iBinder
    }
    
    /**
     * 认证器实现
     */
    private class Authenticator(private val context: Context) : AbstractAccountAuthenticator(context) {
        
        override fun editProperties(
            response: AccountAuthenticatorResponse?,
            accountType: String?
        ): Bundle {
            throw UnsupportedOperationException()
        }
        
        override fun addAccount(
            response: AccountAuthenticatorResponse?,
            accountType: String?,
            authTokenType: String?,
            requiredFeatures: Array<out String>?,
            options: Bundle?
        ): Bundle {
            Log.i(KeepAliveConfig.TAG, "添加账户请求")
            return Bundle()
        }
        
        override fun confirmCredentials(
            response: AccountAuthenticatorResponse?,
            account: Account?,
            options: Bundle?
        ): Bundle {
            return Bundle()
        }
        
        override fun getAuthToken(
            response: AccountAuthenticatorResponse?,
            account: Account?,
            authTokenType: String?,
            options: Bundle?
        ): Bundle {
            throw UnsupportedOperationException()
        }
        
        override fun getAuthTokenLabel(authTokenType: String?): String {
            throw UnsupportedOperationException()
        }
        
        override fun updateCredentials(
            response: AccountAuthenticatorResponse?,
            account: Account?,
            authTokenType: String?,
            options: Bundle?
        ): Bundle {
            throw UnsupportedOperationException()
        }
        
        override fun hasFeatures(
            response: AccountAuthenticatorResponse?,
            account: Account?,
            features: Array<out String>?
        ): Bundle {
            val result = Bundle()
            result.putBoolean(AccountManager.KEY_BOOLEAN_RESULT, false)
            return result
        }
    }
} 