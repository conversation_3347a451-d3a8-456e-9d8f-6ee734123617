{"logs": [{"outputFile": "com.pangu.keepaliveperfect.demo.app-mergeDebugResources-47:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\376ccaceccb8c8d200dd37c294afb78a\\transformed\\preference-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,477,646,731", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "169,256,336,472,641,726,805"}, "to": {"startLines": "59,61,114,116,119,120,121", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5938,6072,10164,10324,10646,10815,10900", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "6002,6154,10239,10455,10810,10895,10974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38bdbb3aec41791523ad0d9573b07666\\transformed\\core-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "10545", "endColumns": "100", "endOffsets": "10641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e0e92451cb7aee5ff8934f376f578f88\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,643,730,834,950,1033,1111,1202,1295,1390,1484,1584,1677,1772,1866,1957,2048,2134,2237,2342,2443,2547,2656,2764,2924,10460", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,638,725,829,945,1028,1106,1197,1290,1385,1479,1579,1672,1767,1861,1952,2043,2129,2232,2337,2438,2542,2651,2759,2919,3018,10540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae5003da7b262492451d61e3e75a684\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4743", "endColumns": "131", "endOffsets": "4870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79230ecb7deb426f90957f200c266d44\\transformed\\jetified-play-services-base-18.0.1\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3737,3844,4008,4134,4240,4395,4522,4637,4875,5041,5146,5310,5436,5591,5735,5799,5859", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "3839,4003,4129,4235,4390,4517,4632,4738,5036,5141,5305,5431,5586,5730,5794,5854,5933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fdd3b69b66e6b65f494e6d96e162c073\\transformed\\material-1.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1048,1142,1207,1266,1353,1415,1477,1537,1603,1665,1719,1831,1888,1949,2003,2075,2201,2287,2371,2510,2591,2672,2762,2815,2867,2933,3005,3089,3172,3247,3323,3396,3471,3556,3631,3723,3817,3891,3964,4058,4110,4179,4264,4351,4413,4477,4540,4643,4743,4838,4940,4997,5053", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "264,343,419,498,588,673,779,895,978,1043,1137,1202,1261,1348,1410,1472,1532,1598,1660,1714,1826,1883,1944,1998,2070,2196,2282,2366,2505,2586,2667,2757,2810,2862,2928,3000,3084,3167,3242,3318,3391,3466,3551,3626,3718,3812,3886,3959,4053,4105,4174,4259,4346,4408,4472,4535,4638,4738,4833,4935,4992,5048,5128"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3023,3102,3178,3257,3347,3432,3538,3654,6007,6159,6253,6318,6377,6464,6526,6588,6648,6714,6776,6830,6942,6999,7060,7114,7186,7312,7398,7482,7621,7702,7783,7873,7926,7978,8044,8116,8200,8283,8358,8434,8507,8582,8667,8742,8834,8928,9002,9075,9169,9221,9290,9375,9462,9524,9588,9651,9754,9854,9949,10051,10108,10244", "endLines": "5,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "endColumns": "12,78,75,78,89,84,105,115,82,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,83,138,80,80,89,52,51,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,56,55,79", "endOffsets": "314,3097,3173,3252,3342,3427,3533,3649,3732,6067,6248,6313,6372,6459,6521,6583,6643,6709,6771,6825,6937,6994,7055,7109,7181,7307,7393,7477,7616,7697,7778,7868,7921,7973,8039,8111,8195,8278,8353,8429,8502,8577,8662,8737,8829,8923,8997,9070,9164,9216,9285,9370,9457,9519,9583,9646,9749,9849,9944,10046,10103,10159,10319"}}]}]}