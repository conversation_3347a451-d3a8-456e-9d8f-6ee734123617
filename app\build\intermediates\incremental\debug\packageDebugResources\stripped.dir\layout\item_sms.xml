<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="类型"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:padding="4dp"
                android:background="#2196F3"
                android:layout_marginEnd="8dp"/>

            <TextView
                android:id="@+id/tvSender"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#333333"
                android:text="发送人" />

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="#777777"
                android:text="时间" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="14sp"
            android:textColor="#555555"
            android:text="短信内容" />

        <TextView
            android:id="@+id/tvVerificationCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#FF5722"
            android:visibility="gone"
            android:text="验证码：123456" />

    </LinearLayout>
</androidx.cardview.widget.CardView> 