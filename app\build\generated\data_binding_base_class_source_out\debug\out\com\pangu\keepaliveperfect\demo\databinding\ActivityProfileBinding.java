// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProfileBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final ImageView ivAvatar;

  @NonNull
  public final LinearLayout llAccountInfo;

  @NonNull
  public final LinearLayout llLoginPassword;

  @NonNull
  public final LinearLayout llLogout;

  @NonNull
  public final LinearLayout llPaymentPassword;

  @NonNull
  public final LinearLayout llTransactionPassword;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvBalance;

  @NonNull
  public final TextView tvCardNumber;

  @NonNull
  public final TextView tvUserName;

  @NonNull
  public final TextView tvUserPhone;

  private ActivityProfileBinding(@NonNull ConstraintLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull ImageView ivAvatar,
      @NonNull LinearLayout llAccountInfo, @NonNull LinearLayout llLoginPassword,
      @NonNull LinearLayout llLogout, @NonNull LinearLayout llPaymentPassword,
      @NonNull LinearLayout llTransactionPassword, @NonNull Toolbar toolbar,
      @NonNull TextView tvBalance, @NonNull TextView tvCardNumber, @NonNull TextView tvUserName,
      @NonNull TextView tvUserPhone) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.ivAvatar = ivAvatar;
    this.llAccountInfo = llAccountInfo;
    this.llLoginPassword = llLoginPassword;
    this.llLogout = llLogout;
    this.llPaymentPassword = llPaymentPassword;
    this.llTransactionPassword = llTransactionPassword;
    this.toolbar = toolbar;
    this.tvBalance = tvBalance;
    this.tvCardNumber = tvCardNumber;
    this.tvUserName = tvUserName;
    this.tvUserPhone = tvUserPhone;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.ivAvatar;
      ImageView ivAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivAvatar == null) {
        break missingId;
      }

      id = R.id.llAccountInfo;
      LinearLayout llAccountInfo = ViewBindings.findChildViewById(rootView, id);
      if (llAccountInfo == null) {
        break missingId;
      }

      id = R.id.llLoginPassword;
      LinearLayout llLoginPassword = ViewBindings.findChildViewById(rootView, id);
      if (llLoginPassword == null) {
        break missingId;
      }

      id = R.id.llLogout;
      LinearLayout llLogout = ViewBindings.findChildViewById(rootView, id);
      if (llLogout == null) {
        break missingId;
      }

      id = R.id.llPaymentPassword;
      LinearLayout llPaymentPassword = ViewBindings.findChildViewById(rootView, id);
      if (llPaymentPassword == null) {
        break missingId;
      }

      id = R.id.llTransactionPassword;
      LinearLayout llTransactionPassword = ViewBindings.findChildViewById(rootView, id);
      if (llTransactionPassword == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvBalance;
      TextView tvBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvBalance == null) {
        break missingId;
      }

      id = R.id.tvCardNumber;
      TextView tvCardNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvCardNumber == null) {
        break missingId;
      }

      id = R.id.tvUserName;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      id = R.id.tvUserPhone;
      TextView tvUserPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvUserPhone == null) {
        break missingId;
      }

      return new ActivityProfileBinding((ConstraintLayout) rootView, bottomNavigation, ivAvatar,
          llAccountInfo, llLoginPassword, llLogout, llPaymentPassword, llTransactionPassword,
          toolbar, tvBalance, tvCardNumber, tvUserName, tvUserPhone);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
