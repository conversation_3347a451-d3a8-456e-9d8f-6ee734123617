// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVisaBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton buttonServices;

  @NonNull
  public final MaterialButton buttonTransactions;

  @NonNull
  public final TextView cardHolderName;

  @NonNull
  public final MaterialCardView cardInfo;

  @NonNull
  public final TextView cardNumber;

  @NonNull
  public final ImageView logo;

  @NonNull
  public final TextView textServiceStatus;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView welcomeText;

  private ActivityVisaBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton buttonServices, @NonNull MaterialButton buttonTransactions,
      @NonNull TextView cardHolderName, @NonNull MaterialCardView cardInfo,
      @NonNull TextView cardNumber, @NonNull ImageView logo, @NonNull TextView textServiceStatus,
      @NonNull Toolbar toolbar, @NonNull TextView welcomeText) {
    this.rootView = rootView;
    this.buttonServices = buttonServices;
    this.buttonTransactions = buttonTransactions;
    this.cardHolderName = cardHolderName;
    this.cardInfo = cardInfo;
    this.cardNumber = cardNumber;
    this.logo = logo;
    this.textServiceStatus = textServiceStatus;
    this.toolbar = toolbar;
    this.welcomeText = welcomeText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVisaBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVisaBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_visa, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVisaBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_services;
      MaterialButton buttonServices = ViewBindings.findChildViewById(rootView, id);
      if (buttonServices == null) {
        break missingId;
      }

      id = R.id.button_transactions;
      MaterialButton buttonTransactions = ViewBindings.findChildViewById(rootView, id);
      if (buttonTransactions == null) {
        break missingId;
      }

      id = R.id.card_holder_name;
      TextView cardHolderName = ViewBindings.findChildViewById(rootView, id);
      if (cardHolderName == null) {
        break missingId;
      }

      id = R.id.card_info;
      MaterialCardView cardInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardInfo == null) {
        break missingId;
      }

      id = R.id.card_number;
      TextView cardNumber = ViewBindings.findChildViewById(rootView, id);
      if (cardNumber == null) {
        break missingId;
      }

      id = R.id.logo;
      ImageView logo = ViewBindings.findChildViewById(rootView, id);
      if (logo == null) {
        break missingId;
      }

      id = R.id.text_service_status;
      TextView textServiceStatus = ViewBindings.findChildViewById(rootView, id);
      if (textServiceStatus == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.welcome_text;
      TextView welcomeText = ViewBindings.findChildViewById(rootView, id);
      if (welcomeText == null) {
        break missingId;
      }

      return new ActivityVisaBinding((ConstraintLayout) rootView, buttonServices,
          buttonTransactions, cardHolderName, cardInfo, cardNumber, logo, textServiceStatus,
          toolbar, welcomeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
