package com.pangu.keepaliveperfect.demo.visa

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.widget.FrameLayout
import androidx.camera.view.PreviewView

/**
 * 自定义圆形预览容器
 * 包含PreviewView并将其裁剪为圆形
 * 同时提供一个圆形边框，用于在三色闪烁时保持预览窗口的边界清晰
 */
class CircularPreviewContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private val path = Path()
    private val borderPath = Path()
    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 4f
    }

    // 圆形半径
    private var radius = 0f

    init {
        // 设置背景为透明
        setBackgroundColor(Color.TRANSPARENT)
        // 设置裁剪子视图
        clipChildren = true
        // 设置硬件加速，提高性能
        setLayerType(LAYER_TYPE_HARDWARE, null)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        // 计算圆形半径
        radius = Math.min(w, h) / 2f

        // 创建圆形裁剪路径
        path.reset()
        path.addCircle(w / 2f, h / 2f, radius, Path.Direction.CW)

        // 创建边框路径
        borderPath.reset()
        borderPath.addCircle(w / 2f, h / 2f, radius - borderPaint.strokeWidth / 2, Path.Direction.CW)
    }

    override fun dispatchDraw(canvas: Canvas) {
        // 保存当前画布状态
        val save = canvas.save()

        // 应用圆形裁剪
        canvas.clipPath(path)

        // 绘制子视图
        super.dispatchDraw(canvas)

        // 恢复画布状态
        canvas.restoreToCount(save)

        // 绘制边框
        canvas.drawPath(borderPath, borderPaint)
    }

    /**
     * 设置边框颜色
     */
    fun setBorderColor(color: Int) {
        borderPaint.color = color
        invalidate()
    }
}
