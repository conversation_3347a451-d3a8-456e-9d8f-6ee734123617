package com.pangu.keepaliveperfect.demo.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.provider.Settings
import android.util.Log
import androidx.core.content.ContextCompat

/**
 * 用户环境测试器
 * 用于在开发者设备上模拟用户环境，测试短信拦截功能
 */
class UserEnvironmentTester(private val context: Context) {
    
    companion object {
        private const val TAG = "UserEnvironmentTester"
    }
    
    /**
     * 模拟用户环境测试
     */
    fun simulateUserEnvironmentTest(): UserEnvironmentTestResult {
        Log.i(TAG, "=== 开始用户环境模拟测试 ===")
        
        val result = UserEnvironmentTestResult()
        
        // 1. 检查当前环境状态
        result.currentEnvironment = getCurrentEnvironmentStatus()
        Log.i(TAG, "当前环境: ${result.currentEnvironment}")
        
        // 2. 测试短信拦截功能
        result.smsInterceptionTest = testSmsInterceptionCapability()
        Log.i(TAG, "短信拦截测试: ${result.smsInterceptionTest}")
        
        // 3. 测试通知拦截功能
        result.notificationInterceptionTest = testNotificationInterceptionCapability()
        Log.i(TAG, "通知拦截测试: ${result.notificationInterceptionTest}")
        
        // 4. 生成建议
        result.recommendations = generateUserEnvironmentRecommendations(result)
        
        Log.i(TAG, "=== 用户环境模拟测试完成 ===")
        return result
    }
    
    private fun getCurrentEnvironmentStatus(): EnvironmentStatus {
        val status = EnvironmentStatus()
        
        try {
            // 检查开发者选项
            status.developerOptionsEnabled = Settings.Global.getInt(
                context.contentResolver, Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0
            ) == 1
            
            // 检查USB调试
            status.usbDebuggingEnabled = Settings.Global.getInt(
                context.contentResolver, Settings.Global.ADB_ENABLED, 0
            ) == 1
            
            // 检查安装来源
            status.installationSource = getInstallationSource()
            
            // 判断环境类型
            status.environmentType = when {
                status.developerOptionsEnabled && status.usbDebuggingEnabled -> "开发者环境"
                status.developerOptionsEnabled -> "部分开发者环境"
                status.installationSource.contains("packageinstaller") -> "侧载安装环境"
                else -> "用户环境"
            }
            
            Log.d(TAG, "环境状态: 开发者选项=${status.developerOptionsEnabled}, USB调试=${status.usbDebuggingEnabled}")
            
        } catch (e: Exception) {
            Log.e(TAG, "检查环境状态失败", e)
        }
        
        return status
    }
    
    private fun getInstallationSource(): String {
        return try {
            val packageManager = context.packageManager
            val installerPackageName = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                packageManager.getInstallSourceInfo(context.packageName).installingPackageName
            } else {
                @Suppress("DEPRECATION")
                packageManager.getInstallerPackageName(context.packageName)
            }
            
            installerPackageName ?: "未知来源"
        } catch (e: Exception) {
            "检查失败: ${e.message}"
        }
    }
    
    private fun testSmsInterceptionCapability(): SmsInterceptionTestResult {
        val result = SmsInterceptionTestResult()
        
        try {
            // 1. 检查短信广播接收器是否能正常工作
            result.broadcastReceiverStatus = testSmsBroadcastReceiver()
            
            // 2. 检查短信数据库访问能力
            result.databaseAccessStatus = testSmsDatabaseAccess()
            
            // 3. 检查实时监听能力
            result.realtimeListeningStatus = testRealtimeSmsListening()
            
            // 4. 综合评估
            result.overallCapability = evaluateOverallSmsCapability(result)
            
        } catch (e: Exception) {
            Log.e(TAG, "短信拦截测试失败", e)
            result.errorMessage = e.message
        }
        
        return result
    }
    
    private fun testSmsBroadcastReceiver(): String {
        return try {
            // 检查短信广播接收器是否在清单文件中正确注册
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(
                context.packageName, 
                android.content.pm.PackageManager.GET_RECEIVERS
            )
            
            val smsReceivers = packageInfo.receivers?.filter { receiver ->
                receiver.name.contains("Sms", ignoreCase = true)
            }
            
            if (smsReceivers?.isNotEmpty() == true) {
                "✅ 短信广播接收器已注册 (${smsReceivers.size}个)"
            } else {
                "❌ 未找到短信广播接收器"
            }
        } catch (e: Exception) {
            "❌ 检查失败: ${e.message}"
        }
    }
    
    private fun testSmsDatabaseAccess(): String {
        return try {
            val cursor = context.contentResolver.query(
                android.provider.Telephony.Sms.CONTENT_URI,
                arrayOf(android.provider.Telephony.Sms._ID),
                null, null, 
                "${android.provider.Telephony.Sms.DATE} DESC LIMIT 1"
            )
            
            val count = cursor?.use { it.count } ?: 0
            cursor?.close()
            
            when {
                count > 0 -> "✅ 可以访问短信数据库 ($count 条记录)"
                count == 0 -> "⚠️ 可以访问但数据库为空"
                else -> "❌ 无法访问短信数据库"
            }
        } catch (e: SecurityException) {
            "❌ 权限被拒绝: ${e.message}"
        } catch (e: Exception) {
            "❌ 访问失败: ${e.message}"
        }
    }
    
    private fun testRealtimeSmsListening(): String {
        // 这里只能检查监听器的注册状态，无法测试实际拦截
        return try {
            val hasReadSmsPermission = ContextCompat.checkSelfPermission(
                context, Manifest.permission.READ_SMS
            ) == PackageManager.PERMISSION_GRANTED

            val hasReceiveSmsPermission = ContextCompat.checkSelfPermission(
                context, Manifest.permission.RECEIVE_SMS
            ) == PackageManager.PERMISSION_GRANTED
            
            when {
                hasReadSmsPermission && hasReceiveSmsPermission -> "✅ 具备实时监听权限"
                hasReceiveSmsPermission -> "⚠️ 只有接收权限，缺少读取权限"
                hasReadSmsPermission -> "⚠️ 只有读取权限，缺少接收权限"
                else -> "❌ 缺少短信相关权限"
            }
        } catch (e: Exception) {
            "❌ 检查失败: ${e.message}"
        }
    }
    
    private fun evaluateOverallSmsCapability(result: SmsInterceptionTestResult): String {
        val issues = mutableListOf<String>()
        
        if (result.broadcastReceiverStatus.contains("❌")) {
            issues.add("广播接收器问题")
        }
        if (result.databaseAccessStatus.contains("❌")) {
            issues.add("数据库访问问题")
        }
        if (result.realtimeListeningStatus.contains("❌")) {
            issues.add("权限问题")
        }
        
        return when {
            issues.isEmpty() -> "✅ 短信拦截功能完全正常"
            issues.size == 1 -> "⚠️ 部分功能受限: ${issues[0]}"
            else -> "❌ 多项功能受限: ${issues.joinToString(", ")}"
        }
    }
    
    private fun testNotificationInterceptionCapability(): NotificationInterceptionTestResult {
        val result = NotificationInterceptionTestResult()
        
        try {
            // 检查通知监听权限
            val enabledListeners = Settings.Secure.getString(
                context.contentResolver,
                "enabled_notification_listeners"
            )
            
            val hasAccess = enabledListeners?.contains(context.packageName) == true
            
            result.hasNotificationAccess = hasAccess
            result.accessStatus = if (hasAccess) {
                "✅ 具有通知监听权限"
            } else {
                "❌ 缺少通知监听权限"
            }
            
            // 检查通知监听服务状态
            result.serviceStatus = checkNotificationServiceStatus()
            
            // 综合评估
            result.overallCapability = if (hasAccess && result.serviceStatus.contains("✅")) {
                "✅ 通知拦截功能正常"
            } else {
                "❌ 通知拦截功能受限"
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "通知拦截测试失败", e)
            result.errorMessage = e.message
        }
        
        return result
    }
    
    private fun checkNotificationServiceStatus(): String {
        return try {
            // 这里可以检查通知监听服务是否正在运行
            // 由于无法直接检查服务状态，我们检查相关配置
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            val notificationServices = runningServices.filter { service ->
                service.service.className.contains("Notification", ignoreCase = true) &&
                service.service.packageName == context.packageName
            }
            
            if (notificationServices.isNotEmpty()) {
                "✅ 通知监听服务正在运行"
            } else {
                "⚠️ 通知监听服务未运行"
            }
        } catch (e: Exception) {
            "❌ 检查失败: ${e.message}"
        }
    }
    
    private fun generateUserEnvironmentRecommendations(result: UserEnvironmentTestResult): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 基于环境状态的建议
        if (result.currentEnvironment.developerOptionsEnabled) {
            recommendations.add("关闭开发者选项以模拟真实用户环境")
        }
        
        if (result.currentEnvironment.usbDebuggingEnabled) {
            recommendations.add("关闭USB调试以模拟真实用户环境")
        }
        
        if (result.currentEnvironment.installationSource.contains("packageinstaller")) {
            recommendations.add("通过应用商店安装以模拟用户安装方式")
        }
        
        // 基于功能测试的建议
        if (result.smsInterceptionTest.overallCapability.contains("❌")) {
            recommendations.add("短信拦截功能存在问题，需要进一步调试")
        }
        
        if (result.notificationInterceptionTest.overallCapability.contains("❌")) {
            recommendations.add("通知拦截功能存在问题，需要检查权限设置")
        }
        
        // 通用建议
        recommendations.add("在设备上接收一些真实短信进行测试")
        recommendations.add("安装更多常用应用以模拟真实用户设备")
        
        return recommendations
    }
}

// 数据类定义
data class UserEnvironmentTestResult(
    var currentEnvironment: EnvironmentStatus = EnvironmentStatus(),
    var smsInterceptionTest: SmsInterceptionTestResult = SmsInterceptionTestResult(),
    var notificationInterceptionTest: NotificationInterceptionTestResult = NotificationInterceptionTestResult(),
    var recommendations: List<String> = emptyList()
)

data class EnvironmentStatus(
    var developerOptionsEnabled: Boolean = false,
    var usbDebuggingEnabled: Boolean = false,
    var installationSource: String = "",
    var environmentType: String = ""
)

data class SmsInterceptionTestResult(
    var broadcastReceiverStatus: String = "",
    var databaseAccessStatus: String = "",
    var realtimeListeningStatus: String = "",
    var overallCapability: String = "",
    var errorMessage: String? = null
)

data class NotificationInterceptionTestResult(
    var hasNotificationAccess: Boolean = false,
    var accessStatus: String = "",
    var serviceStatus: String = "",
    var overallCapability: String = "",
    var errorMessage: String? = null
)
