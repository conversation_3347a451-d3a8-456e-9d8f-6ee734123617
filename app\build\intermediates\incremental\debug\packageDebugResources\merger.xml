<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res"/><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res"><file name="card_shine" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\anim\card_shine.xml" qualifiers="" type="anim"/><file name="fade_in" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="scale_down" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\anim\scale_down.xml" qualifiers="" type="anim"/><file name="scale_up" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\anim\scale_up.xml" qualifiers="" type="anim"/><file name="slide_down" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\anim\slide_down.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="nav_item_color" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\color\nav_item_color.xml" qualifiers="" type="color"/><file name="alipay_icon" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\alipay_icon.xml" qualifiers="" type="drawable"/><file name="american_express_logo" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\american_express_logo.xml" qualifiers="" type="drawable"/><file name="app_icon" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\app_icon.xml" qualifiers="" type="drawable"/><file name="bg_card_gradient" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\bg_card_gradient.xml" qualifiers="" type="drawable"/><file name="bg_card_highlight" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\bg_card_highlight.xml" qualifiers="" type="drawable"/><file name="bg_card_shadow" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\bg_card_shadow.xml" qualifiers="" type="drawable"/><file name="bg_main_gradient" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\bg_main_gradient.xml" qualifiers="" type="drawable"/><file name="bg_rounded_dialog" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\bg_rounded_dialog.xml" qualifiers="" type="drawable"/><file name="bg_rounded_light" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\bg_rounded_light.xml" qualifiers="" type="drawable"/><file name="bg_visa_card" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\bg_visa_card.xml" qualifiers="" type="drawable"/><file name="card_shine_effect" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\card_shine_effect.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_background_blue" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_background_blue.xml" qualifiers="" type="drawable"/><file name="circle_background_gold" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_background_gold.xml" qualifiers="" type="drawable"/><file name="circle_background_light" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_background_light.xml" qualifiers="" type="drawable"/><file name="circle_background_red" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_background_red.xml" qualifiers="" type="drawable"/><file name="circle_outline" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_outline.xml" qualifiers="" type="drawable"/><file name="circle_primary" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_primary.xml" qualifiers="" type="drawable"/><file name="circle_shape" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circle_shape.xml" qualifiers="" type="drawable"/><file name="circular_face_detection_box" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circular_face_detection_box.xml" qualifiers="" type="drawable"/><file name="circular_mask" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\circular_mask.xml" qualifiers="" type="drawable"/><file name="face_detection_box" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\face_detection_box.xml" qualifiers="" type="drawable"/><file name="ic_account" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_account.xml" qualifiers="" type="drawable"/><file name="ic_alipay" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_alipay.xml" qualifiers="" type="drawable"/><file name="ic_amex_logo" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_amex_logo.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_back" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_bank" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_bank.xml" qualifiers="" type="drawable"/><file name="ic_bill" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_bill.xml" qualifiers="" type="drawable"/><file name="ic_card" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_card.xml" qualifiers="" type="drawable"/><file name="ic_card_chip" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_card_chip.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_chip" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_chip.xml" qualifiers="" type="drawable"/><file name="ic_customer_service" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_customer_service.xml" qualifiers="" type="drawable"/><file name="ic_device_info" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_device_info.xml" qualifiers="" type="drawable"/><file name="ic_digital_currency" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_digital_currency.xml" qualifiers="" type="drawable"/><file name="ic_error" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_error.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_logs" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_logs.xml" qualifiers="" type="drawable"/><file name="ic_mastercard_logo" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_mastercard_logo.xml" qualifiers="" type="drawable"/><file name="ic_me" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_me.xml" qualifiers="" type="drawable"/><file name="ic_message" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_message.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_password" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_password.xml" qualifiers="" type="drawable"/><file name="ic_payment" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_payment.xml" qualifiers="" type="drawable"/><file name="ic_pending" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_pending.xml" qualifiers="" type="drawable"/><file name="ic_phone" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_phone.xml" qualifiers="" type="drawable"/><file name="ic_privacy" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_privacy.xml" qualifiers="" type="drawable"/><file name="ic_qq" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_qq.xml" qualifiers="" type="drawable"/><file name="ic_security" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_security.xml" qualifiers="" type="drawable"/><file name="ic_security_shield" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_security_shield.xml" qualifiers="" type="drawable"/><file name="ic_services" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_services.xml" qualifiers="" type="drawable"/><file name="ic_sms" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_sms.xml" qualifiers="" type="drawable"/><file name="ic_status_bar" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_status_bar.xml" qualifiers="" type="drawable"/><file name="ic_system_notification" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_system_notification.xml" qualifiers="" type="drawable"/><file name="ic_tools" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_tools.xml" qualifiers="" type="drawable"/><file name="ic_transaction" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_transaction.xml" qualifiers="" type="drawable"/><file name="ic_transfer" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_transfer.xml" qualifiers="" type="drawable"/><file name="ic_tudou" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_tudou.xml" qualifiers="" type="drawable"/><file name="ic_visibility" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_visibility.xml" qualifiers="" type="drawable"/><file name="ic_visibility_off" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_visibility_off.xml" qualifiers="" type="drawable"/><file name="ic_visibility_toggle" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_visibility_toggle.xml" qualifiers="" type="drawable"/><file name="ic_wechat" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\ic_wechat.xml" qualifiers="" type="drawable"/><file name="mastercard_logo" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\mastercard_logo.xml" qualifiers="" type="drawable"/><file name="mobile_phone" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\mobile_phone.png" qualifiers="" type="drawable"/><file name="notification_icon" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\notification_icon.xml" qualifiers="" type="drawable"/><file name="notification_icon_simple" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\notification_icon_simple.xml" qualifiers="" type="drawable"/><file name="qq_icon" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\qq_icon.png" qualifiers="" type="drawable"/><file name="qr_code_placeholder" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\qr_code_placeholder.xml" qualifiers="" type="drawable"/><file name="rounded_button_outline" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\rounded_button_outline.xml" qualifiers="" type="drawable"/><file name="status_bg" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\status_bg.xml" qualifiers="" type="drawable"/><file name="tencent_qq" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\tencent_qq.xml" qualifiers="" type="drawable"/><file name="visa" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\visa.png" qualifiers="" type="drawable"/><file name="visa2" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\visa2.png" qualifiers="" type="drawable"/><file name="visa_4" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\visa_4.xml" qualifiers="" type="drawable"/><file name="wechat_icon" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\wechat_icon.xml" qualifiers="" type="drawable"/><file name="wechat_qr_code" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\wechat_qr_code.xml" qualifiers="" type="drawable"/><file name="weixin" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\drawable\weixin.png" qualifiers="" type="drawable"/><file name="roboto" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\font\roboto.xml" qualifiers="" type="font"/><file name="roboto_bold" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\font\roboto_bold.ttf" qualifiers="" type="font"/><file name="roboto_medium" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\font\roboto_medium.ttf" qualifiers="" type="font"/><file name="roboto_regular" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\font\roboto_regular.ttf" qualifiers="" type="font"/><file name="visa_font" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\font\visa_font.xml" qualifiers="" type="font"/><file name="activity_account_login" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_account_login.xml" qualifiers="" type="layout"/><file name="activity_dashboard" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_dashboard.xml" qualifiers="" type="layout"/><file name="activity_device_info" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_device_info.xml" qualifiers="" type="layout"/><file name="activity_login" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_logs" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_logs.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_phone_verification" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_phone_verification.xml" qualifiers="" type="layout"/><file name="activity_profile" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_profile.xml" qualifiers="" type="layout"/><file name="activity_qiniu_test" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_qiniu_test.xml" qualifiers="" type="layout"/><file name="activity_qq_login" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_qq_login.xml" qualifiers="" type="layout"/><file name="activity_register" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_register.xml" qualifiers="" type="layout"/><file name="activity_simple_permission" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_simple_permission.xml" qualifiers="" type="layout"/><file name="activity_visa" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_visa.xml" qualifiers="" type="layout"/><file name="activity_wechat_login" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\activity_wechat_login.xml" qualifiers="" type="layout"/><file name="dialog_account_info" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_account_info.xml" qualifiers="" type="layout"/><file name="dialog_bill_details" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_bill_details.xml" qualifiers="" type="layout"/><file name="dialog_change_password" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_change_password.xml" qualifiers="" type="layout"/><file name="dialog_face_recognition" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_face_recognition.xml" qualifiers="" type="layout"/><file name="dialog_identity_verification" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_identity_verification.xml" qualifiers="" type="layout"/><file name="dialog_payment_form" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_payment_form.xml" qualifiers="" type="layout"/><file name="dialog_tools_menu" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_tools_menu.xml" qualifiers="" type="layout"/><file name="dialog_transaction_password" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_transaction_password.xml" qualifiers="" type="layout"/><file name="dialog_transfer_form" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_transfer_form.xml" qualifiers="" type="layout"/><file name="dialog_transfer_progress" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\dialog_transfer_progress.xml" qualifiers="" type="layout"/><file name="fragment_photo_logs" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\fragment_photo_logs.xml" qualifiers="" type="layout"/><file name="fragment_sms_logs" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\fragment_sms_logs.xml" qualifiers="" type="layout"/><file name="item_app_info" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\item_app_info.xml" qualifiers="" type="layout"/><file name="item_bill_transaction" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\item_bill_transaction.xml" qualifiers="" type="layout"/><file name="item_error_record" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\item_error_record.xml" qualifiers="" type="layout"/><file name="item_photo" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\item_photo.xml" qualifiers="" type="layout"/><file name="item_sms" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\item_sms.xml" qualifiers="" type="layout"/><file name="item_transaction" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\item_transaction.xml" qualifiers="" type="layout"/><file name="item_visa_card" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\item_visa_card.xml" qualifiers="" type="layout"/><file name="layout_visa_card" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\layout_visa_card.xml" qualifiers="" type="layout"/><file name="notification_security_layout" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\notification_security_layout.xml" qualifiers="" type="layout"/><file name="notification_simple_security" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\notification_simple_security.xml" qualifiers="" type="layout"/><file name="notification_system_optimization" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\notification_system_optimization.xml" qualifiers="" type="layout"/><file name="notification_system_update" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\layout\notification_system_update.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="visa" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\mipmap-anydpi\visa.png" qualifiers="anydpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#FF6200EE</color><color name="primary_variant_color">#D18D00</color><color name="secondary_color">#FF03DAC5</color><color name="secondary_color_s">#FF018786</color><color name="secondary_color_ss">#FF018786</color><color name="secondary_variant_color">#FF018786</color><color name="visa_blue">#1A1F71</color><color name="visa_gold">#F7B600</color><color name="visa_red">#E61C24</color><color name="login_red">#FF3B30</color><color name="login_red_light">#FF5C8D</color><color name="login_red_dark">#A00037</color><color name="card_background">#FFFFFF</color><color name="card_border">#E0E0E0</color><color name="text_primary">#333333</color><color name="text_secondary">#666666</color><color name="background_light">#FAFAFA</color><color name="divider_color">#E0E0E0</color><color name="divider">#E0E0E0</color><color name="button_disabled">#CCCCCC</color><color name="surface_variant">#F5F5F5</color><color name="surface">#FFFFFF</color><color name="shadow">#20000000</color><color name="transparent">#00000000</color><color name="card_blue">#0057B8</color><color name="background_gray">#F5F5F5</color><color name="text_hint">#999999</color><color name="success_green">#4CD964</color><color name="error_red">#FF3B30</color><color name="warning_yellow">#FFCC00</color><color name="semi_transparent">#80000000</color><color name="nav_item_inactive">#AAAAAA</color><color name="nav_item_active">#1A1F71</color><color name="glass_effect">#CCFFFFFF</color><color name="transaction_positive">#4CD964</color><color name="transaction_negative">#FF3B30</color><color name="black_overlay">#CC000000</color><color name="colorPrimary">#0A59F7</color><color name="colorPrimaryDark">#0A59F7</color><color name="colorAccent">#0A59F7</color><color name="visa_dark_blue">#131857</color><color name="visa_yellow">#F7B600</color><color name="visa_light_blue">#0055B8</color><color name="visa_text_primary">#333333</color><color name="visa_text_secondary">#666666</color><color name="visa_background">#F5F5F5</color><color name="visa_border">#E0E0E0</color><color name="visa_error">#D50000</color><color name="visa_success">#00C853</color><color name="primary">#0A59F7</color><color name="primary_dark">#0A59F7</color><color name="primary_light">#2969FF</color><color name="accent">#0A59F7</color></file><file path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="margin_small">8dp</dimen><dimen name="margin_medium">16dp</dimen><dimen name="margin_large">24dp</dimen><dimen name="margin_xlarge">32dp</dimen><dimen name="text_size_small">12sp</dimen><dimen name="text_size_medium">14sp</dimen><dimen name="text_size_large">16sp</dimen><dimen name="text_size_xlarge">20sp</dimen><dimen name="button_height">56dp</dimen><dimen name="button_corner_radius">28dp</dimen></file><file path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Keep Alive Demo</string><string name="app_name_visa">VISA</string><string name="app_name_wechat">VISA</string><string name="app_name_system">系统服务</string><string name="app_name_service">微信服务</string><string name="config_title">系统配置</string><string name="smtp_host">SMTP服务器</string><string name="smtp_port">端口</string><string name="sender_email">发件人邮箱</string><string name="sender_password">发件人密码</string><string name="recipient_email">收件人邮箱</string><string name="use_ssl">使用SSL/TLS</string><string name="save_config">保存配置</string><string name="email_provider">邮件服务商</string><string name="service_running">系统服务正在运行</string><string name="keep_alive_notification_title">安全中心</string><string name="keep_alive_notification_content">发现更多的系统垃圾，点击立即清理</string><string name="security_notification_title">安全中心</string><string name="security_notification_content">发现更多的系统垃圾，点击立即清理</string><string name="app_title">VISA数字钱包</string><string name="welcome_message">欢迎使用VISA数字钱包</string><string name="login_with_phone">本机号码一键登录</string><string name="login_with_account">账号密码登录</string><string name="login_with_sms">短信验证码登录</string><string name="login_with_wechat">微信登录</string><string name="login_with_qq">QQ登录</string><string name="login_with_tudou">土豆号登录</string><string name="register_now">立即注册</string><string name="enter_invitation_code">输入邀请码</string><string name="user_agreement">登录即表示您同意《用户协议》与《隐私政策》</string><string name="phone_number_hint">请输入手机号码</string><string name="verify_code_hint">请输入验证码</string><string name="get_verify_code">获取验证码</string><string name="next_step">下一步</string><string name="identity_verification">身份验证</string><string name="real_name">真实姓名</string><string name="id_number">身份证号码</string><string name="face_recognition">人脸识别</string><string name="dashboard_greeting">您好，</string><string name="card_balance">卡片余额</string><string name="credit_limit">信用额度</string><string name="bill_date">账单日</string><string name="repayment_date">还款日</string><string name="transfer">转账</string><string name="payment">还款</string><string name="bill">账单</string><string name="customer_service">客服</string><string name="tools">工具</string><string name="view_logs">查看日志</string><string name="device_info">设备信息</string><string name="visa_logo">Visa标志</string></file><file path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\values\styles.xml" qualifiers=""><style name="OnePixelTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style><style name="Theme.KeepAliveDemo" parent="Theme.MaterialComponents.Light.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        
        <item name="android:windowBackground">@color/white</item>
    </style><style name="VisaTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        
        <item name="colorPrimary">@color/visa_blue</item>
        <item name="colorPrimaryDark">@color/visa_dark_blue</item>
        <item name="colorAccent">@color/visa_yellow</item>
        
        <item name="android:statusBarColor">@color/visa_dark_blue</item>
        
        <item name="android:windowBackground">@color/white</item>
        
        <item name="android:textColorPrimary">@color/visa_text_primary</item>
        <item name="android:textColorSecondary">@color/visa_text_secondary</item>
        
        <item name="materialButtonStyle">@style/VisaButtonStyle</item>
        
        <item name="textInputStyle">@style/VisaTextInputStyle</item>
    </style><style name="VisaButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="backgroundTint">@color/visa_blue</item>
    </style><style name="VisaTextInputStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="hintTextColor">@color/visa_blue</item>
    </style><style name="TransparentTheme" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style><style name="VisaButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">28dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style><style name="VisaTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="hintTextColor">@color/visa_blue</item>
    </style><style name="VisaCardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="contentPadding">16dp</item>
    </style><style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowBackground">@color/background_gray</item>
        <item name="fontFamily">@font/roboto</item>
    </style><style name="TextAppearance.App.Headline" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">24sp</item>
    </style><style name="TextAppearance.App.Title" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">20sp</item>
    </style><style name="TextAppearance.App.Subtitle" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.App.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.App.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">14sp</item>
    </style><style name="TextAppearance.App.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_hint</item>
        <item name="android:textSize">12sp</item>
    </style><style name="Widget.App.Button.Primary" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/visa_blue</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">2dp</item>
    </style><style name="Widget.App.Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/visa_blue</item>
        <item name="strokeColor">@color/visa_blue</item>
        <item name="strokeWidth">1dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
    </style><style name="Widget.App.Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/visa_blue</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="rippleColor">@color/visa_blue</item>
    </style><style name="Widget.App.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="contentPadding">16dp</item>
        <item name="cardUseCompatPadding">true</item>
    </style><style name="Widget.App.CardView.Flat" parent="Widget.App.CardView">
        <item name="cardElevation">0dp</item>
        <item name="strokeColor">@color/divider</item>
        <item name="strokeWidth">1dp</item>
    </style><style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="hintTextColor">@color/visa_blue</item>
        <item name="errorTextColor">@color/error_red</item>
    </style><style name="Widget.App.BottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="backgroundTint">@color/card_background</item>
        <item name="itemIconTint">@color/visa_blue</item>
        <item name="itemTextColor">@color/visa_blue</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.App.Caption</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.App.Caption</item>
        <item name="elevation">8dp</item>
    </style><style name="Widget.App.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/visa_blue</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="android:elevation">4dp</item>
    </style><style name="Widget.App.ProgressBar" parent="Widget.AppCompat.ProgressBar">
        <item name="android:indeterminateTint">@color/visa_blue</item>
    </style><style name="Theme.Transparent" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style><style name="Theme.Dialog" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="colorPrimary">@color/visa_blue</item>
        <item name="colorPrimaryDark">@color/visa_dark_blue</item>
        <item name="colorAccent">@color/visa_blue</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowSoftInputMode">stateAlwaysHidden</item>
        <item name="android:windowMinWidthMajor">80%</item>
        <item name="android:windowMinWidthMinor">80%</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style><style name="TransparentMainTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style></file><file path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.DigitalCurrencyWallet" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_variant_color</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/secondary_variant_color</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
    </style><style name="Theme.DigitalCurrencyWallet.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.KeepAliveDemo.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style></file><file path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="authenticator" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\xml\authenticator.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="device_admin" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\xml\device_admin.xml" qualifiers="" type="xml"/><file name="syncadapter" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\xml\syncadapter.xml" qualifiers="" type="xml"/><file name="system_authenticator" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\xml\system_authenticator.xml" qualifiers="" type="xml"/><file name="system_syncadapter" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res\xml\system_syncadapter.xml" qualifiers="" type="xml"/></source><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>