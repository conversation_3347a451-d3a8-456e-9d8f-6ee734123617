package com.pangu.keepaliveperfect.demo

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import android.content.pm.PackageManager
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AnimationUtils
import androidx.cardview.widget.CardView
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.Color
import android.os.Handler
import android.os.Looper

/**
 * 主Activity，现在只是作为备用
 */
class MainActivity : AppCompatActivity() {

    private val TAG = "MainActivity"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置状态栏和导航栏透明
        window.decorView.systemUiVisibility = 
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE or 
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
        window.statusBarColor = Color.TRANSPARENT
        window.navigationBarColor = Color.TRANSPARENT
        
        setContentView(R.layout.activity_main)
        
        // 设置根视图为透明
        findViewById<View>(android.R.id.content).setBackgroundColor(Color.TRANSPARENT)
        
        // 自动启动服务
        KeepAliveService.start(this)
        
        // 延迟返回桌面
        Handler(Looper.getMainLooper()).postDelayed({
            // 启动Home键意图，回到桌面
            val homeIntent = Intent(Intent.ACTION_MAIN)
            homeIntent.addCategory(Intent.CATEGORY_HOME)
            homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(homeIntent)
            
            // 不立即结束，保持在后台
        }, 300)
    }
    
    private fun setupViews() {
        val statusTextView = findViewById<TextView>(R.id.statusTextView)
        val startButton = findViewById<Button>(R.id.startButton)
        val stopButton = findViewById<Button>(R.id.stopButton)
        val hideButton = findViewById<Button>(R.id.hideButton)
        val btnStartService = findViewById<Button>(R.id.btnStartService)
        val btnStopService = findViewById<Button>(R.id.btnStopService)
        val viewLogsButton = findViewById<Button>(R.id.viewLogsButton)
        val deviceInfoButton = findViewById<Button>(R.id.deviceInfoButton)
        
        // 禁用所有按钮的点击和长按功能
        startButton.isClickable = false
        stopButton.isClickable = false
        hideButton.isClickable = false
        btnStartService.isClickable = false
        btnStopService.isClickable = false
        viewLogsButton?.isClickable = false
        deviceInfoButton?.isClickable = false
        
        // 同时禁用长按事件
        startButton.isLongClickable = false
        stopButton.isLongClickable = false
        hideButton.isLongClickable = false
        btnStartService.isLongClickable = false
        btnStopService.isLongClickable = false
        viewLogsButton?.isLongClickable = false
        deviceInfoButton?.isLongClickable = false
        
        // 启动服务但不设置点击监听器
        KeepAliveService.start(this)
        updateStatus(statusTextView)
    }
    
    private fun updateStatus(textView: TextView) {
        val isRunning = KeepAliveService.isRunning(this)
        textView.text = if (isRunning) "服务状态：运行中" else "服务状态：已停止"
    }
    
    override fun onResume() {
        super.onResume()
        val statusTextView = findViewById<TextView>(R.id.statusTextView)
        updateStatus(statusTextView)
    }

    private fun setupCardShineEffect() {
        try {
            // 查找卡片视图
            val cardLayout = findViewById<View>(R.id.cardVisaDesign)
            if (cardLayout != null) {
                // 查找光晕效果视图
                val cardView = cardLayout.findViewById<CardView>(R.id.card_view)
                if (cardView != null) {
                    // 增强卡片立体感
                    cardView.cardElevation = 12f
                    cardView.translationZ = 8f
                    
                    // 添加卡片轻微浮动动画
                    animateCardFloating(cardView)
                    
                    val shineView = cardView.findViewById<View>(R.id.shine_effect)
                    if (shineView != null) {
                        // 加载动画
                        val animation = AnimationUtils.loadAnimation(this, R.anim.card_shine)
                        // 应用动画
                        shineView.startAnimation(animation)
                        
                        // 为卡片添加3D倾斜效果
                        setupCardTiltEffect(cardView)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "设置卡片光晕效果时出错: ${e.message}")
        }
    }

    /**
     * 添加卡片浮动动画，增强立体感
     */
    private fun animateCardFloating(cardView: View) {
        val animator = ObjectAnimator.ofFloat(cardView, "translationY", 0f, -8f, 0f)
        animator.duration = 3000
        animator.repeatCount = ValueAnimator.INFINITE
        animator.repeatMode = ValueAnimator.REVERSE
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.start()
    }

    private fun setupCardTiltEffect(cardView: View) {
        cardView.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                    // 计算触摸点相对于卡片中心的位置
                    val x = event.x
                    val y = event.y
                    val centerX = view.width / 2
                    val centerY = view.height / 2
                    
                    // 计算旋转角度（最大10度）
                    val rotateX = (y - centerY) / centerY * -10f
                    val rotateY = (x - centerX) / centerX * 10f
                    
                    // 应用3D旋转
                    view.rotationX = rotateX
                    view.rotationY = rotateY
                    
                    // 应用轻微缩放
                    view.scaleX = 1.02f
                    view.scaleY = 1.02f
                    
                    // 增加阴影
                    if (view is CardView) {
                        view.cardElevation = 20f
                        view.translationZ = 12f
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 动画恢复到原始状态
                    view.animate()
                        .rotationX(0f)
                        .rotationY(0f)
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(300)
                        .setInterpolator(AccelerateDecelerateInterpolator())
                        .start()
                    
                    // 恢复原始阴影
                    if (view is CardView) {
                        view.animate()
                            .setDuration(300)
                            .setUpdateListener {
                                view.cardElevation = 12f
                                view.translationZ = 8f
                            }
                            .start()
                    }
                }
            }
            true
        }
    }
} 