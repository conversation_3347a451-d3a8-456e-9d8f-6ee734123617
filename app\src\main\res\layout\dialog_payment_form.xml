<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_dialog"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="信用卡还款"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@color/divider_color"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvCardInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_rounded_light"
        android:padding="12dp"
        android:text="VISA信用卡 **** **** **** 1234"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divider" />

    <TextView
        android:id="@+id/tvBalanceInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/bg_rounded_light"
        android:padding="12dp"
        android:text="当前账单：¥3,500.00"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCardInfo" />

    <RadioGroup
        android:id="@+id/rgPaymentType"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBalanceInfo">

        <RadioButton
            android:id="@+id/rbFullPayment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="全额还款"
            android:textColor="@color/text_primary"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbMinimumPayment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="最低还款"
            android:textColor="@color/text_primary"
            android:textSize="16sp" />

        <RadioButton
            android:id="@+id/rbCustomPayment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="自定义金额"
            android:textColor="@color/text_primary"
            android:textSize="16sp" />
    </RadioGroup>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/tilCustomAmount"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="还款金额"
        android:visibility="gone"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusTopStart="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rgPaymentType"
        app:prefixText="¥">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etCustomAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:maxLines="1" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/tvPaymentAmount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="还款金额：¥3,500.00"
        android:textColor="@color/visa_blue"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tilCustomAmount" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnCancel"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:text="取消"
        android:textColor="@color/visa_blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnConfirm"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvPaymentAmount"
        app:strokeColor="@color/visa_blue" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnConfirm"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="@color/visa_blue"
        android:text="确认还款"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/btnCancel"
        app:layout_constraintTop_toBottomOf="@+id/tvPaymentAmount" />

</androidx.constraintlayout.widget.ConstraintLayout>
