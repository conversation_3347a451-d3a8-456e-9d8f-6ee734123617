// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBillTransactionBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView tvBillTransactionAmount;

  @NonNull
  public final TextView tvBillTransactionName;

  @NonNull
  public final TextView tvBillTransactionTime;

  private ItemBillTransactionBinding(@NonNull CardView rootView,
      @NonNull TextView tvBillTransactionAmount, @NonNull TextView tvBillTransactionName,
      @NonNull TextView tvBillTransactionTime) {
    this.rootView = rootView;
    this.tvBillTransactionAmount = tvBillTransactionAmount;
    this.tvBillTransactionName = tvBillTransactionName;
    this.tvBillTransactionTime = tvBillTransactionTime;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBillTransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBillTransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_bill_transaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBillTransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tvBillTransactionAmount;
      TextView tvBillTransactionAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvBillTransactionAmount == null) {
        break missingId;
      }

      id = R.id.tvBillTransactionName;
      TextView tvBillTransactionName = ViewBindings.findChildViewById(rootView, id);
      if (tvBillTransactionName == null) {
        break missingId;
      }

      id = R.id.tvBillTransactionTime;
      TextView tvBillTransactionTime = ViewBindings.findChildViewById(rootView, id);
      if (tvBillTransactionTime == null) {
        break missingId;
      }

      return new ItemBillTransactionBinding((CardView) rootView, tvBillTransactionAmount,
          tvBillTransactionName, tvBillTransactionTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
