<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_bill_details" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_bill_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_bill_details_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="247" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="21" startOffset="4" endLine="27" endOffset="60"/></Target><Target id="@+id/tvCardInfo" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="41" endOffset="60"/></Target><Target id="@+id/cardBillSummary" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="43" startOffset="4" endLine="192" endOffset="39"/></Target><Target id="@+id/tvBillDate" view="TextView"><Expressions/><location startLine="90" startOffset="16" endLine="96" endOffset="45"/></Target><Target id="@+id/tvRepaymentDate" view="TextView"><Expressions/><location startLine="113" startOffset="16" endLine="119" endOffset="45"/></Target><Target id="@+id/tvBillAmount" view="TextView"><Expressions/><location startLine="136" startOffset="16" endLine="143" endOffset="46"/></Target><Target id="@+id/tvMinimumPayment" view="TextView"><Expressions/><location startLine="160" startOffset="16" endLine="166" endOffset="45"/></Target><Target id="@+id/tvAvailableCredit" view="TextView"><Expressions/><location startLine="183" startOffset="16" endLine="189" endOffset="45"/></Target><Target id="@+id/tvBillTransactionsTitle" view="TextView"><Expressions/><location startLine="194" startOffset="4" endLine="205" endOffset="68"/></Target><Target id="@+id/rvBillTransactions" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="207" startOffset="4" endLine="216" endOffset="76"/></Target><Target id="@+id/btnPayNow" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="218" startOffset="4" endLine="230" endOffset="71"/></Target><Target id="@+id/btnClose" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="232" startOffset="4" endLine="245" endOffset="44"/></Target></Targets></Layout>