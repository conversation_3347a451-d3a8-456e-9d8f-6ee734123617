package com.pangu.keepaliveperfect.demo.utils

import android.util.Log
import java.util.regex.Pattern

/**
 * 短信内容提取工具类
 * 用于提取短信中的验证码、链接等信息
 */
object SmsExtractor {
    private const val TAG = "SmsExtractor"
    
    // 通用验证码正则表达式 - 匹配4-8位数字验证码
    private val COMMON_VERIFICATION_CODE_PATTERN = Pattern.compile("(?<![0-9])([0-9]{4,8})(?![0-9])")
    
    // 带有明确标记的验证码正则表达式
    private val MARKED_VERIFICATION_CODE_PATTERN = Pattern.compile(
        "(?i)(验证码|校验码|认证码|动态码|激活码|确认码|code|verification|password)[^0-9]{0,10}([0-9]{4,8})"
    )
    
    // 带有明确标记的英文验证码正则表达式
    private val ENGLISH_VERIFICATION_CODE_PATTERN = Pattern.compile(
        "(?i)(code|verification|password)[^a-zA-Z0-9]{0,10}([a-zA-Z0-9]{4,8})"
    )
    
    // 匹配URL的正则表达式
    private val URL_PATTERN = Pattern.compile(
        "(https?://(?:www\\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|www\\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|https?://(?:www\\.|(?!www))[a-zA-Z0-9]+\\.[^\\s]{2,}|www\\.[a-zA-Z0-9]+\\.[^\\s]{2,})"
    )
    
    /**
     * 提取短信中的验证码
     * 尝试多种模式匹配验证码
     */
    fun extractVerificationCode(smsContent: String): String? {
        if (smsContent.isBlank()) {
            return null
        }
        
        try {
            // 首先尝试匹配带有明确标记的验证码
            val markedMatcher = MARKED_VERIFICATION_CODE_PATTERN.matcher(smsContent)
            if (markedMatcher.find()) {
                return markedMatcher.group(2)
            }
            
            // 尝试匹配带有英文标记的验证码
            val englishMatcher = ENGLISH_VERIFICATION_CODE_PATTERN.matcher(smsContent)
            if (englishMatcher.find()) {
                return englishMatcher.group(2)
            }
            
            // 最后尝试匹配通用数字验证码
            // 这种匹配方式可能会有误判，但作为最后的尝试
            val commonMatcher = COMMON_VERIFICATION_CODE_PATTERN.matcher(smsContent)
            if (commonMatcher.find()) {
                return commonMatcher.group(1)
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取验证码失败: ${e.message}")
        }
        
        return null
    }
    
    /**
     * 提取短信中的URL链接
     */
    fun extractUrl(smsContent: String): String? {
        if (smsContent.isBlank()) {
            return null
        }
        
        try {
            val matcher = URL_PATTERN.matcher(smsContent)
            if (matcher.find()) {
                return matcher.group(1)
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取URL失败: ${e.message}")
        }
        
        return null
    }
    
    /**
     * 判断短信是否可能包含验证码
     */
    fun hasPossibleVerificationCode(smsContent: String): Boolean {
        return extractVerificationCode(smsContent) != null
    }
    
    /**
     * 判断短信是否可能包含URL链接
     */
    fun hasPossibleUrl(smsContent: String): Boolean {
        return extractUrl(smsContent) != null
    }
} 