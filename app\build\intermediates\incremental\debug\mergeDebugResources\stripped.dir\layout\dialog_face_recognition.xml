<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#CC333333">

    <!-- 顶部标题栏 -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:text="人脸识别"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 关闭按钮 -->
    <ImageView
        android:id="@+id/ivClose"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="16dp"
        android:src="@drawable/ic_back"
        android:tint="@android:color/white"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <!-- 人脸扫描区域 - 专业圆形风格 -->
    <FrameLayout
        android:id="@+id/faceFrameContainer"
        android:layout_width="280dp"
        android:layout_height="340dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.4">

        <!-- 圆形预览容器 - 添加边框和背景 -->
        <com.pangu.keepaliveperfect.demo.visa.CircularPreviewContainer
            android:id="@+id/previewContainer"
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:elevation="10dp"
            android:background="@android:color/transparent">

            <!-- 摄像头预览视图 -->
            <androidx.camera.view.PreviewView
                android:id="@+id/previewView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/transparent" />

        </com.pangu.keepaliveperfect.demo.visa.CircularPreviewContainer>

        <!-- 圆形遮罩层 -->
        <View
            android:id="@+id/circularMask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/circular_mask" />

        <!-- 圆形人脸检测框 -->
        <View
            android:id="@+id/faceDetectionBox"
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:background="@drawable/circular_face_detection_box" />

        <!-- 左上角 -->
        <View
            android:id="@+id/cornerTopLeft"
            android:layout_width="30dp"
            android:layout_height="3dp"
            android:layout_gravity="top|left"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="70dp"
            android:background="#4CD964" />

        <View
            android:id="@+id/verticalTopLeft"
            android:layout_width="3dp"
            android:layout_height="30dp"
            android:layout_gravity="top|left"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="70dp"
            android:background="#4CD964" />

        <!-- 右上角 -->
        <View
            android:id="@+id/cornerTopRight"
            android:layout_width="30dp"
            android:layout_height="3dp"
            android:layout_gravity="top|right"
            android:layout_marginRight="20dp"
            android:layout_marginTop="70dp"
            android:background="#4CD964" />

        <View
            android:id="@+id/verticalTopRight"
            android:layout_width="3dp"
            android:layout_height="30dp"
            android:layout_gravity="top|right"
            android:layout_marginRight="20dp"
            android:layout_marginTop="70dp"
            android:background="#4CD964" />

        <!-- 左下角 -->
        <View
            android:id="@+id/cornerBottomLeft"
            android:layout_width="30dp"
            android:layout_height="3dp"
            android:layout_gravity="bottom|left"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="70dp"
            android:background="#4CD964" />

        <View
            android:id="@+id/verticalBottomLeft"
            android:layout_width="3dp"
            android:layout_height="30dp"
            android:layout_gravity="bottom|left"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="70dp"
            android:background="#4CD964" />

        <!-- 右下角 -->
        <View
            android:id="@+id/cornerBottomRight"
            android:layout_width="30dp"
            android:layout_height="3dp"
            android:layout_gravity="bottom|right"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="70dp"
            android:background="#4CD964" />

        <View
            android:id="@+id/verticalBottomRight"
            android:layout_width="3dp"
            android:layout_height="30dp"
            android:layout_gravity="bottom|right"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="70dp"
            android:background="#4CD964" />

    </FrameLayout>

    <!-- 移除了扫描线 -->

    <!-- 状态提示 -->
    <TextView
        android:id="@+id/tvStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="请将脸部置于框内"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/faceFrameContainer" />

    <!-- 底部提示 -->
    <TextView
        android:id="@+id/tvTip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        android:layout_marginBottom="32dp"
        android:gravity="center"
        android:text="请确保光线充足，保持面部在框内"
        android:textColor="#AAFFFFFF"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
