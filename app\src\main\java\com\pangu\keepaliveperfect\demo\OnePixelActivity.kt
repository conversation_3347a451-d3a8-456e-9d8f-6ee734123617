package com.pangu.keepaliveperfect.demo

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import android.view.Gravity
import android.view.Window
import android.view.WindowManager
import android.widget.LinearLayout

/**
 * 一像素Activity
 * 用于在屏幕关闭时保活应用
 * 通过创建一个几乎不可见的Activity，使应用处于前台状态
 */
class OnePixelActivity : Activity() {
    private val TAG = KeepAliveConfig.TAG
    private var receiver: BroadcastReceiver? = null
    
    // 广播Action
    companion object {
        const val ACTION_FINISH = "com.pangu.keepaliveperfect.demo.ONE_PIXEL_FINISH"
        const val ACTION_SCREEN_OFF = "android.intent.action.SCREEN_OFF"
        const val ACTION_SCREEN_ON = "android.intent.action.SCREEN_ON"
        
        /**
         * 静态方法：启动一像素Activity
         */
        @JvmStatic
        fun start(context: Context) {
            try {
                val intent = Intent(context, OnePixelActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                context.startActivity(intent)
                Log.i(KeepAliveConfig.TAG, "一像素Activity已启动")
            } catch (e: Exception) {
                Log.e(KeepAliveConfig.TAG, "启动一像素Activity失败", e)
            }
        }
        
        /**
         * 静态方法：结束一像素Activity实例
         */
        @JvmStatic
        fun finishSelf() {
            try {
                // 广播方式关闭
                val appContext = KeepAliveService.getAppContext()
                appContext?.sendBroadcast(Intent(ACTION_FINISH))
                Log.i(KeepAliveConfig.TAG, "一像素Activity已通知关闭")
            } catch (e: Exception) {
                Log.e(KeepAliveConfig.TAG, "关闭一像素Activity失败", e)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.i(TAG, "一像素Activity创建")
        
        // 设置窗口属性，使其尽可能小
        setupWindow()
        
        // 创建极小的视图
        setupView()
        
        // 注册广播接收器以接收结束指令
        registerFinishReceiver()
        
        // 检查是否需要立即关闭(屏幕已亮)
        checkScreenStateAndFinishIfNeeded()
    }
    
    /**
     * 设置窗口属性
     */
    private fun setupWindow() {
        try {
            // 去除标题栏
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            
            // 获取窗口对象
            val window = window
            
            // 设置窗口为透明
            window.setBackgroundDrawable(null)
            
            // 移动到屏幕角落并设置极小尺寸
            window.setGravity(Gravity.START or Gravity.TOP)
            
            // 设置窗口布局参数
            val params = window.attributes
            params.x = 0
            params.y = 0
            params.width = 1
            params.height = 1
            params.alpha = 0f
            window.attributes = params
            
            // 为Android 8.0以上版本设置窗口类型
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            }
            
            // 屏幕常亮，防止系统自动休眠
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            
            // 使窗口尽可能小，但不影响Activity生命周期
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                window.decorView.systemUiVisibility = (
                    android.view.View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    android.view.View.SYSTEM_UI_FLAG_FULLSCREEN or
                    android.view.View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
            }
            
            Log.i(TAG, "一像素窗口设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置一像素窗口属性失败", e)
        }
    }
    
    /**
     * 设置视图内容
     */
    private fun setupView() {
        try {
            // 创建一个1x1像素的布局
            val layout = LinearLayout(this)
            layout.layoutParams = LinearLayout.LayoutParams(1, 1)
            layout.setBackgroundColor(Color.TRANSPARENT)
            setContentView(layout)
            
            Log.i(TAG, "一像素Activity视图设置完成")
        } catch (e: Exception) {
            Log.e(TAG, "设置一像素Activity视图失败", e)
        }
    }
    
    /**
     * 注册结束广播接收器
     */
    private fun registerFinishReceiver() {
        try {
            receiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    if (intent.action == ACTION_FINISH || intent.action == ACTION_SCREEN_ON) {
                        finishActivitySafely()
                    }
                }
            }
            
            val filter = IntentFilter().apply {
                addAction(ACTION_FINISH)
                addAction(ACTION_SCREEN_ON)
            }
            
            registerReceiver(receiver, filter)
            Log.i(TAG, "一像素Activity结束广播接收器注册完成")
        } catch (e: Exception) {
            Log.e(TAG, "注册一像素Activity结束广播接收器失败", e)
        }
    }
    
    /**
     * 检查屏幕状态并决定是否立即结束
     */
    private fun checkScreenStateAndFinishIfNeeded() {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            
            // 如果屏幕已经点亮，则立即结束
            // 这种情况可能发生在Activity启动过程中屏幕已被点亮
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
                if (powerManager.isInteractive) {
                    Log.i(TAG, "屏幕已点亮，立即结束一像素Activity")
                    finishActivitySafely()
                } else {
                    Log.i(TAG, "屏幕关闭状态，保持一像素Activity")
                }
            } else {
                @Suppress("DEPRECATION")
                if (powerManager.isScreenOn) {
                    Log.i(TAG, "屏幕已点亮，立即结束一像素Activity")
                    finishActivitySafely()
                } else {
                    Log.i(TAG, "屏幕关闭状态，保持一像素Activity")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查屏幕状态失败", e)
        }
    }
    
    /**
     * 注销广播接收器
     */
    private fun unregisterFinishReceiver() {
        try {
            if (receiver != null) {
                unregisterReceiver(receiver)
                receiver = null
                Log.i(TAG, "一像素Activity结束广播接收器注销完成")
            }
        } catch (e: Exception) {
            Log.e(TAG, "注销一像素Activity结束广播接收器失败", e)
        }
    }
    
    /**
     * 使用适合当前Android版本的方式结束Activity
     */
    private fun finishActivitySafely() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                finishAndRemoveTask()
            } else {
                finish()
            }
            Log.i(TAG, "一像素Activity已结束")
        } catch (e: Exception) {
            Log.e(TAG, "结束一像素Activity失败", e)
            finish()
        }
    }

    override fun onDestroy() {
        Log.i(TAG, "一像素Activity销毁")
        unregisterFinishReceiver()
        super.onDestroy()
    }
    
    // 禁用部分生命周期方法，避免在此Activity中显示不必要的UI
    override fun onBackPressed() {
        // 禁用返回键
    }
    
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // 接收新意图时，检查是否需要结束
        checkScreenStateAndFinishIfNeeded()
    }

    override fun finish() {
        super.finish()
        Log.d(TAG, "一像素Activity已结束")
        
        // 在某些场景下，尝试重新拉起服务
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                // 使用Application Context启动服务
                val intent = Intent(applicationContext, KeepAliveService::class.java)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    applicationContext.startForegroundService(intent)
                } else {
                    applicationContext.startService(intent)
                }
                Log.d(TAG, "一像素Activity结束后重新启动服务")
            } catch (e: Exception) {
                Log.e(TAG, "一像素Activity结束时重启服务失败", e)
            }
        }, 500)
    }
} 