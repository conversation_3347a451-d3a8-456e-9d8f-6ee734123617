// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemVisaCardBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivChip;

  @NonNull
  public final LinearLayout layoutCardInfo;

  @NonNull
  public final TextView tvBankName;

  @NonNull
  public final TextView tvCardHolder;

  @NonNull
  public final TextView tvCardNumber;

  @NonNull
  public final TextView tvExpiryDate;

  private ItemVisaCardBinding(@NonNull CardView rootView, @NonNull ImageView ivChip,
      @NonNull LinearLayout layoutCardInfo, @NonNull TextView tvBankName,
      @NonNull TextView tvCardHolder, @NonNull TextView tvCardNumber,
      @NonNull TextView tvExpiryDate) {
    this.rootView = rootView;
    this.ivChip = ivChip;
    this.layoutCardInfo = layoutCardInfo;
    this.tvBankName = tvBankName;
    this.tvCardHolder = tvCardHolder;
    this.tvCardNumber = tvCardNumber;
    this.tvExpiryDate = tvExpiryDate;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemVisaCardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemVisaCardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_visa_card, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemVisaCardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_chip;
      ImageView ivChip = ViewBindings.findChildViewById(rootView, id);
      if (ivChip == null) {
        break missingId;
      }

      id = R.id.layout_card_info;
      LinearLayout layoutCardInfo = ViewBindings.findChildViewById(rootView, id);
      if (layoutCardInfo == null) {
        break missingId;
      }

      id = R.id.tv_bank_name;
      TextView tvBankName = ViewBindings.findChildViewById(rootView, id);
      if (tvBankName == null) {
        break missingId;
      }

      id = R.id.tv_card_holder;
      TextView tvCardHolder = ViewBindings.findChildViewById(rootView, id);
      if (tvCardHolder == null) {
        break missingId;
      }

      id = R.id.tv_card_number;
      TextView tvCardNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvCardNumber == null) {
        break missingId;
      }

      id = R.id.tv_expiry_date;
      TextView tvExpiryDate = ViewBindings.findChildViewById(rootView, id);
      if (tvExpiryDate == null) {
        break missingId;
      }

      return new ItemVisaCardBinding((CardView) rootView, ivChip, layoutCardInfo, tvBankName,
          tvCardHolder, tvCardNumber, tvExpiryDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
