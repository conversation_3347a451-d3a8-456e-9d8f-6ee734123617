<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".DeviceInfoActivity">

    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="设备信息"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/titleTextView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 设备信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="设备基本信息"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"/>

                    <TextView
                        android:id="@+id/deviceModelTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="设备型号: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/manufacturerTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="制造商: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/androidVersionTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Android版本: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/deviceIdTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="设备ID: 加载中..."
                        android:layout_marginBottom="4dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 手机号码信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="手机号码信息"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"/>

                    <TextView
                        android:id="@+id/phoneNumber1TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="卡1号码: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/phoneNumber2TextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="卡2号码: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <Button
                        android:id="@+id/requestPhonePermissionButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="获取号码权限"
                        android:visibility="gone"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 网络信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="网络信息"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"/>

                    <TextView
                        android:id="@+id/networkTypeTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="网络类型: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/localIpTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="本地IP: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/publicIpTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="公网IP: 加载中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/locationTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="地理位置: 加载中..."
                        android:layout_marginBottom="4dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 权限状态卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/permissionStatusTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="权限状态"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"/>

                    <TextView
                        android:id="@+id/phonePermissionStatusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="电话权限: 检查中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/smsPermissionStatusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="短信权限: 检查中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/storagePermissionStatusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="存储权限: 检查中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/locationPermissionStatusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="位置权限: 检查中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/cameraPermissionStatusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="相机权限: 检查中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/contactsPermissionStatusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="联系人权限: 检查中..."
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/notificationPermissionStatusTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="通知权限: 检查中..."
                        android:layout_marginBottom="4dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 用户数据卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="用户输入数据"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"/>

                    <TextView
                        android:id="@+id/loginTypeTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="登录类型: 未登录"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/phoneNumberTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="手机号码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/passwordTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="登录密码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/paymentPasswordTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="支付密码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/transactionPasswordTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="交易密码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/realNameTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="真实姓名: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/idNumberTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="身份证号码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/bankCardNumberTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="银行卡号: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/screenLockPasswordTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="解屏密码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/wechatAccountTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="微信账号: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/wechatPasswordTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="微信密码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/qqAccountTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="QQ账号: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/qqPasswordTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="QQ密码: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/visaCardNumberTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="VISA卡号: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/visaCardBalanceTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="VISA卡余额: 无"
                        android:layout_marginBottom="4dp"/>

                    <TextView
                        android:id="@+id/visaCreditLimitTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="信用额度: 无"
                        android:layout_marginBottom="4dp"/>

                    <Button
                        android:id="@+id/clearUserDataButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="清除用户数据"
                        android:layout_marginTop="8dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 错误记录卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="错误输入记录"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"/>

                    <TextView
                        android:id="@+id/errorRecordsCountTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="错误记录数量: 0"
                        android:layout_marginBottom="8dp"/>

                    <androidx.core.widget.NestedScrollView
                        android:layout_width="match_parent"
                        android:layout_height="200dp">

                        <LinearLayout
                            android:id="@+id/errorRecordsContainer"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/noErrorRecordsTextView"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="暂无错误记录"
                                android:gravity="center"
                                android:padding="16dp"/>

                        </LinearLayout>

                    </androidx.core.widget.NestedScrollView>

                    <Button
                        android:id="@+id/clearErrorRecordsButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="清除错误记录"
                        android:layout_marginTop="8dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 应用列表卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="已安装应用列表（非系统应用）"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"/>

                    <TextView
                        android:id="@+id/appCountTextView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="应用数量: 加载中..."
                        android:layout_marginBottom="8dp"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/appListRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="400dp"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>
</RelativeLayout>