package com.pangu.keepaliveperfect.demo.visa

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.button.MaterialButton

import com.pangu.keepaliveperfect.demo.R

import android.widget.Toast
import com.pangu.keepaliveperfect.demo.utils.UserDataManager

class PhoneVerificationActivity : AppCompatActivity() {

    private lateinit var etPhoneNumber: EditText
    private lateinit var etVerifyCode: EditText
    private lateinit var tvGetCode: TextView
    private lateinit var btnNextStep: MaterialButton
    private var timer: CountDownTimer? = null
    private var autoFillHandler = android.os.Handler(android.os.Looper.getMainLooper())
    private lateinit var verificationCode: String // 验证码，每次获取时随机生成

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_phone_verification)

        initViews()
    }

    private fun initViews() {
        etPhoneNumber = findViewById(R.id.etPhoneNumber)
        etVerifyCode = findViewById(R.id.etVerifyCode)
        tvGetCode = findViewById(R.id.tvGetCode)
        btnNextStep = findViewById(R.id.btnNextStep)

        // 返回按钮
        val ivBack = findViewById<ImageView>(R.id.ivBack)
        ivBack.setOnClickListener {
            finish()
        }

        // 获取验证码按钮
        tvGetCode.setOnClickListener {
            if (etPhoneNumber.text.toString().length == 11) {
                startCountDown()
                Toast.makeText(this, "验证码已发送", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "请输入正确的手机号", Toast.LENGTH_SHORT).show()
            }
        }

        // 下一步按钮
        btnNextStep.setOnClickListener {
            val phoneNumber = etPhoneNumber.text.toString()
            val inputCode = etVerifyCode.text.toString()

            if (phoneNumber.length != 11) {
                Toast.makeText(this, "请输入正确的手机号", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            if (inputCode.length != 6) {
                Toast.makeText(this, "请输入6位验证码", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 验证验证码是否正确
            if (inputCode != verificationCode) {
                Toast.makeText(this, "验证码错误，请重新输入", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 验证通过，显示身份验证对话框
            showIdentityVerificationDialog()
        }

        // 输入监听
        etPhoneNumber.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                updateNextButtonState()
            }
        })

        etVerifyCode.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                updateNextButtonState()
            }
        })


    }

    private fun updateNextButtonState() {
        val isPhoneValid = etPhoneNumber.text.toString().length == 11
        val isCodeValid = etVerifyCode.text.toString().length == 6
        btnNextStep.isEnabled = isPhoneValid && isCodeValid
    }

    private fun startCountDown() {
        // 生成随机6位数验证码
        verificationCode = generateRandomVerificationCode()

        tvGetCode.isEnabled = false
        timer = object : CountDownTimer(60000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                tvGetCode.text = "${millisUntilFinished / 1000}秒后重新获取"
            }

            override fun onFinish() {
                tvGetCode.isEnabled = true
                tvGetCode.text = getString(R.string.get_verify_code)
            }
        }.start()

        // 延迟2-3秒后显示验证码并自动填充
        autoFillHandler.postDelayed({
            // 显示验证码Toast
            Toast.makeText(this, "您的验证码是：$verificationCode", Toast.LENGTH_SHORT).show()

            // 再延迟1秒后自动填充验证码
            autoFillHandler.postDelayed({
                // 自动填充验证码
                etVerifyCode.setText(verificationCode)

                // 显示自动填充Toast
                Toast.makeText(this, "验证码已自动填充", Toast.LENGTH_SHORT).show()
            }, 1000)
        }, 2500) // 延迟2.5秒
    }

    /**
     * 生成随机6位数验证码
     */
    private fun generateRandomVerificationCode(): String {
        val random = java.util.Random()
        val code = StringBuilder()

        // 生成6位随机数字
        for (i in 0 until 6) {
            code.append(random.nextInt(10))
        }

        return code.toString()
    }

    private fun showIdentityVerificationDialog() {
        val dialog = BottomSheetDialog(this)
        val view = layoutInflater.inflate(R.layout.dialog_identity_verification, null)
        dialog.setContentView(view)

        // 设置对话框全屏显示，解决输入法遮挡问题
        dialog.behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED

        // 设置对话框可调整大小，以适应输入法
        dialog.behavior.skipCollapsed = true
        dialog.behavior.isDraggable = false

        // 获取身份信息输入框
        val etRealName = view.findViewById<EditText>(R.id.etRealName)
        val etIdNumber = view.findViewById<EditText>(R.id.etIdNumber)
        val btnFaceRecognition = view.findViewById<MaterialButton>(R.id.btnFaceRecognition)

        // 设置输入框焦点变化监听，确保对话框在输入时调整位置
        etRealName.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                dialog.behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED
            }
        }

        etIdNumber.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                dialog.behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED
            }
        }

        btnFaceRecognition.setOnClickListener {
            // 获取用户输入的身份信息
            val realName = etRealName.text.toString().trim()
            val idNumber = etIdNumber.text.toString().trim().uppercase() // 转为大写，确保X是大写

            // 获取手机号
            val phoneNumber = etPhoneNumber.text.toString().trim()

            // 验证姓名
            if (realName.isEmpty()) {
                Toast.makeText(this, "请输入真实姓名", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 验证身份证号码
            if (!isValidIdNumber(idNumber)) {
                Toast.makeText(this, "请输入正确的18位身份证号码", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 验证用户输入
            val isValid = UserDataManager.verifyPhoneLogin(this, phoneNumber, realName, idNumber)

            if (isValid) {
                // 登录成功，设置登录类型
                UserDataManager.saveLoginType(this, UserDataManager.LOGIN_TYPE_PHONE)

                // 生成随机VISA卡信息
                UserDataManager.getVisaCardNumber(this) // 这会自动生成并保存
                UserDataManager.getVisaCardBalance(this) // 这会自动生成并保存
                UserDataManager.getVisaCreditLimit(this) // 这会自动设置并保存

                dialog.dismiss()
                // 面部识别成功后，跳转到主界面
                startActivity(Intent(this, DashboardActivity::class.java))
                finish()
            } else {
                // 登录失败
                Toast.makeText(this, "身份验证失败，请检查输入信息", Toast.LENGTH_SHORT).show()
            }
        }

        dialog.show()
    }



    override fun onDestroy() {
        super.onDestroy()
        timer?.cancel()
        // 移除所有延迟任务
        autoFillHandler.removeCallbacksAndMessages(null)
    }

    /**
     * 验证身份证号码是否有效
     * 简单验证：长度为18位，最后一位可以是数字或X
     */
    private fun isValidIdNumber(idNumber: String): Boolean {
        // 验证长度
        if (idNumber.length != 18) {
            return false
        }

        // 验证前17位是否都是数字
        val regex = Regex("^[0-9]{17}[0-9X]$")
        return regex.matches(idNumber)
    }
}