package com.pangu.keepaliveperfect.demo.account

import android.accounts.Account
import android.app.Service
import android.content.AbstractThreadedSyncAdapter
import android.content.ContentProviderClient
import android.content.Context
import android.content.Intent
import android.content.SyncResult
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import com.pangu.keepaliveperfect.demo.KeepAliveConfig
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.KeepAliveUtils

/**
 * 同步服务
 * 用于Android账户同步框架的同步部分
 * 定期执行同步操作，作为保活机制的一部分
 */
class SyncService : Service() {
    private lateinit var syncAdapter: SyncAdapter
    
    override fun onCreate() {
        super.onCreate()
        syncAdapter = SyncAdapter(applicationContext, true)
        Log.i(KeepAliveConfig.TAG, "同步服务已创建")
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return syncAdapter.syncAdapterBinder
    }
    
    /**
     * 同步适配器实现
     */
    private class SyncAdapter(context: Context, autoInitialize: Boolean) :
        AbstractThreadedSyncAdapter(context, autoInitialize) {
        
        override fun onPerformSync(
            account: Account?,
            extras: Bundle?,
            authority: String?,
            provider: ContentProviderClient?,
            syncResult: SyncResult?
        ) {
            Log.i(KeepAliveConfig.TAG, "执行同步操作")
            
            // 在同步操作中检查服务状态并确保其运行
            val context = context
            if (!KeepAliveUtils.isServiceRunning(context, KeepAliveService::class.java)) {
                Log.i(KeepAliveConfig.TAG, "从同步操作中启动服务")
                KeepAliveService.startServiceSafely(context, KeepAliveService::class.java)
            }
            
            // 模拟同步操作执行
            try {
                // 提升进程优先级
                KeepAliveUtils.raiseProcessPriority()
                
                // 假装在做一些数据同步工作
                Thread.sleep(1000)
                
                Log.i(KeepAliveConfig.TAG, "同步操作完成")
            } catch (e: Exception) {
                Log.e(KeepAliveConfig.TAG, "同步操作失败", e)
                syncResult?.stats?.numIoExceptions = (syncResult?.stats?.numIoExceptions ?: 0) + 1
            }
        }
    }
} 