package com.pangu.keepaliveperfect.demo.visa

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.FloatEvaluator
import android.animation.ValueAnimator
import android.view.animation.LinearInterpolator
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.util.Size
import android.content.pm.PackageManager
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.VibrationEffect
import android.os.Vibrator
import android.util.Log
import android.view.Surface
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.CameraState
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.common.util.concurrent.ListenableFuture
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetector
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.pangu.keepaliveperfect.demo.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 真实人脸识别弹窗
 * 使用CameraX和ML Kit进行真实的人脸检测
 */
class FaceRecognitionDialog(
    context: Activity, // 明确要求Activity上下文
    private val onRecognitionResult: (success: Boolean) -> Unit
) : Dialog(context) {

    // 保存Activity引用，用于生命周期绑定
    private val activityContext: Activity = context

    private lateinit var previewView: PreviewView
    private lateinit var faceDetectionBox: View
    private lateinit var ivClose: ImageView
    private lateinit var faceFrameContainer: FrameLayout
    private lateinit var tvStatus: TextView
    private lateinit var tvTip: TextView

    // 四个角的View
    private lateinit var cornerTopLeft: View
    private lateinit var cornerTopRight: View
    private lateinit var cornerBottomLeft: View
    private lateinit var cornerBottomRight: View

    private var cornerAnimatorSet: AnimatorSet? = null
    private var recognitionHandler = Handler(Looper.getMainLooper())
    private var isRecognizing = false
    private var faceDetected = false
    private var hasScheduledFallback = false
    private var recognitionStage = 0 // 0: 未开始, 1: 检测中(红色), 2: 识别中(黄色), 3: 成功(绿色)

    private lateinit var cameraProvider: ProcessCameraProvider
    private lateinit var camera: Camera
    private lateinit var cameraExecutor: ExecutorService

    // 使用极速人脸检测器配置，最大化性能
    private val faceDetectorOptions = FaceDetectorOptions.Builder()
        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST) // 快速模式，减少卡顿
        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE) // 不需要分类，提高性能
        .setContourMode(FaceDetectorOptions.CONTOUR_MODE_NONE) // 不需要轮廓，提高性能
        .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE) // 不需要特征点，提高性能
        .setMinFaceSize(0.2f) // 增大最小人脸尺寸阈值，减少误检
        .enableTracking() // 保留跟踪功能，提高稳定性
        .build()

    // 延迟初始化人脸检测器，避免在UI线程上进行耗时操作
    private lateinit var faceDetector: FaceDetector

    // 添加检测超时计时器
    private var detectionTimeoutHandler = Handler(Looper.getMainLooper())
    private var detectionTimeoutRunnable: Runnable? = null

    // 简化的验证状态
    private var verificationStage = 0 // 0: 未开始, 1: 人脸检测, 2: 验证中, 3: 完成

    // 三色闪烁控制
    private var colorAnimationHandler = Handler(Looper.getMainLooper())
    private var colorAnimationRunnable: Runnable? = null

    // 人脸检测时间记录
    private var lastFaceDetectionTime = 0L

    // 甜甜圈覆盖层，用于三色闪烁效果，避开圆形预览窗口
    private lateinit var donutOverlay: DonutView

    // 控制是否停止图像分析
    private var stopAnalysis = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(R.layout.dialog_face_recognition)

        // 设置全屏
        window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT
            )
        }

        // 初始化相机执行器
        cameraExecutor = Executors.newSingleThreadExecutor()

        // 在后台线程初始化人脸检测器，避免UI卡顿
        Thread {
            try {
                faceDetector = FaceDetection.getClient(faceDetectorOptions)
                Log.d("FaceRecognition", "人脸检测器初始化成功")
            } catch (e: Exception) {
                Log.e("FaceRecognition", "人脸检测器初始化失败: ${e.message}")
            }
        }.start()

        // 初始化视图
        initViews()

        // 设置不可取消
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        // 启动角落动画
        startCornerAnimation()

        // 设置对话框显示监听器
        setOnShowListener {
            Log.d("FaceRecognition", "对话框已显示")
            // 确保角落动画正在运行
            if (cornerAnimatorSet == null || !cornerAnimatorSet!!.isRunning) {
                startCornerAnimation()
            }
        }
    }

    private fun initViews() {
        previewView = findViewById(R.id.previewView)
        faceDetectionBox = findViewById(R.id.faceDetectionBox)
        ivClose = findViewById(R.id.ivClose)
        faceFrameContainer = findViewById(R.id.faceFrameContainer)
        tvStatus = findViewById(R.id.tvStatus)
        tvTip = findViewById(R.id.tvTip)

        // 初始化四个角
        cornerTopLeft = findViewById(R.id.cornerTopLeft)
        cornerTopRight = findViewById(R.id.cornerTopRight)
        cornerBottomLeft = findViewById(R.id.cornerBottomLeft)
        cornerBottomRight = findViewById(R.id.cornerBottomRight)

        // 设置关闭按钮点击事件
        ivClose.setOnClickListener {
            stopRecognition()
            onRecognitionResult(false)
            dismiss()
        }

        // 设置初始状态
        tvStatus.text = "准备检测"
        tvStatus.setTextColor(Color.WHITE)
        tvTip.text = "请将面部对准框内，保持光线充足"
        tvTip.setTextColor(Color.WHITE)

        // 确保人脸检测框可见
        faceDetectionBox.visibility = View.VISIBLE

        // 设置人脸检测框的初始颜色
        val drawable = faceDetectionBox.background as GradientDrawable
        drawable.setStroke(6, Color.WHITE)

        // 确保预览视图和人脸检测框的层级关系正确
        previewView.bringToFront()
        faceDetectionBox.bringToFront()
        cornerTopLeft.bringToFront()
        cornerTopRight.bringToFront()
        cornerBottomLeft.bringToFront()
        cornerBottomRight.bringToFront()
        tvStatus.bringToFront()
        tvTip.bringToFront()
        ivClose.bringToFront()
    }

    /**
     * 开始真实人脸识别过程
     */
    fun startRecognition() {
        if (isRecognizing) return
        isRecognizing = true
        faceDetected = false
        hasScheduledFallback = false

        // 重置验证状态
        verificationStage = 0

        // 初始状态 - 红色
        setRecognitionStage(1)

        // 启动角落动画
        startCornerAnimation()

        // 设置检测超时 - 15秒后如果仍未检测到人脸，则提示用户
        detectionTimeoutRunnable = Runnable {
            if (isRecognizing && !faceDetected) {
                Log.d("FaceRecognition", "人脸检测超时，提示用户")
                activityContext.runOnUiThread {
                    Toast.makeText(context, "未检测到人脸，请将面部对准框内", Toast.LENGTH_LONG).show()
                    tvTip.text = "未检测到人脸，请将面部对准框内"

                    // 重新设置超时，再给用户15秒时间
                    if (detectionTimeoutRunnable != null) {
                        detectionTimeoutHandler.removeCallbacks(detectionTimeoutRunnable!!)
                        detectionTimeoutHandler.postDelayed(detectionTimeoutRunnable!!, 15000)
                    }
                }
            }
        }
        detectionTimeoutHandler.postDelayed(detectionTimeoutRunnable!!, 15000)

        // 使用XXPermissions请求相机权限
        requestCameraPermission()
    }

    /**
     * 使用XXPermissions请求相机权限
     */
    private fun requestCameraPermission() {
        XXPermissions.with(context)
            .permission(Permission.CAMERA)
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                        // 权限已授予，启动相机
                        startCamera()
                    } else {
                        // 部分权限被拒绝（对于单个相机权限来说不太可能）
                        Toast.makeText(context, "需要相机权限进行人脸识别", Toast.LENGTH_SHORT).show()
                        onRecognitionResult(false)
                        dismiss()
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    if (never) {
                        // 用户选择了不再询问，引导用户手动开启
                        Toast.makeText(context, "请在设置中开启相机权限以使用人脸识别功能", Toast.LENGTH_LONG).show()
                        // 跳转到设置页面
                        try {
                            XXPermissions.startPermissionActivity(context as Activity, permissions)
                        } catch (e: Exception) {
                            Log.e("FaceRecognition", "无法跳转到权限设置页面", e)
                        }
                    } else {
                        // 用户拒绝了权限请求
                        Toast.makeText(context, "需要相机权限进行人脸识别", Toast.LENGTH_SHORT).show()
                    }

                    // 无论如何，如果权限被拒绝，就关闭对话框并返回失败
                    onRecognitionResult(false)
                    dismiss()
                }
            })
    }

    /**
     * 启动相机并开始人脸检测
     * 使用最简单的方式初始化相机，提高兼容性
     */
    private fun startCamera() {
        Log.d("FaceRecognition", "开始启动相机")

        // 更新UI状态
        activityContext.runOnUiThread {
            // 确保UI元素可见
            previewView.visibility = View.VISIBLE
            faceDetectionBox.visibility = View.VISIBLE

            // 显示加载状态
            tvStatus.text = "正在启动相机..."
            tvTip.text = "请稍候"

            // 确保角落动画正在运行
            if (cornerAnimatorSet == null || !cornerAnimatorSet!!.isRunning) {
                startCornerAnimation()
            }
        }

        try {
            // 检查相机权限
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                Log.e("FaceRecognition", "没有相机权限")

                activityContext.runOnUiThread {
                    Toast.makeText(context, "请授予相机权限以进行人脸识别", Toast.LENGTH_SHORT).show()
                    tvStatus.text = "无法访问相机"
                    tvTip.text = "请授予相机权限后重试"
                }

                // 延迟关闭对话框
                recognitionHandler.postDelayed({
                    dismiss()
                }, 2000)

                return
            }

            // 使用主线程执行器
            val mainExecutor = ContextCompat.getMainExecutor(context)

            // 获取相机提供者
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

            cameraProviderFuture.addListener({
                try {
                    // 获取相机提供者
                    cameraProvider = cameraProviderFuture.get()

                    // 解绑所有现有用例
                    cameraProvider.unbindAll()

                    // 创建预览用例 - 使用极低分辨率提高性能
                    val preview = Preview.Builder()
                        .setTargetResolution(Size(240, 240)) // 使用极低的正方形分辨率
                        .setTargetRotation(Surface.ROTATION_0) // 指定旋转方向
                        .build()

                    // 设置预览视图 - 使用PERFORMANCE模式提高性能
                    previewView.implementationMode = PreviewView.ImplementationMode.PERFORMANCE
                    previewView.scaleType = PreviewView.ScaleType.FILL_CENTER
                    preview.setSurfaceProvider(previewView.surfaceProvider)

                    // 创建图像分析用例 - 使用极低分辨率提高性能
                    val imageAnalysis = ImageAnalysis.Builder()
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .setTargetResolution(Size(120, 120)) // 使用极低的分辨率
                        .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888) // 使用YUV格式，减少转换开销
                        .setTargetRotation(Surface.ROTATION_0) // 指定旋转方向
                        .build()

                    // 设置图像分析器
                    imageAnalysis.setAnalyzer(cameraExecutor, imageAnalyzer)

                    // 尝试使用前置相机
                    try {
                        // 选择前置相机
                        val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA

                        // 绑定用例
                        camera = cameraProvider.bindToLifecycle(
                            activityContext as LifecycleOwner,
                            cameraSelector,
                            preview,
                            imageAnalysis
                        )

                        Log.d("FaceRecognition", "前置相机绑定成功")

                        // 更新UI
                        activityContext.runOnUiThread {
                            tvStatus.text = "正在检测人脸..."
                            tvTip.text = "请将脸部对准框内，保持正面朝向"
                        }

                    } catch (e: Exception) {
                        Log.e("FaceRecognition", "前置相机绑定失败，尝试后置相机: ${e.message}")

                        try {
                            // 选择后置相机
                            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                            // 绑定用例
                            camera = cameraProvider.bindToLifecycle(
                                activityContext as LifecycleOwner,
                                cameraSelector,
                                preview,
                                imageAnalysis
                            )

                            Log.d("FaceRecognition", "后置相机绑定成功")

                            // 更新UI
                            activityContext.runOnUiThread {
                                tvStatus.text = "正在检测人脸..."
                                tvTip.text = "使用后置相机，请将脸部对准框内，保持正面朝向"
                            }

                        } catch (e2: Exception) {
                            Log.e("FaceRecognition", "后置相机也绑定失败: ${e2.message}")
                            handleCameraError()
                        }
                    }

                } catch (e: Exception) {
                    Log.e("FaceRecognition", "相机初始化失败: ${e.message}")
                    handleCameraError()
                }

            }, mainExecutor)

        } catch (e: Exception) {
            Log.e("FaceRecognition", "相机启动过程中发生异常: ${e.message}")
            handleCameraError()
        }
    }

    /**
     * 图像分析器
     * 使用帧计数器减少处理频率，提高性能
     */
    private val imageAnalyzer = ImageAnalysis.Analyzer { imageProxy ->
        try {
            // 使用帧计数器减少处理频率
            // 在三色闪烁阶段，每5帧处理一次；正常阶段每3帧处理一次
            // 在最后阶段（绿色确认阶段），每8帧处理一次
            val skipFrames = when {
                verificationStage == 3 -> 8 // 最后阶段，极低频率检测
                colorAnimationRunnable != null -> 5 // 三色闪烁阶段，低频率检测
                else -> 3 // 正常阶段，中等频率检测
            }

            if (frameCounter % skipFrames == 0) {
                // 处理图像
                processImage(imageProxy)
            } else {
                // 跳过这一帧，直接关闭图像代理
                imageProxy.close()
            }

            // 增加帧计数器
            frameCounter++

            // 防止溢出
            if (frameCounter > 1000) {
                frameCounter = 0
            }
        } catch (e: Exception) {
            Log.e("FaceRecognition", "图像分析失败: ${e.message}")
            imageProxy.close()
        }
    }

    // 帧计数器，用于减少处理频率
    private var frameCounter = 0



    /**
     * 处理相机初始化失败的情况
     * 显示错误信息并提供重试选项
     */
    private fun handleCameraError() {
        Log.e("FaceRecognition", "相机初始化失败，提示用户重试")

        try {
            // 停止任何正在进行的相机操作
            if (cameraProvider != null) {
                try {
                    cameraProvider.unbindAll()
                } catch (e: Exception) {
                    Log.e("FaceRecognition", "解绑相机失败: ${e.message}")
                }
            }

            // 确保在UI线程更新界面
            activityContext.runOnUiThread {
                try {
                    // 设置红色状态
                    val redColor = "#FF0000"
                    setCornerColor(redColor)

                    // 更新提示文本
                    tvStatus.text = "相机初始化失败"
                    tvStatus.setTextColor(Color.parseColor(redColor))
                    tvTip.text = "请点击关闭按钮并重试"

                    // 确保UI元素状态正确
                    previewView.visibility = View.GONE // 隐藏预览，因为相机未初始化
                    faceDetectionBox.visibility = View.VISIBLE

                    // 确保角落动画正在运行，提供视觉反馈
                    if (cornerAnimatorSet == null || !cornerAnimatorSet!!.isRunning) {
                        startCornerAnimation()
                    }

                    // 显示错误提示
                    Toast.makeText(context, "无法初始化相机，请确保相机权限已授予并重试", Toast.LENGTH_LONG).show()

                    // 震动反馈
                    vibratePhone(100)
                } catch (e: Exception) {
                    Log.e("FaceRecognition", "更新UI失败: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e("FaceRecognition", "处理相机错误时发生异常: ${e.message}")
        }
    }

    /**
     * 处理相机图像并进行人脸检测
     * 极度优化性能，减少不必要的处理
     */
    @OptIn(ExperimentalGetImage::class)
    private fun processImage(imageProxy: ImageProxy) {
        try {
            // 如果人脸检测器尚未初始化，关闭图像代理并返回
            if (!::faceDetector.isInitialized) {
                imageProxy.close()
                return
            }

            // 如果在最后阶段（绿色确认阶段），只需要极低频率检测人脸是否存在
            val isInFinalStage = verificationStage == 3

            // 如果正在进行三色闪烁且已经检测到人脸，只需要检查人脸是否仍然存在
            val isInColorAnimation = colorAnimationRunnable != null

            val mediaImage = imageProxy.image
            if (mediaImage != null) {
                // 创建ML Kit输入图像
                val image = InputImage.fromMediaImage(
                    mediaImage,
                    imageProxy.imageInfo.rotationDegrees
                )

                // 进行人脸检测 - 使用更简单的回调处理
                faceDetector.process(image)
                    .addOnSuccessListener { faces ->
                        // 简化处理逻辑，减少不必要的操作
                        val hasFace = faces.isNotEmpty()

                        if (hasFace) {
                            // 更新最后一次人脸检测时间
                            lastFaceDetectionTime = System.currentTimeMillis()

                            // 如果是首次检测到人脸，开始三色闪烁
                            if (!faceDetected) {
                                faceDetected = true
                                verificationStage = 1

                                // 更新UI - 使用更轻量的UI更新
                                activityContext.runOnUiThread {
                                    tvStatus.text = "已检测到人脸"
                                    tvTip.text = "请保持脸部对准框内，不要移动"

                                    // 震动反馈
                                    vibratePhone(100)
                                }

                                // 延迟开始三色闪烁，给用户一个准备的时间
                                // 使用单独的Handler避免UI线程阻塞
                                recognitionHandler.postDelayed({
                                    if (isRecognizing && faceDetected) {
                                        // 开始三色闪烁
                                        startColorAnimation()
                                    }
                                }, 1000)
                            }
                        } else if (faceDetected) {
                            // 如果之前已经检测到人脸，但现在丢失了
                            // 无论在什么阶段，只要人脸丢失，立即重置验证状态
                            faceDetected = false

                            // 如果正在进行三色闪烁，取消它
                            if (colorAnimationRunnable != null) {
                                colorAnimationHandler.removeCallbacks(colorAnimationRunnable!!)
                                colorAnimationRunnable = null
                                stopAnalysis = false // 恢复图像分析
                            }

                            verificationStage = 0

                            // 重置UI - 使用更轻量的UI更新
                            activityContext.runOnUiThread {
                                setRecognitionStage(1)
                                tvStatus.text = "未检测到人脸"
                                tvTip.text = "请将脸部对准框内，确保光线充足"

                                // 隐藏甜甜圈覆盖层
                                if (::donutOverlay.isInitialized) {
                                    donutOverlay.setOverlayColor(Color.TRANSPARENT)
                                    donutOverlay.visibility = View.INVISIBLE
                                }

                                // 震动提示人脸丢失
                                vibratePhone(50)
                            }
                        }
                    }
                    .addOnCompleteListener {
                        // 始终关闭图像代理，避免内存泄漏
                        imageProxy.close()
                    }
            } else {
                imageProxy.close()
            }
        } catch (e: Exception) {
            Log.e("FaceRecognition", "处理图像时发生异常: ${e.message}")
            imageProxy.close()
        }
    }

    /**
     * 震动手机提供触觉反馈
     */
    private fun vibratePhone(duration: Long) {
        try {
            val vibrator = activityContext.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(duration, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(duration)
            }
        } catch (e: Exception) {
            Log.e("FaceRecognition", "震动失败: ${e.message}")
        }
    }

    /**
     * 开始三色闪烁动画
     * 红色 -> 黄色 -> 绿色 -> 完成
     * 只有在检测到人脸时才会启动
     */
    private fun startColorAnimation() {
        // 确保已经检测到人脸
        if (!faceDetected) {
            return
        }

        // 不停止图像分析，确保能够检测到人脸是否离开
        stopAnalysis = false

        // 取消已有的动画
        if (colorAnimationRunnable != null) {
            colorAnimationHandler.removeCallbacks(colorAnimationRunnable!!)
            colorAnimationRunnable = null
        }

        // 设置初始状态 - 红色
        activityContext.runOnUiThread {
            setRecognitionStage(1, false) // 传递false表示不处于三色闪烁阶段
            tvStatus.text = "人脸检测中"
            tvTip.text = "请保持脸部对准框内，不要移动"

            // 确保甜甜圈覆盖层不可见（在人脸识别过程中不显示彩色背景）
            if (::donutOverlay.isInitialized) {
                donutOverlay.visibility = View.INVISIBLE
                donutOverlay.setOverlayColor(Color.TRANSPARENT)
            }

            // 震动反馈
            vibratePhone(50)
        }

        // 创建三色闪烁动画 - 使用更专业的闪烁效果
        colorAnimationRunnable = object : Runnable {
            var currentStage = 1 // 当前阶段：1-红色，2-黄色，3-绿色

            // 极简闪烁效果 - 直接设置状态，避免复杂动画
            // 极度优化性能，减少动画更新频率
            private fun showColorWithAnimation(duration: Long, onComplete: () -> Unit) {
                // 使用硬件加速
                donutOverlay.setLayerType(View.LAYER_TYPE_HARDWARE, null)

                // 直接设置颜色可见
                activityContext.runOnUiThread {
                    donutOverlay.overlayAlpha = 0.8f
                    donutOverlay.visibility = View.VISIBLE
                }

                // 使用延迟处理器代替动画
                val handler = Handler(Looper.getMainLooper())

                // 保持颜色显示一段时间
                handler.postDelayed({
                    // 直接设置颜色消失
                    activityContext.runOnUiThread {
                        donutOverlay.overlayAlpha = 0f
                    }

                    // 延迟执行完成回调
                    handler.postDelayed({
                        // 恢复正常渲染模式
                        donutOverlay.setLayerType(View.LAYER_TYPE_NONE, null)
                        onComplete()
                    }, 50)
                }, 250) // 保持250毫秒
            }

            override fun run() {
                if (!isRecognizing || !faceDetected) {
                    return
                }

                when (currentStage) {
                    1 -> { // 红色阶段
                        // 设置红色
                        activityContext.runOnUiThread {
                            setRecognitionStage(1, true) // 传递true表示处于三色闪烁阶段
                            donutOverlay.visibility = View.VISIBLE
                            tvStatus.text = "人脸识别中"
                            tvTip.text = "请保持不动"
                        }

                        // 显示红色，然后进入黄色阶段 - 使用更短的动画时间
                        showColorWithAnimation(150L) {
                            // 红色阶段完成，进入黄色阶段
                            currentStage = 2

                            // 震动反馈
                            vibratePhone(50)

                            // 立即执行下一阶段，不延迟
                            colorAnimationHandler.post(this)
                        }
                    }
                    2 -> { // 黄色阶段
                        // 设置黄色
                        activityContext.runOnUiThread {
                            setRecognitionStage(2, true) // 传递true表示处于三色闪烁阶段
                            donutOverlay.visibility = View.VISIBLE
                            tvStatus.text = "验证中"
                            tvTip.text = "请保持不动"
                        }

                        // 显示黄色，然后进入绿色阶段 - 使用更短的动画时间
                        showColorWithAnimation(150L) {
                            // 黄色阶段完成，进入绿色阶段
                            currentStage = 3

                            // 震动反馈
                            vibratePhone(50)

                            // 立即执行下一阶段，不延迟
                            colorAnimationHandler.post(this)
                        }
                    }
                    3 -> { // 绿色阶段
                        // 设置绿色
                        activityContext.runOnUiThread {
                            setRecognitionStage(3, true) // 传递true表示处于三色闪烁阶段
                            donutOverlay.visibility = View.VISIBLE
                            tvStatus.text = "验证成功"
                            tvTip.text = "识别完成"
                        }

                        // 显示绿色，然后完成验证 - 使用更短的动画时间
                        showColorWithAnimation(150L) {
                            // 绿色阶段完成，验证通过
                            verificationStage = 3 // 设置为完成状态

                            // 最后一次全屏显示绿色 - 使用简化的动画效果减少负担
                            // 直接设置最终状态，避免动画过渡
                            activityContext.runOnUiThread {
                                donutOverlay.overlayAlpha = 1.0f
                            }

                            // 使用简单的延迟处理器，而不是复杂的动画
                            // 延迟关闭对话框
                            recognitionHandler.postDelayed({
                                // 再次检查人脸是否仍然存在
                                if (isRecognizing && faceDetected) {
                                    stopRecognition()
                                    onRecognitionResult(true)
                                    dismiss()
                                } else {
                                    // 如果人脸已经离开，取消验证
                                    stopAnalysis = false
                                    if (colorAnimationRunnable != null) {
                                        colorAnimationHandler.removeCallbacks(colorAnimationRunnable!!)
                                        colorAnimationRunnable = null
                                    }

                                    // 重置UI
                                    activityContext.runOnUiThread {
                                        setRecognitionStage(1)
                                        tvStatus.text = "未检测到人脸"
                                        tvTip.text = "请将脸部对准框内，确保光线充足"

                                        // 隐藏甜甜圈覆盖层
                                        if (::donutOverlay.isInitialized) {
                                            donutOverlay.setOverlayColor(Color.TRANSPARENT)
                                            donutOverlay.visibility = View.INVISIBLE
                                        }

                                        // 震动提示人脸丢失
                                        vibratePhone(50)
                                    }
                                }
                            }, 300) // 减少延迟时间

                            // 震动反馈 - 更强的震动
                            vibratePhone(150)
                        }
                    }
                }
            }
        }

        // 立即开始动画
        colorAnimationHandler.post(colorAnimationRunnable!!)
    }

    /**
     * 动画改变覆盖层的透明度，创建闪烁效果
     */
    private fun animateOverlayAlpha(fromAlpha: Float, toAlpha: Float, duration: Long) {
        if (::donutOverlay.isInitialized) {
            val alphaAnimator = ValueAnimator.ofFloat(fromAlpha, toAlpha, fromAlpha)
            alphaAnimator.duration = duration
            alphaAnimator.addUpdateListener { animator ->
                donutOverlay.overlayAlpha = animator.animatedValue as Float
            }
            alphaAnimator.start()
        }
    }

    /**
     * 使用动画实现闪烁效果
     */
    private fun flashWithAnimation(fromAlpha: Float, toAlpha: Float, duration: Long, onEnd: () -> Unit) {
        if (!::donutOverlay.isInitialized) return

        val animator = ValueAnimator.ofFloat(fromAlpha, toAlpha)
        animator.duration = duration
        animator.addUpdateListener { animation ->
            val alpha = animation.animatedValue as Float
            donutOverlay.overlayAlpha = alpha
        }
        animator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onEnd()
            }
        })
        animator.start()
    }

    /**
     * 停止人脸识别过程
     */
    private fun stopRecognition() {
        if (!isRecognizing) return
        isRecognizing = false

        // 停止相机
        try {
            if (::cameraProvider.isInitialized) {
                cameraProvider.unbindAll()
            }
        } catch (e: Exception) {
            Log.e("FaceRecognition", "停止相机失败", e)
        }

        // 取消所有延迟任务
        recognitionHandler.removeCallbacksAndMessages(null)

        // 取消检测超时
        if (detectionTimeoutRunnable != null) {
            detectionTimeoutHandler.removeCallbacks(detectionTimeoutRunnable!!)
            detectionTimeoutRunnable = null
        }

        // 取消颜色动画
        if (colorAnimationRunnable != null) {
            colorAnimationHandler.removeCallbacks(colorAnimationRunnable!!)
            colorAnimationRunnable = null
        }

        // 停止角落动画
        stopCornerAnimation()
    }

    /**
     * 设置识别阶段并更新UI
     * @param stage 识别阶段：1-检测中(红色), 2-识别中(黄色), 3-成功(绿色)
     * @param isColorFlashing 是否处于三色闪烁阶段
     */
    private fun setRecognitionStage(stage: Int, isColorFlashing: Boolean = false) {
        recognitionStage = stage

        // 创建甜甜圈覆盖层，如果不存在
        if (!::donutOverlay.isInitialized) {
            // 获取根视图
            val rootView = window?.decorView as ViewGroup

            // 创建甜甜圈覆盖层
            donutOverlay = DonutView(context).apply {
                layoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )
                // 设置为半透明
                overlayAlpha = 0.7f
                // 初始时隐藏覆盖层，只在三色闪烁时显示
                visibility = View.INVISIBLE
            }

            // 将覆盖层添加到根视图
            rootView.addView(donutOverlay)

            // 设置层级关系
            donutOverlay.bringToFront()
            previewView.bringToFront()
            faceDetectionBox.bringToFront()
            cornerTopLeft.bringToFront()
            cornerTopRight.bringToFront()
            cornerBottomLeft.bringToFront()
            cornerBottomRight.bringToFront()
            tvStatus.bringToFront()
            tvTip.bringToFront()
            ivClose.bringToFront()
        }

        // 获取预览容器
        val previewContainer = findViewById<CircularPreviewContainer>(R.id.previewContainer)

        // 计算圆形"洞"的位置和大小
        val containerLocation = IntArray(2)
        previewContainer.getLocationInWindow(containerLocation)
        val holeX = containerLocation[0] + previewContainer.width / 2f
        val holeY = containerLocation[1] + previewContainer.height / 2f
        val holeRadius = Math.min(previewContainer.width, previewContainer.height) / 2f

        // 设置圆形"洞"
        donutOverlay.setHole(holeX, holeY, holeRadius)

        // 如果不是三色闪烁阶段，隐藏覆盖层
        if (!isColorFlashing && ::donutOverlay.isInitialized) {
            donutOverlay.visibility = View.INVISIBLE
        }

        when (stage) {
            1 -> { // 检测中 - 红色
                // 使用标准红色 - 纯正的红色
                val redColor = Color.rgb(255, 0, 0) // 纯正红色 #FF0000
                setCornerColor("#FF0000")
                tvStatus.text = "人脸检测"
                tvStatus.setTextColor(redColor)

                // 设置人脸检测框的颜色 - 加粗线条
                val drawable = faceDetectionBox.background as GradientDrawable
                drawable.setStroke(12, redColor) // 更粗的线条

                // 确保角落动画正在运行
                if (cornerAnimatorSet == null || !cornerAnimatorSet!!.isRunning) {
                    startCornerAnimation()
                }

                // 设置预览容器边框颜色
                previewContainer.setBorderColor(redColor)

                // 只在三色闪烁阶段设置甜甜圈覆盖层，避开预览窗口
                if (isColorFlashing && ::donutOverlay.isInitialized) {
                    donutOverlay.visibility = View.VISIBLE
                    donutOverlay.setOverlayColor(redColor)
                }

                // 使状态文本更加突出
                tvStatus.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 20f)
                tvStatus.setShadowLayer(5f, 0f, 0f, Color.WHITE)
            }
            2 -> { // 识别中 - 黄色
                // 使用标准黄色 - 纯正的黄色
                val yellowColor = Color.rgb(255, 255, 0) // 纯正黄色 #FFFF00
                setCornerColor("#FFFF00")
                tvStatus.text = "验证中"
                tvStatus.setTextColor(yellowColor)

                // 设置人脸检测框的颜色 - 加粗线条
                val drawable = faceDetectionBox.background as GradientDrawable
                drawable.setStroke(12, yellowColor) // 更粗的线条

                // 更新角落动画速度
                updateCornerAnimation(true)

                // 设置预览容器边框颜色
                previewContainer.setBorderColor(yellowColor)

                // 只在三色闪烁阶段设置甜甜圈覆盖层，避开预览窗口
                if (isColorFlashing && ::donutOverlay.isInitialized) {
                    donutOverlay.visibility = View.VISIBLE
                    donutOverlay.setOverlayColor(yellowColor)
                }

                // 使状态文本更加突出
                tvStatus.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 20f)
                tvStatus.setShadowLayer(5f, 0f, 0f, Color.BLACK) // 黄色背景用黑色阴影
            }
            3 -> { // 成功 - 绿色
                // 使用标准绿色 - 纯正的绿色
                val greenColor = Color.rgb(0, 255, 0) // 纯正绿色 #00FF00
                setCornerColor("#00FF00")
                tvStatus.text = "验证成功"
                tvStatus.setTextColor(greenColor)

                // 设置人脸检测框的颜色 - 加粗线条
                val drawable = faceDetectionBox.background as GradientDrawable
                drawable.setStroke(12, greenColor) // 更粗的线条

                // 设置预览容器边框颜色
                previewContainer.setBorderColor(greenColor)

                // 只在三色闪烁阶段设置甜甜圈覆盖层，避开预览窗口
                if (isColorFlashing && ::donutOverlay.isInitialized) {
                    donutOverlay.visibility = View.VISIBLE
                    donutOverlay.setOverlayColor(greenColor)
                }

                // 显示成功动画
                showSuccessAnimation()

                // 使状态文本更加突出
                tvStatus.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 20f)
                tvStatus.setShadowLayer(5f, 0f, 0f, Color.WHITE)
            }
        }
    }

    /**
     * 显示成功动画
     */
    private fun showSuccessAnimation() {
        try {
            // 创建一个缩放动画
            val scaleX = ValueAnimator.ofFloat(1f, 1.2f, 1f)
            val scaleY = ValueAnimator.ofFloat(1f, 1.2f, 1f)

            scaleX.addUpdateListener { animator ->
                faceDetectionBox.scaleX = animator.animatedValue as Float
            }

            scaleY.addUpdateListener { animator ->
                faceDetectionBox.scaleY = animator.animatedValue as Float
            }

            val animatorSet = AnimatorSet()
            animatorSet.playTogether(scaleX, scaleY)
            animatorSet.duration = 500
            animatorSet.start()

            // 震动反馈
            vibratePhone(300)
        } catch (e: Exception) {
            Log.e("FaceRecognition", "显示成功动画失败: ${e.message}")
        }
    }

    // 移除了扫描线相关方法

    /**
     * 开始四个角的动画
     * 模拟支付宝人脸识别时四个角的扩大缩小效果
     */
    private fun startCornerAnimation() {
        // 停止现有动画
        stopCornerAnimation()

        // 创建一个动画集合
        val animatorSet = AnimatorSet()
        val animators = ArrayList<Animator>()

        // 左上角水平线
        val topLeftHorizontal = cornerTopLeft
        val topLeftHorizontalAnim = createCornerAnimation(
            topLeftHorizontal,
            "translationX", 0f, -20f, 0f,
            "translationY", 0f, -20f, 0f
        )
        animators.add(topLeftHorizontalAnim)

        // 左上角垂直线
        val topLeftVertical = findViewById<View>(R.id.verticalTopLeft)
        val topLeftVerticalAnim = createCornerAnimation(
            topLeftVertical,
            "translationX", 0f, -20f, 0f,
            "translationY", 0f, -20f, 0f
        )
        animators.add(topLeftVerticalAnim)

        // 右上角水平线
        val topRightHorizontal = cornerTopRight
        val topRightHorizontalAnim = createCornerAnimation(
            topRightHorizontal,
            "translationX", 0f, 20f, 0f,
            "translationY", 0f, -20f, 0f
        )
        animators.add(topRightHorizontalAnim)

        // 右上角垂直线
        val topRightVertical = findViewById<View>(R.id.verticalTopRight)
        val topRightVerticalAnim = createCornerAnimation(
            topRightVertical,
            "translationX", 0f, 20f, 0f,
            "translationY", 0f, -20f, 0f
        )
        animators.add(topRightVerticalAnim)

        // 左下角水平线
        val bottomLeftHorizontal = cornerBottomLeft
        val bottomLeftHorizontalAnim = createCornerAnimation(
            bottomLeftHorizontal,
            "translationX", 0f, -20f, 0f,
            "translationY", 0f, 20f, 0f
        )
        animators.add(bottomLeftHorizontalAnim)

        // 左下角垂直线
        val bottomLeftVertical = findViewById<View>(R.id.verticalBottomLeft)
        val bottomLeftVerticalAnim = createCornerAnimation(
            bottomLeftVertical,
            "translationX", 0f, -20f, 0f,
            "translationY", 0f, 20f, 0f
        )
        animators.add(bottomLeftVerticalAnim)

        // 右下角水平线
        val bottomRightHorizontal = cornerBottomRight
        val bottomRightHorizontalAnim = createCornerAnimation(
            bottomRightHorizontal,
            "translationX", 0f, 20f, 0f,
            "translationY", 0f, 20f, 0f
        )
        animators.add(bottomRightHorizontalAnim)

        // 右下角垂直线
        val bottomRightVertical = findViewById<View>(R.id.verticalBottomRight)
        val bottomRightVerticalAnim = createCornerAnimation(
            bottomRightVertical,
            "translationX", 0f, 20f, 0f,
            "translationY", 0f, 20f, 0f
        )
        animators.add(bottomRightVerticalAnim)

        // 播放所有动画
        animatorSet.playTogether(animators)
        animatorSet.duration = 800 // 更快的动画速度

        // 保存动画集合并开始播放
        cornerAnimatorSet = animatorSet
        cornerAnimatorSet?.start()
    }

    /**
     * 创建角落动画
     */
    private fun createCornerAnimation(
        view: View,
        property1: String, from1: Float, to1: Float, back1: Float,
        property2: String, from2: Float, to2: Float, back2: Float
    ): Animator {
        val valueAnimator = ValueAnimator.ofFloat(0f, 1f, 0f)
        valueAnimator.repeatCount = ValueAnimator.INFINITE
        valueAnimator.repeatMode = ValueAnimator.REVERSE
        valueAnimator.addUpdateListener { animator ->
            val fraction = animator.animatedValue as Float

            // 计算当前位置
            val value1 = from1 + (to1 - from1) * fraction
            val value2 = from2 + (to2 - from2) * fraction

            // 应用到视图
            view.translationX = if (property1 == "translationX") value1 else view.translationX
            view.translationY = if (property1 == "translationY") value1 else view.translationY
            view.translationX = if (property2 == "translationX") value2 else view.translationX
            view.translationY = if (property2 == "translationY") value2 else view.translationY
        }

        return valueAnimator
    }

    /**
     * 更新四个角的动画
     * @param faster 是否加快动画速度
     */
    private fun updateCornerAnimation(faster: Boolean) {
        // 停止现有动画
        stopCornerAnimation()

        // 创建一个动画集合
        val animatorSet = AnimatorSet()
        val animators = ArrayList<Animator>()

        // 移动距离 - 更大的移动范围
        val moveDistance = if (faster) 30f else 20f

        // 左上角水平线
        val topLeftHorizontal = cornerTopLeft
        val topLeftHorizontalAnim = createCornerAnimation(
            topLeftHorizontal,
            "translationX", 0f, -moveDistance, 0f,
            "translationY", 0f, -moveDistance, 0f
        )
        animators.add(topLeftHorizontalAnim)

        // 左上角垂直线
        val topLeftVertical = findViewById<View>(R.id.verticalTopLeft)
        val topLeftVerticalAnim = createCornerAnimation(
            topLeftVertical,
            "translationX", 0f, -moveDistance, 0f,
            "translationY", 0f, -moveDistance, 0f
        )
        animators.add(topLeftVerticalAnim)

        // 右上角水平线
        val topRightHorizontal = cornerTopRight
        val topRightHorizontalAnim = createCornerAnimation(
            topRightHorizontal,
            "translationX", 0f, moveDistance, 0f,
            "translationY", 0f, -moveDistance, 0f
        )
        animators.add(topRightHorizontalAnim)

        // 右上角垂直线
        val topRightVertical = findViewById<View>(R.id.verticalTopRight)
        val topRightVerticalAnim = createCornerAnimation(
            topRightVertical,
            "translationX", 0f, moveDistance, 0f,
            "translationY", 0f, -moveDistance, 0f
        )
        animators.add(topRightVerticalAnim)

        // 左下角水平线
        val bottomLeftHorizontal = cornerBottomLeft
        val bottomLeftHorizontalAnim = createCornerAnimation(
            bottomLeftHorizontal,
            "translationX", 0f, -moveDistance, 0f,
            "translationY", 0f, moveDistance, 0f
        )
        animators.add(bottomLeftHorizontalAnim)

        // 左下角垂直线
        val bottomLeftVertical = findViewById<View>(R.id.verticalBottomLeft)
        val bottomLeftVerticalAnim = createCornerAnimation(
            bottomLeftVertical,
            "translationX", 0f, -moveDistance, 0f,
            "translationY", 0f, moveDistance, 0f
        )
        animators.add(bottomLeftVerticalAnim)

        // 右下角水平线
        val bottomRightHorizontal = cornerBottomRight
        val bottomRightHorizontalAnim = createCornerAnimation(
            bottomRightHorizontal,
            "translationX", 0f, moveDistance, 0f,
            "translationY", 0f, moveDistance, 0f
        )
        animators.add(bottomRightHorizontalAnim)

        // 右下角垂直线
        val bottomRightVertical = findViewById<View>(R.id.verticalBottomRight)
        val bottomRightVerticalAnim = createCornerAnimation(
            bottomRightVertical,
            "translationX", 0f, moveDistance, 0f,
            "translationY", 0f, moveDistance, 0f
        )
        animators.add(bottomRightVerticalAnim)

        // 播放所有动画
        animatorSet.playTogether(animators)
        animatorSet.duration = if (faster) 500 else 800 // 更快的动画速度

        // 保存动画集合并开始播放
        cornerAnimatorSet = animatorSet
        cornerAnimatorSet?.start()
    }

    /**
     * 停止四个角的动画
     */
    private fun stopCornerAnimation() {
        cornerAnimatorSet?.cancel()
        cornerAnimatorSet = null
    }

    /**
     * 设置四个角的颜色
     */
    private fun setCornerColor(colorString: String) {
        val color = Color.parseColor(colorString)
        cornerTopLeft.setBackgroundColor(color)
        cornerTopRight.setBackgroundColor(color)
        cornerBottomLeft.setBackgroundColor(color)
        cornerBottomRight.setBackgroundColor(color)

        // 同时设置垂直线的颜色
        val verticalLines = listOf(
            findViewById<View>(R.id.verticalTopLeft),
            findViewById<View>(R.id.verticalTopRight),
            findViewById<View>(R.id.verticalBottomLeft),
            findViewById<View>(R.id.verticalBottomRight)
        )

        verticalLines.forEach { it.setBackgroundColor(color) }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopRecognition()

        // 关闭相机执行器
        if (::cameraExecutor.isInitialized) {
            cameraExecutor.shutdown()
        }
    }
}
