{"logs": [{"outputFile": "com.pangu.keepaliveperfect.demo.app-mergeDebugResources-47:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79230ecb7deb426f90957f200c266d44\\transformed\\jetified-play-services-base-18.0.1\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3742,3850,4004,4128,4241,4383,4507,4623,4860,5011,5126,5282,5413,5557,5718,5791,5852", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "3845,3999,4123,4236,4378,4502,4618,4716,5006,5121,5277,5408,5552,5713,5786,5847,5927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae5003da7b262492451d61e3e75a684\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4721", "endColumns": "138", "endOffsets": "4855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fdd3b69b66e6b65f494e6d96e162c073\\transformed\\material-1.9.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1040,1129,1196,1256,1350,1414,1477,1533,1603,1670,1725,1844,1901,1965,2019,2092,2214,2297,2382,2514,2592,2672,2758,2818,2870,2936,3006,3079,3161,3233,3310,3382,3452,3545,3618,3708,3801,3875,3947,4038,4092,4158,4242,4327,4389,4453,4516,4621,4721,4816,4916,4980,5036", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,85,59,51,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,63,55,79", "endOffsets": "264,340,417,495,586,671,773,888,971,1035,1124,1191,1251,1345,1409,1472,1528,1598,1665,1720,1839,1896,1960,2014,2087,2209,2292,2377,2509,2587,2667,2753,2813,2865,2931,3001,3074,3156,3228,3305,3377,3447,3540,3613,3703,3796,3870,3942,4033,4087,4153,4237,4322,4384,4448,4511,4616,4716,4811,4911,4975,5031,5111"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3035,3111,3188,3266,3357,3442,3544,3659,6008,6155,6244,6311,6371,6465,6529,6592,6648,6718,6785,6840,6959,7016,7080,7134,7207,7329,7412,7497,7629,7707,7787,7873,7933,7985,8051,8121,8194,8276,8348,8425,8497,8567,8660,8733,8823,8916,8990,9062,9153,9207,9273,9357,9442,9504,9568,9631,9736,9836,9931,10031,10095,10226", "endLines": "5,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "endColumns": "12,75,76,77,90,84,101,114,82,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,84,131,77,79,85,59,51,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,63,55,79", "endOffsets": "314,3106,3183,3261,3352,3437,3539,3654,3737,6067,6239,6306,6366,6460,6524,6587,6643,6713,6780,6835,6954,7011,7075,7129,7202,7324,7407,7492,7624,7702,7782,7868,7928,7980,8046,8116,8189,8271,8343,8420,8492,8562,8655,8728,8818,8911,8985,9057,9148,9202,9268,9352,9437,9499,9563,9626,9731,9831,9926,10026,10090,10146,10301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e0e92451cb7aee5ff8934f376f578f88\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,10449", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,10526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\376ccaceccb8c8d200dd37c294afb78a\\transformed\\preference-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,264,339,482,651,743", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "176,259,334,477,646,738,825"}, "to": {"startLines": "59,61,114,116,119,120,121", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5932,6072,10151,10306,10632,10801,10893", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "6003,6150,10221,10444,10796,10888,10975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38bdbb3aec41791523ad0d9573b07666\\transformed\\core-1.9.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "10531", "endColumns": "100", "endOffsets": "10627"}}]}]}