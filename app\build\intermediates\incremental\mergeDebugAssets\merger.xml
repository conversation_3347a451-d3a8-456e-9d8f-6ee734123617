<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:face-detection:16.1.5" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets"><file name="models_bundled/BCLjoy_200.emd" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\BCLjoy_200.emd"/><file name="models_bundled/BCLlefteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\BCLlefteyeclosed_200.emd"/><file name="models_bundled/BCLrighteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\BCLrighteyeclosed_200.emd"/><file name="models_bundled/blazeface.tfl" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\blazeface.tfl"/><file name="models_bundled/contours.tfl" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\contours.tfl"/><file name="models_bundled/fssd_25_8bit_gray_v2.tflite" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\fssd_25_8bit_gray_v2.tflite"/><file name="models_bundled/fssd_25_8bit_v2.tflite" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\fssd_25_8bit_v2.tflite"/><file name="models_bundled/fssd_anchors_v2.pb" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\fssd_anchors_v2.pb"/><file name="models_bundled/fssd_anchors_v5.pb" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\fssd_anchors_v5.pb"/><file name="models_bundled/fssd_medium_8bit_gray_v5.tflite" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\fssd_medium_8bit_gray_v5.tflite"/><file name="models_bundled/fssd_medium_8bit_v5.tflite" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\fssd_medium_8bit_v5.tflite"/><file name="models_bundled/LMprec_600.emd" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\LMprec_600.emd"/><file name="models_bundled/MFT_fssd_accgray.pb" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\MFT_fssd_accgray.pb"/><file name="models_bundled/MFT_fssd_fastgray.pb" path="C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\assets\models_bundled\MFT_fssd_fastgray.pb"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\assets"><file name="docs/drawable_readme.md" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\assets\docs\drawable_readme.md"/><file name="implementation_guide.md" path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\assets\implementation_guide.md"/></source><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\build\intermediates\shader_assets\debug\out"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\5.22MM 9999\app\src\debug\assets"/></dataSet></merger>