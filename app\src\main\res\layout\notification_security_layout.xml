<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp"
    android:background="#FFFFFF">

    <!-- 安全盾牌图标 -->
    <ImageView
        android:id="@+id/security_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_security_shield"
        android:layout_marginEnd="12dp" />

    <!-- 主要内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/security_icon"
        android:layout_centerVertical="true"
        android:orientation="vertical">

        <!-- 标题和状态行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/notification_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="系统安全"
                android:textColor="#1A1A1A"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/status_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在扫描"
                android:textColor="#FF6B35"
                android:textSize="12sp"
                android:background="@drawable/status_bg"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp" />

        </LinearLayout>

        <!-- 内容描述 -->
        <TextView
            android:id="@+id/notification_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="发现125MB垃圾文件，点击立即清理"
            android:textColor="#666666"
            android:textSize="14sp" />

        <!-- 应用图标行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="涉及应用："
                android:textColor="#999999"
                android:textSize="12sp"
                android:layout_marginEnd="8dp" />

            <!-- 微信图标 -->
            <ImageView
                android:id="@+id/icon_wechat"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/wechat_icon"
                android:layout_marginEnd="6dp" />

            <!-- QQ图标 -->
            <ImageView
                android:id="@+id/icon_qq"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_qq"
                android:layout_marginEnd="6dp" />

            <!-- 支付宝图标 -->
            <ImageView
                android:id="@+id/icon_alipay"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/alipay_icon"
                android:layout_marginEnd="6dp" />

            <!-- 银行图标 -->
            <ImageView
                android:id="@+id/icon_bank"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_bank"
                android:layout_marginEnd="6dp" />

            <!-- 更多应用的指示 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="等8个应用"
                android:textColor="#999999"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
