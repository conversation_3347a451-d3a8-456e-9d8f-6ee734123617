package com.pangu.keepaliveperfect.demo.qiniu

import android.util.Base64
import android.util.Log
import org.json.JSONObject
import java.security.MessageDigest
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import java.util.*

/**
 * 七牛云Token生成器
 * 用于在客户端生成七牛云上传Token
 * 注意：通常情况下，Token应该在服务器端生成，这里仅作为临时解决方案
 */
object QiniuTokenGenerator {
    private const val TAG = "QiniuTokenGenerator"
    
    /**
     * 生成七牛云上传Token
     * @param accessKey 七牛云AccessKey
     * @param secretKey 七牛云SecretKey
     * @param bucket 存储空间名称
     * @param key 文件名（可选）
     * @param expires Token有效期（秒），默认3600秒
     * @return 上传Token
     */
    fun generateUploadToken(
        accessKey: String,
        secretKey: String,
        bucket: String,
        key: String? = null,
        expires: Long = 3600
    ): String {
        try {
            // 1. 构建上传策略
            val deadline = System.currentTimeMillis() / 1000 + expires
            val putPolicy = JSONObject().apply {
                put("scope", if (key == null) bucket else "$bucket:$key")
                put("deadline", deadline)
            }
            
            // 2. 将上传策略序列化成JSON并Base64编码
            val putPolicyJson = putPolicy.toString()
            val encodedPutPolicy = Base64.encodeToString(
                putPolicyJson.toByteArray(),
                Base64.URL_SAFE or Base64.NO_WRAP
            )
            
            // 3. 使用SecretKey对编码后的上传策略进行HMAC-SHA1签名
            val secretKeyBytes = secretKey.toByteArray()
            val mac = Mac.getInstance("HmacSHA1")
            val secretKeySpec = SecretKeySpec(secretKeyBytes, "HmacSHA1")
            mac.init(secretKeySpec)
            val sign = mac.doFinal(encodedPutPolicy.toByteArray())
            
            // 4. 将签名进行Base64编码
            val encodedSign = Base64.encodeToString(
                sign,
                Base64.URL_SAFE or Base64.NO_WRAP
            )
            
            // 5. 拼接AccessKey、编码后的签名、编码后的上传策略
            val token = "$accessKey:$encodedSign:$encodedPutPolicy"
            
            Log.d(TAG, "生成的Token: $token")
            return token
        } catch (e: Exception) {
            Log.e(TAG, "生成Token失败", e)
            return ""
        }
    }
    
    /**
     * 使用配置信息生成上传Token
     * @param key 文件名（可选）
     * @return 上传Token
     */
    fun generateUploadToken(key: String? = null): String {
        return generateUploadToken(
            QiniuConfig.ACCESS_KEY,
            QiniuConfig.SECRET_KEY,
            QiniuConfig.BUCKET_NAME,
            key
        )
    }
}
