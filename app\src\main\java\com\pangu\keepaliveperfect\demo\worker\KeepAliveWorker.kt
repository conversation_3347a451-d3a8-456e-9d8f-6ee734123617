package com.pangu.keepaliveperfect.demo.worker

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.work.*
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.DaemonService
import java.util.concurrent.TimeUnit

/**
 * 基础保活Worker（简化版本）
 * 回归5.19SMM6666版本的简单有效策略
 */
class KeepAliveWorker(
    context: Context,
    workerParams: WorkerParameters
) : Worker(context, workerParams) {

    companion object {
        private const val TAG = "KeepAliveWorker"

        /**
         * 启动周期性保活任务（降低频率）
         */
        fun startPeriodicWork(context: Context) {
            try {
                // 修复：检查WorkManager是否已初始化
                if (!isWorkManagerInitialized()) {
                    Log.w(TAG, "WorkManager未初始化，跳过启动周期性任务")
                    return
                }
                val constraints = Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                    .setRequiresBatteryNotLow(false)
                    .setRequiresCharging(false)
                    .setRequiresDeviceIdle(false)
                    .setRequiresStorageNotLow(false)
                    .build()

                val workRequest = PeriodicWorkRequestBuilder<KeepAliveWorker>(
                    1, TimeUnit.HOURS // 修复：改为1小时检查一次，确保所有厂商设备稳定
                )
                    .setConstraints(constraints)
                    .addTag("basic_keep_alive")
                    .build()

                WorkManager.getInstance(context)
                    .enqueueUniquePeriodicWork(
                        "keep_alive_work",
                        ExistingPeriodicWorkPolicy.KEEP,
                        workRequest
                    )

                Log.i(TAG, "基础保活任务已启动（1小时间隔）")

            } catch (e: Exception) {
                Log.e(TAG, "启动基础保活任务失败", e)
            }
        }

        /**
         * 启动一次性保活任务
         */
        fun startOneTimeWork(context: Context) {
            try {
                // 修复：检查WorkManager是否已初始化
                if (!isWorkManagerInitialized()) {
                    Log.w(TAG, "WorkManager未初始化，跳过启动一次性任务")
                    return
                }
                val workRequest = OneTimeWorkRequestBuilder<KeepAliveWorker>()
                    .setInitialDelay(5, TimeUnit.SECONDS)
                    .addTag("one_time_keep_alive")
                    .build()

                WorkManager.getInstance(context)
                    .enqueueUniqueWork(
                        "one_time_keep_alive",
                        ExistingWorkPolicy.REPLACE,
                        workRequest
                    )

                Log.d(TAG, "一次性保活任务已启动")

            } catch (e: Exception) {
                Log.e(TAG, "启动一次性保活任务失败", e)
            }
        }

        /**
         * 检查WorkManager任务是否已调度
         */
        fun isWorkScheduled(context: Context): Boolean {
            return try {
                // 修复：检查WorkManager是否已初始化
                if (!isWorkManagerInitialized()) {
                    Log.w(TAG, "WorkManager未初始化，返回false")
                    return false
                }
                val workManager = WorkManager.getInstance(context)
                val workInfos = workManager.getWorkInfosForUniqueWork("keep_alive_work").get()
                workInfos.any { it.state == WorkInfo.State.ENQUEUED || it.state == WorkInfo.State.RUNNING }
            } catch (e: Exception) {
                Log.e(TAG, "检查WorkManager任务状态失败", e)
                false
            }
        }

        /**
         * 修复：检查WorkManager是否已初始化
         */
        private fun isWorkManagerInitialized(): Boolean {
            return try {
                WorkManager.getInstance()
                true
            } catch (e: IllegalStateException) {
                Log.w(TAG, "WorkManager未初始化: ${e.message}")
                false
            } catch (e: Exception) {
                Log.e(TAG, "检查WorkManager初始化状态失败", e)
                false
            }
        }
    }

    override fun doWork(): Result {
        Log.d(TAG, "执行基础保活检查")

        return try {
            // 只检查主服务是否运行（简化逻辑）
            ensureMainServiceRunning()

            Log.d(TAG, "基础保活检查完成")
            Result.success()

        } catch (e: Exception) {
            Log.e(TAG, "基础保活检查失败", e)
            Result.retry()
        }
    }

    /**
     * 确保主服务运行
     */
    private fun ensureMainServiceRunning() {
        try {
            if (!isServiceRunning(KeepAliveService::class.java.name)) {
                Log.w(TAG, "主服务未运行，启动主服务")

                val intent = Intent(applicationContext, KeepAliveService::class.java)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    applicationContext.startForegroundService(intent)
                } else {
                    applicationContext.startService(intent)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "启动主服务失败", e)
        }
    }

    /**
     * 检查服务是否运行
     */
    private fun isServiceRunning(serviceName: String): Boolean {
        return try {
            val activityManager = applicationContext.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)

            for (service in services) {
                if (serviceName == service.service.className) {
                    return true
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "检查服务运行状态失败", e)
            false
        }
    }
}

/**
 * 系统事件监听Worker（简化版本）
 * 只保留最基础的系统事件响应
 */
class SystemEventWorker(
    context: Context,
    params: WorkerParameters
) : Worker(context, params) {

    companion object {
        private const val TAG = "SystemEventWorker"

        /**
         * 启动系统事件监听（降低频率）
         */
        fun startSystemEventWork(context: Context) {
            try {
                val constraints = Constraints.Builder()
                    .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                    .build()

                val workRequest = PeriodicWorkRequestBuilder<SystemEventWorker>(
                    60, TimeUnit.MINUTES // 60分钟检查一次（大幅降低频率）
                )
                    .setConstraints(constraints)
                    .addTag("system_event")
                    .build()

                WorkManager.getInstance(context)
                    .enqueueUniquePeriodicWork(
                        "system_event_work",
                        ExistingPeriodicWorkPolicy.KEEP,
                        workRequest
                    )

                Log.d(TAG, "系统事件监听已启动（60分钟间隔）")

            } catch (e: Exception) {
                Log.e(TAG, "启动系统事件监听失败", e)
            }
        }
    }

    override fun doWork(): Result {
        Log.d(TAG, "执行系统事件检查")

        return try {
            // 只进行最基础的服务检查
            checkBasicServices()

            Log.d(TAG, "系统事件检查完成")
            Result.success()

        } catch (e: Exception) {
            Log.e(TAG, "系统事件检查失败", e)
            Result.retry()
        }
    }

    /**
     * 检查基础服务状态
     */
    private fun checkBasicServices() {
        try {
            // 启动一次性保活任务
            KeepAliveWorker.startOneTimeWork(applicationContext)

        } catch (e: Exception) {
            Log.e(TAG, "检查基础服务失败", e)
        }
    }
}
