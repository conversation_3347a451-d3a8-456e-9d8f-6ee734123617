<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="notification_security_layout" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\notification_security_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/notification_security_layout_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="125" endOffset="16"/></Target><Target id="@+id/security_icon" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="15" endOffset="41"/></Target><Target id="@+id/notification_title" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="40" endOffset="42"/></Target><Target id="@+id/status_text" view="TextView"><Expressions/><location startLine="42" startOffset="12" endLine="51" endOffset="47"/></Target><Target id="@+id/notification_content" view="TextView"><Expressions/><location startLine="56" startOffset="8" endLine="63" endOffset="37"/></Target><Target id="@+id/icon_wechat" view="ImageView"><Expressions/><location startLine="82" startOffset="12" endLine="87" endOffset="48"/></Target><Target id="@+id/icon_qq" view="ImageView"><Expressions/><location startLine="90" startOffset="12" endLine="95" endOffset="48"/></Target><Target id="@+id/icon_alipay" view="ImageView"><Expressions/><location startLine="98" startOffset="12" endLine="103" endOffset="48"/></Target><Target id="@+id/icon_bank" view="ImageView"><Expressions/><location startLine="106" startOffset="12" endLine="111" endOffset="48"/></Target></Targets></Layout>