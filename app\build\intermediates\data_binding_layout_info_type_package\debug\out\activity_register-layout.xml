<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_register" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_register.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_register_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="505" endOffset="51"/></Target><Target id="@+id/ivBack" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="40"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="32" endOffset="51"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="34" startOffset="4" endLine="44" endOffset="60"/></Target><Target id="@+id/scrollView" view="ScrollView"><Expressions/><location startLine="46" startOffset="4" endLine="484" endOffset="16"/></Target><Target id="@+id/llStepIndicator" view="LinearLayout"><Expressions/><location startLine="65" startOffset="12" endLine="136" endOffset="26"/></Target><Target id="@+id/tvStep1" view="TextView"><Expressions/><location startLine="78" startOffset="16" endLine="87" endOffset="46"/></Target><Target id="@+id/tvStep2" view="TextView"><Expressions/><location startLine="94" startOffset="16" endLine="103" endOffset="46"/></Target><Target id="@+id/tvStep3" view="TextView"><Expressions/><location startLine="110" startOffset="16" endLine="119" endOffset="46"/></Target><Target id="@+id/tvStep4" view="TextView"><Expressions/><location startLine="126" startOffset="16" endLine="135" endOffset="46"/></Target><Target id="@+id/tvStepTitle" view="TextView"><Expressions/><location startLine="139" startOffset="12" endLine="150" endOffset="76"/></Target><Target id="@+id/clStep1" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="153" startOffset="12" endLine="247" endOffset="63"/></Target><Target id="@+id/tilPhoneNumber" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="165" startOffset="16" endLine="185" endOffset="71"/></Target><Target id="@+id/etPhoneNumber" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="179" startOffset="20" endLine="184" endOffset="48"/></Target><Target id="@+id/tilPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="188" startOffset="16" endLine="209" endOffset="71"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="204" startOffset="20" endLine="208" endOffset="58"/></Target><Target id="@+id/tilConfirmPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="212" startOffset="16" endLine="233" endOffset="71"/></Target><Target id="@+id/etConfirmPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="228" startOffset="20" endLine="232" endOffset="58"/></Target><Target id="@+id/tvPasswordStrength" view="TextView"><Expressions/><location startLine="236" startOffset="16" endLine="246" endOffset="83"/></Target><Target id="@+id/clStep2" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="250" startOffset="12" endLine="325" endOffset="63"/></Target><Target id="@+id/tilPaymentPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="263" startOffset="16" endLine="284" endOffset="71"/></Target><Target id="@+id/etPaymentPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="278" startOffset="20" endLine="283" endOffset="47"/></Target><Target id="@+id/tilConfirmPaymentPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="287" startOffset="16" endLine="309" endOffset="71"/></Target><Target id="@+id/etConfirmPaymentPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="303" startOffset="20" endLine="308" endOffset="47"/></Target><Target id="@+id/tvPaymentPasswordHint" view="TextView"><Expressions/><location startLine="312" startOffset="16" endLine="324" endOffset="90"/></Target><Target id="@+id/clStep3" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="328" startOffset="12" endLine="403" endOffset="63"/></Target><Target id="@+id/tilTransactionPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="341" startOffset="16" endLine="362" endOffset="71"/></Target><Target id="@+id/etTransactionPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="356" startOffset="20" endLine="361" endOffset="47"/></Target><Target id="@+id/tilConfirmTransactionPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="365" startOffset="16" endLine="387" endOffset="71"/></Target><Target id="@+id/etConfirmTransactionPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="381" startOffset="20" endLine="386" endOffset="47"/></Target><Target id="@+id/tvTransactionPasswordHint" view="TextView"><Expressions/><location startLine="390" startOffset="16" endLine="402" endOffset="94"/></Target><Target id="@+id/clStep4" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="406" startOffset="12" endLine="479" endOffset="63"/></Target><Target id="@+id/tilRealName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="419" startOffset="16" endLine="438" endOffset="71"/></Target><Target id="@+id/etRealName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="433" startOffset="20" endLine="437" endOffset="60"/></Target><Target id="@+id/tilIdNumber" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="441" startOffset="16" endLine="463" endOffset="71"/></Target><Target id="@+id/etIdNumber" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="456" startOffset="20" endLine="462" endOffset="48"/></Target><Target id="@+id/tvFaceRecognitionHint" view="TextView"><Expressions/><location startLine="466" startOffset="16" endLine="478" endOffset="76"/></Target><Target id="@+id/btnNext" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="487" startOffset="4" endLine="501" endOffset="55"/></Target></Targets></Layout>