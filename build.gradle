// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.0'
    }
}

plugins {
    id 'com.android.application' version '7.4.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.0' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

// 由于项目使用了repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
// 不要在此处添加仓库，仓库已在settings.gradle中配置 