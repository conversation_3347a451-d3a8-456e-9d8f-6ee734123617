{"logs": [{"outputFile": "com.pangu.keepaliveperfect.demo.app-mergeDebugResources-47:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fdd3b69b66e6b65f494e6d96e162c073\\transformed\\material-1.9.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,344,431,514,607,691,791,907,989,1052,1143,1208,1267,1355,1417,1479,1539,1606,1669,1723,1837,1894,1955,2009,2079,2198,2279,2364,2499,2576,2653,2739,2795,2847,2913,2983,3061,3148,3218,3294,3365,3434,3530,3604,3702,3798,3872,3942,4044,4099,4166,4253,4346,4409,4473,4536,4636,4739,4833,4937,4997,5053", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,85,55,51,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,59,55,77", "endOffsets": "254,339,426,509,602,686,786,902,984,1047,1138,1203,1262,1350,1412,1474,1534,1601,1664,1718,1832,1889,1950,2004,2074,2193,2274,2359,2494,2571,2648,2734,2790,2842,2908,2978,3056,3143,3213,3289,3360,3429,3525,3599,3697,3793,3867,3937,4039,4094,4161,4248,4341,4404,4468,4531,4631,4734,4828,4932,4992,5048,5126"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2999,3084,3171,3254,3347,3431,3531,3647,5960,6107,6198,6263,6322,6410,6472,6534,6594,6661,6724,6778,6892,6949,7010,7064,7134,7253,7334,7419,7554,7631,7708,7794,7850,7902,7968,8038,8116,8203,8273,8349,8420,8489,8585,8659,8757,8853,8927,8997,9099,9154,9221,9308,9401,9464,9528,9591,9691,9794,9888,9992,10052,10190", "endLines": "5,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "endColumns": "12,84,86,82,92,83,99,115,81,62,90,64,58,87,61,61,59,66,62,53,113,56,60,53,69,118,80,84,134,76,76,85,55,51,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,59,55,77", "endOffsets": "304,3079,3166,3249,3342,3426,3526,3642,3724,6018,6193,6258,6317,6405,6467,6529,6589,6656,6719,6773,6887,6944,7005,7059,7129,7248,7329,7414,7549,7626,7703,7789,7845,7897,7963,8033,8111,8198,8268,8344,8415,8484,8580,8654,8752,8848,8922,8992,9094,9149,9216,9303,9396,9459,9523,9586,9686,9789,9883,9987,10047,10103,10263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\376ccaceccb8c8d200dd37c294afb78a\\transformed\\preference-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,261,343,480,649,729", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "172,256,338,475,644,724,802"}, "to": {"startLines": "59,61,114,116,119,120,121", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5888,6023,10108,10268,10586,10755,10835", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "5955,6102,10185,10400,10750,10830,10908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e0e92451cb7aee5ff8934f376f578f88\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,420,526,633,723,824,936,1014,1091,1182,1275,1368,1465,1565,1658,1753,1847,1938,2029,2109,2216,2317,2414,2523,2625,2739,2896,10405", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "415,521,628,718,819,931,1009,1086,1177,1270,1363,1460,1560,1653,1748,1842,1933,2024,2104,2211,2312,2409,2518,2620,2734,2891,2994,10480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae5003da7b262492451d61e3e75a684\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4710", "endColumns": "142", "endOffsets": "4848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38bdbb3aec41791523ad0d9573b07666\\transformed\\core-1.9.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "10485", "endColumns": "100", "endOffsets": "10581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79230ecb7deb426f90957f200c266d44\\transformed\\jetified-play-services-base-18.0.1\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3729,3836,4004,4127,4239,4384,4505,4613,4853,5003,5111,5265,5389,5528,5681,5741,5807", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "3831,3999,4122,4234,4379,4500,4608,4705,4998,5106,5260,5384,5523,5676,5736,5802,5883"}}]}]}