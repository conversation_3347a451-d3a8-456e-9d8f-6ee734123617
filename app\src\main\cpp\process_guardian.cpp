#include <jni.h>
#include <android/log.h>
#include <unistd.h>
#include <sys/wait.h>
#include <signal.h>
#include <cstdlib>

#define TAG "ProcessGuardian"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

static pid_t guardian_pid = 0;
static bool guardian_active = false;

// Process guardian implementation moved to immortal_core.cpp
// This file now contains only helper functions
