<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_profile" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_profile_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="424" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="7" startOffset="4" endLine="25" endOffset="39"/></Target><Target id="@+id/ivAvatar" view="ImageView"><Expressions/><location startLine="61" startOffset="24" endLine="68" endOffset="53"/></Target><Target id="@+id/tvUserName" view="TextView"><Expressions/><location startLine="77" startOffset="28" endLine="84" endOffset="58"/></Target><Target id="@+id/tvUserPhone" view="TextView"><Expressions/><location startLine="86" startOffset="28" endLine="93" endOffset="57"/></Target><Target id="@+id/tvCardNumber" view="TextView"><Expressions/><location startLine="118" startOffset="28" endLine="125" endOffset="58"/></Target><Target id="@+id/tvBalance" view="TextView"><Expressions/><location startLine="149" startOffset="28" endLine="156" endOffset="58"/></Target><Target id="@+id/llAccountInfo" view="LinearLayout"><Expressions/><location startLine="196" startOffset="20" endLine="228" endOffset="34"/></Target><Target id="@+id/llLoginPassword" view="LinearLayout"><Expressions/><location startLine="236" startOffset="20" endLine="268" endOffset="34"/></Target><Target id="@+id/llPaymentPassword" view="LinearLayout"><Expressions/><location startLine="276" startOffset="20" endLine="308" endOffset="34"/></Target><Target id="@+id/llTransactionPassword" view="LinearLayout"><Expressions/><location startLine="316" startOffset="20" endLine="348" endOffset="34"/></Target><Target id="@+id/llLogout" view="LinearLayout"><Expressions/><location startLine="376" startOffset="20" endLine="402" endOffset="34"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="412" startOffset="4" endLine="422" endOffset="49"/></Target></Targets></Layout>