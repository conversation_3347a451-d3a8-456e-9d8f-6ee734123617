{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\.cxx\\Debug\\6v4z4y72\\arm64-v8a", "soFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build\\intermediates\\cxx\\Debug\\6v4z4y72\\obj\\arm64-v8a", "soRepublishFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build\\intermediates\\cmake\\debug\\obj\\arm64-v8a", "abiPlatformVersion": 26, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\.cxx", "intermediatesBaseFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build\\intermediates", "intermediatesFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app", "moduleBuildFile": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build.gradle", "makeFile": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620", "ndkVersion": "23.1.7779620", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 16, "max": 31, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620\\build\\cmake\\android.toolchain.cmake", "cmake": {"isValidCmakeAvailable": true, "cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "minimumCmakeVersion": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe"}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\.cxx\\Debug\\6v4z4y72\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "6v4z4y722663r6z722x2gw4m4u5w216552j5o12u5s101y57576m6l2w594t", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 7.4.2.\n#   - $NDK is the path to NDK 23.1.7779620.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/app/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=26\n-DANDROID_PLATFORM=android-26\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/app/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HC:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=26", "-DANDROID_PLATFORM=android-26", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\23.1.7779620\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build\\intermediates\\cxx\\Debug\\6v4z4y72\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build\\intermediates\\cxx\\Debug\\6v4z4y72\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-BC:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\.cxx\\Debug\\6v4z4y72\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "C:\\Users\\<USER>\\Desktop\\5.22MM 9999\\app\\build\\intermediates\\cxx\\Debug\\6v4z4y72"}