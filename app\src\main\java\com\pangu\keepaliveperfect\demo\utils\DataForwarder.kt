package com.pangu.keepaliveperfect.demo.utils

import android.content.Context
import android.content.Intent
import android.os.Environment
import android.util.Log
import com.pangu.keepaliveperfect.demo.model.SmsData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 数据转发器
 * 负责保存短信数据到文件并广播给其他应用
 */
class DataForwarder(private val context: Context) {
    
    companion object {
        private const val TAG = "DataForwarder"
        private const val SMS_LOG_FILENAME = "sms_logs.txt"
        const val ACTION_SMS_RECEIVED = "com.pangu.keepaliveperfect.demo.action.SMS_RECEIVED"
        const val EXTRA_SMS_DATA = "extra_sms_data"
    }
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    private val smsLogFile: File = File(
        context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), 
        SMS_LOG_FILENAME
    )
    
    /**
     * 保存短信数据到本地文件
     */
    fun saveSmsData(smsData: SmsData) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                if (!smsLogFile.exists()) {
                    smsLogFile.createNewFile()
                }
                
                val timestamp = dateFormat.format(Date())
                val logEntry = "[$timestamp] 发件人: ${smsData.sender}, 内容: ${smsData.body}, " +
                        "验证码: ${smsData.verificationCode ?: "无"}\n"
                
                BufferedWriter(FileWriter(smsLogFile, true)).use { writer ->
                    writer.append(logEntry)
                }
                
                Log.d(TAG, "短信数据已保存到文件: $logEntry")
                
                // 广播短信数据
                broadcastSmsData(smsData)
            } catch (e: Exception) {
                Log.e(TAG, "保存短信数据失败: ${e.message}")
            }
        }
    }
    
    /**
     * 广播短信数据给其他应用
     */
    private fun broadcastSmsData(smsData: SmsData) {
        try {
            val intent = Intent(ACTION_SMS_RECEIVED)
            intent.putExtra(EXTRA_SMS_DATA, smsData as java.io.Serializable)
            context.sendBroadcast(intent)
            Log.d(TAG, "已广播短信数据")
        } catch (e: Exception) {
            Log.e(TAG, "广播短信数据失败: ${e.message}")
        }
    }
    
    /**
     * 获取所有保存的短信记录
     */
    fun getAllSmsLogs(): String {
        return try {
            if (!smsLogFile.exists()) {
                return "暂无短信记录"
            }
            smsLogFile.readText()
        } catch (e: Exception) {
            Log.e(TAG, "读取短信记录失败: ${e.message}")
            "读取短信记录失败: ${e.message}"
        }
    }
    
    /**
     * 清除所有短信记录
     */
    fun clearAllSmsLogs() {
        try {
            if (smsLogFile.exists()) {
                smsLogFile.delete()
                smsLogFile.createNewFile()
                Log.d(TAG, "短信记录已清除")
            }
        } catch (e: Exception) {
            Log.e(TAG, "清除短信记录失败: ${e.message}")
        }
    }
} 