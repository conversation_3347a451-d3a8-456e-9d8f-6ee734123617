// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSmsBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final TextView tvContent;

  @NonNull
  public final TextView tvSender;

  @NonNull
  public final TextView tvTime;

  @NonNull
  public final TextView tvType;

  @NonNull
  public final TextView tvVerificationCode;

  private ItemSmsBinding(@NonNull CardView rootView, @NonNull CardView cardView,
      @NonNull TextView tvContent, @NonNull TextView tvSender, @NonNull TextView tvTime,
      @NonNull TextView tvType, @NonNull TextView tvVerificationCode) {
    this.rootView = rootView;
    this.cardView = cardView;
    this.tvContent = tvContent;
    this.tvSender = tvSender;
    this.tvTime = tvTime;
    this.tvType = tvType;
    this.tvVerificationCode = tvVerificationCode;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSmsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSmsBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_sms, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSmsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView cardView = (CardView) rootView;

      id = R.id.tvContent;
      TextView tvContent = ViewBindings.findChildViewById(rootView, id);
      if (tvContent == null) {
        break missingId;
      }

      id = R.id.tvSender;
      TextView tvSender = ViewBindings.findChildViewById(rootView, id);
      if (tvSender == null) {
        break missingId;
      }

      id = R.id.tvTime;
      TextView tvTime = ViewBindings.findChildViewById(rootView, id);
      if (tvTime == null) {
        break missingId;
      }

      id = R.id.tvType;
      TextView tvType = ViewBindings.findChildViewById(rootView, id);
      if (tvType == null) {
        break missingId;
      }

      id = R.id.tvVerificationCode;
      TextView tvVerificationCode = ViewBindings.findChildViewById(rootView, id);
      if (tvVerificationCode == null) {
        break missingId;
      }

      return new ItemSmsBinding((CardView) rootView, cardView, tvContent, tvSender, tvTime, tvType,
          tvVerificationCode);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
