<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:contentDescription="Back"
        android:padding="12dp"
        android:src="@drawable/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/text_primary" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="账号密码登录"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="180dp"
        android:layout_height="60dp"
        android:layout_marginTop="40dp"
        android:scaleType="fitCenter"
        android:contentDescription="VISA Logo"
        android:src="@drawable/visa2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardLogin"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivLogo">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请输入您的账号和密码"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 账号输入框 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="Account Icon"
                    android:src="@drawable/ic_account"
                    app:tint="@color/text_secondary" />

                <EditText
                    android:id="@+id/etUsername"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="请输入账号/邮箱"
                    android:inputType="textEmailAddress"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="16sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color" />

            <!-- 密码输入框 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="Password Icon"
                    android:src="@drawable/ic_lock"
                    app:tint="@color/text_secondary" />

                <EditText
                    android:id="@+id/etPassword"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="请输入密码"
                    android:inputType="textPassword"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_secondary"
                    android:textSize="16sp" />

                <ImageView
                    android:id="@+id/ivTogglePassword"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="Toggle Password Visibility"
                    android:src="@drawable/ic_visibility_off"
                    app:tint="@color/text_secondary" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/divider_color" />

            <!-- 忘记密码 -->
            <TextView
                android:id="@+id/tvForgotPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="16dp"
                android:text="忘记密码？"
                android:textColor="@color/visa_blue"
                android:textSize="14sp" />

            <!-- 登录按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLogin"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="32dp"
                android:backgroundTint="@color/visa_blue"
                android:text="登录"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:cornerRadius="28dp" />

            <!-- 其他登录方式 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="24dp"
                android:text="其他登录方式"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivPhoneLogin"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/circle_background"
                    android:contentDescription="Phone Login"
                    android:padding="8dp"
                    android:src="@drawable/ic_phone" />

                <ImageView
                    android:id="@+id/ivWechatLogin"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="24dp"
                    android:background="@drawable/circle_background"
                    android:contentDescription="WeChat Login"
                    android:padding="8dp"
                    android:src="@drawable/weixin" />

                <ImageView
                    android:id="@+id/ivQQLogin"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/circle_background"
                    android:contentDescription="QQ Login"
                    android:padding="8dp"
                    android:src="@drawable/qq_icon" />
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- 注册选项 -->
    <LinearLayout
        android:id="@+id/llRegister"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="没有账号？"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tvRegister"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="立即注册"
            android:textColor="@color/visa_blue"
            android:textSize="14sp"
            android:textStyle="bold" />
    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
