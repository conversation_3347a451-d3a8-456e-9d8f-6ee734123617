#include <jni.h>
#include <android/log.h>
#include <sys/mman.h>
#include <cstring>

#define TAG "MemoryAnchor"
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, TAG, __VA_ARGS__)
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

static void* memory_anchor = nullptr;
static size_t anchor_size = 1024 * 1024; // 1MB

// Memory anchor implementation moved to immortal_core.cpp
// This file now contains only helper functions
