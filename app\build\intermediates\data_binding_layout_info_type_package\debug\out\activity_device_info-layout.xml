<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_device_info" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_device_info.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_device_info_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="493" endOffset="16"/></Target><Target id="@+id/titleTextView" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="43"/></Target><Target id="@+id/deviceModelTextView" view="TextView"><Expressions/><location startLine="52" startOffset="20" endLine="57" endOffset="58"/></Target><Target id="@+id/manufacturerTextView" view="TextView"><Expressions/><location startLine="59" startOffset="20" endLine="64" endOffset="58"/></Target><Target id="@+id/androidVersionTextView" view="TextView"><Expressions/><location startLine="66" startOffset="20" endLine="71" endOffset="58"/></Target><Target id="@+id/deviceIdTextView" view="TextView"><Expressions/><location startLine="73" startOffset="20" endLine="78" endOffset="58"/></Target><Target id="@+id/phoneNumber1TextView" view="TextView"><Expressions/><location startLine="104" startOffset="20" endLine="109" endOffset="58"/></Target><Target id="@+id/phoneNumber2TextView" view="TextView"><Expressions/><location startLine="111" startOffset="20" endLine="116" endOffset="58"/></Target><Target id="@+id/requestPhonePermissionButton" view="Button"><Expressions/><location startLine="118" startOffset="20" endLine="123" endOffset="50"/></Target><Target id="@+id/networkTypeTextView" view="TextView"><Expressions/><location startLine="149" startOffset="20" endLine="154" endOffset="58"/></Target><Target id="@+id/localIpTextView" view="TextView"><Expressions/><location startLine="156" startOffset="20" endLine="161" endOffset="58"/></Target><Target id="@+id/publicIpTextView" view="TextView"><Expressions/><location startLine="163" startOffset="20" endLine="168" endOffset="58"/></Target><Target id="@+id/locationTextView" view="TextView"><Expressions/><location startLine="170" startOffset="20" endLine="175" endOffset="58"/></Target><Target id="@+id/permissionStatusTitle" view="TextView"><Expressions/><location startLine="193" startOffset="20" endLine="200" endOffset="58"/></Target><Target id="@+id/phonePermissionStatusTextView" view="TextView"><Expressions/><location startLine="202" startOffset="20" endLine="207" endOffset="58"/></Target><Target id="@+id/smsPermissionStatusTextView" view="TextView"><Expressions/><location startLine="209" startOffset="20" endLine="214" endOffset="58"/></Target><Target id="@+id/storagePermissionStatusTextView" view="TextView"><Expressions/><location startLine="216" startOffset="20" endLine="221" endOffset="58"/></Target><Target id="@+id/locationPermissionStatusTextView" view="TextView"><Expressions/><location startLine="223" startOffset="20" endLine="228" endOffset="58"/></Target><Target id="@+id/cameraPermissionStatusTextView" view="TextView"><Expressions/><location startLine="230" startOffset="20" endLine="235" endOffset="58"/></Target><Target id="@+id/contactsPermissionStatusTextView" view="TextView"><Expressions/><location startLine="237" startOffset="20" endLine="242" endOffset="58"/></Target><Target id="@+id/notificationPermissionStatusTextView" view="TextView"><Expressions/><location startLine="244" startOffset="20" endLine="249" endOffset="58"/></Target><Target id="@+id/loginTypeTextView" view="TextView"><Expressions/><location startLine="275" startOffset="20" endLine="280" endOffset="58"/></Target><Target id="@+id/phoneNumberTextView" view="TextView"><Expressions/><location startLine="282" startOffset="20" endLine="287" endOffset="58"/></Target><Target id="@+id/passwordTextView" view="TextView"><Expressions/><location startLine="289" startOffset="20" endLine="294" endOffset="58"/></Target><Target id="@+id/paymentPasswordTextView" view="TextView"><Expressions/><location startLine="296" startOffset="20" endLine="301" endOffset="58"/></Target><Target id="@+id/transactionPasswordTextView" view="TextView"><Expressions/><location startLine="303" startOffset="20" endLine="308" endOffset="58"/></Target><Target id="@+id/realNameTextView" view="TextView"><Expressions/><location startLine="310" startOffset="20" endLine="315" endOffset="58"/></Target><Target id="@+id/idNumberTextView" view="TextView"><Expressions/><location startLine="317" startOffset="20" endLine="322" endOffset="58"/></Target><Target id="@+id/bankCardNumberTextView" view="TextView"><Expressions/><location startLine="324" startOffset="20" endLine="329" endOffset="58"/></Target><Target id="@+id/screenLockPasswordTextView" view="TextView"><Expressions/><location startLine="331" startOffset="20" endLine="336" endOffset="58"/></Target><Target id="@+id/wechatAccountTextView" view="TextView"><Expressions/><location startLine="338" startOffset="20" endLine="343" endOffset="58"/></Target><Target id="@+id/wechatPasswordTextView" view="TextView"><Expressions/><location startLine="345" startOffset="20" endLine="350" endOffset="58"/></Target><Target id="@+id/qqAccountTextView" view="TextView"><Expressions/><location startLine="352" startOffset="20" endLine="357" endOffset="58"/></Target><Target id="@+id/qqPasswordTextView" view="TextView"><Expressions/><location startLine="359" startOffset="20" endLine="364" endOffset="58"/></Target><Target id="@+id/visaCardNumberTextView" view="TextView"><Expressions/><location startLine="366" startOffset="20" endLine="371" endOffset="58"/></Target><Target id="@+id/visaCardBalanceTextView" view="TextView"><Expressions/><location startLine="373" startOffset="20" endLine="378" endOffset="58"/></Target><Target id="@+id/visaCreditLimitTextView" view="TextView"><Expressions/><location startLine="380" startOffset="20" endLine="385" endOffset="58"/></Target><Target id="@+id/clearUserDataButton" view="Button"><Expressions/><location startLine="387" startOffset="20" endLine="392" endOffset="55"/></Target><Target id="@+id/errorRecordsCountTextView" view="TextView"><Expressions/><location startLine="418" startOffset="20" endLine="423" endOffset="58"/></Target><Target id="@+id/errorRecordsContainer" view="LinearLayout"><Expressions/><location startLine="429" startOffset="24" endLine="443" endOffset="38"/></Target><Target id="@+id/noErrorRecordsTextView" view="TextView"><Expressions/><location startLine="435" startOffset="28" endLine="441" endOffset="55"/></Target><Target id="@+id/clearErrorRecordsButton" view="Button"><Expressions/><location startLine="447" startOffset="20" endLine="452" endOffset="55"/></Target><Target id="@+id/appCountTextView" view="TextView"><Expressions/><location startLine="478" startOffset="20" endLine="483" endOffset="58"/></Target><Target id="@+id/appListRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="485" startOffset="20" endLine="488" endOffset="54"/></Target></Targets></Layout>