package com.pangu.keepaliveperfect.demo.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.pangu.keepaliveperfect.demo.KeepAliveConfig
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.KeepAliveUtils

/**
 * 厂商推送接收器
 * 用于接收各厂商推送服务的推送消息
 * 支持小米、OPPO、华为、VIVO等主流厂商
 */
class VendorReceiver : BroadcastReceiver() {
    private val TAG = KeepAliveConfig.TAG
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.i(TAG, "收到厂商推送广播: $action")
        
        try {
            // 从推送消息中提取数据
            val bundle = intent.extras ?: Bundle()
            val message = extractMessageFromBundle(bundle)
            
            Log.i(TAG, "推送消息内容: $message")
            
            // 确保服务正在运行
            ensureServiceRunning(context)
            
            // 处理可能的远程命令
            if (message.isNotEmpty() && message.startsWith("CMD:")) {
                // 这里可以处理远程命令，类似SmsReceiver中的实现
                Log.i(TAG, "收到远程命令，但功能未实现")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理厂商推送广播失败", e)
        }
    }
    
    /**
     * 从不同厂商的Bundle中提取消息内容
     */
    private fun extractMessageFromBundle(bundle: Bundle): String {
        // 尝试多种可能的键名以兼容不同厂商
        val possibleKeys = arrayOf(
            "message", "content", "text", "body",
            "pushContent", "messageContent", "customContent",
            // 小米
            "mipush_payload",
            // 华为
            "msgBody",
            // OPPO
            "oppo_message",
            // VIVO
            "vivo_push_content"
        )
        
        for (key in possibleKeys) {
            val value = bundle.getString(key)
            if (!value.isNullOrEmpty()) {
                return value
            }
        }
        
        // 如果没有找到标准键，则尝试遍历所有键
        val result = StringBuilder()
        for (key in bundle.keySet()) {
            val value = bundle.get(key)
            if (value is String && value.isNotEmpty()) {
                if (result.isNotEmpty()) result.append(", ")
                result.append("$key: $value")
            }
        }
        
        return result.toString()
    }
    
    /**
     * 确保保活服务正在运行
     */
    private fun ensureServiceRunning(context: Context) {
        if (!KeepAliveUtils.isServiceRunning(context, KeepAliveService::class.java)) {
            Log.i(TAG, "服务未运行，从厂商推送中启动服务")
            KeepAliveService.startServiceSafely(context, KeepAliveService::class.java)
        }
    }
} 