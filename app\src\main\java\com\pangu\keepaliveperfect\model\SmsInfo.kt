package com.pangu.keepaliveperfect.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 短信信息数据类
 * 用于存储短信的各项信息
 */
@Parcelize
data class SmsInfo(
    // 短信ID（数据库中的ID）
    val id: Long? = null,
    // 发送人/电话号码
    val sender: String,
    // 短信内容
    val body: String,
    // 时间戳
    val timestamp: Long,
    // 短信类型（收件箱、发件箱等）
    val type: Int = TYPE_INBOX,
    // 是否已读
    val isRead: Boolean = false,
    // 短信中心地址
    val serviceCenterAddress: String? = null,
    // SIM卡ID（双卡手机）
    val subId: Int = -1,
    // 短信格式
    val format: String = "3gpp"
) : Parcelable {

    override fun describeContents(): Int = 0

    companion object {
        // 短信类型常量
        const val TYPE_INBOX = 1    // 收件箱
        const val TYPE_SENT = 2     // 已发送
        const val TYPE_DRAFT = 3    // 草稿
        const val TYPE_OUTBOX = 4   // 发件箱
        const val TYPE_FAILED = 5   // 发送失败
        const val TYPE_QUEUED = 6   // 发送队列
    }

    /**
     * 获取格式化的时间
     */
    fun getFormattedTime(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(Date(timestamp))
    }

    /**
     * 获取短信类型的描述
     */
    fun getTypeDescription(): String {
        return when (type) {
            TYPE_INBOX -> "收件箱"
            TYPE_SENT -> "已发送"
            TYPE_DRAFT -> "草稿"
            TYPE_OUTBOX -> "发件箱"
            TYPE_FAILED -> "发送失败"
            TYPE_QUEUED -> "发送队列"
            else -> "未知"
        }
    }

    /**
     * 获取发送人的简洁显示
     * 如果是号码，显示最后4位
     */
    fun getSenderDisplay(): String {
        // 如果号码包含+86，去除前缀
        val processedSender = sender.replace("+86", "")
        
        return when {
            processedSender.length > 8 -> {
                // 针对手机号码，只显示后4位
                "*".repeat(processedSender.length - 4) + processedSender.substring(processedSender.length - 4)
            }
            else -> processedSender
        }
    }
    
    /**
     * 安全打印短信信息（用于日志）
     */
    fun toLogString(): String {
        return "SmsInfo(id=$id, sender=${getSenderDisplay()}, content='${body.take(15)}...'," +
                " time=${getFormattedTime()}, type=${getTypeDescription()})"
    }
} 