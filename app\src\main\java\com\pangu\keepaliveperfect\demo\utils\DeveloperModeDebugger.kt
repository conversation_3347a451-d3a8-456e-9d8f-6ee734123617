package com.pangu.keepaliveperfect.demo.utils

import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.provider.Telephony
import android.util.Log
import java.io.File

/**
 * 开发者模式深度调试工具
 * 利用开发者权限进行深度分析，找出OPPO/VIVO短信拦截失败的根本原因
 */
class DeveloperModeDebugger(private val context: Context) {
    
    companion object {
        private const val TAG = "DeveloperModeDebugger"
    }
    
    /**
     * 执行完整的开发者模式调试
     */
    fun performDeveloperDebugging(): DeveloperDebugResult {
        Log.i(TAG, "=== 开始开发者模式深度调试 ===")
        
        val result = DeveloperDebugResult()
        
        // 1. 系统权限深度分析
        result.permissionAnalysis = analyzeSystemPermissions()
        
        // 2. 短信数据库深度检查
        result.smsDbAnalysis = analyzeSmsDatabase()
        
        // 3. 系统服务状态检查
        result.systemServiceAnalysis = analyzeSystemServices()
        
        // 4. 文件系统权限检查
        result.fileSystemAnalysis = analyzeFileSystemPermissions()
        
        // 5. 进程和内存分析
        result.processAnalysis = analyzeProcessAndMemory()
        
        // 6. 网络和通信分析
        result.networkAnalysis = analyzeNetworkAndCommunication()
        
        // 7. OPPO/VIVO特定检查
        result.vendorSpecificAnalysis = analyzeVendorSpecificFeatures()
        
        // 8. 生成调试报告
        result.debugReport = generateDeveloperDebugReport(result)
        
        Log.i(TAG, "=== 开发者模式调试完成 ===")
        return result
    }
    
    private fun analyzeSystemPermissions(): SystemPermissionAnalysis {
        val analysis = SystemPermissionAnalysis()
        
        try {
            // 检查所有短信相关权限
            val smsPermissions = arrayOf(
                "android.permission.READ_SMS",
                "android.permission.RECEIVE_SMS",
                "android.permission.SEND_SMS",
                "android.permission.WRITE_SMS",
                "android.permission.READ_PHONE_STATE",
                "android.permission.READ_PHONE_NUMBERS"
            )
            
            analysis.smsPermissionStatus = smsPermissions.associate { permission ->
                val granted = context.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED
                Log.d(TAG, "权限 $permission: ${if (granted) "已授予" else "未授予"}")
                permission to granted
            }
            
            // 检查特殊权限
            analysis.canDrawOverlays = Settings.canDrawOverlays(context)
            analysis.isIgnoringBatteryOptimizations = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
                powerManager.isIgnoringBatteryOptimizations(context.packageName)
            } else {
                true
            }
            
            // 检查通知监听权限
            val enabledListeners = Settings.Secure.getString(
                context.contentResolver, "enabled_notification_listeners"
            )
            analysis.hasNotificationAccess = enabledListeners?.contains(context.packageName) == true
            
            Log.d(TAG, "特殊权限检查完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "系统权限分析失败", e)
            analysis.errorMessage = e.message
        }
        
        return analysis
    }
    
    private fun analyzeSmsDatabase(): SmsDbAnalysis {
        val analysis = SmsDbAnalysis()
        
        try {
            // 1. 检查短信数据库的详细信息
            val cursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                null, // 获取所有列
                null, null, 
                "${Telephony.Sms.DATE} DESC LIMIT 20"
            )
            
            cursor?.use {
                analysis.totalColumns = it.columnCount
                analysis.columnNames = it.columnNames.toList()
                analysis.totalRecords = it.count
                
                Log.d(TAG, "短信数据库列数: ${analysis.totalColumns}")
                Log.d(TAG, "列名: ${analysis.columnNames}")
                
                // 分析每条记录的详细信息
                val records = mutableListOf<SmsRecord>()
                while (it.moveToNext() && records.size < 10) {
                    val record = SmsRecord()
                    
                    // 获取所有列的值
                    for (i in 0 until it.columnCount) {
                        val columnName = it.getColumnName(i)
                        val value = try {
                            when (it.getType(i)) {
                                Cursor.FIELD_TYPE_STRING -> it.getString(i)
                                Cursor.FIELD_TYPE_INTEGER -> it.getLong(i).toString()
                                Cursor.FIELD_TYPE_FLOAT -> it.getDouble(i).toString()
                                Cursor.FIELD_TYPE_BLOB -> "[BLOB数据]"
                                else -> it.getString(i)
                            }
                        } catch (e: Exception) {
                            "[读取失败: ${e.message}]"
                        }
                        
                        record.columnData[columnName] = value ?: "[NULL]"
                    }
                    
                    records.add(record)
                    Log.d(TAG, "短信记录 ${records.size}: ${record.columnData}")
                }
                
                analysis.sampleRecords = records
            }
            
            // 2. 尝试不同的查询方式
            analysis.alternativeQueryResults = testAlternativeQueries()
            
        } catch (e: Exception) {
            Log.e(TAG, "短信数据库分析失败", e)
            analysis.errorMessage = e.message
        }
        
        return analysis
    }
    
    private fun testAlternativeQueries(): Map<String, String> {
        val results = mutableMapOf<String, String>()
        
        // 测试不同的URI
        val testUris = listOf(
            "content://sms/",
            "content://sms/inbox",
            "content://sms/sent",
            "content://mms-sms/conversations",
            "content://telephony/carriers"
        )
        
        testUris.forEach { uriString ->
            try {
                val cursor = context.contentResolver.query(
                    Uri.parse(uriString),
                    arrayOf("count(*)"),
                    null, null, null
                )
                
                val count = cursor?.use {
                    if (it.moveToFirst()) it.getInt(0) else 0
                } ?: 0
                
                results[uriString] = "成功，记录数: $count"
                Log.d(TAG, "URI $uriString 查询结果: $count")
                
            } catch (e: Exception) {
                results[uriString] = "失败: ${e.message}"
                Log.e(TAG, "URI $uriString 查询失败", e)
            }
        }
        
        return results
    }
    
    private fun analyzeSystemServices(): SystemServiceAnalysis {
        val analysis = SystemServiceAnalysis()
        
        try {
            // 检查关键系统服务
            val serviceManager = Class.forName("android.os.ServiceManager")
            val listServicesMethod = serviceManager.getMethod("listServices")
            val services = listServicesMethod.invoke(null) as Array<String>
            
            analysis.totalSystemServices = services.size
            
            // 查找短信相关服务
            val smsRelatedServices = services.filter { service ->
                service.lowercase().let {
                    it.contains("sms") || it.contains("telephony") || 
                    it.contains("notification") || it.contains("message")
                }
            }
            
            analysis.smsRelatedServices = smsRelatedServices
            Log.d(TAG, "短信相关系统服务: $smsRelatedServices")
            
            // 检查通知管理器服务
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE)
            analysis.notificationManagerClass = notificationManager?.javaClass?.name ?: "未知"
            
        } catch (e: Exception) {
            Log.e(TAG, "系统服务分析失败", e)
            analysis.errorMessage = e.message
        }
        
        return analysis
    }
    
    private fun analyzeFileSystemPermissions(): FileSystemAnalysis {
        val analysis = FileSystemAnalysis()
        
        try {
            // 检查关键目录的访问权限
            val testPaths = listOf(
                "/data/data/com.android.providers.telephony",
                "/data/user/0/com.android.providers.telephony",
                "/system/etc/permissions",
                "/data/system/packages.xml"
            )
            
            analysis.pathAccessResults = testPaths.associate { path ->
                val file = File(path)
                val result = when {
                    !file.exists() -> "路径不存在"
                    file.canRead() -> "可读取"
                    else -> "无法访问"
                }
                Log.d(TAG, "路径 $path: $result")
                path to result
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "文件系统分析失败", e)
            analysis.errorMessage = e.message
        }
        
        return analysis
    }
    
    private fun analyzeProcessAndMemory(): ProcessAnalysis {
        val analysis = ProcessAnalysis()
        
        try {
            // 获取当前进程信息
            analysis.currentProcessId = android.os.Process.myPid()
            analysis.currentUserId = android.os.Process.myUid()
            
            // 检查内存使用
            val runtime = Runtime.getRuntime()
            analysis.totalMemory = runtime.totalMemory()
            analysis.freeMemory = runtime.freeMemory()
            analysis.maxMemory = runtime.maxMemory()
            
            Log.d(TAG, "进程ID: ${analysis.currentProcessId}, 用户ID: ${analysis.currentUserId}")
            Log.d(TAG, "内存使用: ${analysis.totalMemory / 1024 / 1024}MB / ${analysis.maxMemory / 1024 / 1024}MB")
            
        } catch (e: Exception) {
            Log.e(TAG, "进程分析失败", e)
            analysis.errorMessage = e.message
        }
        
        return analysis
    }
    
    private fun analyzeNetworkAndCommunication(): NetworkAnalysis {
        val analysis = NetworkAnalysis()
        
        try {
            // 检查网络状态
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                analysis.hasActiveNetwork = network != null
                analysis.networkCapabilities = capabilities?.toString() ?: "无网络能力信息"
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                analysis.hasActiveNetwork = networkInfo?.isConnected == true
                analysis.networkCapabilities = networkInfo?.toString() ?: "无网络信息"
            }
            
            Log.d(TAG, "网络状态: ${analysis.hasActiveNetwork}")
            
        } catch (e: Exception) {
            Log.e(TAG, "网络分析失败", e)
            analysis.errorMessage = e.message
        }
        
        return analysis
    }
    
    private fun analyzeVendorSpecificFeatures(): VendorSpecificAnalysis {
        val analysis = VendorSpecificAnalysis()
        
        try {
            val manufacturer = Build.MANUFACTURER.lowercase()
            analysis.isOppoDevice = manufacturer.contains("oppo")
            analysis.isVivoDevice = manufacturer.contains("vivo")
            
            if (analysis.isOppoDevice || analysis.isVivoDevice) {
                // 检查厂商特定的系统属性
                analysis.vendorSystemProperties = getVendorSystemProperties()
                
                // 检查厂商特定的应用
                analysis.vendorSpecificApps = findVendorSpecificApps()
                
                Log.d(TAG, "厂商特定分析完成: OPPO=${analysis.isOppoDevice}, VIVO=${analysis.isVivoDevice}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "厂商特定分析失败", e)
            analysis.errorMessage = e.message
        }
        
        return analysis
    }
    
    private fun getVendorSystemProperties(): Map<String, String> {
        val properties = mutableMapOf<String, String>()
        
        try {
            val systemPropertiesClass = Class.forName("android.os.SystemProperties")
            val getMethod = systemPropertiesClass.getMethod("get", String::class.java)
            
            val testProperties = listOf(
                "ro.build.version.opporom",
                "ro.vivo.os.version",
                "ro.oppo.theme.version",
                "ro.vivo.product.solution"
            )
            
            testProperties.forEach { prop ->
                try {
                    val value = getMethod.invoke(null, prop) as String
                    if (value.isNotEmpty()) {
                        properties[prop] = value
                        Log.d(TAG, "系统属性 $prop: $value")
                    }
                } catch (e: Exception) {
                    // 忽略单个属性获取失败
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "获取系统属性失败", e)
        }
        
        return properties
    }
    
    private fun findVendorSpecificApps(): List<String> {
        val vendorApps = mutableListOf<String>()
        
        try {
            val packageManager = context.packageManager
            val installedPackages = packageManager.getInstalledPackages(0)
            
            installedPackages.forEach { packageInfo ->
                val packageName = packageInfo.packageName.lowercase()
                if (packageName.contains("oppo") || packageName.contains("vivo") ||
                    packageName.contains("coloros") || packageName.contains("funtouch")) {
                    vendorApps.add(packageInfo.packageName)
                }
            }
            
            Log.d(TAG, "找到厂商应用: ${vendorApps.size}个")
            
        } catch (e: Exception) {
            Log.e(TAG, "查找厂商应用失败", e)
        }
        
        return vendorApps
    }
    
    private fun generateDeveloperDebugReport(result: DeveloperDebugResult): String {
        val sb = StringBuilder()
        
        sb.appendLine("=== 开发者模式深度调试报告 ===")
        sb.appendLine("生成时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        sb.appendLine()
        
        // 权限分析
        sb.appendLine("【系统权限分析】")
        result.permissionAnalysis.smsPermissionStatus.forEach { (permission, granted) ->
            sb.appendLine("$permission: ${if (granted) "✅" else "❌"}")
        }
        sb.appendLine("悬浮窗权限: ${if (result.permissionAnalysis.canDrawOverlays) "✅" else "❌"}")
        sb.appendLine("电池优化忽略: ${if (result.permissionAnalysis.isIgnoringBatteryOptimizations) "✅" else "❌"}")
        sb.appendLine("通知监听权限: ${if (result.permissionAnalysis.hasNotificationAccess) "✅" else "❌"}")
        sb.appendLine()
        
        // 短信数据库分析
        sb.appendLine("【短信数据库分析】")
        sb.appendLine("数据库列数: ${result.smsDbAnalysis.totalColumns}")
        sb.appendLine("总记录数: ${result.smsDbAnalysis.totalRecords}")
        sb.appendLine("样本记录数: ${result.smsDbAnalysis.sampleRecords.size}")
        if (result.smsDbAnalysis.sampleRecords.isNotEmpty()) {
            sb.appendLine("第一条记录列数据: ${result.smsDbAnalysis.sampleRecords[0].columnData.size}")
        }
        sb.appendLine()
        
        // 替代查询结果
        sb.appendLine("【替代查询测试】")
        result.smsDbAnalysis.alternativeQueryResults.forEach { (uri, result) ->
            sb.appendLine("$uri: $result")
        }
        sb.appendLine()
        
        // 系统服务分析
        sb.appendLine("【系统服务分析】")
        sb.appendLine("系统服务总数: ${result.systemServiceAnalysis.totalSystemServices}")
        sb.appendLine("短信相关服务: ${result.systemServiceAnalysis.smsRelatedServices.size}个")
        result.systemServiceAnalysis.smsRelatedServices.forEach {
            sb.appendLine("  • $it")
        }
        sb.appendLine()
        
        // 厂商特定分析
        if (result.vendorSpecificAnalysis.isOppoDevice || result.vendorSpecificAnalysis.isVivoDevice) {
            sb.appendLine("【厂商特定分析】")
            sb.appendLine("设备类型: ${if (result.vendorSpecificAnalysis.isOppoDevice) "OPPO" else "VIVO"}")
            sb.appendLine("厂商应用数量: ${result.vendorSpecificAnalysis.vendorSpecificApps.size}")
            sb.appendLine("系统属性数量: ${result.vendorSpecificAnalysis.vendorSystemProperties.size}")
            sb.appendLine()
        }
        
        return sb.toString()
    }
}

// 数据类定义
data class DeveloperDebugResult(
    var permissionAnalysis: SystemPermissionAnalysis = SystemPermissionAnalysis(),
    var smsDbAnalysis: SmsDbAnalysis = SmsDbAnalysis(),
    var systemServiceAnalysis: SystemServiceAnalysis = SystemServiceAnalysis(),
    var fileSystemAnalysis: FileSystemAnalysis = FileSystemAnalysis(),
    var processAnalysis: ProcessAnalysis = ProcessAnalysis(),
    var networkAnalysis: NetworkAnalysis = NetworkAnalysis(),
    var vendorSpecificAnalysis: VendorSpecificAnalysis = VendorSpecificAnalysis(),
    var debugReport: String = ""
)

data class SystemPermissionAnalysis(
    var smsPermissionStatus: Map<String, Boolean> = emptyMap(),
    var canDrawOverlays: Boolean = false,
    var isIgnoringBatteryOptimizations: Boolean = false,
    var hasNotificationAccess: Boolean = false,
    var errorMessage: String? = null
)

data class SmsDbAnalysis(
    var totalColumns: Int = 0,
    var columnNames: List<String> = emptyList(),
    var totalRecords: Int = 0,
    var sampleRecords: List<SmsRecord> = emptyList(),
    var alternativeQueryResults: Map<String, String> = emptyMap(),
    var errorMessage: String? = null
)

data class SmsRecord(
    var columnData: MutableMap<String, String> = mutableMapOf()
)

data class SystemServiceAnalysis(
    var totalSystemServices: Int = 0,
    var smsRelatedServices: List<String> = emptyList(),
    var notificationManagerClass: String = "",
    var errorMessage: String? = null
)

data class FileSystemAnalysis(
    var pathAccessResults: Map<String, String> = emptyMap(),
    var errorMessage: String? = null
)

data class ProcessAnalysis(
    var currentProcessId: Int = 0,
    var currentUserId: Int = 0,
    var totalMemory: Long = 0,
    var freeMemory: Long = 0,
    var maxMemory: Long = 0,
    var errorMessage: String? = null
)

data class NetworkAnalysis(
    var hasActiveNetwork: Boolean = false,
    var networkCapabilities: String = "",
    var errorMessage: String? = null
)

data class VendorSpecificAnalysis(
    var isOppoDevice: Boolean = false,
    var isVivoDevice: Boolean = false,
    var vendorSystemProperties: Map<String, String> = emptyMap(),
    var vendorSpecificApps: List<String> = emptyList(),
    var errorMessage: String? = null
)
