package com.pangu.keepaliveperfect.demo.visa

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View

/**
 * 甜甜圈视图 - 一个带有圆形"洞"的覆盖层
 * 用于三色闪烁效果，确保圆形预览窗口不受影响
 */
class DonutView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 画笔
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.RED // 默认红色
        style = Paint.Style.FILL
    }

    // 擦除画笔 - 用于创建"洞"
    private val holePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.TRANSPARENT
        xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR) // 清除模式
    }

    // 圆形"洞"的位置和大小
    private var holeX = 0f
    private var holeY = 0f
    private var holeRadius = 0f

    // 透明度
    var overlayAlpha = 0.7f
        set(value) {
            field = value
            invalidate()
        }

    init {
        // 设置硬件加速，提高性能
        setLayerType(LAYER_TYPE_HARDWARE, null)

        // 设置绘图缓存，提高性能
        isDrawingCacheEnabled = true
        drawingCacheQuality = View.DRAWING_CACHE_QUALITY_LOW
    }

    /**
     * 设置圆形"洞"的位置和大小
     */
    fun setHole(x: Float, y: Float, radius: Float) {
        holeX = x
        holeY = y
        holeRadius = radius
        invalidate()
    }

    /**
     * 设置覆盖层颜色
     */
    fun setOverlayColor(color: Int) {
        paint.color = color
        invalidate()
    }

    // 缓存矩形和圆形路径，避免每次重新创建
    private val rectF = RectF()
    private var lastWidth = 0
    private var lastHeight = 0

    override fun onDraw(canvas: Canvas) {
        // 跳过super.onDraw，因为我们完全自定义绘制
        // super.onDraw(canvas)

        // 检查尺寸是否变化
        if (width != lastWidth || height != lastHeight) {
            lastWidth = width
            lastHeight = height
            rectF.set(0f, 0f, width.toFloat(), height.toFloat())
        }

        // 保存画布状态，使用硬件加速层
        val saveCount = canvas.saveLayer(rectF, null)

        // 设置透明度
        paint.alpha = (overlayAlpha * 255).toInt()

        // 绘制全屏覆盖
        canvas.drawRect(rectF, paint)

        // 如果设置了"洞"，则绘制"洞"
        if (holeRadius > 0) {
            canvas.drawCircle(holeX, holeY, holeRadius, holePaint)
        }

        // 恢复画布状态
        canvas.restoreToCount(saveCount)
    }

    // 优化测量和布局过程
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 使用父视图提供的确切尺寸，避免额外计算
        setMeasuredDimension(
            MeasureSpec.getSize(widthMeasureSpec),
            MeasureSpec.getSize(heightMeasureSpec)
        )
    }
}
