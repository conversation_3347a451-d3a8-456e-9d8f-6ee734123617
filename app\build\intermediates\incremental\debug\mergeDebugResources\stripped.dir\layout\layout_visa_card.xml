<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardVisaDesign"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="24dp"
    android:paddingVertical="12dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_centerInParent="true"
        app:cardCornerRadius="16dp"
        app:cardElevation="12dp">

        <!-- 外部光晕效果 -->
        <View
            android:id="@+id/card_outer_glow"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_card_shadow"
            android:elevation="-1dp" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- 卡片背景 -->
            <ImageView
                android:id="@+id/background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_card_gradient"
                android:scaleType="centerCrop" />

            <!-- 卡片表面光晕效果 -->
            <View
                android:id="@+id/shine_effect"
                android:layout_width="80dp"
                android:layout_height="match_parent"
                android:background="@drawable/card_shine_effect"
                android:visibility="visible" />

            <!-- Visa Logo (使用png文件) -->
            <ImageView
                android:id="@+id/visa_logo"
                android:layout_width="140dp"
                android:layout_height="40dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentStart="true"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:scaleType="fitStart"
                android:src="@drawable/visa2"
                android:contentDescription="@string/visa_logo" />

            <!-- 银行名称 -->
            <TextView
                android:id="@+id/bank_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="20dp"
                android:fontFamily="@font/visa_font"
                android:text="全球信用卡"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 卡号 -->
            <TextView
                android:id="@+id/card_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/label_holder"
                android:layout_marginStart="20dp"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/visa_font"
                android:text="4000 ****** 9010"
                android:textColor="#FFFFFF"
                android:textSize="18sp" />

            <!-- 持卡人标签 -->
            <TextView
                android:id="@+id/label_holder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="20dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/visa_font"
                android:text="持卡人"
                android:textColor="#E0E0E0"
                android:textSize="12sp" />

            <!-- 持卡人姓名 -->
            <TextView
                android:id="@+id/card_holder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="20dp"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/visa_font"
                android:text="***"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

            <!-- 有效期标签 -->
            <TextView
                android:id="@+id/label_valid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="@font/visa_font"
                android:text="有效期至"
                android:textColor="#E0E0E0"
                android:textSize="12sp" />

            <!-- 有效期 -->
            <TextView
                android:id="@+id/card_valid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/visa_font"
                android:text="12/28"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

            <!-- 装饰性高光效果 -->
            <View
                android:layout_width="150dp"
                android:layout_height="150dp"
                android:layout_alignParentStart="true"
                android:layout_alignParentTop="true"
                android:layout_marginStart="-50dp"
                android:layout_marginTop="-50dp"
                android:alpha="0.07"
                android:background="@drawable/circle_shape" />

            <View
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="-60dp"
                android:layout_marginBottom="-60dp"
                android:alpha="0.10"
                android:background="@drawable/circle_shape" />

        </RelativeLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout> 