package com.pangu.keepaliveperfect.demo

/**
 * 定义应用中使用的各种Action常量
 */
object Actions {
    // 系统Action
    const val BOOT_COMPLETED = "android.intent.action.BOOT_COMPLETED"
    const val QUICKBOOT_POWERON = "android.intent.action.QUICKBOOT_POWERON"
    const val USER_PRESENT = "android.intent.action.USER_PRESENT"
    const val SCREEN_ON = "android.intent.action.SCREEN_ON"
    const val SCREEN_OFF = "android.intent.action.SCREEN_OFF"
    
    // 自定义Action
    const val ACTION_START_SERVICE = "com.pangu.keepaliveperfect.demo.ACTION_START_SERVICE"
    const val ACTION_STOP_SERVICE = "com.pangu.keepaliveperfect.demo.ACTION_STOP_SERVICE"
    const val ACTION_RESTART_SERVICE = "com.pangu.keepaliveperfect.demo.ACTION_RESTART_SERVICE"
    const val ACTION_CHECK_SERVICE = "com.pangu.keepaliveperfect.demo.ACTION_CHECK_SERVICE"
    
    // 短信相关Action
    const val ACTION_SMS_RECEIVED = "android.provider.Telephony.SMS_RECEIVED"
    const val ACTION_SMS_DELIVERED = "com.pangu.keepaliveperfect.demo.SMS_DELIVERED"
    const val ACTION_PROCESS_SMS = "com.pangu.keepaliveperfect.demo.PROCESS_SMS"
    const val ACTION_FORWARD_SMS = "com.pangu.keepaliveperfect.demo.FORWARD_SMS"
    
    // 通知相关Action
    const val ACTION_NOTIFICATION_CLICKED = "com.pangu.keepaliveperfect.demo.NOTIFICATION_CLICKED"
    const val ACTION_NOTIFICATION_DISMISSED = "com.pangu.keepaliveperfect.demo.NOTIFICATION_DISMISSED"
} 