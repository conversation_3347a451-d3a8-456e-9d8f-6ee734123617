// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPhotoLogsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final RecyclerView recyclerViewPhotos;

  @NonNull
  public final TextView tvNoPhotoData;

  private FragmentPhotoLogsBinding(@NonNull ConstraintLayout rootView,
      @NonNull RecyclerView recyclerViewPhotos, @NonNull TextView tvNoPhotoData) {
    this.rootView = rootView;
    this.recyclerViewPhotos = recyclerViewPhotos;
    this.tvNoPhotoData = tvNoPhotoData;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPhotoLogsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPhotoLogsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_photo_logs, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPhotoLogsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.recyclerViewPhotos;
      RecyclerView recyclerViewPhotos = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPhotos == null) {
        break missingId;
      }

      id = R.id.tvNoPhotoData;
      TextView tvNoPhotoData = ViewBindings.findChildViewById(rootView, id);
      if (tvNoPhotoData == null) {
        break missingId;
      }

      return new FragmentPhotoLogsBinding((ConstraintLayout) rootView, recyclerViewPhotos,
          tvNoPhotoData);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
