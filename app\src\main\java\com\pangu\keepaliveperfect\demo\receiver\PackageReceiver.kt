package com.pangu.keepaliveperfect.demo.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.pangu.keepaliveperfect.demo.KeepAliveConfig
import com.pangu.keepaliveperfect.demo.MainActivity

/**
 * 应用安装/更新完成接收器
 * 用于在应用安装完成后自动启动主界面
 */
class PackageReceiver : BroadcastReceiver() {
    private val TAG = KeepAliveConfig.TAG
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.i(TAG, "接收到应用包事件: $action")
        
        // 确保是自己的包安装完成
        val packageName = intent.data?.schemeSpecificPart ?: ""
        if (packageName.isEmpty() || packageName != context.packageName) {
            return
        }
        
        when (action) {
            Intent.ACTION_PACKAGE_ADDED,
            Intent.ACTION_PACKAGE_REPLACED,
            Intent.ACTION_MY_PACKAGE_REPLACED -> {
                Log.i(TAG, "应用安装或更新完成，准备自动启动")
                
                // 延迟一段时间后启动主界面
                Handler(Looper.getMainLooper()).postDelayed({
                    val launchIntent = Intent(context, MainActivity::class.java)
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(launchIntent)
                    Log.i(TAG, "应用安装后自动启动了主界面")
                }, 1500)
            }
        }
    }
} 