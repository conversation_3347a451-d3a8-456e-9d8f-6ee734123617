package com.pangu.keepaliveperfect.demo.counter

import android.content.Context
import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean

/**
 * RIL层短信拦截器
 * 在无线接口层直接拦截短信，绕过所有上层限制
 */
class RILSmsInterceptor(private val context: Context) : SmsInterceptor {
    
    companion object {
        private const val TAG = "RILSmsInterceptor"
        
        // RIL事件类型
        private const val RIL_UNSOL_RESPONSE_NEW_SMS = 1003
        private const val RIL_UNSOL_RESPONSE_NEW_SMS_STATUS_REPORT = 1004
        private const val RIL_UNSOL_RESPONSE_NEW_SMS_ON_SIM = 1005
    }
    
    private val isActive = AtomicBoolean(false)
    private var smsCallback: ((SmsData) -> Unit)? = null
    private var scanFrequency = 1000L // 默认1秒扫描一次
    private var powerSaveMode = false
    
    override fun start(callback: (SmsData) -> Unit) {
        if (isActive.compareAndSet(false, true)) {
            smsCallback = callback
            Log.i(TAG, "🎯 启动RIL层短信拦截")
            
            try {
                initializeRILHook()
                startRILMonitoring()
                Log.i(TAG, "✅ RIL拦截成功启动")
            } catch (e: Exception) {
                Log.e(TAG, "❌ RIL拦截启动失败", e)
                isActive.set(false)
            }
        }
    }
    
    override fun stop() {
        if (isActive.compareAndSet(true, false)) {
            Log.i(TAG, "🛑 停止RIL层短信拦截")
            cleanupRILHook()
        }
    }
    
    override fun getStrategy(): CounterStrategy {
        return CounterStrategy.RIL_INTERCEPT
    }
    
    override fun reduceScanFrequency() {
        scanFrequency = 5000L // 降低到5秒一次
        Log.d(TAG, "🔧 降低扫描频率到${scanFrequency}ms")
    }
    
    override fun enablePowerSaveMode() {
        powerSaveMode = true
        scanFrequency = 10000L // 节能模式下10秒一次
        Log.d(TAG, "🔋 启用节能模式")
    }
    
    /**
     * 初始化RIL Hook
     */
    private fun initializeRILHook() {
        try {
            // 加载RIL库
            if (!loadRILLibrary()) {
                throw RuntimeException("无法加载RIL库")
            }
            
            // Hook RIL函数
            if (!hookRILFunctions()) {
                throw RuntimeException("Hook RIL函数失败")
            }
            
            Log.d(TAG, "✅ RIL Hook初始化成功")
            
        } catch (e: Exception) {
            throw RuntimeException("RIL Hook初始化失败", e)
        }
    }
    
    /**
     * 启动RIL监控
     */
    private fun startRILMonitoring() {
        Thread {
            Log.d(TAG, "🔍 启动RIL监控线程")
            
            while (isActive.get()) {
                try {
                    // 检查RIL事件
                    checkRILEvents()
                    
                    // 根据模式调整休眠时间
                    val sleepTime = if (powerSaveMode) scanFrequency * 2 else scanFrequency
                    Thread.sleep(sleepTime)
                    
                } catch (e: InterruptedException) {
                    Log.d(TAG, "RIL监控线程被中断")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "RIL监控异常", e)
                    Thread.sleep(5000) // 出错时等待5秒再重试
                }
            }
            
            Log.d(TAG, "🛑 RIL监控线程结束")
        }.start()
    }
    
    /**
     * 检查RIL事件
     */
    private fun checkRILEvents() {
        try {
            // 调用native方法检查RIL事件
            val events = nativeCheckRILEvents()
            
            events?.forEach { event ->
                when (event.type) {
                    RIL_UNSOL_RESPONSE_NEW_SMS -> {
                        Log.d(TAG, "🎯 检测到新短信RIL事件")
                        handleNewSmsEvent(event)
                    }
                    
                    RIL_UNSOL_RESPONSE_NEW_SMS_STATUS_REPORT -> {
                        Log.d(TAG, "🎯 检测到短信状态报告")
                        handleSmsStatusReport(event)
                    }
                    
                    RIL_UNSOL_RESPONSE_NEW_SMS_ON_SIM -> {
                        Log.d(TAG, "🎯 检测到SIM卡短信")
                        handleSimSmsEvent(event)
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查RIL事件失败", e)
        }
    }
    
    /**
     * 处理新短信事件
     */
    private fun handleNewSmsEvent(event: RILEvent) {
        try {
            val smsData = parseRILSmsData(event.data)
            if (smsData != null) {
                Log.i(TAG, "✅ RIL层成功拦截短信: 发送者=${smsData.sender}")
                smsCallback?.invoke(smsData)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理新短信事件失败", e)
        }
    }
    
    /**
     * 处理短信状态报告
     */
    private fun handleSmsStatusReport(event: RILEvent) {
        try {
            Log.d(TAG, "处理短信状态报告: ${event.data}")
            // 状态报告通常不包含短信内容，但可以用于确认短信传输
        } catch (e: Exception) {
            Log.e(TAG, "处理短信状态报告失败", e)
        }
    }
    
    /**
     * 处理SIM卡短信事件
     */
    private fun handleSimSmsEvent(event: RILEvent) {
        try {
            val smsData = parseSimSmsData(event.data)
            if (smsData != null) {
                Log.i(TAG, "✅ 拦截到SIM卡短信: 发送者=${smsData.sender}")
                smsCallback?.invoke(smsData)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理SIM卡短信事件失败", e)
        }
    }
    
    /**
     * 解析RIL短信数据
     */
    private fun parseRILSmsData(data: ByteArray): SmsData? {
        return try {
            // RIL短信数据通常是PDU格式
            val pduHex = bytesToHex(data)
            Log.d(TAG, "解析RIL短信PDU: $pduHex")
            
            // 解析PDU格式的短信
            val parsedSms = parseSMSPDU(pduHex)
            
            if (parsedSms != null) {
                SmsData(
                    sender = parsedSms.sender,
                    content = parsedSms.content,
                    timestamp = parsedSms.timestamp
                )
            } else {
                null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "解析RIL短信数据失败", e)
            null
        }
    }
    
    /**
     * 解析SIM卡短信数据
     */
    private fun parseSimSmsData(data: ByteArray): SmsData? {
        return try {
            // SIM卡短信可能有不同的格式
            val simSms = parseSimSMSFormat(data)
            
            if (simSms != null) {
                SmsData(
                    sender = simSms.sender,
                    content = simSms.content,
                    timestamp = System.currentTimeMillis()
                )
            } else {
                null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "解析SIM卡短信失败", e)
            null
        }
    }
    
    /**
     * 解析SMS PDU格式
     */
    private fun parseSMSPDU(pduHex: String): ParsedSMS? {
        return try {
            // 这里需要实现完整的PDU解析逻辑
            // PDU格式比较复杂，包含发送者号码、时间戳、内容等
            
            if (pduHex.length < 20) {
                return null
            }
            
            // 简化的PDU解析
            var offset = 0
            
            // 跳过SMSC信息
            val smscLength = Integer.parseInt(pduHex.substring(offset, offset + 2), 16)
            offset += 2 + smscLength * 2
            
            // 读取PDU类型
            val pduType = Integer.parseInt(pduHex.substring(offset, offset + 2), 16)
            offset += 2
            
            // 读取发送者号码长度
            val senderLength = Integer.parseInt(pduHex.substring(offset, offset + 2), 16)
            offset += 2
            
            // 读取发送者号码类型
            val senderType = Integer.parseInt(pduHex.substring(offset, offset + 2), 16)
            offset += 2
            
            // 读取发送者号码
            val senderHex = pduHex.substring(offset, offset + senderLength)
            val sender = decodeBCDNumber(senderHex)
            offset += senderLength
            
            // 跳过协议标识和编码方式
            offset += 4
            
            // 读取时间戳
            val timestampHex = pduHex.substring(offset, offset + 14)
            val timestamp = decodePDUTimestamp(timestampHex)
            offset += 14
            
            // 读取内容长度
            val contentLength = Integer.parseInt(pduHex.substring(offset, offset + 2), 16)
            offset += 2
            
            // 读取内容
            val contentHex = pduHex.substring(offset)
            val content = decodeGSM7Bit(contentHex, contentLength)
            
            ParsedSMS(sender, content, timestamp)
            
        } catch (e: Exception) {
            Log.e(TAG, "PDU解析失败", e)
            null
        }
    }
    
    /**
     * 清理RIL Hook
     */
    private fun cleanupRILHook() {
        try {
            nativeCleanupRIL()
            Log.d(TAG, "✅ RIL Hook清理完成")
        } catch (e: Exception) {
            Log.e(TAG, "RIL Hook清理失败", e)
        }
    }
    
    // 辅助方法
    private fun loadRILLibrary(): Boolean {
        return try {
            // 检查RIL库文件是否存在，而不是直接加载
            val rilFiles = listOf(
                "/system/lib64/libril.so",
                "/system/lib/libril.so",
                "/vendor/lib64/libril.so",
                "/vendor/lib/libril.so"
            )

            val rilExists = rilFiles.any { path ->
                try {
                    java.io.File(path).exists()
                } catch (e: Exception) {
                    false
                }
            }

            if (rilExists) {
                Log.i(TAG, "✅ RIL库文件存在")
                true
            } else {
                Log.w(TAG, "⚠️ RIL库文件不存在")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查RIL库失败", e)
            false
        }
    }
    
    private fun hookRILFunctions(): Boolean {
        return try {
            nativeHookRIL()
        } catch (e: Exception) {
            Log.e(TAG, "Hook RIL函数失败", e)
            false
        }
    }
    
    private fun bytesToHex(bytes: ByteArray): String {
        return bytes.joinToString("") { "%02X".format(it) }
    }
    
    private fun decodeBCDNumber(hex: String): String {
        // 解码BCD格式的电话号码
        return hex.chunked(2).joinToString("") { 
            val byte = Integer.parseInt(it, 16)
            "${byte and 0x0F}${(byte shr 4) and 0x0F}"
        }.replace("F", "")
    }
    
    private fun decodePDUTimestamp(hex: String): Long {
        // 解码PDU时间戳
        return try {
            System.currentTimeMillis() // 简化实现
        } catch (e: Exception) {
            System.currentTimeMillis()
        }
    }
    
    private fun decodeGSM7Bit(hex: String, length: Int): String {
        // 解码GSM 7-bit编码的短信内容
        return try {
            val bytes = hex.chunked(2).map { Integer.parseInt(it, 16).toByte() }.toByteArray()
            String(bytes, Charsets.UTF_8)
        } catch (e: Exception) {
            "解码失败"
        }
    }
    
    private fun parseSimSMSFormat(data: ByteArray): ParsedSMS? {
        // 解析SIM卡特定格式的短信
        return try {
            // 简化实现
            ParsedSMS("Unknown", String(data), System.currentTimeMillis())
        } catch (e: Exception) {
            null
        }
    }
    
    // Native方法声明
    private external fun nativeHookRIL(): Boolean
    private external fun nativeCheckRILEvents(): Array<RILEvent>?
    private external fun nativeCleanupRIL()

    init {
        try {
            // 不加载不存在的native库，避免崩溃
            Log.i(TAG, "RIL拦截器初始化完成（跳过native库加载）")
        } catch (e: Exception) {
            Log.w(TAG, "RIL拦截器初始化失败", e)
        }
    }
}

// 数据类
data class RILEvent(
    val type: Int,
    val data: ByteArray
)

data class ParsedSMS(
    val sender: String,
    val content: String,
    val timestamp: Long
)
