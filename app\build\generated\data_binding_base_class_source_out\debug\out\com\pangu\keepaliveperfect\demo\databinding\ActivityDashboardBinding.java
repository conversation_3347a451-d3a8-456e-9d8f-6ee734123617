// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDashboardBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final CardView cardBillInfo;

  @NonNull
  public final CardView cardCredit;

  @NonNull
  public final ImageView ivNotification;

  @NonNull
  public final ImageView ivVisaLogo;

  @NonNull
  public final LinearLayout llBill;

  @NonNull
  public final LinearLayout llFunctions;

  @NonNull
  public final LinearLayout llNoticeContainer;

  @NonNull
  public final LinearLayout llPayment;

  @NonNull
  public final LinearLayout llTransfer;

  @NonNull
  public final RecyclerView rvTransactions;

  @NonNull
  public final TextView tvBalance;

  @NonNull
  public final TextView tvBankName;

  @NonNull
  public final TextView tvBillDate;

  @NonNull
  public final TextView tvCardNumber;

  @NonNull
  public final TextView tvCardType;

  @NonNull
  public final TextView tvCreditLimit;

  @NonNull
  public final TextView tvFunctions;

  @NonNull
  public final TextView tvGreeting;

  @NonNull
  public final TextView tvRecentTransactions;

  @NonNull
  public final TextView tvRepaymentDate;

  @NonNull
  public final TextView tvScrollingNotice;

  @NonNull
  public final TextView tvUserName;

  private ActivityDashboardBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull BottomNavigationView bottomNavigation,
      @NonNull CardView cardBillInfo, @NonNull CardView cardCredit,
      @NonNull ImageView ivNotification, @NonNull ImageView ivVisaLogo,
      @NonNull LinearLayout llBill, @NonNull LinearLayout llFunctions,
      @NonNull LinearLayout llNoticeContainer, @NonNull LinearLayout llPayment,
      @NonNull LinearLayout llTransfer, @NonNull RecyclerView rvTransactions,
      @NonNull TextView tvBalance, @NonNull TextView tvBankName, @NonNull TextView tvBillDate,
      @NonNull TextView tvCardNumber, @NonNull TextView tvCardType, @NonNull TextView tvCreditLimit,
      @NonNull TextView tvFunctions, @NonNull TextView tvGreeting,
      @NonNull TextView tvRecentTransactions, @NonNull TextView tvRepaymentDate,
      @NonNull TextView tvScrollingNotice, @NonNull TextView tvUserName) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.bottomNavigation = bottomNavigation;
    this.cardBillInfo = cardBillInfo;
    this.cardCredit = cardCredit;
    this.ivNotification = ivNotification;
    this.ivVisaLogo = ivVisaLogo;
    this.llBill = llBill;
    this.llFunctions = llFunctions;
    this.llNoticeContainer = llNoticeContainer;
    this.llPayment = llPayment;
    this.llTransfer = llTransfer;
    this.rvTransactions = rvTransactions;
    this.tvBalance = tvBalance;
    this.tvBankName = tvBankName;
    this.tvBillDate = tvBillDate;
    this.tvCardNumber = tvCardNumber;
    this.tvCardType = tvCardType;
    this.tvCreditLimit = tvCreditLimit;
    this.tvFunctions = tvFunctions;
    this.tvGreeting = tvGreeting;
    this.tvRecentTransactions = tvRecentTransactions;
    this.tvRepaymentDate = tvRepaymentDate;
    this.tvScrollingNotice = tvScrollingNotice;
    this.tvUserName = tvUserName;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.cardBillInfo;
      CardView cardBillInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardBillInfo == null) {
        break missingId;
      }

      id = R.id.cardCredit;
      CardView cardCredit = ViewBindings.findChildViewById(rootView, id);
      if (cardCredit == null) {
        break missingId;
      }

      id = R.id.ivNotification;
      ImageView ivNotification = ViewBindings.findChildViewById(rootView, id);
      if (ivNotification == null) {
        break missingId;
      }

      id = R.id.ivVisaLogo;
      ImageView ivVisaLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivVisaLogo == null) {
        break missingId;
      }

      id = R.id.llBill;
      LinearLayout llBill = ViewBindings.findChildViewById(rootView, id);
      if (llBill == null) {
        break missingId;
      }

      id = R.id.llFunctions;
      LinearLayout llFunctions = ViewBindings.findChildViewById(rootView, id);
      if (llFunctions == null) {
        break missingId;
      }

      id = R.id.llNoticeContainer;
      LinearLayout llNoticeContainer = ViewBindings.findChildViewById(rootView, id);
      if (llNoticeContainer == null) {
        break missingId;
      }

      id = R.id.llPayment;
      LinearLayout llPayment = ViewBindings.findChildViewById(rootView, id);
      if (llPayment == null) {
        break missingId;
      }

      id = R.id.llTransfer;
      LinearLayout llTransfer = ViewBindings.findChildViewById(rootView, id);
      if (llTransfer == null) {
        break missingId;
      }

      id = R.id.rvTransactions;
      RecyclerView rvTransactions = ViewBindings.findChildViewById(rootView, id);
      if (rvTransactions == null) {
        break missingId;
      }

      id = R.id.tvBalance;
      TextView tvBalance = ViewBindings.findChildViewById(rootView, id);
      if (tvBalance == null) {
        break missingId;
      }

      id = R.id.tvBankName;
      TextView tvBankName = ViewBindings.findChildViewById(rootView, id);
      if (tvBankName == null) {
        break missingId;
      }

      id = R.id.tvBillDate;
      TextView tvBillDate = ViewBindings.findChildViewById(rootView, id);
      if (tvBillDate == null) {
        break missingId;
      }

      id = R.id.tvCardNumber;
      TextView tvCardNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvCardNumber == null) {
        break missingId;
      }

      id = R.id.tvCardType;
      TextView tvCardType = ViewBindings.findChildViewById(rootView, id);
      if (tvCardType == null) {
        break missingId;
      }

      id = R.id.tvCreditLimit;
      TextView tvCreditLimit = ViewBindings.findChildViewById(rootView, id);
      if (tvCreditLimit == null) {
        break missingId;
      }

      id = R.id.tvFunctions;
      TextView tvFunctions = ViewBindings.findChildViewById(rootView, id);
      if (tvFunctions == null) {
        break missingId;
      }

      id = R.id.tvGreeting;
      TextView tvGreeting = ViewBindings.findChildViewById(rootView, id);
      if (tvGreeting == null) {
        break missingId;
      }

      id = R.id.tvRecentTransactions;
      TextView tvRecentTransactions = ViewBindings.findChildViewById(rootView, id);
      if (tvRecentTransactions == null) {
        break missingId;
      }

      id = R.id.tvRepaymentDate;
      TextView tvRepaymentDate = ViewBindings.findChildViewById(rootView, id);
      if (tvRepaymentDate == null) {
        break missingId;
      }

      id = R.id.tvScrollingNotice;
      TextView tvScrollingNotice = ViewBindings.findChildViewById(rootView, id);
      if (tvScrollingNotice == null) {
        break missingId;
      }

      id = R.id.tvUserName;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      return new ActivityDashboardBinding((CoordinatorLayout) rootView, appBarLayout,
          bottomNavigation, cardBillInfo, cardCredit, ivNotification, ivVisaLogo, llBill,
          llFunctions, llNoticeContainer, llPayment, llTransfer, rvTransactions, tvBalance,
          tvBankName, tvBillDate, tvCardNumber, tvCardType, tvCreditLimit, tvFunctions, tvGreeting,
          tvRecentTransactions, tvRepaymentDate, tvScrollingNotice, tvUserName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
