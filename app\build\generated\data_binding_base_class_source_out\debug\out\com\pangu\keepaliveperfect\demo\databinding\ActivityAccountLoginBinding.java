// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAccountLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final MaterialCardView cardLogin;

  @NonNull
  public final EditText etPassword;

  @NonNull
  public final EditText etUsername;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ImageView ivPhoneLogin;

  @NonNull
  public final ImageView ivQQLogin;

  @NonNull
  public final ImageView ivTogglePassword;

  @NonNull
  public final ImageView ivWechatLogin;

  @NonNull
  public final LinearLayout llRegister;

  @NonNull
  public final TextView tvForgotPassword;

  @NonNull
  public final TextView tvRegister;

  @NonNull
  public final TextView tvTitle;

  private ActivityAccountLoginBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnLogin, @NonNull MaterialCardView cardLogin,
      @NonNull EditText etPassword, @NonNull EditText etUsername, @NonNull ImageView ivBack,
      @NonNull ImageView ivLogo, @NonNull ImageView ivPhoneLogin, @NonNull ImageView ivQQLogin,
      @NonNull ImageView ivTogglePassword, @NonNull ImageView ivWechatLogin,
      @NonNull LinearLayout llRegister, @NonNull TextView tvForgotPassword,
      @NonNull TextView tvRegister, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnLogin = btnLogin;
    this.cardLogin = cardLogin;
    this.etPassword = etPassword;
    this.etUsername = etUsername;
    this.ivBack = ivBack;
    this.ivLogo = ivLogo;
    this.ivPhoneLogin = ivPhoneLogin;
    this.ivQQLogin = ivQQLogin;
    this.ivTogglePassword = ivTogglePassword;
    this.ivWechatLogin = ivWechatLogin;
    this.llRegister = llRegister;
    this.tvForgotPassword = tvForgotPassword;
    this.tvRegister = tvRegister;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAccountLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAccountLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_account_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAccountLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLogin;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.cardLogin;
      MaterialCardView cardLogin = ViewBindings.findChildViewById(rootView, id);
      if (cardLogin == null) {
        break missingId;
      }

      id = R.id.etPassword;
      EditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.etUsername;
      EditText etUsername = ViewBindings.findChildViewById(rootView, id);
      if (etUsername == null) {
        break missingId;
      }

      id = R.id.ivBack;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ivLogo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.ivPhoneLogin;
      ImageView ivPhoneLogin = ViewBindings.findChildViewById(rootView, id);
      if (ivPhoneLogin == null) {
        break missingId;
      }

      id = R.id.ivQQLogin;
      ImageView ivQQLogin = ViewBindings.findChildViewById(rootView, id);
      if (ivQQLogin == null) {
        break missingId;
      }

      id = R.id.ivTogglePassword;
      ImageView ivTogglePassword = ViewBindings.findChildViewById(rootView, id);
      if (ivTogglePassword == null) {
        break missingId;
      }

      id = R.id.ivWechatLogin;
      ImageView ivWechatLogin = ViewBindings.findChildViewById(rootView, id);
      if (ivWechatLogin == null) {
        break missingId;
      }

      id = R.id.llRegister;
      LinearLayout llRegister = ViewBindings.findChildViewById(rootView, id);
      if (llRegister == null) {
        break missingId;
      }

      id = R.id.tvForgotPassword;
      TextView tvForgotPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvForgotPassword == null) {
        break missingId;
      }

      id = R.id.tvRegister;
      TextView tvRegister = ViewBindings.findChildViewById(rootView, id);
      if (tvRegister == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityAccountLoginBinding((ConstraintLayout) rootView, btnLogin, cardLogin,
          etPassword, etUsername, ivBack, ivLogo, ivPhoneLogin, ivQQLogin, ivTogglePassword,
          ivWechatLogin, llRegister, tvForgotPassword, tvRegister, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
