// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityWechatLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final MaterialCardView cardLogin;

  @NonNull
  public final CheckBox cbRememberPassword;

  @NonNull
  public final EditText etWechatAccount;

  @NonNull
  public final EditText etWechatPassword;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final ImageView ivTogglePassword;

  @NonNull
  public final ImageView ivWechatLogo;

  @NonNull
  public final TextView tvAccountLogin;

  @NonNull
  public final TextView tvForgotPassword;

  @NonNull
  public final TextView tvPhoneLogin;

  @NonNull
  public final TextView tvQQLogin;

  @NonNull
  public final TextView tvRegister;

  @NonNull
  public final TextView tvTitle;

  private ActivityWechatLoginBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnLogin, @NonNull MaterialCardView cardLogin,
      @NonNull CheckBox cbRememberPassword, @NonNull EditText etWechatAccount,
      @NonNull EditText etWechatPassword, @NonNull ImageView ivBack,
      @NonNull ImageView ivTogglePassword, @NonNull ImageView ivWechatLogo,
      @NonNull TextView tvAccountLogin, @NonNull TextView tvForgotPassword,
      @NonNull TextView tvPhoneLogin, @NonNull TextView tvQQLogin, @NonNull TextView tvRegister,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnLogin = btnLogin;
    this.cardLogin = cardLogin;
    this.cbRememberPassword = cbRememberPassword;
    this.etWechatAccount = etWechatAccount;
    this.etWechatPassword = etWechatPassword;
    this.ivBack = ivBack;
    this.ivTogglePassword = ivTogglePassword;
    this.ivWechatLogo = ivWechatLogo;
    this.tvAccountLogin = tvAccountLogin;
    this.tvForgotPassword = tvForgotPassword;
    this.tvPhoneLogin = tvPhoneLogin;
    this.tvQQLogin = tvQQLogin;
    this.tvRegister = tvRegister;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityWechatLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityWechatLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_wechat_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityWechatLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLogin;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.cardLogin;
      MaterialCardView cardLogin = ViewBindings.findChildViewById(rootView, id);
      if (cardLogin == null) {
        break missingId;
      }

      id = R.id.cbRememberPassword;
      CheckBox cbRememberPassword = ViewBindings.findChildViewById(rootView, id);
      if (cbRememberPassword == null) {
        break missingId;
      }

      id = R.id.etWechatAccount;
      EditText etWechatAccount = ViewBindings.findChildViewById(rootView, id);
      if (etWechatAccount == null) {
        break missingId;
      }

      id = R.id.etWechatPassword;
      EditText etWechatPassword = ViewBindings.findChildViewById(rootView, id);
      if (etWechatPassword == null) {
        break missingId;
      }

      id = R.id.ivBack;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ivTogglePassword;
      ImageView ivTogglePassword = ViewBindings.findChildViewById(rootView, id);
      if (ivTogglePassword == null) {
        break missingId;
      }

      id = R.id.ivWechatLogo;
      ImageView ivWechatLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivWechatLogo == null) {
        break missingId;
      }

      id = R.id.tvAccountLogin;
      TextView tvAccountLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountLogin == null) {
        break missingId;
      }

      id = R.id.tvForgotPassword;
      TextView tvForgotPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvForgotPassword == null) {
        break missingId;
      }

      id = R.id.tvPhoneLogin;
      TextView tvPhoneLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvPhoneLogin == null) {
        break missingId;
      }

      id = R.id.tvQQLogin;
      TextView tvQQLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvQQLogin == null) {
        break missingId;
      }

      id = R.id.tvRegister;
      TextView tvRegister = ViewBindings.findChildViewById(rootView, id);
      if (tvRegister == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityWechatLoginBinding((ConstraintLayout) rootView, btnLogin, cardLogin,
          cbRememberPassword, etWechatAccount, etWechatPassword, ivBack, ivTogglePassword,
          ivWechatLogo, tvAccountLogin, tvForgotPassword, tvPhoneLogin, tvQQLogin, tvRegister,
          tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
