<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="18dp"
    android:height="18dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <!-- 微信绿色背景圆形 -->
    <path
        android:fillColor="#07C160"
        android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2z"/>
    <!-- 微信图标 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M8.5,7.5C7.12,7.5 6,8.62 6,10c0,0.78 0.45,1.47 1.11,1.8l-0.31,1.04l1.13,-0.61c0.52,0.11 1.07,0.11 1.57,-0.23c1.38,0 2.5,-1.12 2.5,-2.5S9.88,7.5 8.5,7.5zM7.5,9.5c-0.28,0 -0.5,-0.22 -0.5,-0.5s0.22,-0.5 0.5,-0.5 0.5,0.22 0.5,0.5 -0.22,0.5 -0.5,0.5zM9.5,9.5c-0.28,0 -0.5,-0.22 -0.5,-0.5s0.22,-0.5 0.5,-0.5 0.5,0.22 0.5,0.5 -0.22,0.5 -0.5,0.5zM15.5,11.5c-1.38,0 -2.5,1.12 -2.5,2.5s1.12,2.5 2.5,2.5c0.28,0 0.55,-0.05 0.8,-0.14l1.13,0.61 -0.31,-1.04c0.66,-0.33 1.11,-1.02 1.11,-1.8 0,-1.38 -1.12,-2.5 -2.5,-2.5h-0.23zM14.5,14c-0.28,0 -0.5,-0.22 -0.5,-0.5s0.22,-0.5 0.5,-0.5 0.5,0.22 0.5,0.5 -0.22,0.5 -0.5,0.5zM16.5,14c-0.28,0 -0.5,-0.22 -0.5,-0.5s0.22,-0.5 0.5,-0.5 0.5,0.22 0.5,0.5 -0.22,0.5 -0.5,0.5z"/>
</vector>
