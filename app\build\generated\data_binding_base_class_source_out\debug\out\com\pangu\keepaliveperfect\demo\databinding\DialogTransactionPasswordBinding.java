// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTransactionPasswordBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnConfirm;

  @NonNull
  public final View divider;

  @NonNull
  public final TextInputEditText etTransactionPassword;

  @NonNull
  public final TextInputLayout tilTransactionPassword;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvTransactionInfo;

  private DialogTransactionPasswordBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnConfirm, @NonNull View divider,
      @NonNull TextInputEditText etTransactionPassword,
      @NonNull TextInputLayout tilTransactionPassword, @NonNull TextView tvTitle,
      @NonNull TextView tvTransactionInfo) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.divider = divider;
    this.etTransactionPassword = etTransactionPassword;
    this.tilTransactionPassword = tilTransactionPassword;
    this.tvTitle = tvTitle;
    this.tvTransactionInfo = tvTransactionInfo;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTransactionPasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTransactionPasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_transaction_password, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTransactionPasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      MaterialButton btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.etTransactionPassword;
      TextInputEditText etTransactionPassword = ViewBindings.findChildViewById(rootView, id);
      if (etTransactionPassword == null) {
        break missingId;
      }

      id = R.id.tilTransactionPassword;
      TextInputLayout tilTransactionPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilTransactionPassword == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tvTransactionInfo;
      TextView tvTransactionInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionInfo == null) {
        break missingId;
      }

      return new DialogTransactionPasswordBinding((ConstraintLayout) rootView, btnCancel,
          btnConfirm, divider, etTransactionPassword, tilTransactionPassword, tvTitle,
          tvTransactionInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
