<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_transaction_password" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_transaction_password.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_transaction_password_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="99" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="21" startOffset="4" endLine="27" endOffset="60"/></Target><Target id="@+id/tvTransactionInfo" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="40" endOffset="60"/></Target><Target id="@+id/tilTransactionPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="42" startOffset="4" endLine="64" endOffset="59"/></Target><Target id="@+id/etTransactionPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="58" startOffset="8" endLine="63" endOffset="34"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="66" startOffset="4" endLine="81" endOffset="44"/></Target><Target id="@+id/btnConfirm" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="83" startOffset="4" endLine="97" endOffset="75"/></Target></Targets></Layout>