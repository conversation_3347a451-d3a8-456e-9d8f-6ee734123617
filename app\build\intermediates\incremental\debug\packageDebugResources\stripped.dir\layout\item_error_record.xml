<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="4dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/timeTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="时间: 2023-05-01 12:34:56"
            android:textStyle="bold"
            android:layout_marginBottom="4dp"/>

        <TextView
            android:id="@+id/loginTypeTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="登录类型: 账号密码登录"
            android:layout_marginBottom="8dp"/>

        <LinearLayout
            android:id="@+id/errorDataContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            
            <!-- 错误数据将在代码中动态添加 -->
            
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
