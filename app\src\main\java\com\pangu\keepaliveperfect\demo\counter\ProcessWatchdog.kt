package com.pangu.keepaliveperfect.demo.counter

import android.content.Context
import android.content.Intent
import android.os.Process
import android.util.Log
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 进程看门狗
 * 持续监控主进程状态，一旦发现异常立即重启
 */
class ProcessWatchdog private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ProcessWatchdog"
        private const val CHECK_INTERVAL = 2000L // 2秒检查一次
        
        @Volatile
        private var INSTANCE: ProcessWatchdog? = null
        
        fun getInstance(context: Context): ProcessWatchdog {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ProcessWatchdog(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val isWatching = AtomicBoolean(false)
    private val mainProcessPid = Process.myPid()
    private var lastHeartbeat = System.currentTimeMillis()
    
    /**
     * 启动看门狗
     */
    fun startWatching() {
        if (isWatching.compareAndSet(false, true)) {
            Log.i(TAG, "🐕 启动进程看门狗，监控PID: $mainProcessPid")
            
            // 启动心跳线程
            startHeartbeatThread()
            
            // 启动监控线程
            startWatchdogThread()
        }
    }
    
    /**
     * 停止看门狗
     */
    fun stopWatching() {
        if (isWatching.compareAndSet(true, false)) {
            Log.i(TAG, "🛑 停止进程看门狗")
        }
    }
    
    /**
     * 更新心跳
     */
    fun updateHeartbeat() {
        lastHeartbeat = System.currentTimeMillis()
    }
    
    /**
     * 启动心跳线程
     */
    private fun startHeartbeatThread() {
        Thread {
            Log.d(TAG, "💓 心跳线程启动")
            
            while (isWatching.get()) {
                try {
                    // 更新心跳
                    updateHeartbeat()
                    
                    // 写入心跳文件
                    writeHeartbeatFile()
                    
                    // 每秒更新一次心跳
                    Thread.sleep(1000)
                    
                } catch (e: InterruptedException) {
                    Log.d(TAG, "心跳线程被中断")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "心跳线程异常", e)
                }
            }
            
            Log.d(TAG, "💓 心跳线程结束")
        }.start()
    }
    
    /**
     * 启动监控线程
     */
    private fun startWatchdogThread() {
        Thread {
            Log.d(TAG, "🔍 看门狗监控线程启动")
            
            while (isWatching.get()) {
                try {
                    // 检查主进程状态
                    checkMainProcessStatus()
                    
                    // 检查心跳状态
                    checkHeartbeatStatus()
                    
                    // 检查系统威胁
                    checkSystemThreats()
                    
                    Thread.sleep(CHECK_INTERVAL)
                    
                } catch (e: InterruptedException) {
                    Log.d(TAG, "看门狗线程被中断")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "看门狗线程异常", e)
                    Thread.sleep(5000) // 出错时等待更长时间
                }
            }
            
            Log.d(TAG, "🔍 看门狗监控线程结束")
        }.start()
    }
    
    /**
     * 检查主进程状态
     */
    private fun checkMainProcessStatus() {
        try {
            // 检查进程是否存在
            val procFile = File("/proc/$mainProcessPid")
            if (!procFile.exists()) {
                Log.w(TAG, "⚠️ 主进程 $mainProcessPid 不存在")
                handleProcessDeath("进程文件不存在")
                return
            }
            
            // 检查进程状态
            val statFile = File("/proc/$mainProcessPid/stat")
            if (statFile.exists()) {
                val stat = statFile.readText()
                val parts = stat.split(" ")
                
                if (parts.size > 2) {
                    val state = parts[2]
                    when (state) {
                        "Z" -> {
                            Log.w(TAG, "⚠️ 主进程变为僵尸进程")
                            handleProcessDeath("进程变为僵尸状态")
                        }
                        "T" -> {
                            Log.w(TAG, "⚠️ 主进程被暂停")
                            handleProcessSuspended()
                        }
                        "D" -> {
                            Log.w(TAG, "⚠️ 主进程处于不可中断睡眠状态")
                            // 这可能是正常的，但需要监控
                        }
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查主进程状态失败", e)
        }
    }
    
    /**
     * 检查心跳状态
     */
    private fun checkHeartbeatStatus() {
        try {
            val currentTime = System.currentTimeMillis()
            val timeSinceLastHeartbeat = currentTime - lastHeartbeat
            
            // 如果超过10秒没有心跳，认为进程可能有问题
            if (timeSinceLastHeartbeat > 10000) {
                Log.w(TAG, "⚠️ 心跳超时: ${timeSinceLastHeartbeat}ms")
                handleHeartbeatTimeout()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查心跳状态失败", e)
        }
    }
    
    /**
     * 检查系统威胁
     */
    private fun checkSystemThreats() {
        try {
            // 检查是否有SafeCenter活动
            checkSafeCenterActivity()
            
            // 检查内存压力
            checkMemoryPressure()
            
        } catch (e: Exception) {
            Log.e(TAG, "检查系统威胁失败", e)
        }
    }
    
    /**
     * 检查SafeCenter活动
     */
    private fun checkSafeCenterActivity() {
        try {
            // 检查SafeCenter进程
            val procDir = File("/proc")
            val processes = procDir.listFiles { file ->
                file.isDirectory && file.name.matches(Regex("\\d+"))
            }
            
            processes?.forEach { processDir ->
                try {
                    val cmdlineFile = File(processDir, "cmdline")
                    if (cmdlineFile.exists()) {
                        val processName = cmdlineFile.readText().trim('\u0000')
                        
                        if (processName.contains("safecenter", ignoreCase = true) ||
                            processName.contains("oppoguardelf", ignoreCase = true)) {
                            
                            Log.w(TAG, "⚠️ 检测到威胁进程: $processName")
                            recordThreat("SafeCenter活动", processName)
                        }
                    }
                } catch (e: Exception) {
                    // 忽略无法访问的进程
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查SafeCenter活动失败", e)
        }
    }
    
    /**
     * 检查内存压力
     */
    private fun checkMemoryPressure() {
        try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val memoryUsagePercent = (usedMemory * 100) / totalMemory
            
            if (memoryUsagePercent > 90) {
                Log.w(TAG, "⚠️ 内存使用率过高: $memoryUsagePercent%")
                recordThreat("内存压力", "内存使用率: $memoryUsagePercent%")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检查内存压力失败", e)
        }
    }
    
    /**
     * 处理进程死亡
     */
    private fun handleProcessDeath(reason: String) {
        Log.w(TAG, "🚨 处理进程死亡: $reason")
        
        recordThreat("进程死亡", reason)
        
        // 立即重启应用
        restartApplication()
    }
    
    /**
     * 处理进程被暂停
     */
    private fun handleProcessSuspended() {
        Log.w(TAG, "🚨 处理进程暂停")
        
        recordThreat("进程暂停", "进程被系统暂停")
        
        // 尝试恢复进程
        try {
            Runtime.getRuntime().exec("kill -CONT $mainProcessPid")
            Log.i(TAG, "尝试恢复暂停的进程")
        } catch (e: Exception) {
            Log.e(TAG, "恢复进程失败", e)
        }
    }
    
    /**
     * 处理心跳超时
     */
    private fun handleHeartbeatTimeout() {
        Log.w(TAG, "🚨 处理心跳超时")
        
        recordThreat("心跳超时", "主线程可能被阻塞")
        
        // 可以考虑重启应用或其他恢复措施
    }
    
    /**
     * 写入心跳文件
     */
    private fun writeHeartbeatFile() {
        try {
            val heartbeatFile = File(context.filesDir, "heartbeat.txt")
            heartbeatFile.writeText("${System.currentTimeMillis()}\n$mainProcessPid")
        } catch (e: Exception) {
            // 心跳文件写入失败不影响主要功能
        }
    }
    
    /**
     * 记录威胁
     */
    private fun recordThreat(threatType: String, details: String) {
        try {
            val threatLog = buildString {
                appendLine("=== 看门狗威胁记录 ===")
                appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                appendLine("威胁类型: $threatType")
                appendLine("详细信息: $details")
                appendLine("主进程PID: $mainProcessPid")
                appendLine("=== 记录结束 ===")
                appendLine()
            }
            
            // 写入本地文件
            val logFile = File(context.filesDir, "watchdog_threats.log")
            logFile.appendText(threatLog)
            
            // 上传到七牛云
            Thread {
                try {
                    val fileName = "watchdog_threat_${System.currentTimeMillis()}.log"
                    com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadThreatLog(
                        context,
                        threatLog,
                        fileName
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "上传威胁日志失败", e)
                }
            }.start()
            
        } catch (e: Exception) {
            Log.e(TAG, "记录威胁失败", e)
        }
    }
    
    /**
     * 重启应用
     */
    private fun restartApplication() {
        try {
            Log.i(TAG, "🔄 看门狗重启应用")
            
            val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
            intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            
            context.startActivity(intent)
            
            // 杀死当前进程
            Process.killProcess(Process.myPid())
            
        } catch (e: Exception) {
            Log.e(TAG, "看门狗重启应用失败", e)
        }
    }
}
