package com.pangu.keepaliveperfect.demo.service

import android.app.ActivityManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.pangu.keepaliveperfect.demo.KeepAliveConfig
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

/**
 * 守护服务
 * 负责检测和维持主服务的运行状态
 */
class DaemonService : Service() {

    companion object {
        private const val TAG = "DaemonService"
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "wx_service_channel"  // 使用与系统安全扫描相同的渠道
        private const val CHECK_INTERVAL = 300L // 检查间隔（秒）- 5分钟
    }

    private var scheduler: ScheduledExecutorService? = null
    private var isServiceRunning = false

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "守护服务创建")

        // 创建前台通知
        startForeground(NOTIFICATION_ID, createNotification())

        // 初始化调度器
        scheduler = Executors.newSingleThreadScheduledExecutor()

        // 标记服务正在运行
        isServiceRunning = true
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "守护服务启动")

        // 启动守护任务
        startDaemonTask()

        // 确保前台通知始终存在
        startForeground(NOTIFICATION_ID, createNotification())

        // 返回START_STICKY使服务在被杀死后自动重启
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        Log.d(TAG, "守护服务销毁，尝试重启主服务和自身")
        isServiceRunning = false

        // 停止调度器
        scheduler?.shutdown()

        try {
            // 尝试在服务销毁前启动主服务
            val serviceIntent = Intent(this, KeepAliveService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }

            // 触发广播来重启自身
            val restartIntent = Intent("com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE")
            sendBroadcast(restartIntent)
        } catch (e: Exception) {
            Log.e(TAG, "在销毁时重启服务失败: ${e.message}")
        }

        super.onDestroy()
    }

    /**
     * 创建前台通知（使用系统更新类型）
     */
    private fun createNotification(): Notification {
        // 使用KeepAliveConfig创建系统更新类型的通知
        // 这样可以确保与系统安全扫描通知相同的技术实现，但显示不同的内容
        return KeepAliveConfig.createNotification(this, KeepAliveConfig.NotificationType.SYSTEM_UPDATE)
    }

    /**
     * 启动守护任务
     */
    private fun startDaemonTask() {
        scheduler?.scheduleAtFixedRate({
            try {
                if (isServiceRunning) {
                    Log.d(TAG, "执行守护任务检查")

                    // 检查主服务是否运行
                    if (!isServiceRunning(KeepAliveService::class.java.name)) {
                        Log.d(TAG, "主服务不在运行，尝试启动")
                        startKeepAliveService()
                    }

                    // 检查通知监听服务
                    if (NotificationAccessHelper.isNotificationAccessEnabled(this)) {
                        NotificationAccessHelper.refreshNotificationListenerConnection(this)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "守护任务执行异常: ${e.message}")
            }
        }, 5, CHECK_INTERVAL, TimeUnit.SECONDS)
    }

    /**
     * 启动主保活服务
     */
    private fun startKeepAliveService() {
        try {
            val serviceIntent = Intent(this, KeepAliveService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }
            Log.d(TAG, "已从守护服务启动主服务")
        } catch (e: Exception) {
            Log.e(TAG, "从守护服务启动主服务失败: ${e.message}")
        }
    }

    /**
     * 检查服务是否在运行
     */
    private fun isServiceRunning(serviceClassName: String): Boolean {
        try {
            val am = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = am.getRunningServices(100)

            for (service in runningServices) {
                if (service.service.className == serviceClassName) {
                    return true
                }
            }
            return false
        } catch (e: Exception) {
            Log.e(TAG, "检查服务运行状态失败: ${e.message}")
            return false
        }
    }
}