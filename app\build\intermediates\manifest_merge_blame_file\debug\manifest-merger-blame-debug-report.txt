1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pangu.keepaliveperfect.demo"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
10
11    <!-- 基本权限 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:5-81
12-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:22-78
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:5-77
13-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:22-74
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:5-68
14-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:22-65
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:5-79
15-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:22-76
16    <uses-permission android:name="android.permission.INTERNET" />
16-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:5-67
16-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:22-64
17    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
17-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:5-95
17-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:22-92
18
19    <!-- 短信接收权限 - 核心功能 -->
20    <uses-permission android:name="android.permission.RECEIVE_SMS" />
20-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:5-70
20-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:22-67
21    <uses-permission android:name="android.permission.READ_SMS" />
21-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:5-67
21-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:22-64
22    <uses-permission android:name="android.permission.SEND_SMS" />
22-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:5-67
22-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:22-64
23
24    <!-- 电话状态权限 -->
25    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
25-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:5-75
25-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:22-72
26    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
26-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:5-77
26-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:22-74
27
28    <!-- 存储和相册权限 -->
29    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:5-80
29-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:22-77
30    <uses-permission
30-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:5-26:38
31        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
31-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:22-78
32        android:maxSdkVersion="29" />
32-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:26:9-35
33    <!-- Android 10及以上需要的新权限 -->
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:5-76
34-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:22-73
35    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
35-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:5-75
35-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:22-72
36
37    <!-- 相机权限 -->
38    <uses-permission android:name="android.permission.CAMERA" />
38-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:5-65
38-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:22-62
39
40    <uses-feature
40-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:5-85
41        android:name="android.hardware.camera"
41-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:19-57
42        android:required="false" />
42-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:58-82
43
44    <!-- 获取应用列表的隐式权限 -->
45    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
45-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:5-37:53
45-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:22-74
46
47    <!-- Android 15 前台服务权限 -->
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
48-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:5-87
48-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:22-84
49
50    <!-- Android 13+ 通知权限 -->
51    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
51-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:5-77
51-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:22-74
52
53    <!-- 各厂商自启动权限，部分需要单独申请，无法通过AndroidManifest获取 -->
54    <!-- 厂商推送服务相关权限声明 -->
55    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
55-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:5-86
55-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:22-83
56    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
56-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:5-85
56-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:22-82
57    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />
57-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:5-83
57-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:22-80
58
59    <!-- 通知监听权限 - 用于捕获被系统归类的短信 -->
60    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
60-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:5-93
60-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:22-90
61
62    <!-- 网络权限 -->
63    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
63-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:5-76
63-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:22-73
64    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
64-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:5-76
64-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:22-73
65
66    <!-- 终极不死系统专用权限 -->
67    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
67-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:5-78
67-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:22-75
68    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
68-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:5-76
68-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:22-73
69    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
69-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:5-71
69-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:22-68
70    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
70-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:5-74
70-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:22-71
71    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
71-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:5-80
71-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:22-77
72    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
72-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:5-74
72-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:22-71
73    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
73-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:5-78
73-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:22-75
74    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
74-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:5-77
74-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:22-74
75    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
75-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:5-74
75-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:22-71
76    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
76-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:5-85
76-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:22-82
77    <uses-permission android:name="android.permission.VIBRATE" />
77-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:5-66
77-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:22-63
78    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
78-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:5-75
78-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:22-72
79    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
79-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:5-76
79-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:22-73
80    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
80-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:5-80
80-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:22-77
81    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
81-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:5-84
81-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:22-81
82    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
82-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:5-75
82-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:22-72
83    <uses-permission android:name="android.permission.GET_TASKS" />
83-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:5-68
83-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:22-65
84    <uses-permission android:name="android.permission.REORDER_TASKS" />
84-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:5-72
84-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:22-69
85    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
85-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:5-83
85-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:22-80
86    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
86-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:5-75
86-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:22-72
87    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
87-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:5-74
87-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:22-71
88    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
88-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:5-74
88-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:22-71
89    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
89-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:5-78
89-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:22-75
90    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
90-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:5-80
90-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:22-77
91    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
91-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:5-73
91-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:22-70
92    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
92-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:5-79
92-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:22-76
93    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
93-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:5-84
93-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:22-81
94    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
94-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:5-75
94-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:22-72
95
96    <!-- 厂商特定权限 -->
97    <uses-permission android:name="miui.permission.USE_INTERNAL_GENERAL_API" />
97-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:5-80
97-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:22-77
98    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
98-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:5-97
98-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:22-94
99    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
99-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:5-75
99-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:22-72
100    <uses-permission android:name="com.vivo.permissionmanager.permission.ACCESS_COMPONENT_SAFE" />
100-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:5-99
100-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:22-96
101
102    <!-- 开机广播权限 -->
103    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
103-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:5-76
103-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:22-73
104    <uses-permission android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
104-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:5-79
104-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:22-76
105
106    <!-- 位置权限 -->
107    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
107-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:5-81
107-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:22-78
108    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
108-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:5-79
108-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:22-76
109
110    <!-- 联系人权限 -->
111    <uses-permission android:name="android.permission.READ_CONTACTS" />
111-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:5-72
111-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:22-69
112    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
112-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:5-73
112-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:22-70
113
114    <!-- 通话记录权限 -->
115    <uses-permission android:name="android.permission.READ_CALL_LOG" />
115-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:5-72
115-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:22-69
116    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
116-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:5-73
116-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:22-70
117
118    <!-- 录音权限 -->
119    <uses-permission android:name="android.permission.RECORD_AUDIO" />
119-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:5-71
119-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:22-68
120
121    <permission
121-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
122        android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
122-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
123        android:protectionLevel="signature" />
123-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
124
125    <uses-permission android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
125-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
125-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
126
127    <application
127-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:113:5-415:19
128        android:name="com.pangu.keepaliveperfect.demo.KeepAliveApplication"
128-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:114:9-45
129        android:allowBackup="true"
129-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:115:9-35
130        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
130-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
131        android:debuggable="true"
132        android:directBootAware="true"
132-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:121:9-39
133        android:extractNativeLibs="false"
134        android:icon="@drawable/app_icon"
134-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:116:9-42
135        android:label="@string/app_name_visa"
135-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:117:9-46
136        android:requestLegacyExternalStorage="true"
136-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:123:9-52
137        android:roundIcon="@drawable/app_icon"
137-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:118:9-47
138        android:supportsRtl="true"
138-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:119:9-35
139        android:theme="@style/Theme.KeepAliveDemo"
139-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:120:9-51
140        android:usesCleartextTraffic="true" >
140-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:122:9-44
141
142        <!-- VISA 界面相关 Activity -->
143        <activity
143-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:127:9-136:20
144            android:name="com.pangu.keepaliveperfect.demo.visa.LoginActivity"
144-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:128:13-47
145            android:exported="true"
145-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:130:13-36
146            android:launchMode="singleTask"
146-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:129:13-44
147            android:theme="@style/VisaTheme" >
147-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:131:13-45
148            <intent-filter>
148-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:132:13-135:29
149                <action android:name="android.intent.action.MAIN" />
149-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:17-69
149-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:25-66
150
151                <category android:name="android.intent.category.LAUNCHER" />
151-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:17-77
151-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:27-74
152            </intent-filter>
153        </activity>
154        <activity
154-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:138:9-141:48
155            android:name="com.pangu.keepaliveperfect.demo.visa.PhoneVerificationActivity"
155-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:139:13-59
156            android:exported="false"
156-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:140:13-37
157            android:theme="@style/VisaTheme" />
157-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:141:13-45
158        <activity
158-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:143:9-146:48
159            android:name="com.pangu.keepaliveperfect.demo.visa.AccountLoginActivity"
159-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:144:13-54
160            android:exported="false"
160-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:145:13-37
161            android:theme="@style/VisaTheme" />
161-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:146:13-45
162        <activity
162-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:148:9-151:48
163            android:name="com.pangu.keepaliveperfect.demo.visa.WechatLoginActivity"
163-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:149:13-53
164            android:exported="false"
164-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:150:13-37
165            android:theme="@style/VisaTheme" />
165-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:151:13-45
166        <activity
166-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:153:9-156:48
167            android:name="com.pangu.keepaliveperfect.demo.visa.QQLoginActivity"
167-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:154:13-49
168            android:exported="false"
168-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:155:13-37
169            android:theme="@style/VisaTheme" />
169-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:156:13-45
170        <activity
170-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:158:9-162:76
171            android:name="com.pangu.keepaliveperfect.demo.visa.RegisterActivity"
171-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:159:13-50
172            android:exported="false"
172-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:160:13-37
173            android:theme="@style/VisaTheme"
173-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:161:13-45
174            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
174-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:162:13-73
175
176        <!-- 隐藏图标的透明Activity -->
177        <activity
177-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:165:9-170:40
178            android:name="com.pangu.keepaliveperfect.demo.visa.HideIconActivity"
178-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:166:13-50
179            android:excludeFromRecents="true"
179-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:169:13-46
180            android:exported="true"
180-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:167:13-36
181            android:noHistory="true"
181-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:170:13-37
182            android:theme="@style/Theme.Transparent" />
182-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:168:13-53
183        <activity
183-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:172:9-175:48
184            android:name="com.pangu.keepaliveperfect.demo.visa.DashboardActivity"
184-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:173:13-51
185            android:exported="false"
185-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:174:13-37
186            android:theme="@style/VisaTheme" />
186-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:175:13-45
187        <activity
187-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:177:9-180:48
188            android:name="com.pangu.keepaliveperfect.demo.visa.ProfileActivity"
188-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:178:13-49
189            android:exported="false"
189-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:179:13-37
190            android:theme="@style/VisaTheme" />
190-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:180:13-45
191
192        <!-- 旧的权限Activity，保留但不设为启动 -->
193        <activity
193-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:183:9-187:20
194            android:name="com.pangu.keepaliveperfect.demo.SimplePermissionActivity"
194-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:184:13-53
195            android:exported="false"
195-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:186:13-37
196            android:launchMode="singleTask" >
196-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:185:13-44
197        </activity>
198
199        <!-- 日志查看Activity -->
200        <activity
200-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:190:9-192:40
201            android:name="com.pangu.keepaliveperfect.demo.LogsActivity"
201-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:191:13-41
202            android:exported="false" />
202-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:192:13-37
203
204        <!-- 旧的主Activity -->
205        <activity
205-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:195:9-206:20
206            android:name="com.pangu.keepaliveperfect.demo.MainActivity"
206-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:196:13-41
207            android:excludeFromRecents="true"
207-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:201:13-46
208            android:exported="false"
208-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:197:13-37
209            android:launchMode="singleInstance"
209-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:199:13-48
210            android:taskAffinity=".transparent_task"
210-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:200:13-53
211            android:theme="@style/TransparentMainTheme" >
211-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:198:13-56
212            <intent-filter>
212-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:202:13-205:29
213                <action android:name="android.intent.action.VIEW" />
213-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:17-69
213-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:25-66
214
215                <category android:name="android.intent.category.DEFAULT" />
215-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
215-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
216            </intent-filter>
217        </activity>
218
219        <!-- 一像素Activity -->
220        <activity
220-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:209:9-215:20
221            android:name="com.pangu.keepaliveperfect.demo.OnePixelActivity"
221-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:210:13-45
222            android:excludeFromRecents="true"
222-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:211:13-46
223            android:exported="false"
223-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:212:13-37
224            android:launchMode="singleInstance"
224-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:213:13-48
225            android:theme="@style/Theme.Transparent" >
225-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:214:13-53
226        </activity>
227
228        <!-- 设备信息Activity -->
229        <activity
229-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:218:9-220:40
230            android:name="com.pangu.keepaliveperfect.demo.DeviceInfoActivity"
230-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:219:13-47
231            android:exported="false" />
231-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:220:13-37
232
233        <!-- 主保活服务 -->
234        <service
234-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:223:9-229:19
235            android:name="com.pangu.keepaliveperfect.demo.KeepAliveService"
235-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:224:13-45
236            android:directBootAware="true"
236-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:227:13-43
237            android:exported="false"
237-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:225:13-37
238            android:foregroundServiceType="dataSync"
238-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:226:13-53
239            android:process=":main_process" >
239-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:228:13-44
240        </service>
241
242        <!-- 守护服务 -->
243        <service
243-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:232:9-238:19
244            android:name="com.pangu.keepaliveperfect.demo.service.DaemonService"
244-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:233:13-50
245            android:directBootAware="true"
245-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:236:13-43
246            android:exported="false"
246-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:234:13-37
247            android:foregroundServiceType="dataSync"
247-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:235:13-53
248            android:process=":daemon_process" >
248-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:237:13-46
249        </service>
250
251        <!-- 独立守护服务 - 运行在独立进程中，专门监控主进程 -->
252        <service
252-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:241:9-249:19
253            android:name="com.pangu.keepaliveperfect.demo.service.IndependentGuardianService"
253-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:242:13-63
254            android:directBootAware="true"
254-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:244:13-43
255            android:enabled="true"
255-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:245:13-35
256            android:exported="false"
256-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:246:13-37
257            android:foregroundServiceType="dataSync"
257-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:247:13-53
258            android:priority="1000"
258-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:248:13-36
259            android:process=":guardian" >
259-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:243:13-40
260        </service>
261
262        <!-- 任务调度服务 -->
263        <service
263-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:254:9-260:19
264            android:name="com.pangu.keepaliveperfect.demo.KeepAliveJobService"
264-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:255:13-48
265            android:directBootAware="true"
265-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:258:13-43
266            android:exported="false"
266-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:257:13-37
267            android:permission="android.permission.BIND_JOB_SERVICE"
267-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:256:13-69
268            android:process=":job_process" >
268-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:259:13-43
269        </service>
270
271        <!-- 新增账户认证服务 -->
272        <service
272-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:263:9-273:19
273            android:name="com.pangu.keepaliveperfect.demo.account.AuthenticatorService"
273-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:264:13-57
274            android:directBootAware="true"
274-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:266:13-43
275            android:exported="false" >
275-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:265:13-37
276            <intent-filter>
276-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:267:13-269:29
277                <action android:name="android.accounts.AccountAuthenticator" />
277-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:268:17-80
277-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:268:25-77
278            </intent-filter>
279
280            <meta-data
280-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:270:13-272:57
281                android:name="android.accounts.AccountAuthenticator"
281-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:271:17-69
282                android:resource="@xml/authenticator" />
282-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:272:17-54
283        </service>
284
285        <!-- 新增同步服务 -->
286        <service
286-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:276:9-287:19
287            android:name="com.pangu.keepaliveperfect.demo.account.SyncService"
287-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:277:13-48
288            android:directBootAware="true"
288-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:279:13-43
289            android:exported="false"
289-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:278:13-37
290            android:process=":sync_process" >
290-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:280:13-44
291            <intent-filter>
291-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:281:13-283:29
292                <action android:name="android.content.SyncAdapter" />
292-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:282:17-70
292-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:282:25-67
293            </intent-filter>
294
295            <meta-data
295-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:284:13-286:55
296                android:name="android.content.SyncAdapter"
296-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:285:17-59
297                android:resource="@xml/syncadapter" />
297-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:286:17-52
298        </service>
299
300        <!-- 内容提供者 -->
301        <provider
301-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:290:9-294:39
302            android:name="com.pangu.keepaliveperfect.demo.account.StubProvider"
302-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:291:13-49
303            android:authorities="com.pangu.keepaliveperfect.demo.provider"
303-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:292:13-75
304            android:exported="false"
304-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:293:13-37
305            android:syncable="true" />
305-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:294:13-36
306
307        <!-- 广播接收器 -->
308        <receiver
308-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:297:9-307:20
309            android:name="com.pangu.keepaliveperfect.demo.BootReceiver"
309-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:298:13-41
310            android:directBootAware="true"
310-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:299:13-43
311            android:exported="true" >
311-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:300:13-36
312            <intent-filter>
312-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:301:13-306:29
313                <action android:name="android.intent.action.BOOT_COMPLETED" />
313-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:17-79
313-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:25-76
314                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
314-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:303:17-82
314-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:303:25-79
315                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
315-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:304:17-82
315-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:304:25-79
316
317                <category android:name="android.intent.category.DEFAULT" />
317-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
317-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
318            </intent-filter>
319        </receiver>
320
321        <!-- 安装完成接收器 -->
322        <receiver
322-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:310:9-319:20
323            android:name="com.pangu.keepaliveperfect.demo.receiver.PackageReceiver"
323-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:311:13-53
324            android:exported="true" >
324-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:312:13-36
325            <intent-filter>
325-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:313:13-318:29
326                <action android:name="android.intent.action.PACKAGE_ADDED" />
326-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:17-78
326-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:25-75
327                <action android:name="android.intent.action.PACKAGE_REPLACED" />
327-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:17-81
327-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:25-78
328                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
328-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:316:17-84
328-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:316:25-81
329
330                <data android:scheme="package" />
330-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:317:17-50
330-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:317:23-47
331            </intent-filter>
332        </receiver>
333
334        <!-- 短信接收器 -->
335        <receiver
335-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:322:9-330:20
336            android:name="com.pangu.keepaliveperfect.demo.SmsReceiver"
336-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:323:13-40
337            android:directBootAware="true"
337-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:325:13-43
338            android:exported="true"
338-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:324:13-36
339            android:permission="android.permission.BROADCAST_SMS" >
339-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:326:13-66
340            <intent-filter android:priority="999" >
340-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:13-329:29
340-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:28-50
341                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
341-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:17-82
341-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:25-79
342            </intent-filter>
343        </receiver>
344
345        <!-- 服务保活广播接收器 - 通过通知服务激活 -->
346        <receiver
346-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:333:9-341:20
347            android:name="com.pangu.keepaliveperfect.demo.receiver.ServiceRestartReceiver"
347-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:334:13-60
348            android:directBootAware="true"
348-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:336:13-43
349            android:exported="false" >
349-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:335:13-37
350            <intent-filter>
350-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:337:13-340:29
351                <action android:name="com.pangu.keepaliveperfect.demo.action.START_SERVICE" />
351-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:17-95
351-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:25-92
352                <action android:name="com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE" />
352-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:17-97
352-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:25-94
353            </intent-filter>
354        </receiver>
355
356        <!-- 厂商推送接收器 -->
357        <receiver
357-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:344:9-358:20
358            android:name="com.pangu.keepaliveperfect.demo.receiver.VendorReceiver"
358-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:345:13-52
359            android:exported="true" >
359-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:346:13-36
360            <intent-filter>
360-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:347:13-357:29
361
362                <!-- 小米推送 -->
363                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
363-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:17-76
363-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:25-73
364                <!-- 华为推送 -->
365                <action android:name="com.huawei.android.push.intent.RECEIVE" />
365-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:351:17-81
365-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:351:25-78
366                <!-- OPPO推送 -->
367                <action android:name="com.coloros.mcssdk.action.RECEIVE_MCS_MESSAGE" />
367-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:353:17-88
367-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:353:25-85
368                <action android:name="com.heytap.mcssdk.action.RECEIVE_MCS_MESSAGE" />
368-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:354:17-87
368-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:354:25-84
369                <!-- VIVO推送 -->
370                <action android:name="com.vivo.pushclient.action.RECEIVE" />
370-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:356:17-77
370-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:356:25-74
371            </intent-filter>
372        </receiver>
373
374        <!-- 应用快捷方式广播接收器 -->
375        <receiver
375-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:361:9-368:20
376            android:name="com.pangu.keepaliveperfect.demo.receiver.ShortcutBroadcastReceiver"
376-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:362:13-63
377            android:exported="true" >
377-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:363:13-36
378            <intent-filter>
378-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:364:13-367:29
379                <action android:name="com.pangu.keepaliveperfect.demo.action.SHORTCUT_LONG_PRESS" />
379-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:17-101
379-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:25-98
380
381                <category android:name="android.intent.category.DEFAULT" />
381-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
381-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
382            </intent-filter>
383        </receiver>
384
385        <!-- 统一通知监听服务 - 处理所有通知 - 同时实现保活机制 -->
386        <service
386-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:371:9-382:19
387            android:name="com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper$UniversalNotificationListener"
387-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:372:13-89
388            android:directBootAware="true"
388-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:375:13-43
389            android:exported="false"
389-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:373:13-37
390            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
390-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:374:13-87
391            <intent-filter>
391-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:376:13-378:29
392                <action android:name="android.service.notification.NotificationListenerService" />
392-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:377:17-99
392-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:377:25-96
393            </intent-filter>
394
395            <meta-data
395-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:379:13-381:73
396                android:name="android.service.notification.default_filter_types"
396-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:380:17-81
397                android:value="conversations|alerting|ongoing|silent" />
397-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:381:17-70
398        </service>
399
400        <!-- 七牛云上传服务 -->
401        <service
401-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:385:9-390:19
402            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService"
402-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:386:13-53
403            android:directBootAware="true"
403-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:387:13-43
404            android:exported="false"
404-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:388:13-37
405            android:foregroundServiceType="dataSync" >
405-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:389:13-53
406        </service>
407
408        <!-- 七牛云测试活动 -->
409        <activity
409-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:393:9-396:37
410            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuTestActivity"
410-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:394:13-52
411            android:exported="false"
411-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:395:13-37
412            android:label="七牛云测试" />
412-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:396:13-34
413
414        <meta-data
414-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:400:9-402:69
415            android:name="com.google.firebase.messaging.default_notification_icon"
415-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:401:13-83
416            android:resource="@drawable/notification_icon_simple" />
416-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:402:13-66
417
418        <!-- WorkManager配置 -->
419        <provider
420            android:name="androidx.startup.InitializationProvider"
420-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:406:13-67
421            android:authorities="com.pangu.keepaliveperfect.demo.androidx-startup"
421-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:407:13-68
422            android:exported="false" >
422-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:408:13-37
423            <meta-data
423-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
424                android:name="androidx.emoji2.text.EmojiCompatInitializer"
424-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
425                android:value="androidx.startup" />
425-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
426            <meta-data
426-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
427                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
427-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
428                android:value="androidx.startup" />
428-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
429        </provider>
430
431        <service
431-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:9:9-15:19
432            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
432-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:10:13-91
433            android:directBootAware="true"
433-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:17:13-43
434            android:exported="false" >
434-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:11:13-37
435            <meta-data
435-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:12:13-14:85
436                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
436-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:13:17-114
437                android:value="com.google.firebase.components.ComponentRegistrar" />
437-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:14:17-82
438            <meta-data
438-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:12:13-14:85
439                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
439-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:13:17-124
440                android:value="com.google.firebase.components.ComponentRegistrar" />
440-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:14:17-82
441            <meta-data
441-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:20:13-22:85
442                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
442-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:21:17-120
443                android:value="com.google.firebase.components.ComponentRegistrar" />
443-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:22:17-82
444        </service>
445
446        <provider
446-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:9:9-13:38
447            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
447-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:10:13-78
448            android:authorities="com.pangu.keepaliveperfect.demo.mlkitinitprovider"
448-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:11:13-69
449            android:exported="false"
449-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:12:13-37
450            android:initOrder="99" />
450-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:13:13-35
451
452        <activity
452-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
453            android:name="com.google.android.gms.common.api.GoogleApiActivity"
453-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
454            android:exported="false"
454-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
455            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
455-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
456
457        <meta-data
457-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:21:9-23:69
458            android:name="com.google.android.gms.version"
458-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:22:13-58
459            android:value="@integer/google_play_services_version" />
459-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:23:13-66
460
461        <uses-library
461-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
462            android:name="androidx.window.extensions"
462-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
463            android:required="false" />
463-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
464        <uses-library
464-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
465            android:name="androidx.window.sidecar"
465-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
466            android:required="false" />
466-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
467
468        <service
468-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
469            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
469-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
470            android:directBootAware="false"
470-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
471            android:enabled="@bool/enable_system_alarm_service_default"
471-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
472            android:exported="false" />
472-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
473        <service
473-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
474            android:name="androidx.work.impl.background.systemjob.SystemJobService"
474-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
475            android:directBootAware="false"
475-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
476            android:enabled="@bool/enable_system_job_service_default"
476-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
477            android:exported="true"
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
478            android:permission="android.permission.BIND_JOB_SERVICE" />
478-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
479        <service
479-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
480            android:name="androidx.work.impl.foreground.SystemForegroundService"
480-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
481            android:directBootAware="false"
481-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
482            android:enabled="@bool/enable_system_foreground_service_default"
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
483            android:exported="false" />
483-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
484
485        <receiver
485-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
486            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
487            android:directBootAware="false"
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
488            android:enabled="true"
488-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
489            android:exported="false" />
489-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
490        <receiver
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
491            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
491-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
492            android:directBootAware="false"
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
493            android:enabled="false"
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
494            android:exported="false" >
494-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
495            <intent-filter>
495-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
496                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
496-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
496-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
497                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
498            </intent-filter>
499        </receiver>
500        <receiver
500-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
501            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
501-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
502            android:directBootAware="false"
502-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
503            android:enabled="false"
503-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
504            android:exported="false" >
504-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
505            <intent-filter>
505-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
506                <action android:name="android.intent.action.BATTERY_OKAY" />
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
507                <action android:name="android.intent.action.BATTERY_LOW" />
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
508            </intent-filter>
509        </receiver>
510        <receiver
510-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
511            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
511-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
512            android:directBootAware="false"
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
513            android:enabled="false"
513-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
514            android:exported="false" >
514-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
515            <intent-filter>
515-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
516                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
517                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
518            </intent-filter>
519        </receiver>
520        <receiver
520-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
521            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
521-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
522            android:directBootAware="false"
522-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
523            android:enabled="false"
523-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
524            android:exported="false" >
524-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
525            <intent-filter>
525-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
526                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
526-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
526-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
527            </intent-filter>
528        </receiver>
529        <receiver
529-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
530            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
530-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
531            android:directBootAware="false"
531-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
532            android:enabled="false"
532-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
533            android:exported="false" >
533-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
534            <intent-filter>
534-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
535                <action android:name="android.intent.action.BOOT_COMPLETED" />
535-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:17-79
535-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:25-76
536                <action android:name="android.intent.action.TIME_SET" />
536-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
536-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
537                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
537-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
537-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
538            </intent-filter>
539        </receiver>
540        <receiver
540-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
541            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
541-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
542            android:directBootAware="false"
542-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
543            android:enabled="@bool/enable_system_alarm_service_default"
543-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
544            android:exported="false" >
544-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
545            <intent-filter>
545-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
546                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
546-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
546-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
547            </intent-filter>
548        </receiver>
549        <receiver
549-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
550            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
550-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
551            android:directBootAware="false"
551-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
552            android:enabled="true"
552-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
553            android:exported="true"
553-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
554            android:permission="android.permission.DUMP" >
554-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
555            <intent-filter>
555-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
556                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
556-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
556-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
557            </intent-filter>
558        </receiver>
559
560        <service
560-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
561            android:name="androidx.room.MultiInstanceInvalidationService"
561-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
562            android:directBootAware="true"
562-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
563            android:exported="false" />
563-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
564        <service
564-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
565            android:name="androidx.camera.core.impl.MetadataHolderService"
565-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
566            android:enabled="false"
566-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
567            android:exported="false" >
567-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
568            <meta-data
568-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
569                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
569-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
570                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
570-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
571        </service>
572        <service
572-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
573            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
573-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
574            android:exported="false" >
574-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
575            <meta-data
575-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
576                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
576-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
577                android:value="cct" />
577-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
578        </service>
579        <service
579-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
580            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
580-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
581            android:exported="false"
581-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
582            android:permission="android.permission.BIND_JOB_SERVICE" >
582-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
583        </service>
584
585        <receiver
585-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
586            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
586-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
587            android:exported="false" />
587-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
588    </application>
589
590</manifest>
