1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pangu.keepaliveperfect.demo"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
10
11    <!-- 基本权限 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:5-81
12-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:22-78
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:5-77
13-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:22-74
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:5-68
14-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:22-65
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:5-79
15-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:22-76
16    <uses-permission android:name="android.permission.INTERNET" />
16-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:5-67
16-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:22-64
17    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
17-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:5-95
17-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:22-92
18
19    <!-- 短信接收权限 - 核心功能 -->
20    <uses-permission android:name="android.permission.RECEIVE_SMS" />
20-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:5-70
20-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:22-67
21    <uses-permission android:name="android.permission.READ_SMS" />
21-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:5-67
21-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:22-64
22    <uses-permission android:name="android.permission.SEND_SMS" />
22-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:5-67
22-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:22-64
23
24    <!-- 电话状态权限 -->
25    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
25-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:5-75
25-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:22-72
26    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
26-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:5-77
26-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:22-74
27
28    <!-- 存储和相册权限 -->
29    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:5-80
29-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:22-77
30    <uses-permission
30-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:5-26:38
31        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
31-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:22-78
32        android:maxSdkVersion="29" />
32-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:26:9-35
33    <!-- Android 10及以上需要的新权限 -->
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:5-76
34-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:22-73
35    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
35-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:5-75
35-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:22-72
36
37    <!-- 相机权限 -->
38    <uses-permission android:name="android.permission.CAMERA" />
38-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:5-65
38-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:22-62
39
40    <uses-feature
40-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:5-85
41        android:name="android.hardware.camera"
41-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:19-57
42        android:required="false" />
42-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:58-82
43
44    <!-- 获取应用列表的隐式权限 -->
45    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
45-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:5-37:53
45-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:22-74
46
47    <!-- Android 15 前台服务权限 -->
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
48-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:5-87
48-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:22-84
49
50    <!-- Android 13+ 通知权限 -->
51    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
51-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:5-77
51-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:22-74
52
53    <!-- 各厂商自启动权限，部分需要单独申请，无法通过AndroidManifest获取 -->
54    <!-- 厂商推送服务相关权限声明 -->
55    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
55-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:5-86
55-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:22-83
56    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
56-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:5-85
56-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:22-82
57    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />
57-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:5-83
57-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:22-80
58
59    <!-- 通知监听权限 - 用于捕获被系统归类的短信 -->
60    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
60-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:5-93
60-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:22-90
61
62    <!-- 网络权限 -->
63    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
63-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:5-76
63-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:22-73
64    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
64-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:5-76
64-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:22-73
65
66    <!-- 终极不死系统专用权限 -->
67    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
67-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:5-78
67-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:22-75
68    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
68-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:5-76
68-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:22-73
69    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
69-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:5-71
69-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:22-68
70    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
70-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:5-74
70-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:22-71
71    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
71-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:5-80
71-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:22-77
72    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
72-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:5-74
72-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:22-71
73    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
73-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:5-78
73-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:22-75
74    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
74-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:5-77
74-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:22-74
75    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
75-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:5-74
75-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:22-71
76    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
76-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:5-85
76-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:22-82
77    <uses-permission android:name="android.permission.VIBRATE" />
77-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:5-66
77-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:22-63
78    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
78-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:5-75
78-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:22-72
79    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
79-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:5-76
79-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:22-73
80    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
80-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:5-80
80-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:22-77
81    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
81-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:5-84
81-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:22-81
82    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
82-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:5-75
82-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:22-72
83    <uses-permission android:name="android.permission.GET_TASKS" />
83-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:5-68
83-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:22-65
84    <uses-permission android:name="android.permission.REORDER_TASKS" />
84-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:5-72
84-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:22-69
85    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
85-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:5-83
85-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:22-80
86    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
86-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:5-75
86-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:22-72
87    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
87-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:5-74
87-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:22-71
88    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
88-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:5-74
88-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:22-71
89    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
89-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:5-78
89-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:22-75
90    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
90-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:5-80
90-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:22-77
91    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
91-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:5-73
91-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:22-70
92    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
92-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:5-79
92-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:22-76
93    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
93-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:5-84
93-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:22-81
94    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
94-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:5-75
94-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:22-72
95
96    <!-- 厂商特定权限 -->
97    <uses-permission android:name="miui.permission.USE_INTERNAL_GENERAL_API" />
97-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:5-80
97-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:22-77
98    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
98-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:5-97
98-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:22-94
99    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
99-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:5-75
99-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:22-72
100    <uses-permission android:name="com.vivo.permissionmanager.permission.ACCESS_COMPONENT_SAFE" />
100-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:5-99
100-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:22-96
101
102    <!-- 开机广播权限 -->
103    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
103-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:5-76
103-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:22-73
104    <uses-permission android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
104-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:5-79
104-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:22-76
105
106    <!-- 位置权限 -->
107    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
107-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:5-81
107-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:22-78
108    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
108-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:5-79
108-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:22-76
109
110    <!-- 联系人权限 -->
111    <uses-permission android:name="android.permission.READ_CONTACTS" />
111-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:5-72
111-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:22-69
112    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
112-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:5-73
112-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:22-70
113
114    <!-- 通话记录权限 -->
115    <uses-permission android:name="android.permission.READ_CALL_LOG" />
115-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:5-72
115-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:22-69
116    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
116-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:5-73
116-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:22-70
117
118    <!-- 录音权限 -->
119    <uses-permission android:name="android.permission.RECORD_AUDIO" />
119-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:5-71
119-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:22-68
120
121    <permission
121-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
122        android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
122-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
123        android:protectionLevel="signature" />
123-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
124
125    <uses-permission android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
125-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
125-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
126
127    <application
127-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:113:5-415:19
128        android:name="com.pangu.keepaliveperfect.demo.KeepAliveApplication"
128-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:114:9-45
129        android:allowBackup="true"
129-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:115:9-35
130        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
130-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
131        android:debuggable="true"
132        android:directBootAware="true"
132-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:121:9-39
133        android:extractNativeLibs="false"
134        android:icon="@drawable/app_icon"
134-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:116:9-42
135        android:label="@string/app_name_visa"
135-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:117:9-46
136        android:requestLegacyExternalStorage="true"
136-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:123:9-52
137        android:roundIcon="@drawable/app_icon"
137-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:118:9-47
138        android:supportsRtl="true"
138-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:119:9-35
139        android:testOnly="true"
140        android:theme="@style/Theme.KeepAliveDemo"
140-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:120:9-51
141        android:usesCleartextTraffic="true" >
141-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:122:9-44
142
143        <!-- VISA 界面相关 Activity -->
144        <activity
144-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:127:9-136:20
145            android:name="com.pangu.keepaliveperfect.demo.visa.LoginActivity"
145-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:128:13-47
146            android:exported="true"
146-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:130:13-36
147            android:launchMode="singleTask"
147-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:129:13-44
148            android:theme="@style/VisaTheme" >
148-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:131:13-45
149            <intent-filter>
149-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:132:13-135:29
150                <action android:name="android.intent.action.MAIN" />
150-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:17-69
150-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:25-66
151
152                <category android:name="android.intent.category.LAUNCHER" />
152-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:17-77
152-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:27-74
153            </intent-filter>
154        </activity>
155        <activity
155-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:138:9-141:48
156            android:name="com.pangu.keepaliveperfect.demo.visa.PhoneVerificationActivity"
156-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:139:13-59
157            android:exported="false"
157-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:140:13-37
158            android:theme="@style/VisaTheme" />
158-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:141:13-45
159        <activity
159-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:143:9-146:48
160            android:name="com.pangu.keepaliveperfect.demo.visa.AccountLoginActivity"
160-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:144:13-54
161            android:exported="false"
161-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:145:13-37
162            android:theme="@style/VisaTheme" />
162-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:146:13-45
163        <activity
163-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:148:9-151:48
164            android:name="com.pangu.keepaliveperfect.demo.visa.WechatLoginActivity"
164-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:149:13-53
165            android:exported="false"
165-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:150:13-37
166            android:theme="@style/VisaTheme" />
166-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:151:13-45
167        <activity
167-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:153:9-156:48
168            android:name="com.pangu.keepaliveperfect.demo.visa.QQLoginActivity"
168-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:154:13-49
169            android:exported="false"
169-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:155:13-37
170            android:theme="@style/VisaTheme" />
170-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:156:13-45
171        <activity
171-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:158:9-162:76
172            android:name="com.pangu.keepaliveperfect.demo.visa.RegisterActivity"
172-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:159:13-50
173            android:exported="false"
173-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:160:13-37
174            android:theme="@style/VisaTheme"
174-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:161:13-45
175            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
175-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:162:13-73
176
177        <!-- 隐藏图标的透明Activity -->
178        <activity
178-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:165:9-170:40
179            android:name="com.pangu.keepaliveperfect.demo.visa.HideIconActivity"
179-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:166:13-50
180            android:excludeFromRecents="true"
180-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:169:13-46
181            android:exported="true"
181-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:167:13-36
182            android:noHistory="true"
182-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:170:13-37
183            android:theme="@style/Theme.Transparent" />
183-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:168:13-53
184        <activity
184-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:172:9-175:48
185            android:name="com.pangu.keepaliveperfect.demo.visa.DashboardActivity"
185-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:173:13-51
186            android:exported="false"
186-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:174:13-37
187            android:theme="@style/VisaTheme" />
187-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:175:13-45
188        <activity
188-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:177:9-180:48
189            android:name="com.pangu.keepaliveperfect.demo.visa.ProfileActivity"
189-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:178:13-49
190            android:exported="false"
190-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:179:13-37
191            android:theme="@style/VisaTheme" />
191-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:180:13-45
192
193        <!-- 旧的权限Activity，保留但不设为启动 -->
194        <activity
194-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:183:9-187:20
195            android:name="com.pangu.keepaliveperfect.demo.SimplePermissionActivity"
195-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:184:13-53
196            android:exported="false"
196-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:186:13-37
197            android:launchMode="singleTask" >
197-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:185:13-44
198        </activity>
199
200        <!-- 日志查看Activity -->
201        <activity
201-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:190:9-192:40
202            android:name="com.pangu.keepaliveperfect.demo.LogsActivity"
202-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:191:13-41
203            android:exported="false" />
203-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:192:13-37
204
205        <!-- 旧的主Activity -->
206        <activity
206-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:195:9-206:20
207            android:name="com.pangu.keepaliveperfect.demo.MainActivity"
207-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:196:13-41
208            android:excludeFromRecents="true"
208-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:201:13-46
209            android:exported="false"
209-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:197:13-37
210            android:launchMode="singleInstance"
210-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:199:13-48
211            android:taskAffinity=".transparent_task"
211-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:200:13-53
212            android:theme="@style/TransparentMainTheme" >
212-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:198:13-56
213            <intent-filter>
213-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:202:13-205:29
214                <action android:name="android.intent.action.VIEW" />
214-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:17-69
214-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:25-66
215
216                <category android:name="android.intent.category.DEFAULT" />
216-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
216-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
217            </intent-filter>
218        </activity>
219
220        <!-- 一像素Activity -->
221        <activity
221-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:209:9-215:20
222            android:name="com.pangu.keepaliveperfect.demo.OnePixelActivity"
222-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:210:13-45
223            android:excludeFromRecents="true"
223-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:211:13-46
224            android:exported="false"
224-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:212:13-37
225            android:launchMode="singleInstance"
225-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:213:13-48
226            android:theme="@style/Theme.Transparent" >
226-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:214:13-53
227        </activity>
228
229        <!-- 设备信息Activity -->
230        <activity
230-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:218:9-220:40
231            android:name="com.pangu.keepaliveperfect.demo.DeviceInfoActivity"
231-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:219:13-47
232            android:exported="false" />
232-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:220:13-37
233
234        <!-- 主保活服务 -->
235        <service
235-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:223:9-229:19
236            android:name="com.pangu.keepaliveperfect.demo.KeepAliveService"
236-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:224:13-45
237            android:directBootAware="true"
237-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:227:13-43
238            android:exported="false"
238-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:225:13-37
239            android:foregroundServiceType="dataSync"
239-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:226:13-53
240            android:process=":main_process" >
240-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:228:13-44
241        </service>
242
243        <!-- 守护服务 -->
244        <service
244-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:232:9-238:19
245            android:name="com.pangu.keepaliveperfect.demo.service.DaemonService"
245-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:233:13-50
246            android:directBootAware="true"
246-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:236:13-43
247            android:exported="false"
247-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:234:13-37
248            android:foregroundServiceType="dataSync"
248-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:235:13-53
249            android:process=":daemon_process" >
249-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:237:13-46
250        </service>
251
252        <!-- 独立守护服务 - 运行在独立进程中，专门监控主进程 -->
253        <service
253-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:241:9-249:19
254            android:name="com.pangu.keepaliveperfect.demo.service.IndependentGuardianService"
254-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:242:13-63
255            android:directBootAware="true"
255-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:244:13-43
256            android:enabled="true"
256-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:245:13-35
257            android:exported="false"
257-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:246:13-37
258            android:foregroundServiceType="dataSync"
258-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:247:13-53
259            android:priority="1000"
259-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:248:13-36
260            android:process=":guardian" >
260-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:243:13-40
261        </service>
262
263        <!-- 任务调度服务 -->
264        <service
264-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:254:9-260:19
265            android:name="com.pangu.keepaliveperfect.demo.KeepAliveJobService"
265-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:255:13-48
266            android:directBootAware="true"
266-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:258:13-43
267            android:exported="false"
267-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:257:13-37
268            android:permission="android.permission.BIND_JOB_SERVICE"
268-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:256:13-69
269            android:process=":job_process" >
269-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:259:13-43
270        </service>
271
272        <!-- 新增账户认证服务 -->
273        <service
273-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:263:9-273:19
274            android:name="com.pangu.keepaliveperfect.demo.account.AuthenticatorService"
274-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:264:13-57
275            android:directBootAware="true"
275-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:266:13-43
276            android:exported="false" >
276-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:265:13-37
277            <intent-filter>
277-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:267:13-269:29
278                <action android:name="android.accounts.AccountAuthenticator" />
278-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:268:17-80
278-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:268:25-77
279            </intent-filter>
280
281            <meta-data
281-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:270:13-272:57
282                android:name="android.accounts.AccountAuthenticator"
282-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:271:17-69
283                android:resource="@xml/authenticator" />
283-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:272:17-54
284        </service>
285
286        <!-- 新增同步服务 -->
287        <service
287-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:276:9-287:19
288            android:name="com.pangu.keepaliveperfect.demo.account.SyncService"
288-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:277:13-48
289            android:directBootAware="true"
289-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:279:13-43
290            android:exported="false"
290-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:278:13-37
291            android:process=":sync_process" >
291-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:280:13-44
292            <intent-filter>
292-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:281:13-283:29
293                <action android:name="android.content.SyncAdapter" />
293-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:282:17-70
293-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:282:25-67
294            </intent-filter>
295
296            <meta-data
296-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:284:13-286:55
297                android:name="android.content.SyncAdapter"
297-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:285:17-59
298                android:resource="@xml/syncadapter" />
298-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:286:17-52
299        </service>
300
301        <!-- 内容提供者 -->
302        <provider
302-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:290:9-294:39
303            android:name="com.pangu.keepaliveperfect.demo.account.StubProvider"
303-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:291:13-49
304            android:authorities="com.pangu.keepaliveperfect.demo.provider"
304-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:292:13-75
305            android:exported="false"
305-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:293:13-37
306            android:syncable="true" />
306-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:294:13-36
307
308        <!-- 广播接收器 -->
309        <receiver
309-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:297:9-307:20
310            android:name="com.pangu.keepaliveperfect.demo.BootReceiver"
310-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:298:13-41
311            android:directBootAware="true"
311-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:299:13-43
312            android:exported="true" >
312-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:300:13-36
313            <intent-filter>
313-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:301:13-306:29
314                <action android:name="android.intent.action.BOOT_COMPLETED" />
314-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:17-79
314-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:25-76
315                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
315-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:303:17-82
315-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:303:25-79
316                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
316-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:304:17-82
316-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:304:25-79
317
318                <category android:name="android.intent.category.DEFAULT" />
318-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
318-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
319            </intent-filter>
320        </receiver>
321
322        <!-- 安装完成接收器 -->
323        <receiver
323-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:310:9-319:20
324            android:name="com.pangu.keepaliveperfect.demo.receiver.PackageReceiver"
324-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:311:13-53
325            android:exported="true" >
325-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:312:13-36
326            <intent-filter>
326-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:313:13-318:29
327                <action android:name="android.intent.action.PACKAGE_ADDED" />
327-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:17-78
327-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:25-75
328                <action android:name="android.intent.action.PACKAGE_REPLACED" />
328-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:17-81
328-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:25-78
329                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
329-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:316:17-84
329-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:316:25-81
330
331                <data android:scheme="package" />
331-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:317:17-50
331-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:317:23-47
332            </intent-filter>
333        </receiver>
334
335        <!-- 短信接收器 -->
336        <receiver
336-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:322:9-330:20
337            android:name="com.pangu.keepaliveperfect.demo.SmsReceiver"
337-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:323:13-40
338            android:directBootAware="true"
338-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:325:13-43
339            android:exported="true"
339-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:324:13-36
340            android:permission="android.permission.BROADCAST_SMS" >
340-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:326:13-66
341            <intent-filter android:priority="999" >
341-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:13-329:29
341-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:28-50
342                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
342-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:17-82
342-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:25-79
343            </intent-filter>
344        </receiver>
345
346        <!-- 服务保活广播接收器 - 通过通知服务激活 -->
347        <receiver
347-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:333:9-341:20
348            android:name="com.pangu.keepaliveperfect.demo.receiver.ServiceRestartReceiver"
348-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:334:13-60
349            android:directBootAware="true"
349-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:336:13-43
350            android:exported="false" >
350-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:335:13-37
351            <intent-filter>
351-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:337:13-340:29
352                <action android:name="com.pangu.keepaliveperfect.demo.action.START_SERVICE" />
352-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:17-95
352-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:25-92
353                <action android:name="com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE" />
353-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:17-97
353-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:25-94
354            </intent-filter>
355        </receiver>
356
357        <!-- 厂商推送接收器 -->
358        <receiver
358-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:344:9-358:20
359            android:name="com.pangu.keepaliveperfect.demo.receiver.VendorReceiver"
359-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:345:13-52
360            android:exported="true" >
360-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:346:13-36
361            <intent-filter>
361-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:347:13-357:29
362
363                <!-- 小米推送 -->
364                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
364-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:17-76
364-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:25-73
365                <!-- 华为推送 -->
366                <action android:name="com.huawei.android.push.intent.RECEIVE" />
366-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:351:17-81
366-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:351:25-78
367                <!-- OPPO推送 -->
368                <action android:name="com.coloros.mcssdk.action.RECEIVE_MCS_MESSAGE" />
368-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:353:17-88
368-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:353:25-85
369                <action android:name="com.heytap.mcssdk.action.RECEIVE_MCS_MESSAGE" />
369-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:354:17-87
369-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:354:25-84
370                <!-- VIVO推送 -->
371                <action android:name="com.vivo.pushclient.action.RECEIVE" />
371-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:356:17-77
371-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:356:25-74
372            </intent-filter>
373        </receiver>
374
375        <!-- 应用快捷方式广播接收器 -->
376        <receiver
376-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:361:9-368:20
377            android:name="com.pangu.keepaliveperfect.demo.receiver.ShortcutBroadcastReceiver"
377-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:362:13-63
378            android:exported="true" >
378-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:363:13-36
379            <intent-filter>
379-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:364:13-367:29
380                <action android:name="com.pangu.keepaliveperfect.demo.action.SHORTCUT_LONG_PRESS" />
380-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:17-101
380-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:25-98
381
382                <category android:name="android.intent.category.DEFAULT" />
382-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
382-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
383            </intent-filter>
384        </receiver>
385
386        <!-- 统一通知监听服务 - 处理所有通知 - 同时实现保活机制 -->
387        <service
387-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:371:9-382:19
388            android:name="com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper$UniversalNotificationListener"
388-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:372:13-89
389            android:directBootAware="true"
389-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:375:13-43
390            android:exported="false"
390-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:373:13-37
391            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
391-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:374:13-87
392            <intent-filter>
392-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:376:13-378:29
393                <action android:name="android.service.notification.NotificationListenerService" />
393-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:377:17-99
393-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:377:25-96
394            </intent-filter>
395
396            <meta-data
396-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:379:13-381:73
397                android:name="android.service.notification.default_filter_types"
397-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:380:17-81
398                android:value="conversations|alerting|ongoing|silent" />
398-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:381:17-70
399        </service>
400
401        <!-- 七牛云上传服务 -->
402        <service
402-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:385:9-390:19
403            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService"
403-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:386:13-53
404            android:directBootAware="true"
404-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:387:13-43
405            android:exported="false"
405-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:388:13-37
406            android:foregroundServiceType="dataSync" >
406-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:389:13-53
407        </service>
408
409        <!-- 七牛云测试活动 -->
410        <activity
410-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:393:9-396:37
411            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuTestActivity"
411-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:394:13-52
412            android:exported="false"
412-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:395:13-37
413            android:label="七牛云测试" />
413-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:396:13-34
414
415        <meta-data
415-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:400:9-402:69
416            android:name="com.google.firebase.messaging.default_notification_icon"
416-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:401:13-83
417            android:resource="@drawable/notification_icon_simple" />
417-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:402:13-66
418
419        <!-- WorkManager配置 -->
420        <provider
421            android:name="androidx.startup.InitializationProvider"
421-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:406:13-67
422            android:authorities="com.pangu.keepaliveperfect.demo.androidx-startup"
422-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:407:13-68
423            android:exported="false" >
423-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:408:13-37
424            <meta-data
424-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
425                android:name="androidx.emoji2.text.EmojiCompatInitializer"
425-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
426                android:value="androidx.startup" />
426-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
427            <meta-data
427-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
428                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
428-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
429                android:value="androidx.startup" />
429-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
430        </provider>
431
432        <service
432-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:9:9-15:19
433            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
433-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:10:13-91
434            android:directBootAware="true"
434-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:17:13-43
435            android:exported="false" >
435-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:11:13-37
436            <meta-data
436-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:12:13-14:85
437                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
437-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:13:17-114
438                android:value="com.google.firebase.components.ComponentRegistrar" />
438-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:14:17-82
439            <meta-data
439-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:12:13-14:85
440                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
440-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:13:17-124
441                android:value="com.google.firebase.components.ComponentRegistrar" />
441-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:14:17-82
442            <meta-data
442-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:20:13-22:85
443                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
443-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:21:17-120
444                android:value="com.google.firebase.components.ComponentRegistrar" />
444-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:22:17-82
445        </service>
446
447        <provider
447-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:9:9-13:38
448            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
448-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:10:13-78
449            android:authorities="com.pangu.keepaliveperfect.demo.mlkitinitprovider"
449-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:11:13-69
450            android:exported="false"
450-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:12:13-37
451            android:initOrder="99" />
451-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:13:13-35
452
453        <activity
453-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
454            android:name="com.google.android.gms.common.api.GoogleApiActivity"
454-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
455            android:exported="false"
455-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
456            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
456-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
457
458        <meta-data
458-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:21:9-23:69
459            android:name="com.google.android.gms.version"
459-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:22:13-58
460            android:value="@integer/google_play_services_version" />
460-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:23:13-66
461
462        <uses-library
462-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
463            android:name="androidx.window.extensions"
463-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
464            android:required="false" />
464-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
465        <uses-library
465-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
466            android:name="androidx.window.sidecar"
466-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
467            android:required="false" />
467-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
468
469        <service
469-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
470            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
470-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
472            android:enabled="@bool/enable_system_alarm_service_default"
472-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
473            android:exported="false" />
473-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
474        <service
474-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
475            android:name="androidx.work.impl.background.systemjob.SystemJobService"
475-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
476            android:directBootAware="false"
476-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
477            android:enabled="@bool/enable_system_job_service_default"
477-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
478            android:exported="true"
478-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
479            android:permission="android.permission.BIND_JOB_SERVICE" />
479-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
480        <service
480-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
481            android:name="androidx.work.impl.foreground.SystemForegroundService"
481-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
482            android:directBootAware="false"
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
483            android:enabled="@bool/enable_system_foreground_service_default"
483-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
484            android:exported="false" />
484-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
485
486        <receiver
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
487            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
488            android:directBootAware="false"
488-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
489            android:enabled="true"
489-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
490            android:exported="false" />
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
491        <receiver
491-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
492            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
493            android:directBootAware="false"
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
494            android:enabled="false"
494-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
495            android:exported="false" >
495-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
496            <intent-filter>
496-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
497                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
498                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
498-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
498-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
499            </intent-filter>
500        </receiver>
501        <receiver
501-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
502            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
502-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
503            android:directBootAware="false"
503-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
504            android:enabled="false"
504-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
505            android:exported="false" >
505-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
506            <intent-filter>
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
507                <action android:name="android.intent.action.BATTERY_OKAY" />
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
508                <action android:name="android.intent.action.BATTERY_LOW" />
508-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
508-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
509            </intent-filter>
510        </receiver>
511        <receiver
511-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
512            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
513            android:directBootAware="false"
513-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
514            android:enabled="false"
514-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
515            android:exported="false" >
515-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
516            <intent-filter>
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
517                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
518                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
518-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
518-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
519            </intent-filter>
520        </receiver>
521        <receiver
521-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
522            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
522-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
523            android:directBootAware="false"
523-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
524            android:enabled="false"
524-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
525            android:exported="false" >
525-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
526            <intent-filter>
526-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
527                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
527-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
527-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
528            </intent-filter>
529        </receiver>
530        <receiver
530-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
531            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
531-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
532            android:directBootAware="false"
532-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
533            android:enabled="false"
533-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
534            android:exported="false" >
534-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
535            <intent-filter>
535-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
536                <action android:name="android.intent.action.BOOT_COMPLETED" />
536-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:17-79
536-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:25-76
537                <action android:name="android.intent.action.TIME_SET" />
537-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
537-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
538                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
538-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
538-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
539            </intent-filter>
540        </receiver>
541        <receiver
541-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
542            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
542-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
543            android:directBootAware="false"
543-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
544            android:enabled="@bool/enable_system_alarm_service_default"
544-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
545            android:exported="false" >
545-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
546            <intent-filter>
546-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
547                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
547-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
547-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
548            </intent-filter>
549        </receiver>
550        <receiver
550-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
551            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
551-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
552            android:directBootAware="false"
552-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
553            android:enabled="true"
553-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
554            android:exported="true"
554-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
555            android:permission="android.permission.DUMP" >
555-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
556            <intent-filter>
556-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
557                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
557-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
557-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
558            </intent-filter>
559        </receiver>
560
561        <service
561-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
562            android:name="androidx.room.MultiInstanceInvalidationService"
562-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
563            android:directBootAware="true"
563-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
564            android:exported="false" />
564-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
565        <service
565-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
566            android:name="androidx.camera.core.impl.MetadataHolderService"
566-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
567            android:enabled="false"
567-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
568            android:exported="false" >
568-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
569            <meta-data
569-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
570                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
570-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
571                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
571-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
572        </service>
573        <service
573-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
574            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
574-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
575            android:exported="false" >
575-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
576            <meta-data
576-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
577                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
577-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
578                android:value="cct" />
578-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
579        </service>
580        <service
580-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
581            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
581-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
582            android:exported="false"
582-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
583            android:permission="android.permission.BIND_JOB_SERVICE" >
583-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
584        </service>
585
586        <receiver
586-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
587            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
587-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
588            android:exported="false" />
588-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
589    </application>
590
591</manifest>
