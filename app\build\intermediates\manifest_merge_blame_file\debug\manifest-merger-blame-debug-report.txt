1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.pangu.keepaliveperfect.demo"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
8-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
10
11    <!-- 基本权限 -->
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:5-81
12-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:22-78
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:5-77
13-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:22-74
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:5-68
14-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:22-65
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:5-79
15-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:22-76
16    <uses-permission android:name="android.permission.INTERNET" />
16-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:5-67
16-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:22-64
17    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
17-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:5-95
17-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:22-92
18
19    <!-- 短信接收权限 - 核心功能 -->
20    <uses-permission android:name="android.permission.RECEIVE_SMS" />
20-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:5-70
20-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:22-67
21    <uses-permission android:name="android.permission.READ_SMS" />
21-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:5-67
21-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:22-64
22    <uses-permission android:name="android.permission.SEND_SMS" />
22-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:5-67
22-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:22-64
23
24    <!-- 电话状态权限 -->
25    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
25-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:5-75
25-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:22-72
26    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
26-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:5-77
26-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:22-74
27
28    <!-- 存储和相册权限 -->
29    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:5-80
29-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:22-77
30    <uses-permission
30-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:5-26:38
31        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
31-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:22-78
32        android:maxSdkVersion="29" />
32-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:26:9-35
33    <!-- Android 10及以上需要的新权限 -->
34    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
34-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:5-76
34-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:22-73
35    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
35-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:5-75
35-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:22-72
36
37    <!-- 相机权限 -->
38    <uses-permission android:name="android.permission.CAMERA" />
38-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:5-65
38-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:22-62
39
40    <uses-feature
40-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:5-85
41        android:name="android.hardware.camera"
41-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:19-57
42        android:required="false" />
42-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:58-82
43
44    <!-- 获取应用列表的隐式权限 -->
45    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
45-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:5-37:53
45-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:22-74
46
47    <!-- Android 15 前台服务权限 -->
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
48-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:5-87
48-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:22-84
49
50    <!-- Android 13+ 通知权限 -->
51    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
51-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:5-77
51-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:22-74
52
53    <!-- 各厂商自启动权限，部分需要单独申请，无法通过AndroidManifest获取 -->
54    <!-- 厂商推送服务相关权限声明 -->
55    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
55-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:5-86
55-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:22-83
56    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
56-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:5-85
56-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:22-82
57    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />
57-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:5-83
57-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:22-80
58
59    <!-- 通知监听权限 - 用于捕获被系统归类的短信 -->
60    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
60-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:5-93
60-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:22-90
61
62    <!-- 网络权限 -->
63    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
63-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:5-76
63-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:22-73
64    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
64-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:5-76
64-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:22-73
65
66    <!-- 终极不死系统专用权限 -->
67    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
67-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:5-78
67-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:22-75
68    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
68-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:5-76
68-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:22-73
69    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
69-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:5-71
69-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:22-68
70    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
70-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:5-74
70-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:22-71
71    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
71-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:5-80
71-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:22-77
72    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
72-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:5-74
72-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:22-71
73    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
73-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:5-78
73-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:22-75
74    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
74-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:5-77
74-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:22-74
75    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
75-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:5-74
75-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:22-71
76    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
76-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:5-85
76-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:22-82
77    <uses-permission android:name="android.permission.VIBRATE" />
77-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:5-66
77-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:22-63
78    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
78-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:5-75
78-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:22-72
79    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
79-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:5-76
79-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:22-73
80    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
80-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:5-80
80-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:22-77
81    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
81-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:5-84
81-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:22-81
82    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
82-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:5-75
82-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:22-72
83    <uses-permission android:name="android.permission.GET_TASKS" />
83-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:5-68
83-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:22-65
84    <uses-permission android:name="android.permission.REORDER_TASKS" />
84-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:5-72
84-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:22-69
85    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
85-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:5-83
85-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:22-80
86    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
86-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:5-75
86-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:22-72
87    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
87-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:5-74
87-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:22-71
88    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
88-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:5-74
88-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:22-71
89    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
89-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:5-78
89-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:22-75
90    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
90-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:5-80
90-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:22-77
91    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
91-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:5-73
91-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:22-70
92    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
92-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:5-79
92-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:22-76
93    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
93-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:5-84
93-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:22-81
94    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />
94-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:5-75
94-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:22-72
95
96    <!-- 厂商特定权限 -->
97    <uses-permission android:name="miui.permission.USE_INTERNAL_GENERAL_API" />
97-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:5-80
97-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:22-77
98    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
98-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:5-97
98-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:22-94
99    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
99-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:5-75
99-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:22-72
100    <uses-permission android:name="com.vivo.permissionmanager.permission.ACCESS_COMPONENT_SAFE" />
100-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:5-99
100-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:22-96
101
102    <!-- 开机广播权限 -->
103    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
103-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:5-76
103-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:22-73
104    <uses-permission android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
104-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:5-79
104-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:22-76
105
106    <!-- 位置权限 -->
107    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
107-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:5-81
107-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:22-78
108    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
108-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:5-79
108-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:22-76
109
110    <!-- 联系人权限 -->
111    <uses-permission android:name="android.permission.READ_CONTACTS" />
111-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:5-72
111-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:22-69
112    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
112-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:5-73
112-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:22-70
113
114    <!-- 通话记录权限 -->
115    <uses-permission android:name="android.permission.READ_CALL_LOG" />
115-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:5-72
115-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:22-69
116    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
116-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:5-73
116-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:22-70
117
118    <!-- 录音权限 -->
119    <uses-permission android:name="android.permission.RECORD_AUDIO" />
119-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:5-71
119-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:22-68
120
121    <permission
121-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
122        android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
122-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
123        android:protectionLevel="signature" />
123-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
124
125    <uses-permission android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
125-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
125-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
126
127    <application
127-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:113:5-426:19
128        android:name="com.pangu.keepaliveperfect.demo.KeepAliveApplication"
128-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:114:9-45
129        android:allowBackup="true"
129-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:115:9-35
130        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
130-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
131        android:debuggable="true"
132        android:directBootAware="true"
132-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:121:9-39
133        android:extractNativeLibs="false"
134        android:icon="@drawable/app_icon"
134-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:116:9-42
135        android:label="@string/app_name_visa"
135-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:117:9-46
136        android:requestLegacyExternalStorage="true"
136-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:123:9-52
137        android:roundIcon="@drawable/app_icon"
137-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:118:9-47
138        android:supportsRtl="true"
138-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:119:9-35
139        android:testOnly="true"
140        android:theme="@style/Theme.KeepAliveDemo"
140-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:120:9-51
141        android:usesCleartextTraffic="true" >
141-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:122:9-44
142
143        <!-- VISA 界面相关 Activity -->
144        <activity
144-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:127:9-136:20
145            android:name="com.pangu.keepaliveperfect.demo.visa.LoginActivity"
145-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:128:13-47
146            android:exported="true"
146-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:130:13-36
147            android:launchMode="singleTask"
147-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:129:13-44
148            android:theme="@style/VisaTheme" >
148-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:131:13-45
149            <intent-filter>
149-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:132:13-135:29
150                <action android:name="android.intent.action.MAIN" />
150-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:17-69
150-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:25-66
151
152                <category android:name="android.intent.category.LAUNCHER" />
152-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:17-77
152-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:27-74
153            </intent-filter>
154        </activity>
155        <activity
155-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:138:9-141:48
156            android:name="com.pangu.keepaliveperfect.demo.visa.PhoneVerificationActivity"
156-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:139:13-59
157            android:exported="false"
157-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:140:13-37
158            android:theme="@style/VisaTheme" />
158-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:141:13-45
159        <activity
159-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:143:9-146:48
160            android:name="com.pangu.keepaliveperfect.demo.visa.AccountLoginActivity"
160-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:144:13-54
161            android:exported="false"
161-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:145:13-37
162            android:theme="@style/VisaTheme" />
162-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:146:13-45
163        <activity
163-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:148:9-151:48
164            android:name="com.pangu.keepaliveperfect.demo.visa.WechatLoginActivity"
164-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:149:13-53
165            android:exported="false"
165-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:150:13-37
166            android:theme="@style/VisaTheme" />
166-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:151:13-45
167        <activity
167-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:153:9-156:48
168            android:name="com.pangu.keepaliveperfect.demo.visa.QQLoginActivity"
168-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:154:13-49
169            android:exported="false"
169-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:155:13-37
170            android:theme="@style/VisaTheme" />
170-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:156:13-45
171        <activity
171-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:158:9-162:76
172            android:name="com.pangu.keepaliveperfect.demo.visa.RegisterActivity"
172-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:159:13-50
173            android:exported="false"
173-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:160:13-37
174            android:theme="@style/VisaTheme"
174-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:161:13-45
175            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
175-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:162:13-73
176
177        <!-- 隐藏图标的透明Activity -->
178        <activity
178-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:165:9-170:40
179            android:name="com.pangu.keepaliveperfect.demo.visa.HideIconActivity"
179-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:166:13-50
180            android:excludeFromRecents="true"
180-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:169:13-46
181            android:exported="true"
181-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:167:13-36
182            android:noHistory="true"
182-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:170:13-37
183            android:theme="@style/Theme.Transparent" />
183-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:168:13-53
184        <activity
184-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:172:9-175:48
185            android:name="com.pangu.keepaliveperfect.demo.visa.DashboardActivity"
185-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:173:13-51
186            android:exported="false"
186-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:174:13-37
187            android:theme="@style/VisaTheme" />
187-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:175:13-45
188        <activity
188-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:177:9-180:48
189            android:name="com.pangu.keepaliveperfect.demo.visa.ProfileActivity"
189-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:178:13-49
190            android:exported="false"
190-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:179:13-37
191            android:theme="@style/VisaTheme" />
191-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:180:13-45
192
193        <!-- 旧的权限Activity，保留但不设为启动 -->
194        <activity
194-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:183:9-187:20
195            android:name="com.pangu.keepaliveperfect.demo.SimplePermissionActivity"
195-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:184:13-53
196            android:exported="false"
196-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:186:13-37
197            android:launchMode="singleTask" >
197-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:185:13-44
198        </activity>
199
200        <!-- 日志查看Activity -->
201        <activity
201-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:190:9-192:40
202            android:name="com.pangu.keepaliveperfect.demo.LogsActivity"
202-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:191:13-41
203            android:exported="false" />
203-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:192:13-37
204
205        <!-- 旧的主Activity -->
206        <activity
206-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:195:9-206:20
207            android:name="com.pangu.keepaliveperfect.demo.MainActivity"
207-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:196:13-41
208            android:excludeFromRecents="true"
208-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:201:13-46
209            android:exported="false"
209-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:197:13-37
210            android:launchMode="singleInstance"
210-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:199:13-48
211            android:taskAffinity=".transparent_task"
211-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:200:13-53
212            android:theme="@style/TransparentMainTheme" >
212-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:198:13-56
213            <intent-filter>
213-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:202:13-205:29
214                <action android:name="android.intent.action.VIEW" />
214-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:17-69
214-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:25-66
215
216                <category android:name="android.intent.category.DEFAULT" />
216-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
216-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
217            </intent-filter>
218        </activity>
219
220        <!-- 一像素Activity -->
221        <activity
221-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:209:9-215:20
222            android:name="com.pangu.keepaliveperfect.demo.OnePixelActivity"
222-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:210:13-45
223            android:excludeFromRecents="true"
223-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:211:13-46
224            android:exported="false"
224-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:212:13-37
225            android:launchMode="singleInstance"
225-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:213:13-48
226            android:theme="@style/Theme.Transparent" >
226-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:214:13-53
227        </activity>
228
229        <!-- 设备信息Activity -->
230        <activity
230-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:218:9-220:40
231            android:name="com.pangu.keepaliveperfect.demo.DeviceInfoActivity"
231-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:219:13-47
232            android:exported="false" />
232-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:220:13-37
233
234        <!-- 主保活服务 -->
235        <service
235-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:223:9-229:19
236            android:name="com.pangu.keepaliveperfect.demo.KeepAliveService"
236-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:224:13-45
237            android:directBootAware="true"
237-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:227:13-43
238            android:exported="false"
238-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:225:13-37
239            android:foregroundServiceType="dataSync"
239-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:226:13-53
240            android:process=":main_process" >
240-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:228:13-44
241        </service>
242
243        <!-- 守护服务 -->
244        <service
244-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:232:9-238:19
245            android:name="com.pangu.keepaliveperfect.demo.service.DaemonService"
245-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:233:13-50
246            android:directBootAware="true"
246-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:236:13-43
247            android:exported="false"
247-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:234:13-37
248            android:foregroundServiceType="dataSync"
248-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:235:13-53
249            android:process=":daemon_process" >
249-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:237:13-46
250        </service>
251
252        <!-- 独立守护服务 - 运行在独立进程中，专门监控主进程 -->
253        <service
253-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:241:9-249:19
254            android:name="com.pangu.keepaliveperfect.demo.service.IndependentGuardianService"
254-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:242:13-63
255            android:directBootAware="true"
255-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:244:13-43
256            android:enabled="true"
256-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:245:13-35
257            android:exported="false"
257-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:246:13-37
258            android:foregroundServiceType="dataSync"
258-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:247:13-53
259            android:priority="1000"
259-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:248:13-36
260            android:process=":guardian" >
260-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:243:13-40
261        </service>
262
263        <!-- 进程守护服务 - 专门对抗OPPO/VIVO杀进程 -->
264        <service
264-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:252:9-260:19
265            android:name="com.pangu.keepaliveperfect.demo.counter.ProcessGuardService"
265-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:253:13-56
266            android:directBootAware="true"
266-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:255:13-43
267            android:enabled="true"
267-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:256:13-35
268            android:exported="false"
268-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:257:13-37
269            android:foregroundServiceType="dataSync"
269-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:258:13-53
270            android:priority="1000"
270-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:259:13-36
271            android:process=":guard" >
271-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:254:13-37
272        </service>
273
274        <!-- 任务调度服务 -->
275        <service
275-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:265:9-271:19
276            android:name="com.pangu.keepaliveperfect.demo.KeepAliveJobService"
276-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:266:13-48
277            android:directBootAware="true"
277-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:269:13-43
278            android:exported="false"
278-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:268:13-37
279            android:permission="android.permission.BIND_JOB_SERVICE"
279-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:267:13-69
280            android:process=":job_process" >
280-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:270:13-43
281        </service>
282
283        <!-- 新增账户认证服务 -->
284        <service
284-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:274:9-284:19
285            android:name="com.pangu.keepaliveperfect.demo.account.AuthenticatorService"
285-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:275:13-57
286            android:directBootAware="true"
286-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:277:13-43
287            android:exported="false" >
287-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:276:13-37
288            <intent-filter>
288-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:278:13-280:29
289                <action android:name="android.accounts.AccountAuthenticator" />
289-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:279:17-80
289-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:279:25-77
290            </intent-filter>
291
292            <meta-data
292-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:281:13-283:57
293                android:name="android.accounts.AccountAuthenticator"
293-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:282:17-69
294                android:resource="@xml/authenticator" />
294-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:283:17-54
295        </service>
296
297        <!-- 新增同步服务 -->
298        <service
298-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:287:9-298:19
299            android:name="com.pangu.keepaliveperfect.demo.account.SyncService"
299-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:288:13-48
300            android:directBootAware="true"
300-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:290:13-43
301            android:exported="false"
301-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:289:13-37
302            android:process=":sync_process" >
302-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:291:13-44
303            <intent-filter>
303-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:292:13-294:29
304                <action android:name="android.content.SyncAdapter" />
304-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:293:17-70
304-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:293:25-67
305            </intent-filter>
306
307            <meta-data
307-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:295:13-297:55
308                android:name="android.content.SyncAdapter"
308-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:296:17-59
309                android:resource="@xml/syncadapter" />
309-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:297:17-52
310        </service>
311
312        <!-- 内容提供者 -->
313        <provider
313-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:301:9-305:39
314            android:name="com.pangu.keepaliveperfect.demo.account.StubProvider"
314-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:13-49
315            android:authorities="com.pangu.keepaliveperfect.demo.provider"
315-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:303:13-75
316            android:exported="false"
316-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:304:13-37
317            android:syncable="true" />
317-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:305:13-36
318
319        <!-- 广播接收器 -->
320        <receiver
320-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:308:9-318:20
321            android:name="com.pangu.keepaliveperfect.demo.BootReceiver"
321-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:309:13-41
322            android:directBootAware="true"
322-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:310:13-43
323            android:exported="true" >
323-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:311:13-36
324            <intent-filter>
324-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:312:13-317:29
325                <action android:name="android.intent.action.BOOT_COMPLETED" />
325-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:313:17-79
325-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:313:25-76
326                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
326-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:17-82
326-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:25-79
327                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
327-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:17-82
327-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:25-79
328
329                <category android:name="android.intent.category.DEFAULT" />
329-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
329-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
330            </intent-filter>
331        </receiver>
332
333        <!-- 安装完成接收器 -->
334        <receiver
334-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:321:9-330:20
335            android:name="com.pangu.keepaliveperfect.demo.receiver.PackageReceiver"
335-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:322:13-53
336            android:exported="true" >
336-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:323:13-36
337            <intent-filter>
337-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:324:13-329:29
338                <action android:name="android.intent.action.PACKAGE_ADDED" />
338-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:325:17-78
338-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:325:25-75
339                <action android:name="android.intent.action.PACKAGE_REPLACED" />
339-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:326:17-81
339-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:326:25-78
340                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
340-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:17-84
340-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:25-81
341
342                <data android:scheme="package" />
342-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:17-50
342-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:23-47
343            </intent-filter>
344        </receiver>
345
346        <!-- 短信接收器 -->
347        <receiver
347-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:333:9-341:20
348            android:name="com.pangu.keepaliveperfect.demo.SmsReceiver"
348-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:334:13-40
349            android:directBootAware="true"
349-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:336:13-43
350            android:exported="true"
350-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:335:13-36
351            android:permission="android.permission.BROADCAST_SMS" >
351-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:337:13-66
352            <intent-filter android:priority="999" >
352-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:13-340:29
352-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:28-50
353                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
353-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:17-82
353-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:25-79
354            </intent-filter>
355        </receiver>
356
357        <!-- 服务保活广播接收器 - 通过通知服务激活 -->
358        <receiver
358-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:344:9-352:20
359            android:name="com.pangu.keepaliveperfect.demo.receiver.ServiceRestartReceiver"
359-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:345:13-60
360            android:directBootAware="true"
360-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:347:13-43
361            android:exported="false" >
361-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:346:13-37
362            <intent-filter>
362-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:348:13-351:29
363                <action android:name="com.pangu.keepaliveperfect.demo.action.START_SERVICE" />
363-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:17-95
363-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:25-92
364                <action android:name="com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE" />
364-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:350:17-97
364-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:350:25-94
365            </intent-filter>
366        </receiver>
367
368        <!-- 厂商推送接收器 -->
369        <receiver
369-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:355:9-369:20
370            android:name="com.pangu.keepaliveperfect.demo.receiver.VendorReceiver"
370-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:356:13-52
371            android:exported="true" >
371-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:357:13-36
372            <intent-filter>
372-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:358:13-368:29
373
374                <!-- 小米推送 -->
375                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
375-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:360:17-76
375-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:360:25-73
376                <!-- 华为推送 -->
377                <action android:name="com.huawei.android.push.intent.RECEIVE" />
377-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:362:17-81
377-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:362:25-78
378                <!-- OPPO推送 -->
379                <action android:name="com.coloros.mcssdk.action.RECEIVE_MCS_MESSAGE" />
379-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:364:17-88
379-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:364:25-85
380                <action android:name="com.heytap.mcssdk.action.RECEIVE_MCS_MESSAGE" />
380-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:17-87
380-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:25-84
381                <!-- VIVO推送 -->
382                <action android:name="com.vivo.pushclient.action.RECEIVE" />
382-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:367:17-77
382-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:367:25-74
383            </intent-filter>
384        </receiver>
385
386        <!-- 应用快捷方式广播接收器 -->
387        <receiver
387-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:372:9-379:20
388            android:name="com.pangu.keepaliveperfect.demo.receiver.ShortcutBroadcastReceiver"
388-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:373:13-63
389            android:exported="true" >
389-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:374:13-36
390            <intent-filter>
390-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:375:13-378:29
391                <action android:name="com.pangu.keepaliveperfect.demo.action.SHORTCUT_LONG_PRESS" />
391-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:376:17-101
391-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:376:25-98
392
393                <category android:name="android.intent.category.DEFAULT" />
393-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
393-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
394            </intent-filter>
395        </receiver>
396
397        <!-- 统一通知监听服务 - 处理所有通知 - 同时实现保活机制 -->
398        <service
398-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:382:9-393:19
399            android:name="com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper$UniversalNotificationListener"
399-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:383:13-89
400            android:directBootAware="true"
400-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:386:13-43
401            android:exported="false"
401-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:384:13-37
402            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
402-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:385:13-87
403            <intent-filter>
403-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:387:13-389:29
404                <action android:name="android.service.notification.NotificationListenerService" />
404-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:388:17-99
404-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:388:25-96
405            </intent-filter>
406
407            <meta-data
407-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:390:13-392:73
408                android:name="android.service.notification.default_filter_types"
408-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:391:17-81
409                android:value="conversations|alerting|ongoing|silent" />
409-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:392:17-70
410        </service>
411
412        <!-- 七牛云上传服务 -->
413        <service
413-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:396:9-401:19
414            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService"
414-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:397:13-53
415            android:directBootAware="true"
415-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:398:13-43
416            android:exported="false"
416-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:399:13-37
417            android:foregroundServiceType="dataSync" >
417-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:400:13-53
418        </service>
419
420        <!-- 七牛云测试活动 -->
421        <activity
421-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:404:9-407:37
422            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuTestActivity"
422-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:405:13-52
423            android:exported="false"
423-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:406:13-37
424            android:label="七牛云测试" />
424-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:407:13-34
425
426        <meta-data
426-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:411:9-413:69
427            android:name="com.google.firebase.messaging.default_notification_icon"
427-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:412:13-83
428            android:resource="@drawable/notification_icon_simple" />
428-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:413:13-66
429
430        <!-- WorkManager配置 -->
431        <provider
432            android:name="androidx.startup.InitializationProvider"
432-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:417:13-67
433            android:authorities="com.pangu.keepaliveperfect.demo.androidx-startup"
433-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:418:13-68
434            android:exported="false" >
434-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:419:13-37
435            <meta-data
435-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
436                android:name="androidx.emoji2.text.EmojiCompatInitializer"
436-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
437                android:value="androidx.startup" />
437-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
438            <meta-data
438-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
439                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
439-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
440                android:value="androidx.startup" />
440-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
441        </provider>
442
443        <service
443-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:9:9-15:19
444            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
444-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:10:13-91
445            android:directBootAware="true"
445-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:17:13-43
446            android:exported="false" >
446-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:11:13-37
447            <meta-data
447-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:12:13-14:85
448                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
448-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:13:17-114
449                android:value="com.google.firebase.components.ComponentRegistrar" />
449-->[com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:14:17-82
450            <meta-data
450-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:12:13-14:85
451                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
451-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:13:17-124
452                android:value="com.google.firebase.components.ComponentRegistrar" />
452-->[com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:14:17-82
453            <meta-data
453-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:20:13-22:85
454                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
454-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:21:17-120
455                android:value="com.google.firebase.components.ComponentRegistrar" />
455-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:22:17-82
456        </service>
457
458        <provider
458-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:9:9-13:38
459            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
459-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:10:13-78
460            android:authorities="com.pangu.keepaliveperfect.demo.mlkitinitprovider"
460-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:11:13-69
461            android:exported="false"
461-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:12:13-37
462            android:initOrder="99" />
462-->[com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:13:13-35
463
464        <activity
464-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
465            android:name="com.google.android.gms.common.api.GoogleApiActivity"
465-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
466            android:exported="false"
466-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
467            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
467-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
468
469        <meta-data
469-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:21:9-23:69
470            android:name="com.google.android.gms.version"
470-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:22:13-58
471            android:value="@integer/google_play_services_version" />
471-->[com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:23:13-66
472
473        <uses-library
473-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
474            android:name="androidx.window.extensions"
474-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
475            android:required="false" />
475-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
476        <uses-library
476-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
477            android:name="androidx.window.sidecar"
477-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
478            android:required="false" />
478-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
479
480        <service
480-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
481            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
481-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
482            android:directBootAware="false"
482-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
483            android:enabled="@bool/enable_system_alarm_service_default"
483-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
484            android:exported="false" />
484-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
485        <service
485-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
486            android:name="androidx.work.impl.background.systemjob.SystemJobService"
486-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
487            android:directBootAware="false"
487-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
488            android:enabled="@bool/enable_system_job_service_default"
488-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
489            android:exported="true"
489-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
490            android:permission="android.permission.BIND_JOB_SERVICE" />
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
491        <service
491-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
492            android:name="androidx.work.impl.foreground.SystemForegroundService"
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
493            android:directBootAware="false"
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
494            android:enabled="@bool/enable_system_foreground_service_default"
494-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
495            android:exported="false" />
495-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
496
497        <receiver
497-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
498            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
498-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
499            android:directBootAware="false"
499-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
500            android:enabled="true"
500-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
501            android:exported="false" />
501-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
502        <receiver
502-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
503            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
503-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
504            android:directBootAware="false"
504-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
505            android:enabled="false"
505-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
506            android:exported="false" >
506-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
507            <intent-filter>
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
508                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
508-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
508-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
509                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
509-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
509-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
510            </intent-filter>
511        </receiver>
512        <receiver
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
513            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
513-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
514            android:directBootAware="false"
514-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
515            android:enabled="false"
515-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
516            android:exported="false" >
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
517            <intent-filter>
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
518                <action android:name="android.intent.action.BATTERY_OKAY" />
518-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
518-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
519                <action android:name="android.intent.action.BATTERY_LOW" />
519-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
519-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
520            </intent-filter>
521        </receiver>
522        <receiver
522-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
523            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
523-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
524            android:directBootAware="false"
524-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
525            android:enabled="false"
525-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
526            android:exported="false" >
526-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
527            <intent-filter>
527-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
528                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
528-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
528-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
529                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
529-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
529-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
530            </intent-filter>
531        </receiver>
532        <receiver
532-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
533            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
533-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
534            android:directBootAware="false"
534-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
535            android:enabled="false"
535-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
536            android:exported="false" >
536-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
537            <intent-filter>
537-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
538                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
538-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
538-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
539            </intent-filter>
540        </receiver>
541        <receiver
541-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
542            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
542-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
543            android:directBootAware="false"
543-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
544            android:enabled="false"
544-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
545            android:exported="false" >
545-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
546            <intent-filter>
546-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
547                <action android:name="android.intent.action.BOOT_COMPLETED" />
547-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:313:17-79
547-->C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:313:25-76
548                <action android:name="android.intent.action.TIME_SET" />
548-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
548-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
549                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
549-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
549-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
550            </intent-filter>
551        </receiver>
552        <receiver
552-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
553            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
553-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
554            android:directBootAware="false"
554-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
555            android:enabled="@bool/enable_system_alarm_service_default"
555-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
556            android:exported="false" >
556-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
557            <intent-filter>
557-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
558                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
558-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
558-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
559            </intent-filter>
560        </receiver>
561        <receiver
561-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
562            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
562-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
563            android:directBootAware="false"
563-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
564            android:enabled="true"
564-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
565            android:exported="true"
565-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
566            android:permission="android.permission.DUMP" >
566-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
567            <intent-filter>
567-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
568                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
568-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
568-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
569            </intent-filter>
570        </receiver>
571
572        <service
572-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
573            android:name="androidx.room.MultiInstanceInvalidationService"
573-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
574            android:directBootAware="true"
574-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
575            android:exported="false" />
575-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
576        <service
576-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
577            android:name="androidx.camera.core.impl.MetadataHolderService"
577-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
578            android:enabled="false"
578-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
579            android:exported="false" >
579-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
580            <meta-data
580-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
581                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
581-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
582                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
582-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
583        </service>
584        <service
584-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
585            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
585-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
586            android:exported="false" >
586-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
587            <meta-data
587-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
588                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
588-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
589                android:value="cct" />
589-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
590        </service>
591        <service
591-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
592            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
592-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
593            android:exported="false"
593-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
594            android:permission="android.permission.BIND_JOB_SERVICE" >
594-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
595        </service>
596
597        <receiver
597-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
598            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
598-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
599            android:exported="false" />
599-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
600    </application>
601
602</manifest>
