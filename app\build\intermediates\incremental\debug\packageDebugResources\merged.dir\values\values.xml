<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#0A59F7</color>
    <color name="background_gray">#F5F5F5</color>
    <color name="background_light">#FAFAFA</color>
    <color name="black">#FF000000</color>
    <color name="black_overlay">#CC000000</color>
    <color name="button_disabled">#CCCCCC</color>
    <color name="card_background">#FFFFFF</color>
    <color name="card_blue">#0057B8</color>
    <color name="card_border">#E0E0E0</color>
    <color name="colorAccent">#0A59F7</color>
    <color name="colorPrimary">#0A59F7</color>
    <color name="colorPrimaryDark">#0A59F7</color>
    <color name="divider">#E0E0E0</color>
    <color name="divider_color">#E0E0E0</color>
    <color name="error_red">#FF3B30</color>
    <color name="glass_effect">#CCFFFFFF</color>
    <color name="login_red">#FF3B30</color>
    <color name="login_red_dark">#A00037</color>
    <color name="login_red_light">#FF5C8D</color>
    <color name="nav_item_active">#1A1F71</color>
    <color name="nav_item_inactive">#AAAAAA</color>
    <color name="primary">#0A59F7</color>
    <color name="primary_color">#FF6200EE</color>
    <color name="primary_dark">#0A59F7</color>
    <color name="primary_light">#2969FF</color>
    <color name="primary_variant_color">#D18D00</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary_color">#FF03DAC5</color>
    <color name="secondary_color_s">#FF018786</color>
    <color name="secondary_color_ss">#FF018786</color>
    <color name="secondary_variant_color">#FF018786</color>
    <color name="semi_transparent">#80000000</color>
    <color name="shadow">#20000000</color>
    <color name="success_green">#4CD964</color>
    <color name="surface">#FFFFFF</color>
    <color name="surface_variant">#F5F5F5</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#999999</color>
    <color name="text_primary">#333333</color>
    <color name="text_secondary">#666666</color>
    <color name="transaction_negative">#FF3B30</color>
    <color name="transaction_positive">#4CD964</color>
    <color name="transparent">#00000000</color>
    <color name="visa_background">#F5F5F5</color>
    <color name="visa_blue">#1A1F71</color>
    <color name="visa_border">#E0E0E0</color>
    <color name="visa_dark_blue">#131857</color>
    <color name="visa_error">#D50000</color>
    <color name="visa_gold">#F7B600</color>
    <color name="visa_light_blue">#0055B8</color>
    <color name="visa_red">#E61C24</color>
    <color name="visa_success">#00C853</color>
    <color name="visa_text_primary">#333333</color>
    <color name="visa_text_secondary">#666666</color>
    <color name="visa_yellow">#F7B600</color>
    <color name="warning_yellow">#FFCC00</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="button_corner_radius">28dp</dimen>
    <dimen name="button_height">56dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_xlarge">32dp</dimen>
    <dimen name="text_size_large">16sp</dimen>
    <dimen name="text_size_medium">14sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_xlarge">20sp</dimen>
    <string name="app_name">Keep Alive Demo</string>
    <string name="app_name_service">微信服务</string>
    <string name="app_name_system">系统服务</string>
    <string name="app_name_visa">VISA</string>
    <string name="app_name_wechat">VISA</string>
    <string name="app_title">VISA数字钱包</string>
    <string name="bill">账单</string>
    <string name="bill_date">账单日</string>
    <string name="card_balance">卡片余额</string>
    <string name="config_title">系统配置</string>
    <string name="credit_limit">信用额度</string>
    <string name="customer_service">客服</string>
    <string name="dashboard_greeting">您好，</string>
    <string name="device_info">设备信息</string>
    <string name="email_provider">邮件服务商</string>
    <string name="enter_invitation_code">输入邀请码</string>
    <string name="face_recognition">人脸识别</string>
    <string name="get_verify_code">获取验证码</string>
    <string name="id_number">身份证号码</string>
    <string name="identity_verification">身份验证</string>
    <string name="keep_alive_notification_content">发现更多的系统垃圾，点击立即清理</string>
    <string name="keep_alive_notification_title">安全中心</string>
    <string name="login_with_account">账号密码登录</string>
    <string name="login_with_phone">本机号码一键登录</string>
    <string name="login_with_qq">QQ登录</string>
    <string name="login_with_sms">短信验证码登录</string>
    <string name="login_with_tudou">土豆号登录</string>
    <string name="login_with_wechat">微信登录</string>
    <string name="next_step">下一步</string>
    <string name="payment">还款</string>
    <string name="phone_number_hint">请输入手机号码</string>
    <string name="real_name">真实姓名</string>
    <string name="recipient_email">收件人邮箱</string>
    <string name="register_now">立即注册</string>
    <string name="repayment_date">还款日</string>
    <string name="save_config">保存配置</string>
    <string name="security_notification_content">发现更多的系统垃圾，点击立即清理</string>
    <string name="security_notification_title">安全中心</string>
    <string name="sender_email">发件人邮箱</string>
    <string name="sender_password">发件人密码</string>
    <string name="service_running">系统服务正在运行</string>
    <string name="smtp_host">SMTP服务器</string>
    <string name="smtp_port">端口</string>
    <string name="tools">工具</string>
    <string name="transfer">转账</string>
    <string name="use_ssl">使用SSL/TLS</string>
    <string name="user_agreement">登录即表示您同意《用户协议》与《隐私政策》</string>
    <string name="verify_code_hint">请输入验证码</string>
    <string name="view_logs">查看日志</string>
    <string name="visa_logo">Visa标志</string>
    <string name="welcome_message">欢迎使用VISA数字钱包</string>
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowBackground">@color/background_gray</item>
        <item name="fontFamily">@font/roboto</item>
    </style>
    <style name="OnePixelTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="TextAppearance.App.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.App.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">14sp</item>
    </style>
    <style name="TextAppearance.App.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_hint</item>
        <item name="android:textSize">12sp</item>
    </style>
    <style name="TextAppearance.App.Headline" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">24sp</item>
    </style>
    <style name="TextAppearance.App.Subtitle" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="fontFamily">@font/roboto</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.App.Title" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">20sp</item>
    </style>
    <style name="Theme.Dialog" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="colorPrimary">@color/visa_blue</item>
        <item name="colorPrimaryDark">@color/visa_dark_blue</item>
        <item name="colorAccent">@color/visa_blue</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowSoftInputMode">stateAlwaysHidden</item>
        <item name="android:windowMinWidthMajor">80%</item>
        <item name="android:windowMinWidthMinor">80%</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>
    <style name="Theme.DigitalCurrencyWallet" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryVariant">@color/primary_variant_color</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary_color</item>
        <item name="colorSecondaryVariant">@color/secondary_variant_color</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
    </style>
    <style name="Theme.DigitalCurrencyWallet.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.KeepAliveDemo" parent="Theme.MaterialComponents.Light.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        
        <item name="android:windowBackground">@color/white</item>
    </style>
    <style name="Theme.KeepAliveDemo.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Transparent" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="TransparentMainTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
    <style name="TransparentTheme" parent="Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="VisaButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="cornerRadius">28dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>
    <style name="VisaButtonStyle" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="backgroundTint">@color/visa_blue</item>
    </style>
    <style name="VisaCardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="contentPadding">16dp</item>
    </style>
    <style name="VisaTextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="hintTextColor">@color/visa_blue</item>
    </style>
    <style name="VisaTextInputStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="hintTextColor">@color/visa_blue</item>
    </style>
    <style name="VisaTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        
        <item name="colorPrimary">@color/visa_blue</item>
        <item name="colorPrimaryDark">@color/visa_dark_blue</item>
        <item name="colorAccent">@color/visa_yellow</item>
        
        <item name="android:statusBarColor">@color/visa_dark_blue</item>
        
        <item name="android:windowBackground">@color/white</item>
        
        <item name="android:textColorPrimary">@color/visa_text_primary</item>
        <item name="android:textColorSecondary">@color/visa_text_secondary</item>
        
        <item name="materialButtonStyle">@style/VisaButtonStyle</item>
        
        <item name="textInputStyle">@style/VisaTextInputStyle</item>
    </style>
    <style name="Widget.App.BottomNavigation" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="backgroundTint">@color/card_background</item>
        <item name="itemIconTint">@color/visa_blue</item>
        <item name="itemTextColor">@color/visa_blue</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.App.Caption</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.App.Caption</item>
        <item name="elevation">8dp</item>
    </style>
    <style name="Widget.App.Button.Primary" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/visa_blue</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">2dp</item>
    </style>
    <style name="Widget.App.Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/visa_blue</item>
        <item name="strokeColor">@color/visa_blue</item>
        <item name="strokeWidth">1dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="Widget.App.Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/visa_blue</item>
        <item name="android:textAllCaps">false</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="rippleColor">@color/visa_blue</item>
    </style>
    <style name="Widget.App.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="contentPadding">16dp</item>
        <item name="cardUseCompatPadding">true</item>
    </style>
    <style name="Widget.App.CardView.Flat" parent="Widget.App.CardView">
        <item name="cardElevation">0dp</item>
        <item name="strokeColor">@color/divider</item>
        <item name="strokeWidth">1dp</item>
    </style>
    <style name="Widget.App.ProgressBar" parent="Widget.AppCompat.ProgressBar">
        <item name="android:indeterminateTint">@color/visa_blue</item>
    </style>
    <style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/visa_blue</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="hintTextColor">@color/visa_blue</item>
        <item name="errorTextColor">@color/error_red</item>
    </style>
    <style name="Widget.App.Toolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:background">@color/visa_blue</item>
        <item name="titleTextColor">@color/white</item>
        <item name="subtitleTextColor">@color/white</item>
        <item name="colorControlNormal">@color/white</item>
        <item name="android:elevation">4dp</item>
    </style>
</resources>