# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-module-model
      create-ndk-meta-abi-list 14ms
    create-module-model completed in 28ms
    [gap of 24ms]
  create-initial-cxx-model completed in 93ms
create_cxx_tasks completed in 95ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 53ms
create_cxx_tasks completed in 57ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 28ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 11ms
    [gap of 63ms]
  create-initial-cxx-model completed in 82ms
create_cxx_tasks completed in 84ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 17ms]
    create-module-model 10ms
    [gap of 16ms]
  create-initial-cxx-model completed in 43ms
create_cxx_tasks completed in 45ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 30ms

