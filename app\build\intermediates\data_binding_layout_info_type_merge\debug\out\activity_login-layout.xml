<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="51"/></Target><Target id="@+id/cardVisaDesign" tag="layout/activity_login_0" include="layout_visa_card"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="51"/></Target><Target id="@+id/tvWelcome" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="30" endOffset="67"/></Target><Target id="@+id/cardLogin" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="32" startOffset="4" endLine="191" endOffset="55"/></Target><Target id="@+id/btnPhoneLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="52" startOffset="12" endLine="64" endOffset="45"/></Target><Target id="@+id/llAccountLogin" view="LinearLayout"><Expressions/><location startLine="88" startOffset="16" endLine="111" endOffset="30"/></Target><Target id="@+id/llWechatLogin" view="LinearLayout"><Expressions/><location startLine="113" startOffset="16" endLine="136" endOffset="30"/></Target><Target id="@+id/llQQLogin" view="LinearLayout"><Expressions/><location startLine="138" startOffset="16" endLine="161" endOffset="30"/></Target><Target id="@+id/llRegister" view="LinearLayout"><Expressions/><location startLine="165" startOffset="12" endLine="189" endOffset="26"/></Target><Target id="@+id/tvRegister" view="TextView"><Expressions/><location startLine="180" startOffset="16" endLine="188" endOffset="46"/></Target><Target id="@+id/tvAgreement" view="TextView"><Expressions/><location startLine="193" startOffset="4" endLine="203" endOffset="55"/></Target></Targets></Layout>