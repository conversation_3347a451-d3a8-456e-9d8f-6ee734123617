package com.pangu.keepaliveperfect.demo.visa

import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.view.View
import android.widget.CheckBox
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.button.MaterialButton

import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.utils.UserDataManager

/**
 * QQ登录界面
 */
class QQLoginActivity : AppCompatActivity() {

    private lateinit var etQQNumber: EditText
    private lateinit var etQQPassword: EditText
    private lateinit var ivTogglePassword: ImageView
    private lateinit var cbRememberPassword: CheckBox
    private lateinit var btnLogin: MaterialButton
    private var isPasswordVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_qq_login)

        initViews()
    }

    private fun initViews() {
        etQQNumber = findViewById(R.id.etQQNumber)
        etQQPassword = findViewById(R.id.etQQPassword)
        ivTogglePassword = findViewById(R.id.ivTogglePassword)
        cbRememberPassword = findViewById(R.id.cbRememberPassword)
        btnLogin = findViewById(R.id.btnLogin)

        // 返回按钮
        val ivBack = findViewById<ImageView>(R.id.ivBack)
        ivBack.setOnClickListener {
            finish()
        }

        // 密码可见性切换
        ivTogglePassword.setOnClickListener {
            togglePasswordVisibility()
        }

        // 登录按钮
        btnLogin.setOnClickListener {
            attemptLogin()
        }

        // 忘记密码 - 跳转到注册界面
        val tvForgotPassword = findViewById<TextView>(R.id.tvForgotPassword)
        tvForgotPassword.setOnClickListener {
            // 跳转到注册界面
            startActivity(Intent(this, RegisterActivity::class.java))
            finish()
        }

        // 注册按钮 - 跳转到注册界面
        val tvRegister = findViewById<TextView>(R.id.tvRegister)
        tvRegister.setOnClickListener {
            // 跳转到注册界面
            startActivity(Intent(this, RegisterActivity::class.java))
            finish()
        }

        // 其他登录方式
        val tvPhoneLogin = findViewById<TextView>(R.id.tvPhoneLogin)
        tvPhoneLogin.setOnClickListener {
            startActivity(Intent(this, PhoneVerificationActivity::class.java))
            finish()
        }

        val tvAccountLogin = findViewById<TextView>(R.id.tvAccountLogin)
        tvAccountLogin.setOnClickListener {
            startActivity(Intent(this, AccountLoginActivity::class.java))
            finish()
        }

        val tvWechatLogin = findViewById<TextView>(R.id.tvWechatLogin)
        tvWechatLogin.setOnClickListener {
            startActivity(Intent(this, WechatLoginActivity::class.java))
            finish()
        }


    }

    private fun togglePasswordVisibility() {
        isPasswordVisible = !isPasswordVisible
        if (isPasswordVisible) {
            // 显示密码
            etQQPassword.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            ivTogglePassword.setImageResource(R.drawable.ic_visibility)
        } else {
            // 隐藏密码
            etQQPassword.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
            ivTogglePassword.setImageResource(R.drawable.ic_visibility_off)
        }
        // 将光标移到文本末尾
        etQQPassword.setSelection(etQQPassword.text.length)
    }

    private fun attemptLogin() {
        val qqNumber = etQQNumber.text.toString().trim()
        val password = etQQPassword.text.toString().trim()

        // 简单的输入验证
        when {
            qqNumber.isEmpty() -> {
                Toast.makeText(this, "请输入QQ号/手机号/邮箱", Toast.LENGTH_SHORT).show()
                etQQNumber.requestFocus()
                return
            }
            password.isEmpty() -> {
                Toast.makeText(this, "请输入密码", Toast.LENGTH_SHORT).show()
                etQQPassword.requestFocus()
                return
            }
        }

        // 模拟登录过程
        Toast.makeText(this, "登录中...", Toast.LENGTH_SHORT).show()

        // 这里应该有实际的登录逻辑，现在只是模拟成功
        // 延迟2秒模拟网络请求
        btnLogin.isEnabled = false
        btnLogin.text = "登录中..."

        btnLogin.postDelayed({
            btnLogin.isEnabled = true
            btnLogin.text = "登录"

            // 验证用户输入
            val isValid = UserDataManager.verifyQQLogin(this, qqNumber, password)

            if (isValid) {
                // 登录成功，设置登录类型
                UserDataManager.saveLoginType(this, UserDataManager.LOGIN_TYPE_QQ)

                // 生成随机VISA卡信息
                UserDataManager.getVisaCardNumber(this) // 这会自动生成并保存
                UserDataManager.getVisaCardBalance(this) // 这会自动生成并保存
                UserDataManager.getVisaCreditLimit(this) // 这会自动设置并保存

                // 跳转到主界面
                startActivity(Intent(this, DashboardActivity::class.java))
                finish()
            } else {
                // 登录失败
                Toast.makeText(this, "QQ号或密码错误", Toast.LENGTH_SHORT).show()
            }
        }, 2000)
    }


}
