package com.pangu.keepaliveperfect.utils

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.lang.reflect.Method
import java.util.*

/**
 * 设备工具类
 * 用于识别各厂商设备和系统版本
 * 提供适配不同设备的工具方法
 */
object DeviceUtils {
    private const val TAG = "DeviceUtils"

    // 设备厂商
    private val BRAND = Build.BRAND.lowercase(Locale.ROOT)
    private val MANUFACTURER = Build.MANUFACTURER.lowercase(Locale.ROOT)
    private val MODEL = Build.MODEL.lowercase(Locale.ROOT)
    
    // 系统版本
    val SDK_INT = Build.VERSION.SDK_INT
    
    // 设备信息缓存
    private var cachedDeviceInfo: Map<String, String>? = null
    
    /**
     * 判断是否是华为设备
     */
    fun isHuawei(): Boolean {
        return BRAND.contains("huawei") || MANUFACTURER.contains("huawei") || 
               BRAND.contains("honor") || MANUFACTURER.contains("honor")
    }
    
    /**
     * 判断是否是小米设备
     */
    fun isXiaomi(): Boolean {
        return BRAND.contains("xiaomi") || MANUFACTURER.contains("xiaomi") || 
               BRAND.contains("redmi") || MANUFACTURER.contains("redmi")
    }
    
    /**
     * 判断是否是OPPO设备
     */
    fun isOppo(): Boolean {
        return BRAND.contains("oppo") || MANUFACTURER.contains("oppo") ||
               BRAND.contains("oneplus") || MANUFACTURER.contains("oneplus") ||
               BRAND.contains("realme") || MANUFACTURER.contains("realme")
    }
    
    /**
     * 判断是否是vivo设备
     */
    fun isVivo(): Boolean {
        return BRAND.contains("vivo") || MANUFACTURER.contains("vivo") ||
               BRAND.contains("iqoo") || MANUFACTURER.contains("iqoo")
    }
    
    /**
     * 判断是否是三星设备
     */
    fun isSamsung(): Boolean {
        return BRAND.contains("samsung") || MANUFACTURER.contains("samsung")
    }
    
    /**
     * 判断是否是魅族设备
     */
    fun isMeizu(): Boolean {
        return BRAND.contains("meizu") || MANUFACTURER.contains("meizu")
    }

    /**
     * 判断是否是联想设备
     */
    fun isLenovo(): Boolean {
        return BRAND.contains("lenovo") || MANUFACTURER.contains("lenovo")
    }
    
    /**
     * 获取设备品牌
     */
    fun getDeviceBrand(): String {
        return Build.BRAND
    }
    
    /**
     * 获取ROM版本
     */
    fun getRomVersion(): String {
        try {
            when {
                isHuawei() -> {
                    return getSystemProperty("ro.build.version.emui", "")
                }
                isXiaomi() -> {
                    return getSystemProperty("ro.miui.ui.version.name", "")
                }
                isOppo() -> {
                    return getSystemProperty("ro.build.version.opporom", "")
                }
                isVivo() -> {
                    return getSystemProperty("ro.vivo.os.version", "")
                }
                else -> {
                    return ""
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取ROM版本失败", e)
            return ""
        }
    }
    
    /**
     * 获取系统属性
     */
    @SuppressLint("PrivateApi")
    fun getSystemProperty(key: String, defaultValue: String): String {
        try {
            val clz = Class.forName("android.os.SystemProperties")
            val get: Method = clz.getMethod("get", String::class.java, String::class.java)
            return get.invoke(clz, key, defaultValue) as String
        } catch (e: Exception) {
            Log.e(TAG, "获取系统属性失败: $key", e)
        }
        
        // 尝试通过命令行获取
        try {
            val process = Runtime.getRuntime().exec("getprop $key")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val line = reader.readLine()
            reader.close()
            return if (TextUtils.isEmpty(line)) defaultValue else line
        } catch (e: Exception) {
            Log.e(TAG, "通过命令行获取系统属性失败: $key", e)
        }
        
        return defaultValue
    }
    
    /**
     * 获取设备唯一标识符
     */
    @SuppressLint("HardwareIds")
    fun getDeviceId(context: Context): String {
        var deviceId = ""
        
        try {
            // 尝试获取ANDROID_ID
            val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
            if (!TextUtils.isEmpty(androidId) && !"9774d56d682e549c".equals(androidId, ignoreCase = true)) {
                deviceId = androidId
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取ANDROID_ID失败", e)
        }
        
        if (TextUtils.isEmpty(deviceId)) {
            // 使用设备硬件信息生成标识符
            val sb = StringBuilder()
            sb.append(Build.BOARD)
            sb.append(Build.BOOTLOADER)
            sb.append(Build.BRAND)
            sb.append(Build.DEVICE)
            sb.append(Build.HARDWARE)
            sb.append(Build.PRODUCT)
            sb.append(Build.SERIAL)
            
            deviceId = UUID.nameUUIDFromBytes(sb.toString().toByteArray()).toString()
        }
        
        return deviceId
    }
    
    /**
     * 获取设备详细信息
     */
    fun getDeviceInfo(context: Context): Map<String, String> {
        if (cachedDeviceInfo != null) {
            return cachedDeviceInfo!!
        }
        
        val info = HashMap<String, String>()
        
        try {
            // 基本设备信息
            info["deviceId"] = getDeviceId(context)
            info["brand"] = Build.BRAND
            info["manufacturer"] = Build.MANUFACTURER
            info["model"] = Build.MODEL
            info["device"] = Build.DEVICE
            info["product"] = Build.PRODUCT
            info["sdk"] = Build.VERSION.SDK_INT.toString()
            info["release"] = Build.VERSION.RELEASE
            info["hardware"] = Build.HARDWARE
            info["serial"] = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) "unknown" else Build.SERIAL
            info["androidId"] = getAndroidId(context)
            info["romVersion"] = getRomVersion()
            
            // 尝试获取更多信息，可能需要权限
            try {
                info["phoneNumber"] = getPhoneNumber(context)
            } catch (e: Exception) {
                Log.e(TAG, "获取手机号码失败", e)
                info["phoneNumber"] = ""
            }
            
            try {
                info["imei"] = getImei(context)
            } catch (e: Exception) {
                Log.e(TAG, "获取IMEI失败", e)
                info["imei"] = ""
            }
            
            // 缓存结果
            cachedDeviceInfo = info
        } catch (e: Exception) {
            Log.e(TAG, "获取设备信息失败", e)
        }
        
        return info
    }
    
    /**
     * 获取手机号码
     * 需要READ_PHONE_STATE权限
     */
    @SuppressLint("MissingPermission", "HardwareIds")
    fun getPhoneNumber(context: Context): String {
        try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as android.telephony.TelephonyManager
            val phoneNumber = telephonyManager.line1Number
            return if (!TextUtils.isEmpty(phoneNumber)) {
                phoneNumber
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取手机号码失败", e)
            return ""
        }
    }
    
    /**
     * 获取IMEI
     * 注意：Android 10及以上无法获取IMEI
     * 需要READ_PHONE_STATE权限
     */
    @SuppressLint("MissingPermission", "HardwareIds")
    fun getImei(context: Context): String {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10及以上无法获取IMEI
            return ""
        }
        
        try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as android.telephony.TelephonyManager
            
            @Suppress("DEPRECATION")
            val imei = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                telephonyManager.imei
            } else {
                telephonyManager.deviceId
            }
            
            return imei ?: ""
        } catch (e: Exception) {
            Log.e(TAG, "获取IMEI失败", e)
            return ""
        }
    }
    
    /**
     * 获取AndroidID
     */
    @SuppressLint("HardwareIds")
    fun getAndroidId(context: Context): String {
        try {
            val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
            return androidId ?: ""
        } catch (e: Exception) {
            Log.e(TAG, "获取AndroidID失败", e)
            return ""
        }
    }
    
    /**
     * 判断设备是否已Root
     */
    fun isRooted(): Boolean {
        val paths = arrayOf(
            "/system/bin/su", 
            "/system/xbin/su",
            "/system/sbin/su", 
            "/sbin/su",
            "/vendor/bin/su",
            "/data/local/su", 
            "/data/local/bin/su", 
            "/data/local/xbin/su"
        )
        
        for (path in paths) {
            if (File(path).exists()) {
                return true
            }
        }
        
        return false
    }
    
    /**
     * 判断是否是模拟器
     */
    fun isEmulator(): Boolean {
        return (Build.BRAND.contains("generic") && Build.DEVICE.contains("generic"))
                || Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk" == Build.PRODUCT
    }
    
    /**
     * 检查网络是否可用
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            return capabilities.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            return networkInfo != null && networkInfo.isConnected
        }
    }
} 