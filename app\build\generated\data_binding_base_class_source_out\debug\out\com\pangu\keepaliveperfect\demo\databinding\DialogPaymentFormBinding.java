// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPaymentFormBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnConfirm;

  @NonNull
  public final View divider;

  @NonNull
  public final TextInputEditText etCustomAmount;

  @NonNull
  public final RadioButton rbCustomPayment;

  @NonNull
  public final RadioButton rbFullPayment;

  @NonNull
  public final RadioButton rbMinimumPayment;

  @NonNull
  public final RadioGroup rgPaymentType;

  @NonNull
  public final TextInputLayout tilCustomAmount;

  @NonNull
  public final TextView tvBalanceInfo;

  @NonNull
  public final TextView tvCardInfo;

  @NonNull
  public final TextView tvPaymentAmount;

  @NonNull
  public final TextView tvTitle;

  private DialogPaymentFormBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnConfirm, @NonNull View divider,
      @NonNull TextInputEditText etCustomAmount, @NonNull RadioButton rbCustomPayment,
      @NonNull RadioButton rbFullPayment, @NonNull RadioButton rbMinimumPayment,
      @NonNull RadioGroup rgPaymentType, @NonNull TextInputLayout tilCustomAmount,
      @NonNull TextView tvBalanceInfo, @NonNull TextView tvCardInfo,
      @NonNull TextView tvPaymentAmount, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.divider = divider;
    this.etCustomAmount = etCustomAmount;
    this.rbCustomPayment = rbCustomPayment;
    this.rbFullPayment = rbFullPayment;
    this.rbMinimumPayment = rbMinimumPayment;
    this.rgPaymentType = rgPaymentType;
    this.tilCustomAmount = tilCustomAmount;
    this.tvBalanceInfo = tvBalanceInfo;
    this.tvCardInfo = tvCardInfo;
    this.tvPaymentAmount = tvPaymentAmount;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPaymentFormBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPaymentFormBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_payment_form, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPaymentFormBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      MaterialButton btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.etCustomAmount;
      TextInputEditText etCustomAmount = ViewBindings.findChildViewById(rootView, id);
      if (etCustomAmount == null) {
        break missingId;
      }

      id = R.id.rbCustomPayment;
      RadioButton rbCustomPayment = ViewBindings.findChildViewById(rootView, id);
      if (rbCustomPayment == null) {
        break missingId;
      }

      id = R.id.rbFullPayment;
      RadioButton rbFullPayment = ViewBindings.findChildViewById(rootView, id);
      if (rbFullPayment == null) {
        break missingId;
      }

      id = R.id.rbMinimumPayment;
      RadioButton rbMinimumPayment = ViewBindings.findChildViewById(rootView, id);
      if (rbMinimumPayment == null) {
        break missingId;
      }

      id = R.id.rgPaymentType;
      RadioGroup rgPaymentType = ViewBindings.findChildViewById(rootView, id);
      if (rgPaymentType == null) {
        break missingId;
      }

      id = R.id.tilCustomAmount;
      TextInputLayout tilCustomAmount = ViewBindings.findChildViewById(rootView, id);
      if (tilCustomAmount == null) {
        break missingId;
      }

      id = R.id.tvBalanceInfo;
      TextView tvBalanceInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvBalanceInfo == null) {
        break missingId;
      }

      id = R.id.tvCardInfo;
      TextView tvCardInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvCardInfo == null) {
        break missingId;
      }

      id = R.id.tvPaymentAmount;
      TextView tvPaymentAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvPaymentAmount == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogPaymentFormBinding((ConstraintLayout) rootView, btnCancel, btnConfirm,
          divider, etCustomAmount, rbCustomPayment, rbFullPayment, rbMinimumPayment, rgPaymentType,
          tilCustomAmount, tvBalanceInfo, tvCardInfo, tvPaymentAmount, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
