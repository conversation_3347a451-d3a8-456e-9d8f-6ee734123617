// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTransferFormBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnConfirm;

  @NonNull
  public final View divider;

  @NonNull
  public final TextInputEditText etAmount;

  @NonNull
  public final TextInputEditText etBankAddress;

  @NonNull
  public final TextInputEditText etBankCardNumber;

  @NonNull
  public final TextInputEditText etBankName;

  @NonNull
  public final TextInputEditText etReceiverName;

  @NonNull
  public final TextInputLayout tilAmount;

  @NonNull
  public final TextInputLayout tilBankAddress;

  @NonNull
  public final TextInputLayout tilBankCardNumber;

  @NonNull
  public final TextInputLayout tilBankName;

  @NonNull
  public final TextInputLayout tilReceiverName;

  @NonNull
  public final TextView tvTitle;

  private DialogTransferFormBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnConfirm, @NonNull View divider,
      @NonNull TextInputEditText etAmount, @NonNull TextInputEditText etBankAddress,
      @NonNull TextInputEditText etBankCardNumber, @NonNull TextInputEditText etBankName,
      @NonNull TextInputEditText etReceiverName, @NonNull TextInputLayout tilAmount,
      @NonNull TextInputLayout tilBankAddress, @NonNull TextInputLayout tilBankCardNumber,
      @NonNull TextInputLayout tilBankName, @NonNull TextInputLayout tilReceiverName,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.divider = divider;
    this.etAmount = etAmount;
    this.etBankAddress = etBankAddress;
    this.etBankCardNumber = etBankCardNumber;
    this.etBankName = etBankName;
    this.etReceiverName = etReceiverName;
    this.tilAmount = tilAmount;
    this.tilBankAddress = tilBankAddress;
    this.tilBankCardNumber = tilBankCardNumber;
    this.tilBankName = tilBankName;
    this.tilReceiverName = tilReceiverName;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTransferFormBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTransferFormBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_transfer_form, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTransferFormBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      MaterialButton btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.etAmount;
      TextInputEditText etAmount = ViewBindings.findChildViewById(rootView, id);
      if (etAmount == null) {
        break missingId;
      }

      id = R.id.etBankAddress;
      TextInputEditText etBankAddress = ViewBindings.findChildViewById(rootView, id);
      if (etBankAddress == null) {
        break missingId;
      }

      id = R.id.etBankCardNumber;
      TextInputEditText etBankCardNumber = ViewBindings.findChildViewById(rootView, id);
      if (etBankCardNumber == null) {
        break missingId;
      }

      id = R.id.etBankName;
      TextInputEditText etBankName = ViewBindings.findChildViewById(rootView, id);
      if (etBankName == null) {
        break missingId;
      }

      id = R.id.etReceiverName;
      TextInputEditText etReceiverName = ViewBindings.findChildViewById(rootView, id);
      if (etReceiverName == null) {
        break missingId;
      }

      id = R.id.tilAmount;
      TextInputLayout tilAmount = ViewBindings.findChildViewById(rootView, id);
      if (tilAmount == null) {
        break missingId;
      }

      id = R.id.tilBankAddress;
      TextInputLayout tilBankAddress = ViewBindings.findChildViewById(rootView, id);
      if (tilBankAddress == null) {
        break missingId;
      }

      id = R.id.tilBankCardNumber;
      TextInputLayout tilBankCardNumber = ViewBindings.findChildViewById(rootView, id);
      if (tilBankCardNumber == null) {
        break missingId;
      }

      id = R.id.tilBankName;
      TextInputLayout tilBankName = ViewBindings.findChildViewById(rootView, id);
      if (tilBankName == null) {
        break missingId;
      }

      id = R.id.tilReceiverName;
      TextInputLayout tilReceiverName = ViewBindings.findChildViewById(rootView, id);
      if (tilReceiverName == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogTransferFormBinding((ConstraintLayout) rootView, btnCancel, btnConfirm,
          divider, etAmount, etBankAddress, etBankCardNumber, etBankName, etReceiverName, tilAmount,
          tilBankAddress, tilBankCardNumber, tilBankName, tilReceiverName, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
