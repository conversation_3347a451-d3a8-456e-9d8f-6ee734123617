<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_qq_login" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_qq_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_qq_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="271" endOffset="51"/></Target><Target id="@+id/ivBack" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="33"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="31" endOffset="51"/></Target><Target id="@+id/ivQQLogo" view="ImageView"><Expressions/><location startLine="33" startOffset="4" endLine="43" endOffset="33"/></Target><Target id="@+id/cardLogin" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="45" startOffset="4" endLine="202" endOffset="55"/></Target><Target id="@+id/etQQNumber" view="EditText"><Expressions/><location startLine="89" startOffset="16" endLine="100" endOffset="45"/></Target><Target id="@+id/etQQPassword" view="EditText"><Expressions/><location startLine="123" startOffset="16" endLine="134" endOffset="45"/></Target><Target id="@+id/ivTogglePassword" view="ImageView"><Expressions/><location startLine="136" startOffset="16" endLine="142" endOffset="54"/></Target><Target id="@+id/cbRememberPassword" view="CheckBox"><Expressions/><location startLine="157" startOffset="16" endLine="163" endOffset="45"/></Target><Target id="@+id/tvForgotPassword" view="TextView"><Expressions/><location startLine="170" startOffset="16" endLine="176" endOffset="45"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="180" startOffset="12" endLine="189" endOffset="41"/></Target><Target id="@+id/tvRegister" view="TextView"><Expressions/><location startLine="192" startOffset="12" endLine="200" endOffset="41"/></Target><Target id="@+id/tvPhoneLogin" view="TextView"><Expressions/><location startLine="222" startOffset="8" endLine="230" endOffset="38"/></Target><Target id="@+id/tvAccountLogin" view="TextView"><Expressions/><location startLine="241" startOffset="8" endLine="248" endOffset="38"/></Target><Target id="@+id/tvWechatLogin" view="TextView"><Expressions/><location startLine="259" startOffset="8" endLine="266" endOffset="38"/></Target></Targets></Layout>