package com.pangu.keepaliveperfect.demo.counter

import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Process
import android.util.Log
import java.io.File
import java.io.PrintWriter
import java.io.StringWriter

/**
 * 崩溃防御系统
 * 检测和防御OPPO/VIVO的恶意杀进程行为
 */
class CrashDefenseSystem private constructor(private val context: Context) : Thread.UncaughtExceptionHandler {
    
    companion object {
        private const val TAG = "CrashDefenseSystem"
        private const val CRASH_LOG_FILE = "crash_defense.log"
        
        @Volatile
        private var INSTANCE: CrashDefenseSystem? = null
        
        fun getInstance(context: Context): CrashDefenseSystem {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CrashDefenseSystem(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
    private var isInstalled = false
    
    /**
     * 安装崩溃防御系统
     */
    fun install() {
        if (!isInstalled) {
            Thread.setDefaultUncaughtExceptionHandler(this)
            isInstalled = true
            Log.i(TAG, "🛡️ 崩溃防御系统已安装")
            
            // 记录启动信息
            recordStartupInfo()
        }
    }
    
    /**
     * 卸载崩溃防御系统
     */
    fun uninstall() {
        if (isInstalled) {
            Thread.setDefaultUncaughtExceptionHandler(defaultHandler)
            isInstalled = false
            Log.i(TAG, "🛑 崩溃防御系统已卸载")
        }
    }
    
    override fun uncaughtException(t: Thread, e: Throwable) {
        try {
            Log.e(TAG, "⚠️ 检测到未捕获异常: ${e.javaClass.simpleName}: ${e.message}", e)

            // 立即记录崩溃（防止后续处理失败）
            recordImmediateCrash(t, e)

            // 检查重启频率，防止无限循环
            if (!shouldAttemptRestart()) {
                Log.w(TAG, "🛑 重启频率过高，停止自动重启")
                defaultHandler?.uncaughtException(t, e)
                return
            }

            // 分析崩溃原因
            val crashAnalysis = analyzeCrash(e)

            // 记录详细崩溃信息
            recordCrashInfo(t, e, crashAnalysis)

            // 只有在确认是恶意杀进程时才重启
            if (isOppoVivoDevice() && crashAnalysis.isMaliciousKill) {
                Log.w(TAG, "🎯 检测到OPPO/VIVO恶意杀进程，执行重启策略")

                // 记录重启时间
                recordRestartAttempt()

                // 执行反制措施
                executeCounterMeasures(crashAnalysis)

                // 延迟重启，确保日志上传
                Thread {
                    try {
                        Thread.sleep(5000) // 增加延迟时间
                        restartApplication()
                    } catch (ex: Exception) {
                        Log.e(TAG, "延迟重启失败", ex)
                    }
                }.start()

                // 不调用默认处理器，避免系统杀死进程
                return
            }

            // 正常崩溃或非OPPO/VIVO设备，正常处理
            Log.i(TAG, "正常崩溃，交给系统处理")
            defaultHandler?.uncaughtException(t, e)

        } catch (ex: Exception) {
            Log.e(TAG, "崩溃处理器本身出现异常", ex)
            // 确保不会无限循环
            defaultHandler?.uncaughtException(t, e)
        }
    }
    
    /**
     * 分析崩溃原因
     */
    private fun analyzeCrash(e: Throwable): CrashAnalysis {
        val analysis = CrashAnalysis()
        
        try {
            val stackTrace = getStackTraceString(e)
            val message = e.message ?: ""
            
            // 检查是否是OPPO/VIVO特有的异常
            analysis.isMaliciousKill = when {
                // 检查是否是进程被强制杀死
                isProcessKilled() -> {
                    analysis.reason = "进程被系统强制杀死"
                    true
                }
                
                // 检查是否是内存访问被阻止
                message.contains("Permission denied") && stackTrace.contains("proc") -> {
                    analysis.reason = "系统阻止了进程间通信"
                    true
                }
                
                // 检查是否是Binder调用被拒绝
                message.contains("Binder") && message.contains("dead") -> {
                    analysis.reason = "Binder通信被系统阻断"
                    true
                }
                
                // 检查是否是安全策略阻止
                stackTrace.contains("SecurityException") && stackTrace.contains("oppo") -> {
                    analysis.reason = "OPPO安全策略阻止"
                    true
                }
                
                // 检查是否是VIVO限制
                stackTrace.contains("SecurityException") && stackTrace.contains("vivo") -> {
                    analysis.reason = "VIVO安全策略阻止"
                    true
                }
                
                else -> false
            }
            
            analysis.stackTrace = stackTrace
            analysis.errorMessage = message
            analysis.threadName = Thread.currentThread().name
            analysis.timestamp = System.currentTimeMillis()
            
        } catch (ex: Exception) {
            Log.e(TAG, "分析崩溃失败", ex)
        }
        
        return analysis
    }
    
    /**
     * 检查进程是否被杀死
     */
    private fun isProcessKilled(): Boolean {
        return try {
            // 检查进程状态文件
            val myPid = Process.myPid()
            val statFile = File("/proc/$myPid/stat")
            
            if (!statFile.exists()) {
                // 进程状态文件不存在，说明进程被杀死
                true
            } else {
                // 检查进程状态
                val stat = statFile.readText()
                val parts = stat.split(" ")
                
                if (parts.size > 2) {
                    val state = parts[2]
                    // Z表示僵尸进程，说明被杀死了
                    state == "Z"
                } else {
                    false
                }
            }
            
        } catch (e: Exception) {
            // 无法读取进程状态，可能是权限问题
            true
        }
    }
    
    /**
     * 记录崩溃信息
     */
    private fun recordCrashInfo(thread: Thread, exception: Throwable, analysis: CrashAnalysis) {
        try {
            val crashLog = buildString {
                appendLine("=== 崩溃防御系统报告 ===")
                appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                appendLine("线程: ${thread.name}")
                appendLine("恶意杀进程: ${analysis.isMaliciousKill}")
                appendLine("原因: ${analysis.reason}")
                appendLine("错误消息: ${analysis.errorMessage}")
                appendLine("堆栈跟踪:")
                appendLine(analysis.stackTrace)
                appendLine("=== 报告结束 ===")
                appendLine()
            }
            
            // 写入本地日志文件
            val logFile = File(context.filesDir, CRASH_LOG_FILE)
            logFile.appendText(crashLog)
            
            // 上传到七牛云
            uploadCrashLog(crashLog)
            
            Log.i(TAG, "📝 崩溃信息已记录")
            
        } catch (e: Exception) {
            Log.e(TAG, "记录崩溃信息失败", e)
        }
    }
    
    /**
     * 执行反制措施
     */
    private fun executeCounterMeasures(analysis: CrashAnalysis) {
        try {
            Log.i(TAG, "⚔️ 执行反制措施: ${analysis.reason}")
            
            when {
                analysis.reason.contains("进程被系统强制杀死") -> {
                    // 启动多进程守护
                    startProcessGuards()
                    
                    // 干扰SafeCenter
                    val oppoDefense = OppoKillerDefense.getInstance(context)
                    oppoDefense.startDefense()
                }
                
                analysis.reason.contains("Binder通信被系统阻断") -> {
                    // 切换到内存注入模式
                    switchToMemoryInjection()
                }
                
                analysis.reason.contains("安全策略阻止") -> {
                    // 启动反检测系统
                    val antiDetection = AntiDetectionSystem()
                    antiDetection.activate()
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "执行反制措施失败", e)
        }
    }
    
    /**
     * 重启应用
     */
    private fun restartApplication() {
        try {
            Log.i(TAG, "🔄 准备重启应用...")
            
            // 延迟重启，给系统一些时间
            Thread.sleep(2000)
            
            val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
            intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            
            context.startActivity(intent)
            
            Log.i(TAG, "✅ 应用重启指令已发送")
            
            // 杀死当前进程
            Process.killProcess(Process.myPid())
            
        } catch (e: Exception) {
            Log.e(TAG, "重启应用失败", e)
        }
    }
    
    /**
     * 立即记录崩溃（简化版本，确保能记录）
     */
    private fun recordImmediateCrash(thread: Thread, exception: Throwable) {
        try {
            val crashInfo = "CRASH: ${exception.javaClass.simpleName}: ${exception.message} in ${thread.name}"
            Log.e(TAG, crashInfo)

            // 写入本地文件
            val logFile = File(context.filesDir, "immediate_crash.log")
            logFile.appendText("${System.currentTimeMillis()}: $crashInfo\n")

        } catch (e: Exception) {
            // 即使这个失败也不能影响崩溃处理
        }
    }

    /**
     * 检查是否是OPPO/VIVO设备
     */
    private fun isOppoVivoDevice(): Boolean {
        val manufacturer = android.os.Build.MANUFACTURER.lowercase()
        return manufacturer.contains("oppo") || manufacturer.contains("vivo")
    }

    /**
     * 检查是否应该尝试重启
     */
    private fun shouldAttemptRestart(): Boolean {
        try {
            val prefs = context.getSharedPreferences("crash_defense", Context.MODE_PRIVATE)
            val currentTime = System.currentTimeMillis()

            // 获取最近的重启时间列表
            val recentRestarts = mutableListOf<Long>()
            for (i in 0 until 5) { // 检查最近5次重启
                val restartTime = prefs.getLong("restart_time_$i", 0)
                if (restartTime > 0) {
                    recentRestarts.add(restartTime)
                }
            }

            // 清理超过10分钟的记录
            val tenMinutesAgo = currentTime - 10 * 60 * 1000
            val validRestarts = recentRestarts.filter { it > tenMinutesAgo }

            // 如果10分钟内重启超过3次，停止重启
            if (validRestarts.size >= 3) {
                Log.w(TAG, "10分钟内已重启${validRestarts.size}次，停止自动重启")
                return false
            }

            // 检查最近一次重启是否太频繁（30秒内）
            val lastRestart = validRestarts.maxOrNull() ?: 0
            if (currentTime - lastRestart < 30000) {
                Log.w(TAG, "距离上次重启不足30秒，停止重启")
                return false
            }

            return true

        } catch (e: Exception) {
            Log.e(TAG, "检查重启频率失败", e)
            return false // 出错时不重启，避免风险
        }
    }

    /**
     * 记录重启尝试
     */
    private fun recordRestartAttempt() {
        try {
            val prefs = context.getSharedPreferences("crash_defense", Context.MODE_PRIVATE)
            val currentTime = System.currentTimeMillis()

            // 获取现有记录
            val existingTimes = mutableListOf<Long>()
            for (i in 0 until 4) { // 保留最近4次
                val time = prefs.getLong("restart_time_$i", 0)
                if (time > 0) {
                    existingTimes.add(time)
                }
            }

            // 添加当前时间并保存
            val editor = prefs.edit()
            editor.putLong("restart_time_0", currentTime)
            for (i in 0 until minOf(existingTimes.size, 4)) {
                editor.putLong("restart_time_${i + 1}", existingTimes[i])
            }
            editor.apply()

            Log.i(TAG, "记录重启尝试: $currentTime")

        } catch (e: Exception) {
            Log.e(TAG, "记录重启尝试失败", e)
        }
    }

    /**
     * 记录启动信息
     */
    private fun recordStartupInfo() {
        try {
            val startupInfo = buildString {
                appendLine("=== 应用启动信息 ===")
                appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                appendLine("进程ID: ${Process.myPid()}")
                appendLine("设备厂商: ${android.os.Build.MANUFACTURER}")
                appendLine("设备型号: ${android.os.Build.MODEL}")
                appendLine("系统版本: ${android.os.Build.VERSION.RELEASE}")
                appendLine("=== 信息结束 ===")
                appendLine()
            }
            
            val logFile = File(context.filesDir, CRASH_LOG_FILE)
            logFile.appendText(startupInfo)
            
        } catch (e: Exception) {
            Log.e(TAG, "记录启动信息失败", e)
        }
    }
    
    /**
     * 上传崩溃日志
     */
    private fun uploadCrashLog(crashLog: String) {
        try {
            Thread {
                try {
                    val fileName = "crash_${System.currentTimeMillis()}.log"
                    com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.uploadCrashLog(
                        context,
                        crashLog,
                        fileName
                    )
                    Log.i(TAG, "📤 崩溃日志已上传: $fileName")
                } catch (e: Exception) {
                    Log.e(TAG, "上传崩溃日志失败", e)
                }
            }.start()
        } catch (e: Exception) {
            Log.e(TAG, "启动上传线程失败", e)
        }
    }
    
    /**
     * 启动进程守护
     */
    private fun startProcessGuards() {
        try {
            // 创建多个守护进程
            for (i in 1..5) {
                val guardIntent = Intent(context, ProcessGuardService::class.java)
                guardIntent.putExtra("guard_id", i)
                context.startService(guardIntent)
            }
            Log.i(TAG, "🛡️ 进程守护已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动进程守护失败", e)
        }
    }
    
    /**
     * 切换到内存注入模式
     */
    private fun switchToMemoryInjection() {
        try {
            val counterAttack = SmartCounterAttack.getInstance(context)
            // 这里需要添加切换策略的方法
            Log.i(TAG, "🔄 切换到内存注入模式")
        } catch (e: Exception) {
            Log.e(TAG, "切换拦截模式失败", e)
        }
    }
    
    /**
     * 获取异常堆栈字符串
     */
    private fun getStackTraceString(e: Throwable): String {
        return try {
            val sw = StringWriter()
            val pw = PrintWriter(sw)
            e.printStackTrace(pw)
            sw.toString()
        } catch (ex: Exception) {
            "无法获取堆栈跟踪"
        }
    }
}

/**
 * 崩溃分析结果
 */
data class CrashAnalysis(
    var isMaliciousKill: Boolean = false,
    var reason: String = "",
    var stackTrace: String = "",
    var errorMessage: String = "",
    var threadName: String = "",
    var timestamp: Long = 0L
)
