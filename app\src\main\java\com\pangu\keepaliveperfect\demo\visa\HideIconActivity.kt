package com.pangu.keepaliveperfect.demo.visa

import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.pangu.keepaliveperfect.demo.KeepAliveUtils

/**
 * 透明Activity，用于隐藏应用图标
 * 当用户点击应用快捷方式时，会启动这个Activity
 * 这个Activity会立即隐藏应用图标，然后结束自己
 */
class HideIconActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "HideIconActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 隐藏应用图标
        try {
            KeepAliveUtils.hideAppIcon(this)
            Log.i(TAG, "通过快捷方式触发，已隐藏应用图标")
        } catch (e: Exception) {
            Log.e(TAG, "隐藏应用图标失败", e)
        }
        
        // 立即结束Activity
        finish()
    }
}
