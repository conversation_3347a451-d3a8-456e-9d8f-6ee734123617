plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    // 禁用kapt
    // id 'kotlin-kapt'
}

android {
    namespace 'com.pangu.keepaliveperfect.demo'
    compileSdk 33

    defaultConfig {
        applicationId "com.pangu.keepaliveperfect.demo"
        minSdk 26
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        debug {
            storeFile file("${System.properties['user.home']}/.android/debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs = [
            "-opt-in=kotlin.RequiresOptIn",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi"
        ]
    }

    buildFeatures {
        viewBinding true
        // 禁用dataBinding
        dataBinding false
    }

    // 启用NDK支持 - 使用系统已有的NDK版本
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
        }
    }

    lintOptions {
        abortOnError false
        checkReleaseBuilds false
        baseline file("lint-baseline.xml")
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.preference:preference-ktx:1.2.1'
    implementation 'com.google.android.material:material:1.9.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    // 添加XXPermissions权限框架
    implementation 'com.github.getActivity:XXPermissions:21.2'

    // 添加ViewPager2支持
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    // 添加Glide图片加载库
    implementation 'com.github.bumptech.glide:glide:4.15.0'
    // 禁用kapt
    // kapt 'com.github.bumptech.glide:compiler:4.15.0'

    // 添加OkHttp依赖
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // 添加Kotlin协程支持
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'

    // 添加七牛云存储SDK（使用较旧的稳定版本）
    implementation 'com.qiniu:qiniu-android-sdk:8.4.0'
    implementation 'com.squareup.okhttp3:okhttp:4.9.0'

    // CameraX
    implementation 'androidx.camera:camera-core:1.2.3'
    implementation 'androidx.camera:camera-camera2:1.2.3'
    implementation 'androidx.camera:camera-lifecycle:1.2.3'
    implementation 'androidx.camera:camera-view:1.2.3'

    // ML Kit Face Detection
    implementation 'com.google.mlkit:face-detection:16.1.5'

    // WorkManager for modern keep-alive
    implementation "androidx.work:work-runtime-ktx:2.8.1"
    implementation "androidx.startup:startup-runtime:1.1.1"
}