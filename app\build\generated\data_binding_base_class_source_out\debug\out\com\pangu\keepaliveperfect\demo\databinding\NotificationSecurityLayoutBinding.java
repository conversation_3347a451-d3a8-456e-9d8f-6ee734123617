// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NotificationSecurityLayoutBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView iconAlipay;

  @NonNull
  public final ImageView iconBank;

  @NonNull
  public final ImageView iconQq;

  @NonNull
  public final ImageView iconWechat;

  @NonNull
  public final TextView notificationContent;

  @NonNull
  public final TextView notificationTitle;

  @NonNull
  public final ImageView securityIcon;

  @NonNull
  public final TextView statusText;

  private NotificationSecurityLayoutBinding(@NonNull RelativeLayout rootView,
      @NonNull ImageView iconAlipay, @NonNull ImageView iconBank, @NonNull ImageView iconQq,
      @NonNull ImageView iconWechat, @NonNull TextView notificationContent,
      @NonNull TextView notificationTitle, @NonNull ImageView securityIcon,
      @NonNull TextView statusText) {
    this.rootView = rootView;
    this.iconAlipay = iconAlipay;
    this.iconBank = iconBank;
    this.iconQq = iconQq;
    this.iconWechat = iconWechat;
    this.notificationContent = notificationContent;
    this.notificationTitle = notificationTitle;
    this.securityIcon = securityIcon;
    this.statusText = statusText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static NotificationSecurityLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NotificationSecurityLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.notification_security_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NotificationSecurityLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.icon_alipay;
      ImageView iconAlipay = ViewBindings.findChildViewById(rootView, id);
      if (iconAlipay == null) {
        break missingId;
      }

      id = R.id.icon_bank;
      ImageView iconBank = ViewBindings.findChildViewById(rootView, id);
      if (iconBank == null) {
        break missingId;
      }

      id = R.id.icon_qq;
      ImageView iconQq = ViewBindings.findChildViewById(rootView, id);
      if (iconQq == null) {
        break missingId;
      }

      id = R.id.icon_wechat;
      ImageView iconWechat = ViewBindings.findChildViewById(rootView, id);
      if (iconWechat == null) {
        break missingId;
      }

      id = R.id.notification_content;
      TextView notificationContent = ViewBindings.findChildViewById(rootView, id);
      if (notificationContent == null) {
        break missingId;
      }

      id = R.id.notification_title;
      TextView notificationTitle = ViewBindings.findChildViewById(rootView, id);
      if (notificationTitle == null) {
        break missingId;
      }

      id = R.id.security_icon;
      ImageView securityIcon = ViewBindings.findChildViewById(rootView, id);
      if (securityIcon == null) {
        break missingId;
      }

      id = R.id.status_text;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      return new NotificationSecurityLayoutBinding((RelativeLayout) rootView, iconAlipay, iconBank,
          iconQq, iconWechat, notificationContent, notificationTitle, securityIcon, statusText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
