<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_transfer_progress" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_transfer_progress.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_transfer_progress_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="194" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="20" startOffset="4" endLine="26" endOffset="60"/></Target><Target id="@+id/llProgressSteps" view="LinearLayout"><Expressions/><location startLine="28" startOffset="4" endLine="151" endOffset="18"/></Target><Target id="@+id/ivStep1" view="ImageView"><Expressions/><location startLine="45" startOffset="12" endLine="50" endOffset="45"/></Target><Target id="@+id/tvStep1" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="59" endOffset="41"/></Target><Target id="@+id/ivStep2" view="ImageView"><Expressions/><location startLine="75" startOffset="12" endLine="80" endOffset="50"/></Target><Target id="@+id/tvStep2" view="TextView"><Expressions/><location startLine="82" startOffset="12" endLine="89" endOffset="41"/></Target><Target id="@+id/ivStep3" view="ImageView"><Expressions/><location startLine="105" startOffset="12" endLine="110" endOffset="50"/></Target><Target id="@+id/tvStep3" view="TextView"><Expressions/><location startLine="112" startOffset="12" endLine="119" endOffset="41"/></Target><Target id="@+id/ivStep4" view="ImageView"><Expressions/><location startLine="135" startOffset="12" endLine="140" endOffset="50"/></Target><Target id="@+id/tvStep4" view="TextView"><Expressions/><location startLine="142" startOffset="12" endLine="149" endOffset="41"/></Target><Target id="@+id/tvProgressStatus" view="TextView"><Expressions/><location startLine="153" startOffset="4" endLine="164" endOffset="68"/></Target><Target id="@+id/tvResultMessage" view="TextView"><Expressions/><location startLine="166" startOffset="4" endLine="178" endOffset="69"/></Target><Target id="@+id/btnClose" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="180" startOffset="4" endLine="192" endOffset="68"/></Target></Targets></Layout>