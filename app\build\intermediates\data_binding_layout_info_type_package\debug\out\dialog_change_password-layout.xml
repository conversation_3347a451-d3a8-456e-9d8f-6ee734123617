<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_change_password" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_change_password.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_change_password_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="146" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="21" startOffset="4" endLine="27" endOffset="60"/></Target><Target id="@+id/tilCurrentPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="29" startOffset="4" endLine="51" endOffset="59"/></Target><Target id="@+id/etCurrentPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="45" startOffset="8" endLine="50" endOffset="34"/></Target><Target id="@+id/tilNewPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="53" startOffset="4" endLine="75" endOffset="59"/></Target><Target id="@+id/etNewPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="69" startOffset="8" endLine="74" endOffset="34"/></Target><Target id="@+id/tilConfirmPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="77" startOffset="4" endLine="99" endOffset="59"/></Target><Target id="@+id/etConfirmPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="93" startOffset="8" endLine="98" endOffset="34"/></Target><Target id="@+id/tvPasswordHint" view="TextView"><Expressions/><location startLine="101" startOffset="4" endLine="111" endOffset="71"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="113" startOffset="4" endLine="128" endOffset="44"/></Target><Target id="@+id/btnConfirm" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="130" startOffset="4" endLine="144" endOffset="67"/></Target></Targets></Layout>