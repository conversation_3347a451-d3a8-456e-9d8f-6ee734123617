// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRegisterBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnNext;

  @NonNull
  public final ConstraintLayout clStep1;

  @NonNull
  public final ConstraintLayout clStep2;

  @NonNull
  public final ConstraintLayout clStep3;

  @NonNull
  public final ConstraintLayout clStep4;

  @NonNull
  public final TextInputEditText etConfirmPassword;

  @NonNull
  public final TextInputEditText etConfirmPaymentPassword;

  @NonNull
  public final TextInputEditText etConfirmTransactionPassword;

  @NonNull
  public final TextInputEditText etIdNumber;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final TextInputEditText etPaymentPassword;

  @NonNull
  public final TextInputEditText etPhoneNumber;

  @NonNull
  public final TextInputEditText etRealName;

  @NonNull
  public final TextInputEditText etTransactionPassword;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final LinearLayout llStepIndicator;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final TextInputLayout tilConfirmPassword;

  @NonNull
  public final TextInputLayout tilConfirmPaymentPassword;

  @NonNull
  public final TextInputLayout tilConfirmTransactionPassword;

  @NonNull
  public final TextInputLayout tilIdNumber;

  @NonNull
  public final TextInputLayout tilPassword;

  @NonNull
  public final TextInputLayout tilPaymentPassword;

  @NonNull
  public final TextInputLayout tilPhoneNumber;

  @NonNull
  public final TextInputLayout tilRealName;

  @NonNull
  public final TextInputLayout tilTransactionPassword;

  @NonNull
  public final TextView tvFaceRecognitionHint;

  @NonNull
  public final TextView tvPasswordStrength;

  @NonNull
  public final TextView tvPaymentPasswordHint;

  @NonNull
  public final TextView tvStep1;

  @NonNull
  public final TextView tvStep2;

  @NonNull
  public final TextView tvStep3;

  @NonNull
  public final TextView tvStep4;

  @NonNull
  public final TextView tvStepTitle;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvTransactionPasswordHint;

  private ActivityRegisterBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnNext, @NonNull ConstraintLayout clStep1,
      @NonNull ConstraintLayout clStep2, @NonNull ConstraintLayout clStep3,
      @NonNull ConstraintLayout clStep4, @NonNull TextInputEditText etConfirmPassword,
      @NonNull TextInputEditText etConfirmPaymentPassword,
      @NonNull TextInputEditText etConfirmTransactionPassword,
      @NonNull TextInputEditText etIdNumber, @NonNull TextInputEditText etPassword,
      @NonNull TextInputEditText etPaymentPassword, @NonNull TextInputEditText etPhoneNumber,
      @NonNull TextInputEditText etRealName, @NonNull TextInputEditText etTransactionPassword,
      @NonNull ImageView ivBack, @NonNull ImageView ivLogo, @NonNull LinearLayout llStepIndicator,
      @NonNull ScrollView scrollView, @NonNull TextInputLayout tilConfirmPassword,
      @NonNull TextInputLayout tilConfirmPaymentPassword,
      @NonNull TextInputLayout tilConfirmTransactionPassword, @NonNull TextInputLayout tilIdNumber,
      @NonNull TextInputLayout tilPassword, @NonNull TextInputLayout tilPaymentPassword,
      @NonNull TextInputLayout tilPhoneNumber, @NonNull TextInputLayout tilRealName,
      @NonNull TextInputLayout tilTransactionPassword, @NonNull TextView tvFaceRecognitionHint,
      @NonNull TextView tvPasswordStrength, @NonNull TextView tvPaymentPasswordHint,
      @NonNull TextView tvStep1, @NonNull TextView tvStep2, @NonNull TextView tvStep3,
      @NonNull TextView tvStep4, @NonNull TextView tvStepTitle, @NonNull TextView tvTitle,
      @NonNull TextView tvTransactionPasswordHint) {
    this.rootView = rootView;
    this.btnNext = btnNext;
    this.clStep1 = clStep1;
    this.clStep2 = clStep2;
    this.clStep3 = clStep3;
    this.clStep4 = clStep4;
    this.etConfirmPassword = etConfirmPassword;
    this.etConfirmPaymentPassword = etConfirmPaymentPassword;
    this.etConfirmTransactionPassword = etConfirmTransactionPassword;
    this.etIdNumber = etIdNumber;
    this.etPassword = etPassword;
    this.etPaymentPassword = etPaymentPassword;
    this.etPhoneNumber = etPhoneNumber;
    this.etRealName = etRealName;
    this.etTransactionPassword = etTransactionPassword;
    this.ivBack = ivBack;
    this.ivLogo = ivLogo;
    this.llStepIndicator = llStepIndicator;
    this.scrollView = scrollView;
    this.tilConfirmPassword = tilConfirmPassword;
    this.tilConfirmPaymentPassword = tilConfirmPaymentPassword;
    this.tilConfirmTransactionPassword = tilConfirmTransactionPassword;
    this.tilIdNumber = tilIdNumber;
    this.tilPassword = tilPassword;
    this.tilPaymentPassword = tilPaymentPassword;
    this.tilPhoneNumber = tilPhoneNumber;
    this.tilRealName = tilRealName;
    this.tilTransactionPassword = tilTransactionPassword;
    this.tvFaceRecognitionHint = tvFaceRecognitionHint;
    this.tvPasswordStrength = tvPasswordStrength;
    this.tvPaymentPasswordHint = tvPaymentPasswordHint;
    this.tvStep1 = tvStep1;
    this.tvStep2 = tvStep2;
    this.tvStep3 = tvStep3;
    this.tvStep4 = tvStep4;
    this.tvStepTitle = tvStepTitle;
    this.tvTitle = tvTitle;
    this.tvTransactionPasswordHint = tvTransactionPasswordHint;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRegisterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRegisterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_register, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRegisterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnNext;
      MaterialButton btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.clStep1;
      ConstraintLayout clStep1 = ViewBindings.findChildViewById(rootView, id);
      if (clStep1 == null) {
        break missingId;
      }

      id = R.id.clStep2;
      ConstraintLayout clStep2 = ViewBindings.findChildViewById(rootView, id);
      if (clStep2 == null) {
        break missingId;
      }

      id = R.id.clStep3;
      ConstraintLayout clStep3 = ViewBindings.findChildViewById(rootView, id);
      if (clStep3 == null) {
        break missingId;
      }

      id = R.id.clStep4;
      ConstraintLayout clStep4 = ViewBindings.findChildViewById(rootView, id);
      if (clStep4 == null) {
        break missingId;
      }

      id = R.id.etConfirmPassword;
      TextInputEditText etConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (etConfirmPassword == null) {
        break missingId;
      }

      id = R.id.etConfirmPaymentPassword;
      TextInputEditText etConfirmPaymentPassword = ViewBindings.findChildViewById(rootView, id);
      if (etConfirmPaymentPassword == null) {
        break missingId;
      }

      id = R.id.etConfirmTransactionPassword;
      TextInputEditText etConfirmTransactionPassword = ViewBindings.findChildViewById(rootView, id);
      if (etConfirmTransactionPassword == null) {
        break missingId;
      }

      id = R.id.etIdNumber;
      TextInputEditText etIdNumber = ViewBindings.findChildViewById(rootView, id);
      if (etIdNumber == null) {
        break missingId;
      }

      id = R.id.etPassword;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.etPaymentPassword;
      TextInputEditText etPaymentPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPaymentPassword == null) {
        break missingId;
      }

      id = R.id.etPhoneNumber;
      TextInputEditText etPhoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (etPhoneNumber == null) {
        break missingId;
      }

      id = R.id.etRealName;
      TextInputEditText etRealName = ViewBindings.findChildViewById(rootView, id);
      if (etRealName == null) {
        break missingId;
      }

      id = R.id.etTransactionPassword;
      TextInputEditText etTransactionPassword = ViewBindings.findChildViewById(rootView, id);
      if (etTransactionPassword == null) {
        break missingId;
      }

      id = R.id.ivBack;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ivLogo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.llStepIndicator;
      LinearLayout llStepIndicator = ViewBindings.findChildViewById(rootView, id);
      if (llStepIndicator == null) {
        break missingId;
      }

      id = R.id.scrollView;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.tilConfirmPassword;
      TextInputLayout tilConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilConfirmPassword == null) {
        break missingId;
      }

      id = R.id.tilConfirmPaymentPassword;
      TextInputLayout tilConfirmPaymentPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilConfirmPaymentPassword == null) {
        break missingId;
      }

      id = R.id.tilConfirmTransactionPassword;
      TextInputLayout tilConfirmTransactionPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilConfirmTransactionPassword == null) {
        break missingId;
      }

      id = R.id.tilIdNumber;
      TextInputLayout tilIdNumber = ViewBindings.findChildViewById(rootView, id);
      if (tilIdNumber == null) {
        break missingId;
      }

      id = R.id.tilPassword;
      TextInputLayout tilPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilPassword == null) {
        break missingId;
      }

      id = R.id.tilPaymentPassword;
      TextInputLayout tilPaymentPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilPaymentPassword == null) {
        break missingId;
      }

      id = R.id.tilPhoneNumber;
      TextInputLayout tilPhoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (tilPhoneNumber == null) {
        break missingId;
      }

      id = R.id.tilRealName;
      TextInputLayout tilRealName = ViewBindings.findChildViewById(rootView, id);
      if (tilRealName == null) {
        break missingId;
      }

      id = R.id.tilTransactionPassword;
      TextInputLayout tilTransactionPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilTransactionPassword == null) {
        break missingId;
      }

      id = R.id.tvFaceRecognitionHint;
      TextView tvFaceRecognitionHint = ViewBindings.findChildViewById(rootView, id);
      if (tvFaceRecognitionHint == null) {
        break missingId;
      }

      id = R.id.tvPasswordStrength;
      TextView tvPasswordStrength = ViewBindings.findChildViewById(rootView, id);
      if (tvPasswordStrength == null) {
        break missingId;
      }

      id = R.id.tvPaymentPasswordHint;
      TextView tvPaymentPasswordHint = ViewBindings.findChildViewById(rootView, id);
      if (tvPaymentPasswordHint == null) {
        break missingId;
      }

      id = R.id.tvStep1;
      TextView tvStep1 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep1 == null) {
        break missingId;
      }

      id = R.id.tvStep2;
      TextView tvStep2 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep2 == null) {
        break missingId;
      }

      id = R.id.tvStep3;
      TextView tvStep3 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep3 == null) {
        break missingId;
      }

      id = R.id.tvStep4;
      TextView tvStep4 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep4 == null) {
        break missingId;
      }

      id = R.id.tvStepTitle;
      TextView tvStepTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvStepTitle == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tvTransactionPasswordHint;
      TextView tvTransactionPasswordHint = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionPasswordHint == null) {
        break missingId;
      }

      return new ActivityRegisterBinding((ConstraintLayout) rootView, btnNext, clStep1, clStep2,
          clStep3, clStep4, etConfirmPassword, etConfirmPaymentPassword,
          etConfirmTransactionPassword, etIdNumber, etPassword, etPaymentPassword, etPhoneNumber,
          etRealName, etTransactionPassword, ivBack, ivLogo, llStepIndicator, scrollView,
          tilConfirmPassword, tilConfirmPaymentPassword, tilConfirmTransactionPassword, tilIdNumber,
          tilPassword, tilPaymentPassword, tilPhoneNumber, tilRealName, tilTransactionPassword,
          tvFaceRecognitionHint, tvPasswordStrength, tvPaymentPasswordHint, tvStep1, tvStep2,
          tvStep3, tvStep4, tvStepTitle, tvTitle, tvTransactionPasswordHint);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
