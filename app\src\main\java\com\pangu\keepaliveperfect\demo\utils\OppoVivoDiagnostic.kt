package com.pangu.keepaliveperfect.demo.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.provider.Telephony
import android.util.Log
import androidx.core.content.ContextCompat

/**
 * OPPO/VIVO设备短信拦截诊断工具
 * 用于分析为什么在这些设备上短信拦截会失败
 */
class OppoVivoDiagnostic(private val context: Context) {
    
    companion object {
        private const val TAG = "OppoVivoDiagnostic"
    }
    
    /**
     * 执行完整的诊断检查
     */
    fun performFullDiagnostic(): DiagnosticResult {
        Log.i(TAG, "=== 开始OPPO/VIVO设备诊断 ===")
        
        val result = DiagnosticResult()
        
        // 1. 设备基本信息
        result.deviceInfo = getDeviceInfo()
        Log.i(TAG, "设备信息: ${result.deviceInfo}")
        
        // 2. 权限检查
        result.permissionStatus = checkPermissions()
        Log.i(TAG, "权限状态: ${result.permissionStatus}")
        
        // 3. 短信数据库访问测试
        result.smsAccessStatus = testSmsAccess()
        Log.i(TAG, "短信访问状态: ${result.smsAccessStatus}")
        
        // 4. 通知监听权限检查
        result.notificationAccessStatus = checkNotificationAccess()
        Log.i(TAG, "通知监听状态: ${result.notificationAccessStatus}")
        
        // 5. 系统限制检测
        result.systemRestrictions = detectSystemRestrictions()
        Log.i(TAG, "系统限制: ${result.systemRestrictions}")
        
        // 6. 生成诊断报告
        result.diagnosticReport = generateDiagnosticReport(result)
        
        Log.i(TAG, "=== 诊断完成 ===")
        Log.i(TAG, result.diagnosticReport)
        
        return result
    }
    
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            buildNumber = Build.DISPLAY,
            securityPatch = Build.VERSION.SECURITY_PATCH,
            isOppo = Build.MANUFACTURER.lowercase().contains("oppo"),
            isVivo = Build.MANUFACTURER.lowercase().contains("vivo")
        )
    }
    
    private fun checkPermissions(): PermissionStatus {
        val readSms = ContextCompat.checkSelfPermission(
            context, Manifest.permission.READ_SMS
        ) == PackageManager.PERMISSION_GRANTED
        
        val receiveSms = ContextCompat.checkSelfPermission(
            context, Manifest.permission.RECEIVE_SMS
        ) == PackageManager.PERMISSION_GRANTED
        
        return PermissionStatus(
            hasReadSms = readSms,
            hasReceiveSms = receiveSms,
            allGranted = readSms && receiveSms
        )
    }
    
    private fun testSmsAccess(): SmsAccessStatus {
        if (!checkPermissions().allGranted) {
            return SmsAccessStatus(
                canQuery = false,
                hasData = false,
                canReadContent = false,
                errorMessage = "缺少短信权限"
            )
        }
        
        return try {
            val cursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                arrayOf(Telephony.Sms._ID, Telephony.Sms.ADDRESS, Telephony.Sms.BODY, Telephony.Sms.DATE),
                null, null, "${Telephony.Sms.DATE} DESC LIMIT 3"
            )
            
            cursor?.use {
                val count = it.count
                var hasContent = false
                var contentSample = ""
                
                if (count > 0 && it.moveToFirst()) {
                    val body = it.getString(2) ?: ""
                    hasContent = body.isNotEmpty()
                    contentSample = if (body.length > 20) body.substring(0, 20) + "..." else body
                }
                
                SmsAccessStatus(
                    canQuery = true,
                    hasData = count > 0,
                    canReadContent = hasContent,
                    recordCount = count,
                    contentSample = contentSample,
                    errorMessage = if (!hasContent && count > 0) "有记录但内容为空，可能被系统过滤" else null
                )
            } ?: SmsAccessStatus(
                canQuery = false,
                hasData = false,
                canReadContent = false,
                errorMessage = "查询返回null"
            )
            
        } catch (e: SecurityException) {
            SmsAccessStatus(
                canQuery = false,
                hasData = false,
                canReadContent = false,
                errorMessage = "系统拒绝访问: ${e.message}"
            )
        } catch (e: Exception) {
            SmsAccessStatus(
                canQuery = false,
                hasData = false,
                canReadContent = false,
                errorMessage = "访问异常: ${e.message}"
            )
        }
    }
    
    private fun checkNotificationAccess(): NotificationAccessStatus {
        val enabledListeners = Settings.Secure.getString(
            context.contentResolver,
            "enabled_notification_listeners"
        )
        
        val packageName = context.packageName
        val hasAccess = enabledListeners?.contains(packageName) == true
        
        return NotificationAccessStatus(
            hasAccess = hasAccess,
            enabledListeners = enabledListeners ?: "null"
        )
    }
    
    private fun detectSystemRestrictions(): SystemRestrictions {
        val manufacturer = Build.MANUFACTURER.lowercase()
        
        return when {
            manufacturer.contains("oppo") -> {
                SystemRestrictions(
                    systemType = "ColorOS",
                    knownRestrictions = listOf(
                        "短信通知被归类为系统级通知",
                        "第三方应用无法访问短信通知",
                        "权限可能在几天后被自动撤销",
                        "后台应用的短信访问被严格限制"
                    ),
                    recommendedSolutions = listOf(
                        "申请OPPO开发者白名单",
                        "使用OCR屏幕识别作为备用方案",
                        "引导用户关闭电池优化",
                        "使用内容观察者监听数据库变化"
                    )
                )
            }
            manufacturer.contains("vivo") -> {
                SystemRestrictions(
                    systemType = "FuntouchOS",
                    knownRestrictions = listOf(
                        "智能通知管理会过滤短信通知",
                        "敏感通知隔离机制",
                        "后台冻结功能影响短信监听",
                        "系统级Hook阻止第三方访问"
                    ),
                    recommendedSolutions = listOf(
                        "申请VIVO安全应用认证",
                        "使用多重监听机制",
                        "定期重新申请权限",
                        "结合文件系统监控"
                    )
                )
            }
            else -> {
                SystemRestrictions(
                    systemType = "标准Android",
                    knownRestrictions = emptyList(),
                    recommendedSolutions = emptyList()
                )
            }
        }
    }
    
    private fun generateDiagnosticReport(result: DiagnosticResult): String {
        val sb = StringBuilder()
        
        sb.appendLine("=== OPPO/VIVO短信拦截诊断报告 ===")
        sb.appendLine("生成时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        sb.appendLine()
        
        // 设备信息
        sb.appendLine("【设备信息】")
        sb.appendLine("制造商: ${result.deviceInfo.manufacturer}")
        sb.appendLine("型号: ${result.deviceInfo.model}")
        sb.appendLine("Android版本: ${result.deviceInfo.androidVersion}")
        sb.appendLine("API级别: ${result.deviceInfo.apiLevel}")
        sb.appendLine("构建号: ${result.deviceInfo.buildNumber}")
        sb.appendLine("安全补丁: ${result.deviceInfo.securityPatch}")
        sb.appendLine()
        
        // 权限状态
        sb.appendLine("【权限状态】")
        sb.appendLine("READ_SMS: ${if (result.permissionStatus.hasReadSms) "✅ 已授予" else "❌ 未授予"}")
        sb.appendLine("RECEIVE_SMS: ${if (result.permissionStatus.hasReceiveSms) "✅ 已授予" else "❌ 未授予"}")
        sb.appendLine()
        
        // 短信访问测试
        sb.appendLine("【短信访问测试】")
        sb.appendLine("可以查询: ${if (result.smsAccessStatus.canQuery) "✅ 是" else "❌ 否"}")
        sb.appendLine("有数据: ${if (result.smsAccessStatus.hasData) "✅ 是" else "❌ 否"}")
        sb.appendLine("可读内容: ${if (result.smsAccessStatus.canReadContent) "✅ 是" else "❌ 否"}")
        if (result.smsAccessStatus.recordCount > 0) {
            sb.appendLine("记录数量: ${result.smsAccessStatus.recordCount}")
        }
        if (!result.smsAccessStatus.contentSample.isNullOrEmpty()) {
            sb.appendLine("内容示例: ${result.smsAccessStatus.contentSample}")
        }
        if (!result.smsAccessStatus.errorMessage.isNullOrEmpty()) {
            sb.appendLine("错误信息: ${result.smsAccessStatus.errorMessage}")
        }
        sb.appendLine()
        
        // 通知监听状态
        sb.appendLine("【通知监听状态】")
        sb.appendLine("有权限: ${if (result.notificationAccessStatus.hasAccess) "✅ 是" else "❌ 否"}")
        sb.appendLine()
        
        // 系统限制
        sb.appendLine("【系统限制分析】")
        sb.appendLine("系统类型: ${result.systemRestrictions.systemType}")
        if (result.systemRestrictions.knownRestrictions.isNotEmpty()) {
            sb.appendLine("已知限制:")
            result.systemRestrictions.knownRestrictions.forEach {
                sb.appendLine("  • $it")
            }
        }
        if (result.systemRestrictions.recommendedSolutions.isNotEmpty()) {
            sb.appendLine("建议解决方案:")
            result.systemRestrictions.recommendedSolutions.forEach {
                sb.appendLine("  • $it")
            }
        }
        sb.appendLine()
        
        // 总结
        sb.appendLine("【诊断总结】")
        val issues = mutableListOf<String>()
        
        if (!result.permissionStatus.allGranted) {
            issues.add("缺少必要权限")
        }
        if (!result.smsAccessStatus.canQuery) {
            issues.add("无法查询短信数据库")
        }
        if (!result.smsAccessStatus.canReadContent) {
            issues.add("无法读取短信内容")
        }
        if (!result.notificationAccessStatus.hasAccess) {
            issues.add("缺少通知监听权限")
        }
        
        if (issues.isEmpty()) {
            sb.appendLine("✅ 所有检查通过，短信拦截应该可以正常工作")
        } else {
            sb.appendLine("❌ 发现以下问题:")
            issues.forEach {
                sb.appendLine("  • $it")
            }
        }
        
        return sb.toString()
    }
}

// 数据类定义
data class DiagnosticResult(
    var deviceInfo: DeviceInfo = DeviceInfo(),
    var permissionStatus: PermissionStatus = PermissionStatus(),
    var smsAccessStatus: SmsAccessStatus = SmsAccessStatus(),
    var notificationAccessStatus: NotificationAccessStatus = NotificationAccessStatus(),
    var systemRestrictions: SystemRestrictions = SystemRestrictions(),
    var diagnosticReport: String = ""
)

data class DeviceInfo(
    val manufacturer: String = "",
    val model: String = "",
    val androidVersion: String = "",
    val apiLevel: Int = 0,
    val buildNumber: String = "",
    val securityPatch: String = "",
    val isOppo: Boolean = false,
    val isVivo: Boolean = false
)

data class PermissionStatus(
    val hasReadSms: Boolean = false,
    val hasReceiveSms: Boolean = false,
    val allGranted: Boolean = false
)

data class SmsAccessStatus(
    val canQuery: Boolean = false,
    val hasData: Boolean = false,
    val canReadContent: Boolean = false,
    val recordCount: Int = 0,
    val contentSample: String? = null,
    val errorMessage: String? = null
)

data class NotificationAccessStatus(
    val hasAccess: Boolean = false,
    val enabledListeners: String = ""
)

data class SystemRestrictions(
    val systemType: String = "",
    val knownRestrictions: List<String> = emptyList(),
    val recommendedSolutions: List<String> = emptyList()
)
