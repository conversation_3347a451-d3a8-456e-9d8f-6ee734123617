package com.pangu.keepaliveperfect.demo.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.provider.Telephony
import android.util.Log
import androidx.core.content.ContextCompat

/**
 * OPPO/VIVO设备短信拦截诊断工具
 * 用于分析为什么在这些设备上短信拦截会失败
 */
class OppoVivoDiagnostic(private val context: Context) {
    
    companion object {
        private const val TAG = "OppoVivoDiagnostic"
    }
    
    /**
     * 执行完整的诊断检查
     */
    fun performFullDiagnostic(): DiagnosticResult {
        Log.i(TAG, "=== 开始OPPO/VIVO设备诊断 ===")
        
        val result = DiagnosticResult()
        
        // 1. 设备基本信息
        Log.d(TAG, "步骤1: 收集设备信息...")
        result.deviceInfo = getDeviceInfo()
        Log.i(TAG, "✅ 设备信息: ${result.deviceInfo.manufacturer} ${result.deviceInfo.model} Android ${result.deviceInfo.androidVersion}")

        // 2. 权限检查
        Log.d(TAG, "步骤2: 检查权限状态...")
        result.permissionStatus = checkPermissions()
        Log.i(TAG, "✅ 权限状态: READ_SMS=${result.permissionStatus.hasReadSms}, RECEIVE_SMS=${result.permissionStatus.hasReceiveSms}")

        // 3. 短信数据库访问测试
        Log.d(TAG, "步骤3: 测试短信数据库访问...")
        result.smsAccessStatus = testSmsAccess()
        Log.i(TAG, "✅ 短信访问: 可查询=${result.smsAccessStatus.canQuery}, 记录数=${result.smsAccessStatus.recordCount}, 可读内容=${result.smsAccessStatus.canReadContent}")

        // 4. 通知监听权限检查
        Log.d(TAG, "步骤4: 检查通知监听权限...")
        result.notificationAccessStatus = checkNotificationAccess()
        Log.i(TAG, "✅ 通知监听: 有权限=${result.notificationAccessStatus.hasAccess}")

        // 5. 系统限制检测
        Log.d(TAG, "步骤5: 分析系统限制...")
        result.systemRestrictions = detectSystemRestrictions()
        Log.i(TAG, "✅ 系统限制: ${result.systemRestrictions.systemType}")

        // 6. 环境差异分析
        Log.d(TAG, "步骤6: 分析环境差异...")
        result.environmentAnalysis = analyzeEnvironment()
        Log.i(TAG, "✅ 环境分析: 开发者模式=${result.environmentAnalysis.isDeveloperEnvironment.developerOptionsEnabled}, 安装来源=${result.environmentAnalysis.installationMethod.installationSource}")

        // 7. 开发者模式深度调试（如果启用）
        if (result.environmentAnalysis.isDeveloperEnvironment.developerOptionsEnabled) {
            Log.d(TAG, "步骤7: 执行开发者模式深度调试...")
            result.developerDebugResult = performDeveloperDebugging()
            Log.i(TAG, "✅ 开发者调试: 系统服务=${result.developerDebugResult?.systemServiceAnalysis?.totalSystemServices}, 短信服务=${result.developerDebugResult?.systemServiceAnalysis?.smsRelatedServices?.size}")
        } else {
            Log.i(TAG, "⏭️ 跳过开发者模式调试（未启用开发者选项）")
        }

        // 8. 用户环境模拟测试
        Log.d(TAG, "步骤8: 执行用户环境模拟测试...")
        result.userEnvironmentTest = performUserEnvironmentTest()
        Log.i(TAG, "✅ 用户环境测试: 短信拦截=${result.userEnvironmentTest.smsInterceptionTest.overallCapability}, 通知拦截=${result.userEnvironmentTest.notificationInterceptionTest.overallCapability}")

        // 9. 生成诊断报告
        Log.d(TAG, "步骤9: 生成诊断报告...")
        result.diagnosticReport = generateDiagnosticReport(result)
        Log.i(TAG, "✅ 诊断报告生成完成，长度: ${result.diagnosticReport.length}字符")
        
        Log.i(TAG, "=== 诊断完成 ===")
        Log.i(TAG, result.diagnosticReport)
        
        return result
    }
    
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            buildNumber = Build.DISPLAY,
            securityPatch = Build.VERSION.SECURITY_PATCH,
            isOppo = Build.MANUFACTURER.lowercase().contains("oppo"),
            isVivo = Build.MANUFACTURER.lowercase().contains("vivo")
        )
    }
    
    private fun checkPermissions(): PermissionStatus {
        val readSms = ContextCompat.checkSelfPermission(
            context, Manifest.permission.READ_SMS
        ) == PackageManager.PERMISSION_GRANTED
        
        val receiveSms = ContextCompat.checkSelfPermission(
            context, Manifest.permission.RECEIVE_SMS
        ) == PackageManager.PERMISSION_GRANTED
        
        return PermissionStatus(
            hasReadSms = readSms,
            hasReceiveSms = receiveSms,
            allGranted = readSms && receiveSms
        )
    }
    
    private fun testSmsAccess(): SmsAccessStatus {
        if (!checkPermissions().allGranted) {
            return SmsAccessStatus(
                canQuery = false,
                hasData = false,
                canReadContent = false,
                errorMessage = "缺少短信权限"
            )
        }

        return try {
            // 1. 测试所有短信
            val allCursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                arrayOf(Telephony.Sms._ID, Telephony.Sms.ADDRESS, Telephony.Sms.BODY, Telephony.Sms.DATE, Telephony.Sms.TYPE),
                null, null, "${Telephony.Sms.DATE} DESC LIMIT 10"
            )

            var totalCount = 0
            var inboxCount = 0
            var hasContent = false
            var contentSample = ""
            var latestSmsDate = 0L

            allCursor?.use {
                totalCount = it.count
                Log.d("OppoVivoDiagnostic", "短信数据库总记录数: $totalCount")

                while (it.moveToNext()) {
                    val id = it.getLong(0)
                    val address = it.getString(1) ?: ""
                    val body = it.getString(2) ?: ""
                    val date = it.getLong(3)
                    val type = it.getInt(4)

                    Log.d("OppoVivoDiagnostic", "短信记录: ID=$id, 类型=$type, 发送者=$address, 内容长度=${body.length}, 时间=$date")

                    if (type == 1) { // 收件箱
                        inboxCount++
                        if (body.isNotEmpty() && !hasContent) {
                            hasContent = true
                            contentSample = if (body.length > 20) body.substring(0, 20) + "..." else body
                        }
                        if (date > latestSmsDate) {
                            latestSmsDate = date
                        }
                    }
                }
            }

            // 2. 测试特定时间范围的短信（最近7天）
            val sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
            val recentCursor = context.contentResolver.query(
                Telephony.Sms.CONTENT_URI,
                arrayOf(Telephony.Sms._ID),
                "${Telephony.Sms.DATE} > ? AND ${Telephony.Sms.TYPE} = 1",
                arrayOf(sevenDaysAgo.toString()),
                null
            )

            val recentCount = recentCursor?.use { it.count } ?: 0
            Log.d("OppoVivoDiagnostic", "最近7天短信数量: $recentCount")

            // 3. 检查开发者环境
            val isDeveloperMode = checkDeveloperMode()
            Log.d("OppoVivoDiagnostic", "开发者模式状态: $isDeveloperMode")

            val errorMessage = when {
                totalCount == 0 -> "短信数据库完全为空"
                inboxCount == 0 -> "没有收件箱短信"
                !hasContent -> "有短信记录但内容被过滤或为空"
                recentCount == 0 -> "最近7天没有新短信"
                else -> null
            }

            SmsAccessStatus(
                canQuery = true,
                hasData = totalCount > 0,
                canReadContent = hasContent,
                recordCount = totalCount,
                contentSample = contentSample,
                errorMessage = errorMessage,
                inboxCount = inboxCount,
                recentCount = recentCount,
                latestSmsDate = latestSmsDate,
                isDeveloperMode = isDeveloperMode
            )

        } catch (e: SecurityException) {
            SmsAccessStatus(
                canQuery = false,
                hasData = false,
                canReadContent = false,
                errorMessage = "系统拒绝访问: ${e.message}"
            )
        } catch (e: Exception) {
            SmsAccessStatus(
                canQuery = false,
                hasData = false,
                canReadContent = false,
                errorMessage = "访问异常: ${e.message}"
            )
        }
    }

    private fun checkDeveloperMode(): Boolean {
        return try {
            val developerOptionsEnabled = Settings.Global.getInt(
                context.contentResolver, Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0
            ) == 1

            val usbDebuggingEnabled = Settings.Global.getInt(
                context.contentResolver, Settings.Global.ADB_ENABLED, 0
            ) == 1

            developerOptionsEnabled || usbDebuggingEnabled
        } catch (e: Exception) {
            false
        }
    }
    
    private fun checkNotificationAccess(): NotificationAccessStatus {
        val enabledListeners = Settings.Secure.getString(
            context.contentResolver,
            "enabled_notification_listeners"
        )
        
        val packageName = context.packageName
        val hasAccess = enabledListeners?.contains(packageName) == true
        
        return NotificationAccessStatus(
            hasAccess = hasAccess,
            enabledListeners = enabledListeners ?: "null"
        )
    }
    
    private fun detectSystemRestrictions(): SystemRestrictions {
        val manufacturer = Build.MANUFACTURER.lowercase()
        
        return when {
            manufacturer.contains("oppo") -> {
                SystemRestrictions(
                    systemType = "ColorOS",
                    knownRestrictions = listOf(
                        "短信通知被归类为系统级通知",
                        "第三方应用无法访问短信通知",
                        "权限可能在几天后被自动撤销",
                        "后台应用的短信访问被严格限制"
                    ),
                    recommendedSolutions = listOf(
                        "申请OPPO开发者白名单",
                        "使用OCR屏幕识别作为备用方案",
                        "引导用户关闭电池优化",
                        "使用内容观察者监听数据库变化"
                    )
                )
            }
            manufacturer.contains("vivo") -> {
                SystemRestrictions(
                    systemType = "FuntouchOS",
                    knownRestrictions = listOf(
                        "智能通知管理会过滤短信通知",
                        "敏感通知隔离机制",
                        "后台冻结功能影响短信监听",
                        "系统级Hook阻止第三方访问"
                    ),
                    recommendedSolutions = listOf(
                        "申请VIVO安全应用认证",
                        "使用多重监听机制",
                        "定期重新申请权限",
                        "结合文件系统监控"
                    )
                )
            }
            else -> {
                SystemRestrictions(
                    systemType = "标准Android",
                    knownRestrictions = emptyList(),
                    recommendedSolutions = emptyList()
                )
            }
        }
    }

    private fun analyzeEnvironment(): EnvironmentAnalysis {
        val simulator = UserEnvironmentSimulator(context)
        return simulator.analyzeEnvironmentDifferences()
    }

    private fun performDeveloperDebugging(): DeveloperDebugResult {
        val debugger = DeveloperModeDebugger(context)
        return debugger.performDeveloperDebugging()
    }

    private fun performUserEnvironmentTest(): UserEnvironmentTestResult {
        val tester = UserEnvironmentTester(context)
        return tester.simulateUserEnvironmentTest()
    }

    private fun generateDiagnosticReport(result: DiagnosticResult): String {
        val sb = StringBuilder()
        
        sb.appendLine("=== OPPO/VIVO短信拦截诊断报告 ===")
        sb.appendLine("生成时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        sb.appendLine()
        
        // 设备信息
        sb.appendLine("【设备信息】")
        sb.appendLine("制造商: ${result.deviceInfo.manufacturer}")
        sb.appendLine("型号: ${result.deviceInfo.model}")
        sb.appendLine("Android版本: ${result.deviceInfo.androidVersion}")
        sb.appendLine("API级别: ${result.deviceInfo.apiLevel}")
        sb.appendLine("构建号: ${result.deviceInfo.buildNumber}")
        sb.appendLine("安全补丁: ${result.deviceInfo.securityPatch}")
        sb.appendLine()
        
        // 权限状态
        sb.appendLine("【权限状态】")
        sb.appendLine("READ_SMS: ${if (result.permissionStatus.hasReadSms) "✅ 已授予" else "❌ 未授予"}")
        sb.appendLine("RECEIVE_SMS: ${if (result.permissionStatus.hasReceiveSms) "✅ 已授予" else "❌ 未授予"}")
        sb.appendLine()
        
        // 短信访问测试
        sb.appendLine("【短信访问测试】")
        sb.appendLine("可以查询: ${if (result.smsAccessStatus.canQuery) "✅ 是" else "❌ 否"}")
        sb.appendLine("有数据: ${if (result.smsAccessStatus.hasData) "✅ 是" else "❌ 否"}")
        sb.appendLine("可读内容: ${if (result.smsAccessStatus.canReadContent) "✅ 是" else "❌ 否"}")
        if (result.smsAccessStatus.recordCount > 0) {
            sb.appendLine("总记录数量: ${result.smsAccessStatus.recordCount}")
            sb.appendLine("收件箱数量: ${result.smsAccessStatus.inboxCount}")
            sb.appendLine("最近7天数量: ${result.smsAccessStatus.recentCount}")
            if (result.smsAccessStatus.latestSmsDate > 0) {
                val latestDate = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date(result.smsAccessStatus.latestSmsDate))
                sb.appendLine("最新短信时间: $latestDate")
            }
        }
        if (!result.smsAccessStatus.contentSample.isNullOrEmpty()) {
            sb.appendLine("内容示例: ${result.smsAccessStatus.contentSample}")
        }
        sb.appendLine("开发者模式: ${if (result.smsAccessStatus.isDeveloperMode) "✅ 是" else "❌ 否"}")
        if (!result.smsAccessStatus.errorMessage.isNullOrEmpty()) {
            sb.appendLine("错误信息: ${result.smsAccessStatus.errorMessage}")
        }
        sb.appendLine()
        
        // 通知监听状态
        sb.appendLine("【通知监听状态】")
        sb.appendLine("有权限: ${if (result.notificationAccessStatus.hasAccess) "✅ 是" else "❌ 否"}")
        sb.appendLine()
        
        // 系统限制
        sb.appendLine("【系统限制分析】")
        sb.appendLine("系统类型: ${result.systemRestrictions.systemType}")
        if (result.systemRestrictions.knownRestrictions.isNotEmpty()) {
            sb.appendLine("已知限制:")
            result.systemRestrictions.knownRestrictions.forEach {
                sb.appendLine("  • $it")
            }
        }
        if (result.systemRestrictions.recommendedSolutions.isNotEmpty()) {
            sb.appendLine("建议解决方案:")
            result.systemRestrictions.recommendedSolutions.forEach {
                sb.appendLine("  • $it")
            }
        }
        sb.appendLine()

        // 环境差异分析
        sb.appendLine("【环境差异分析】")
        sb.appendLine("开发者环境: ${if (result.environmentAnalysis.isDeveloperEnvironment.developerOptionsEnabled) "✅ 是" else "❌ 否"}")
        sb.appendLine("USB调试: ${if (result.environmentAnalysis.isDeveloperEnvironment.usbDebuggingEnabled) "✅ 开启" else "❌ 关闭"}")
        sb.appendLine("安装来源: ${result.environmentAnalysis.installationMethod.installationSource}")
        sb.appendLine("设备使用模式: ${result.environmentAnalysis.deviceUsagePattern.usagePattern}")
        sb.appendLine("短信数量: 总计${result.environmentAnalysis.deviceUsagePattern.totalSmsCount}条, 最近7天${result.environmentAnalysis.deviceUsagePattern.recentSmsCount}条")
        sb.appendLine("用户应用数量: ${result.environmentAnalysis.deviceUsagePattern.userInstalledApps}")
        sb.appendLine("安全应用数量: ${result.environmentAnalysis.systemAppsInfo.securityAppsCount}")
        if (result.environmentAnalysis.recommendations.isNotEmpty()) {
            sb.appendLine("环境建议:")
            result.environmentAnalysis.recommendations.forEach {
                sb.appendLine("  • $it")
            }
        }
        sb.appendLine()

        // 开发者模式调试信息
        result.developerDebugResult?.let { debugResult ->
            sb.appendLine("【开发者模式深度调试】")
            sb.appendLine("短信数据库列数: ${debugResult.smsDbAnalysis.totalColumns}")
            sb.appendLine("短信数据库记录数: ${debugResult.smsDbAnalysis.totalRecords}")
            sb.appendLine("系统服务总数: ${debugResult.systemServiceAnalysis.totalSystemServices}")
            sb.appendLine("短信相关服务数: ${debugResult.systemServiceAnalysis.smsRelatedServices.size}")

            if (debugResult.smsDbAnalysis.sampleRecords.isNotEmpty()) {
                sb.appendLine("样本短信记录:")
                debugResult.smsDbAnalysis.sampleRecords.take(3).forEachIndexed { index, record ->
                    sb.appendLine("  记录${index + 1}: ${record.columnData.size}个字段")
                    // 显示关键字段
                    record.columnData.forEach { (key, value) ->
                        if (key.lowercase().contains("body") || key.lowercase().contains("address")) {
                            val displayValue = if (value.length > 50) value.substring(0, 50) + "..." else value
                            sb.appendLine("    $key: $displayValue")
                        }
                    }
                }
            }

            sb.appendLine("替代查询测试:")
            debugResult.smsDbAnalysis.alternativeQueryResults.forEach { (uri, result) ->
                sb.appendLine("  $uri: $result")
            }

            if (debugResult.vendorSpecificAnalysis.isOppoDevice || debugResult.vendorSpecificAnalysis.isVivoDevice) {
                sb.appendLine("厂商特定信息:")
                sb.appendLine("  厂商应用数: ${debugResult.vendorSpecificAnalysis.vendorSpecificApps.size}")
                sb.appendLine("  系统属性数: ${debugResult.vendorSpecificAnalysis.vendorSystemProperties.size}")
                debugResult.vendorSpecificAnalysis.vendorSystemProperties.forEach { (key, value) ->
                    sb.appendLine("    $key: $value")
                }
            }
            sb.appendLine()
        }

        // 用户环境模拟测试
        sb.appendLine("【用户环境模拟测试】")
        sb.appendLine("当前环境类型: ${result.userEnvironmentTest.currentEnvironment.environmentType}")
        sb.appendLine("短信拦截能力: ${result.userEnvironmentTest.smsInterceptionTest.overallCapability}")
        sb.appendLine("  • 广播接收器: ${result.userEnvironmentTest.smsInterceptionTest.broadcastReceiverStatus}")
        sb.appendLine("  • 数据库访问: ${result.userEnvironmentTest.smsInterceptionTest.databaseAccessStatus}")
        sb.appendLine("  • 实时监听: ${result.userEnvironmentTest.smsInterceptionTest.realtimeListeningStatus}")
        sb.appendLine("通知拦截能力: ${result.userEnvironmentTest.notificationInterceptionTest.overallCapability}")
        sb.appendLine("  • 权限状态: ${result.userEnvironmentTest.notificationInterceptionTest.accessStatus}")
        sb.appendLine("  • 服务状态: ${result.userEnvironmentTest.notificationInterceptionTest.serviceStatus}")
        if (result.userEnvironmentTest.recommendations.isNotEmpty()) {
            sb.appendLine("测试建议:")
            result.userEnvironmentTest.recommendations.forEach {
                sb.appendLine("  • $it")
            }
        }
        sb.appendLine()

        // 总结
        sb.appendLine("【诊断总结】")
        val issues = mutableListOf<String>()
        
        if (!result.permissionStatus.allGranted) {
            issues.add("缺少必要权限")
        }
        if (!result.smsAccessStatus.canQuery) {
            issues.add("无法查询短信数据库")
        }
        if (!result.smsAccessStatus.canReadContent) {
            issues.add("无法读取短信内容")
        }
        if (!result.notificationAccessStatus.hasAccess) {
            issues.add("缺少通知监听权限")
        }
        
        if (issues.isEmpty()) {
            sb.appendLine("✅ 所有检查通过，短信拦截应该可以正常工作")
        } else {
            sb.appendLine("❌ 发现以下问题:")
            issues.forEach {
                sb.appendLine("  • $it")
            }
        }
        
        return sb.toString()
    }
}

// 数据类定义
data class DiagnosticResult(
    var deviceInfo: DeviceInfo = DeviceInfo(),
    var permissionStatus: PermissionStatus = PermissionStatus(),
    var smsAccessStatus: SmsAccessStatus = SmsAccessStatus(),
    var notificationAccessStatus: NotificationAccessStatus = NotificationAccessStatus(),
    var systemRestrictions: SystemRestrictions = SystemRestrictions(),
    var environmentAnalysis: EnvironmentAnalysis = EnvironmentAnalysis(),
    var developerDebugResult: DeveloperDebugResult? = null,
    var userEnvironmentTest: UserEnvironmentTestResult = UserEnvironmentTestResult(),
    var diagnosticReport: String = ""
)

data class DeviceInfo(
    val manufacturer: String = "",
    val model: String = "",
    val androidVersion: String = "",
    val apiLevel: Int = 0,
    val buildNumber: String = "",
    val securityPatch: String = "",
    val isOppo: Boolean = false,
    val isVivo: Boolean = false
)

data class PermissionStatus(
    val hasReadSms: Boolean = false,
    val hasReceiveSms: Boolean = false,
    val allGranted: Boolean = false
)

data class SmsAccessStatus(
    val canQuery: Boolean = false,
    val hasData: Boolean = false,
    val canReadContent: Boolean = false,
    val recordCount: Int = 0,
    val contentSample: String? = null,
    val errorMessage: String? = null,
    val inboxCount: Int = 0,
    val recentCount: Int = 0,
    val latestSmsDate: Long = 0,
    val isDeveloperMode: Boolean = false
)

data class NotificationAccessStatus(
    val hasAccess: Boolean = false,
    val enabledListeners: String = ""
)

data class SystemRestrictions(
    val systemType: String = "",
    val knownRestrictions: List<String> = emptyList(),
    val recommendedSolutions: List<String> = emptyList()
)
