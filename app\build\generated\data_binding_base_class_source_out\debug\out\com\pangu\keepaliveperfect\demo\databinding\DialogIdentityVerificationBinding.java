// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogIdentityVerificationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnFaceRecognition;

  @NonNull
  public final TextInputEditText etIdNumber;

  @NonNull
  public final TextInputEditText etRealName;

  @NonNull
  public final TextInputLayout tilIdNumber;

  @NonNull
  public final TextInputLayout tilRealName;

  @NonNull
  public final TextView tvSubtitle;

  @NonNull
  public final TextView tvTitle;

  private DialogIdentityVerificationBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnFaceRecognition, @NonNull TextInputEditText etIdNumber,
      @NonNull TextInputEditText etRealName, @NonNull TextInputLayout tilIdNumber,
      @NonNull TextInputLayout tilRealName, @NonNull TextView tvSubtitle,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnFaceRecognition = btnFaceRecognition;
    this.etIdNumber = etIdNumber;
    this.etRealName = etRealName;
    this.tilIdNumber = tilIdNumber;
    this.tilRealName = tilRealName;
    this.tvSubtitle = tvSubtitle;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogIdentityVerificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogIdentityVerificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_identity_verification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogIdentityVerificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnFaceRecognition;
      MaterialButton btnFaceRecognition = ViewBindings.findChildViewById(rootView, id);
      if (btnFaceRecognition == null) {
        break missingId;
      }

      id = R.id.etIdNumber;
      TextInputEditText etIdNumber = ViewBindings.findChildViewById(rootView, id);
      if (etIdNumber == null) {
        break missingId;
      }

      id = R.id.etRealName;
      TextInputEditText etRealName = ViewBindings.findChildViewById(rootView, id);
      if (etRealName == null) {
        break missingId;
      }

      id = R.id.tilIdNumber;
      TextInputLayout tilIdNumber = ViewBindings.findChildViewById(rootView, id);
      if (tilIdNumber == null) {
        break missingId;
      }

      id = R.id.tilRealName;
      TextInputLayout tilRealName = ViewBindings.findChildViewById(rootView, id);
      if (tilRealName == null) {
        break missingId;
      }

      id = R.id.tvSubtitle;
      TextView tvSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtitle == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogIdentityVerificationBinding((ConstraintLayout) rootView, btnFaceRecognition,
          etIdNumber, etRealName, tilIdNumber, tilRealName, tvSubtitle, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
