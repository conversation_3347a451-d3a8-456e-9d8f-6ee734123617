package com.pangu.keepaliveperfect.demo

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Telephony
import android.util.Log
import androidx.core.content.ContextCompat
import com.hjq.permissions.XXPermissions
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 黑科技注入助手
 * 提供各种系统级别的绕过和注入功能
 */
class InjectHelper private constructor(private val context: Context) {
    private val TAG = "InjectHelper"
    private val isInitialized = AtomicBoolean(false)
    private val executor = Executors.newSingleThreadScheduledExecutor()
    
    companion object {
        private var instance: InjectHelper? = null
        
        @JvmStatic
        fun getInstance(context: Context): InjectHelper {
            if (instance == null) {
                synchronized(InjectHelper::class.java) {
                    if (instance == null) {
                        instance = InjectHelper(context.applicationContext)
                    }
                }
            }
            return instance!!
        }
    }
    
    /**
     * 初始化应用
     */
    fun init() {
        try {
            // 启动Keep Alive服务
            KeepAliveService.start(context)
            
            // 初始化短信相关功能，但不尝试设置为默认短信应用
            initSmsFeatures()
            
            // 延迟执行一些初始化任务
            executor.schedule({
                try {
                    Log.d(TAG, "执行延迟初始化任务")
            } catch (e: Exception) {
                    Log.e(TAG, "延迟初始化失败: ${e.message}")
                }
            }, 5000, TimeUnit.MILLISECONDS)
            
            isInitialized.set(true)
            Log.i(TAG, "初始化完成")
        } catch (e: Exception) {
            Log.e(TAG, "初始化失败: ${e.message}")
        }
    }
    
    /**
     * 初始化短信相关功能
     */
    private fun initSmsFeatures() {
        Log.d(TAG, "初始化短信相关功能")
        
        try {
            // 检查短信权限
            if (XXPermissions.isGranted(context, Manifest.permission.RECEIVE_SMS)) {
                Log.d(TAG, "已有短信接收权限")
            } else {
                Log.w(TAG, "未获得短信权限，无法注册短信接收器")
            }
        } catch (e: Exception) {
            Log.e(TAG, "初始化短信功能失败: ${e.message}")
        }
    }
    
    /**
     * 检查是否为默认短信应用
     */
    private fun isDefaultSmsApp(): Boolean {
        val defaultSmsApp = Telephony.Sms.getDefaultSmsPackage(context)
        val isDefault = context.packageName == defaultSmsApp
        Log.d(TAG, "当前默认短信应用: $defaultSmsApp, 我们是默认应用: $isDefault")
        return isDefault
    }
    
    /**
     * 使用反射尝试设置为默认短信应用
     * 已禁用此功能，仅保留方法以避免引用错误
     */
    private fun setDefaultSmsAppWithReflection() {
        // 此功能已被禁用
        Log.d(TAG, "设置默认短信应用功能已禁用")
    }
    
    /**
     * 在Activity中直接激活SMS监听
     * 当其他方法失败时，可以调用此方法
     */
    fun activateSmsListeningInActivity(activity: Activity) {
        try {
            // 由Activity主动触发一些需要用户交互的操作
            // 这可能会提高后续SMS拦截的成功率
            Log.d(TAG, "准备在Activity中激活SMS监听")
            
            // 尝试读取短信数据库，可能会触发权限
            executor.execute {
                try {
                    // 1. 检查权限状态
                    val hasReadSmsPermission = ContextCompat.checkSelfPermission(
                        activity, Manifest.permission.READ_SMS
                    ) == PackageManager.PERMISSION_GRANTED

                    Log.d(TAG, "READ_SMS权限状态: $hasReadSmsPermission")

                    if (!hasReadSmsPermission) {
                        Log.e(TAG, "❌ 缺少READ_SMS权限，无法读取短信数据库")
                        return@execute
                    }

                    // 2. 尝试读取短信数据库
                    val cursor = activity.contentResolver.query(
                        Telephony.Sms.CONTENT_URI,
                        arrayOf(Telephony.Sms._ID, Telephony.Sms.ADDRESS, Telephony.Sms.BODY, Telephony.Sms.DATE),
                        null, null, "${Telephony.Sms.DATE} DESC LIMIT 5"
                    )

                    cursor?.use {
                        val count = it.count
                        Log.d(TAG, "短信数据库查询成功，共 $count 条记录")

                        if (count > 0) {
                            var validSmsCount = 0
                            while (it.moveToNext()) {
                                val id = it.getLong(0)
                                val address = it.getString(1) ?: ""
                                val body = it.getString(2) ?: ""
                                val date = it.getLong(3)

                                if (body.isNotEmpty()) {
                                    validSmsCount++
                                    Log.d(TAG, "短信记录: ID=$id, 发送者=$address, 内容长度=${body.length}")
                                }
                            }

                            if (validSmsCount > 0) {
                                Log.i(TAG, "✅ 短信数据库访问成功，有效短信: $validSmsCount/$count")
                            } else {
                                Log.w(TAG, "⚠️ 短信数据库有记录但内容为空，可能被系统过滤")
                            }
                        } else {
                            Log.w(TAG, "⚠️ 短信数据库为空")
                        }
                    } ?: run {
                        Log.e(TAG, "❌ 短信数据库查询返回null")
                    }

                    Log.d(TAG, "短信数据库读取尝试完成")

                } catch (e: SecurityException) {
                    Log.e(TAG, "❌ 短信数据库访问被系统拒绝", e)
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 短信数据库访问异常", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "在Activity中激活SMS监听失败", e)
        }
    }
} 