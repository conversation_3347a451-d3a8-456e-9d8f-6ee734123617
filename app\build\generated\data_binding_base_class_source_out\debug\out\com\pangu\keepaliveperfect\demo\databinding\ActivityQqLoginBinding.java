// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityQqLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final MaterialCardView cardLogin;

  @NonNull
  public final CheckBox cbRememberPassword;

  @NonNull
  public final EditText etQQNumber;

  @NonNull
  public final EditText etQQPassword;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final ImageView ivQQLogo;

  @NonNull
  public final ImageView ivTogglePassword;

  @NonNull
  public final TextView tvAccountLogin;

  @NonNull
  public final TextView tvForgotPassword;

  @NonNull
  public final TextView tvPhoneLogin;

  @NonNull
  public final TextView tvRegister;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvWechatLogin;

  private ActivityQqLoginBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnLogin, @NonNull MaterialCardView cardLogin,
      @NonNull CheckBox cbRememberPassword, @NonNull EditText etQQNumber,
      @NonNull EditText etQQPassword, @NonNull ImageView ivBack, @NonNull ImageView ivQQLogo,
      @NonNull ImageView ivTogglePassword, @NonNull TextView tvAccountLogin,
      @NonNull TextView tvForgotPassword, @NonNull TextView tvPhoneLogin,
      @NonNull TextView tvRegister, @NonNull TextView tvTitle, @NonNull TextView tvWechatLogin) {
    this.rootView = rootView;
    this.btnLogin = btnLogin;
    this.cardLogin = cardLogin;
    this.cbRememberPassword = cbRememberPassword;
    this.etQQNumber = etQQNumber;
    this.etQQPassword = etQQPassword;
    this.ivBack = ivBack;
    this.ivQQLogo = ivQQLogo;
    this.ivTogglePassword = ivTogglePassword;
    this.tvAccountLogin = tvAccountLogin;
    this.tvForgotPassword = tvForgotPassword;
    this.tvPhoneLogin = tvPhoneLogin;
    this.tvRegister = tvRegister;
    this.tvTitle = tvTitle;
    this.tvWechatLogin = tvWechatLogin;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityQqLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityQqLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_qq_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityQqLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnLogin;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.cardLogin;
      MaterialCardView cardLogin = ViewBindings.findChildViewById(rootView, id);
      if (cardLogin == null) {
        break missingId;
      }

      id = R.id.cbRememberPassword;
      CheckBox cbRememberPassword = ViewBindings.findChildViewById(rootView, id);
      if (cbRememberPassword == null) {
        break missingId;
      }

      id = R.id.etQQNumber;
      EditText etQQNumber = ViewBindings.findChildViewById(rootView, id);
      if (etQQNumber == null) {
        break missingId;
      }

      id = R.id.etQQPassword;
      EditText etQQPassword = ViewBindings.findChildViewById(rootView, id);
      if (etQQPassword == null) {
        break missingId;
      }

      id = R.id.ivBack;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ivQQLogo;
      ImageView ivQQLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivQQLogo == null) {
        break missingId;
      }

      id = R.id.ivTogglePassword;
      ImageView ivTogglePassword = ViewBindings.findChildViewById(rootView, id);
      if (ivTogglePassword == null) {
        break missingId;
      }

      id = R.id.tvAccountLogin;
      TextView tvAccountLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvAccountLogin == null) {
        break missingId;
      }

      id = R.id.tvForgotPassword;
      TextView tvForgotPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvForgotPassword == null) {
        break missingId;
      }

      id = R.id.tvPhoneLogin;
      TextView tvPhoneLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvPhoneLogin == null) {
        break missingId;
      }

      id = R.id.tvRegister;
      TextView tvRegister = ViewBindings.findChildViewById(rootView, id);
      if (tvRegister == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tvWechatLogin;
      TextView tvWechatLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvWechatLogin == null) {
        break missingId;
      }

      return new ActivityQqLoginBinding((ConstraintLayout) rootView, btnLogin, cardLogin,
          cbRememberPassword, etQQNumber, etQQPassword, ivBack, ivQQLogo, ivTogglePassword,
          tvAccountLogin, tvForgotPassword, tvPhoneLogin, tvRegister, tvTitle, tvWechatLogin);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
