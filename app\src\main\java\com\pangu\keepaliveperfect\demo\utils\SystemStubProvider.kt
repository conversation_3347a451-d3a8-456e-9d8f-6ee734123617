package com.pangu.keepaliveperfect.demo.utils

import android.content.ContentProvider
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.util.Log

/**
 * 系统级内容提供者
 * 用于账户同步保活机制
 */
class SystemStubProvider : ContentProvider() {
    
    companion object {
        private const val TAG = "SystemStubProvider"
    }
    
    override fun onCreate(): Boolean {
        Log.d(TAG, "SystemStubProvider created")
        return true
    }
    
    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        return null
    }
    
    override fun getType(uri: Uri): String? {
        return null
    }
    
    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }
    
    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }
    
    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }
}
