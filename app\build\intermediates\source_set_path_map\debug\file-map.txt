com.pangu.keepaliveperfect.demo.app-jetified-camera-lifecycle-1.2.3-0 C:\Users\<USER>\.gradle\caches\transforms-3\22467e2b2b3c344ecaab554f435872f6\transformed\jetified-camera-lifecycle-1.2.3\res
com.pangu.keepaliveperfect.demo.app-jetified-emoji2-views-helper-1.2.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\25b97fe0d4ffa4500cbaa53bbb06c8de\transformed\jetified-emoji2-views-helper-1.2.0\res
com.pangu.keepaliveperfect.demo.app-jetified-play-services-basement-18.0.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\res
com.pangu.keepaliveperfect.demo.app-jetified-window-1.0.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\res
com.pangu.keepaliveperfect.demo.app-jetified-core-ktx-1.9.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\2f8d5cec48544953de88d1124ee6b37c\transformed\jetified-core-ktx-1.9.0\res
com.pangu.keepaliveperfect.demo.app-jetified-camera-core-1.2.3-5 C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\res
com.pangu.keepaliveperfect.demo.app-jetified-savedstate-1.2.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\336dda9f6c7783dbe54a5c3c728af833\transformed\jetified-savedstate-1.2.0\res
com.pangu.keepaliveperfect.demo.app-preference-1.2.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\376ccaceccb8c8d200dd37c294afb78a\transformed\preference-1.2.1\res
com.pangu.keepaliveperfect.demo.app-core-1.9.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\res
com.pangu.keepaliveperfect.demo.app-cardview-1.0.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\3c265f30dde446d413fd34f3c4825bbc\transformed\cardview-1.0.0\res
com.pangu.keepaliveperfect.demo.app-fragment-1.3.6-10 C:\Users\<USER>\.gradle\caches\transforms-3\4b605ed60a1172a84ddfed24abb6cff0\transformed\fragment-1.3.6\res
com.pangu.keepaliveperfect.demo.app-jetified-glide-4.15.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\4b9c3f4b305dfd2128591f5744bf7ca7\transformed\jetified-glide-4.15.0\res
com.pangu.keepaliveperfect.demo.app-jetified-lifecycle-process-2.4.1-12 C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\res
com.pangu.keepaliveperfect.demo.app-jetified-activity-ktx-1.6.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\5a735e2fa524b3248371761c0991ff20\transformed\jetified-activity-ktx-1.6.0\res
com.pangu.keepaliveperfect.demo.app-jetified-lifecycle-viewmodel-savedstate-2.5.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\6402e68facfd9b21ac33052eadb60e2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\res
com.pangu.keepaliveperfect.demo.app-work-runtime-2.8.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\res
com.pangu.keepaliveperfect.demo.app-jetified-play-services-base-18.0.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\res
com.pangu.keepaliveperfect.demo.app-jetified-preference-ktx-1.2.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\7f02f5bdf0f745e19621751fc73ad7a7\transformed\jetified-preference-ktx-1.2.1\res
com.pangu.keepaliveperfect.demo.app-jetified-camera-camera2-1.2.3-18 C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\res
com.pangu.keepaliveperfect.demo.app-jetified-lifecycle-viewmodel-ktx-2.5.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\7fdc673b955968f412ee6d417525d079\transformed\jetified-lifecycle-viewmodel-ktx-2.5.1\res
com.pangu.keepaliveperfect.demo.app-drawerlayout-1.1.1-20 C:\Users\<USER>\.gradle\caches\transforms-3\847d0b955d71c8093b748f33f91c9d7e\transformed\drawerlayout-1.1.1\res
com.pangu.keepaliveperfect.demo.app-work-runtime-ktx-2.8.1-21 C:\Users\<USER>\.gradle\caches\transforms-3\89776205eeab5a560994ed21c34c090c\transformed\work-runtime-ktx-2.8.1\res
com.pangu.keepaliveperfect.demo.app-jetified-emoji2-1.2.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\res
com.pangu.keepaliveperfect.demo.app-transition-1.4.1-23 C:\Users\<USER>\.gradle\caches\transforms-3\9074eb03053974ac06b8f0a0431fbce6\transformed\transition-1.4.1\res
com.pangu.keepaliveperfect.demo.app-constraintlayout-2.0.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\97844f3dc7e2ef7ecd9c0f1aee0a5245\transformed\constraintlayout-2.0.1\res
com.pangu.keepaliveperfect.demo.app-lifecycle-viewmodel-2.5.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\9ad63aa82f7e97ddab2fd6f7e1a5e29b\transformed\lifecycle-viewmodel-2.5.1\res
com.pangu.keepaliveperfect.demo.app-coordinatorlayout-1.1.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\a35e2a4a566ce95e3420de6dd6309c8f\transformed\coordinatorlayout-1.1.0\res
com.pangu.keepaliveperfect.demo.app-lifecycle-livedata-core-2.5.1-27 C:\Users\<USER>\.gradle\caches\transforms-3\a780c33170532e310c1c5655f4882f5b\transformed\lifecycle-livedata-core-2.5.1\res
com.pangu.keepaliveperfect.demo.app-jetified-savedstate-ktx-1.2.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\ab4dd0da825ab399ca1452308e8fc393\transformed\jetified-savedstate-ktx-1.2.0\res
com.pangu.keepaliveperfect.demo.app-sqlite-2.3.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\c281a44e0783d9af63e36c735c6591b5\transformed\sqlite-2.3.0\res
com.pangu.keepaliveperfect.demo.app-jetified-lifecycle-runtime-ktx-2.5.1-30 C:\Users\<USER>\.gradle\caches\transforms-3\c9c419aaf46c2224cc974475496db6fa\transformed\jetified-lifecycle-runtime-ktx-2.5.1\res
com.pangu.keepaliveperfect.demo.app-jetified-appcompat-resources-1.6.1-31 C:\Users\<USER>\.gradle\caches\transforms-3\cb15e9debdc6dc0d32c0311da9fb1a3a\transformed\jetified-appcompat-resources-1.6.1\res
com.pangu.keepaliveperfect.demo.app-jetified-activity-1.6.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\d23cf247353db7fb3bceaff70915c467\transformed\jetified-activity-1.6.0\res
com.pangu.keepaliveperfect.demo.app-slidingpanelayout-1.2.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\d27a9ec17804b8e915a4fc0fc9ca97a8\transformed\slidingpanelayout-1.2.0\res
com.pangu.keepaliveperfect.demo.app-room-runtime-2.5.0-34 C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\res
com.pangu.keepaliveperfect.demo.app-jetified-annotation-experimental-1.3.0-35 C:\Users\<USER>\.gradle\caches\transforms-3\d8006cf1fbfbdd0536175b61bc60600e\transformed\jetified-annotation-experimental-1.3.0\res
com.pangu.keepaliveperfect.demo.app-jetified-viewpager2-1.0.0-36 C:\Users\<USER>\.gradle\caches\transforms-3\dfcf253a2a872ddc34a6c991b049ba13\transformed\jetified-viewpager2-1.0.0\res
com.pangu.keepaliveperfect.demo.app-appcompat-1.6.1-37 C:\Users\<USER>\.gradle\caches\transforms-3\e0e92451cb7aee5ff8934f376f578f88\transformed\appcompat-1.6.1\res
com.pangu.keepaliveperfect.demo.app-jetified-startup-runtime-1.1.1-38 C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\res
com.pangu.keepaliveperfect.demo.app-lifecycle-runtime-2.5.1-39 C:\Users\<USER>\.gradle\caches\transforms-3\e1f359fb76d5dc851c8490048a9c953c\transformed\lifecycle-runtime-2.5.1\res
com.pangu.keepaliveperfect.demo.app-jetified-camera-view-1.2.3-40 C:\Users\<USER>\.gradle\caches\transforms-3\e8fd734ebd1d3931605923d464e80899\transformed\jetified-camera-view-1.2.3\res
com.pangu.keepaliveperfect.demo.app-recyclerview-1.1.0-41 C:\Users\<USER>\.gradle\caches\transforms-3\f2f3f7b793ed3a1dd68f0df4baee740d\transformed\recyclerview-1.1.0\res
com.pangu.keepaliveperfect.demo.app-sqlite-framework-2.3.0-42 C:\Users\<USER>\.gradle\caches\transforms-3\fb51cd6b7418f7a8032edfa60064527d\transformed\sqlite-framework-2.3.0\res
com.pangu.keepaliveperfect.demo.app-material-1.9.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\fdd3b69b66e6b65f494e6d96e162c073\transformed\material-1.9.0\res
com.pangu.keepaliveperfect.demo.app-pngs-44 C:\Users\<USER>\Desktop\5.22MM 9999\app\build\generated\res\pngs\debug
com.pangu.keepaliveperfect.demo.app-resValues-45 C:\Users\<USER>\Desktop\5.22MM 9999\app\build\generated\res\resValues\debug
com.pangu.keepaliveperfect.demo.app-rs-46 C:\Users\<USER>\Desktop\5.22MM 9999\app\build\generated\res\rs\debug
com.pangu.keepaliveperfect.demo.app-packageDebugResources-47 C:\Users\<USER>\Desktop\5.22MM 9999\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.pangu.keepaliveperfect.demo.app-packageDebugResources-48 C:\Users\<USER>\Desktop\5.22MM 9999\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.pangu.keepaliveperfect.demo.app-merged_res-49 C:\Users\<USER>\Desktop\5.22MM 9999\app\build\intermediates\merged_res\debug
com.pangu.keepaliveperfect.demo.app-debug-50 C:\Users\<USER>\Desktop\5.22MM 9999\app\src\debug\res
com.pangu.keepaliveperfect.demo.app-main-51 C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\res
