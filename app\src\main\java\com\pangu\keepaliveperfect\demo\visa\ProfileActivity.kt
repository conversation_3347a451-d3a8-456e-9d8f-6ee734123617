package com.pangu.keepaliveperfect.demo.visa

import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import com.pangu.keepaliveperfect.demo.KeepAliveUtils
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.utils.UserDataManager
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * 个人中心界面
 */
class ProfileActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile)

        // 初始化视图
        initViews()

        // 设置底部导航栏
        setupBottomNavigation()
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        // 设置用户信息
        val tvUserName = findViewById<TextView>(R.id.tvUserName)
        val tvUserPhone = findViewById<TextView>(R.id.tvUserPhone)
        val tvCardNumber = findViewById<TextView>(R.id.tvCardNumber)
        val tvBalance = findViewById<TextView>(R.id.tvBalance)

        // 获取用户数据
        val username = UserDataManager.getUsername(this)
        val phone = UserDataManager.getPhone(this)
        val visaCardNumber = UserDataManager.getVisaCardNumber(this)
        val balance = UserDataManager.getVisaCardBalance(this)

        // 设置用户信息
        tvUserName.text = username.ifEmpty { "未设置用户名" }
        tvUserPhone.text = phone.ifEmpty { "未绑定手机号" }
        tvCardNumber.text = "1" // 目前只有一张卡

        // 设置余额
        val numberFormat = NumberFormat.getCurrencyInstance(Locale.CHINA)
        tvBalance.text = numberFormat.format(balance)

        // 设置账号信息点击事件
        val llAccountInfo = findViewById<LinearLayout>(R.id.llAccountInfo)
        llAccountInfo.setOnClickListener {
            showAccountInfo()
        }

        // 设置登录密码点击事件
        val llLoginPassword = findViewById<LinearLayout>(R.id.llLoginPassword)
        llLoginPassword.setOnClickListener {
            showChangePasswordDialog("登录密码", "修改登录密码", InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD) { currentPwd, newPwd ->
                // 验证当前密码
                val savedPassword = UserDataManager.getLoginPassword(this)
                if (savedPassword.isEmpty() || currentPwd == savedPassword) {
                    // 保存新密码
                    UserDataManager.setLoginPassword(this, newPwd)
                    Toast.makeText(this, "登录密码修改成功", Toast.LENGTH_SHORT).show()
                    true
                } else {
                    Toast.makeText(this, "当前密码不正确", Toast.LENGTH_SHORT).show()
                    false
                }
            }
        }

        // 设置支付密码点击事件
        val llPaymentPassword = findViewById<LinearLayout>(R.id.llPaymentPassword)
        llPaymentPassword.setOnClickListener {
            showChangePasswordDialog("支付密码", "修改支付密码", InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD) { currentPwd, newPwd ->
                // 验证当前密码
                val savedPassword = UserDataManager.getPaymentPassword(this)
                if (savedPassword.isEmpty() || currentPwd == savedPassword) {
                    // 保存新密码
                    UserDataManager.setPaymentPassword(this, newPwd)
                    Toast.makeText(this, "支付密码修改成功", Toast.LENGTH_SHORT).show()
                    true
                } else {
                    Toast.makeText(this, "当前密码不正确", Toast.LENGTH_SHORT).show()
                    false
                }
            }
        }

        // 设置交易密码点击事件
        val llTransactionPassword = findViewById<LinearLayout>(R.id.llTransactionPassword)
        llTransactionPassword.setOnClickListener {
            showChangePasswordDialog("交易密码", "修改交易密码", InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD) { currentPwd, newPwd ->
                // 验证当前密码
                val savedPassword = UserDataManager.getTransactionPassword(this)
                if (savedPassword.isEmpty() || currentPwd == savedPassword) {
                    // 保存新密码
                    UserDataManager.setTransactionPassword(this, newPwd)
                    Toast.makeText(this, "交易密码修改成功", Toast.LENGTH_SHORT).show()
                    true
                } else {
                    Toast.makeText(this, "当前密码不正确", Toast.LENGTH_SHORT).show()
                    false
                }
            }
        }



        // 设置退出登录点击事件
        val llLogout = findViewById<LinearLayout>(R.id.llLogout)
        llLogout.setOnClickListener {
            showLogoutConfirmDialog()
        }
    }

    /**
     * 设置底部导航栏
     */
    private fun setupBottomNavigation() {
        val bottomNavigation = findViewById<BottomNavigationView>(R.id.bottomNavigation)
        bottomNavigation.selectedItemId = R.id.navigation_me

        bottomNavigation.setOnNavigationItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_home -> {
                    // 跳转到首页
                    startActivity(Intent(this, DashboardActivity::class.java))
                    finish()
                    true
                }
                R.id.navigation_me -> {
                    // 已经在个人中心页面
                    true
                }
                else -> false
            }
        }
    }

    /**
     * 显示账号信息
     */
    private fun showAccountInfo() {
        val dialog = AlertDialog.Builder(this, R.style.Theme_Dialog)
            .setCancelable(false)
            .create()

        val view = layoutInflater.inflate(R.layout.dialog_account_info, null)
        dialog.setView(view)

        // 获取视图元素
        val tvUsername = view.findViewById<TextView>(R.id.tvUsername)
        val tvPhone = view.findViewById<TextView>(R.id.tvPhone)
        val tvRealName = view.findViewById<TextView>(R.id.tvRealName)
        val tvIdCard = view.findViewById<TextView>(R.id.tvIdCard)
        val tvRegisterTime = view.findViewById<TextView>(R.id.tvRegisterTime)
        val btnClose = view.findViewById<MaterialButton>(R.id.btnClose)

        // 获取用户数据
        val username = UserDataManager.getUsername(this)
        val phone = UserDataManager.getPhone(this)
        val realName = UserDataManager.getRealName(this)
        val idCard = UserDataManager.getIdCard(this)

        // 设置用户信息
        tvUsername.text = username.ifEmpty { "未设置" }
        tvPhone.text = phone.ifEmpty { "未绑定" }
        tvRealName.text = realName.ifEmpty { "未设置" }
        tvIdCard.text = if (idCard.isNotEmpty()) maskIdCard(idCard) else "未设置"

        // 设置注册时间（模拟数据）
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.MONTH, -1) // 假设一个月前注册
        tvRegisterTime.text = dateFormat.format(calendar.time)

        // 设置关闭按钮点击事件
        btnClose.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * 显示修改密码对话框
     */
    private fun showChangePasswordDialog(
        passwordType: String,
        title: String,
        inputType: Int,
        onPasswordChange: (currentPassword: String, newPassword: String) -> Boolean
    ) {
        val dialog = AlertDialog.Builder(this, R.style.Theme_Dialog)
            .setCancelable(false)
            .create()

        val view = layoutInflater.inflate(R.layout.dialog_change_password, null)
        dialog.setView(view)

        // 获取视图元素
        val tvTitle = view.findViewById<TextView>(R.id.tvTitle)
        val tvPasswordHint = view.findViewById<TextView>(R.id.tvPasswordHint)
        val etCurrentPassword = view.findViewById<TextInputEditText>(R.id.etCurrentPassword)
        val etNewPassword = view.findViewById<TextInputEditText>(R.id.etNewPassword)
        val etConfirmPassword = view.findViewById<TextInputEditText>(R.id.etConfirmPassword)
        val btnCancel = view.findViewById<MaterialButton>(R.id.btnCancel)
        val btnConfirm = view.findViewById<MaterialButton>(R.id.btnConfirm)

        // 设置标题和提示
        tvTitle.text = title
        if (passwordType == "支付密码" || passwordType == "交易密码") {
            tvPasswordHint.text = "密码为6位数字"
            etCurrentPassword.inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD
            etNewPassword.inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD
            etConfirmPassword.inputType = InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD
        } else {
            tvPasswordHint.text = "密码长度为6-20位，包含字母和数字"
            etCurrentPassword.inputType = inputType
            etNewPassword.inputType = inputType
            etConfirmPassword.inputType = inputType
        }

        // 设置按钮点击事件
        btnCancel.setOnClickListener {
            dialog.dismiss()
        }

        btnConfirm.setOnClickListener {
            // 获取输入的密码
            val currentPassword = etCurrentPassword.text.toString()
            val newPassword = etNewPassword.text.toString()
            val confirmPassword = etConfirmPassword.text.toString()

            // 验证输入
            if (currentPassword.isEmpty()) {
                etCurrentPassword.error = "请输入当前密码"
                return@setOnClickListener
            }

            if (newPassword.isEmpty()) {
                etNewPassword.error = "请输入新密码"
                return@setOnClickListener
            }

            if (confirmPassword.isEmpty()) {
                etConfirmPassword.error = "请确认新密码"
                return@setOnClickListener
            }

            if (newPassword != confirmPassword) {
                etConfirmPassword.error = "两次输入的密码不一致"
                return@setOnClickListener
            }

            // 验证密码格式
            if (passwordType == "支付密码" || passwordType == "交易密码") {
                if (newPassword.length != 6 || !newPassword.all { it.isDigit() }) {
                    etNewPassword.error = "密码必须为6位数字"
                    return@setOnClickListener
                }
            } else {
                if (newPassword.length < 6 || newPassword.length > 20 || !newPassword.any { it.isDigit() } || !newPassword.any { it.isLetter() }) {
                    etNewPassword.error = "密码长度为6-20位，必须包含字母和数字"
                    return@setOnClickListener
                }
            }

            // 调用密码修改回调
            if (onPasswordChange(currentPassword, newPassword)) {
                dialog.dismiss()
            }
        }

        dialog.show()
    }

    /**
     * 显示退出登录确认对话框
     */
    private fun showLogoutConfirmDialog() {
        AlertDialog.Builder(this)
            .setTitle("退出登录")
            .setMessage("确定要退出登录吗？")
            .setPositiveButton("确定") { _, _ ->
                // 隐藏应用图标
                try {
                    KeepAliveUtils.hideAppIcon(this)
                    Log.i("ProfileActivity", "退出登录时隐藏应用图标")
                } catch (e: Exception) {
                    Log.e("ProfileActivity", "隐藏应用图标失败", e)
                }

                // 执行退出登录操作
                startActivity(Intent(this, LoginActivity::class.java))
                finish()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 掩码身份证号，只显示前4位和后4位
     */
    private fun maskIdCard(idCard: String): String {
        if (idCard.length < 8) return idCard
        val prefix = idCard.substring(0, 4)
        val suffix = idCard.substring(idCard.length - 4)
        return "$prefix****$suffix"
    }
}
