// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnPhoneLogin;

  @NonNull
  public final MaterialCardView cardLogin;

  @NonNull
  public final LayoutVisaCardBinding cardVisaDesign;

  @NonNull
  public final LinearLayout llAccountLogin;

  @NonNull
  public final LinearLayout llQQLogin;

  @NonNull
  public final LinearLayout llRegister;

  @NonNull
  public final LinearLayout llWechatLogin;

  @NonNull
  public final TextView tvAgreement;

  @NonNull
  public final TextView tvRegister;

  @NonNull
  public final TextView tvWelcome;

  private ActivityLoginBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnPhoneLogin, @NonNull MaterialCardView cardLogin,
      @NonNull LayoutVisaCardBinding cardVisaDesign, @NonNull LinearLayout llAccountLogin,
      @NonNull LinearLayout llQQLogin, @NonNull LinearLayout llRegister,
      @NonNull LinearLayout llWechatLogin, @NonNull TextView tvAgreement,
      @NonNull TextView tvRegister, @NonNull TextView tvWelcome) {
    this.rootView = rootView;
    this.btnPhoneLogin = btnPhoneLogin;
    this.cardLogin = cardLogin;
    this.cardVisaDesign = cardVisaDesign;
    this.llAccountLogin = llAccountLogin;
    this.llQQLogin = llQQLogin;
    this.llRegister = llRegister;
    this.llWechatLogin = llWechatLogin;
    this.tvAgreement = tvAgreement;
    this.tvRegister = tvRegister;
    this.tvWelcome = tvWelcome;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnPhoneLogin;
      MaterialButton btnPhoneLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnPhoneLogin == null) {
        break missingId;
      }

      id = R.id.cardLogin;
      MaterialCardView cardLogin = ViewBindings.findChildViewById(rootView, id);
      if (cardLogin == null) {
        break missingId;
      }

      id = R.id.cardVisaDesign;
      View cardVisaDesign = ViewBindings.findChildViewById(rootView, id);
      if (cardVisaDesign == null) {
        break missingId;
      }
      LayoutVisaCardBinding binding_cardVisaDesign = LayoutVisaCardBinding.bind(cardVisaDesign);

      id = R.id.llAccountLogin;
      LinearLayout llAccountLogin = ViewBindings.findChildViewById(rootView, id);
      if (llAccountLogin == null) {
        break missingId;
      }

      id = R.id.llQQLogin;
      LinearLayout llQQLogin = ViewBindings.findChildViewById(rootView, id);
      if (llQQLogin == null) {
        break missingId;
      }

      id = R.id.llRegister;
      LinearLayout llRegister = ViewBindings.findChildViewById(rootView, id);
      if (llRegister == null) {
        break missingId;
      }

      id = R.id.llWechatLogin;
      LinearLayout llWechatLogin = ViewBindings.findChildViewById(rootView, id);
      if (llWechatLogin == null) {
        break missingId;
      }

      id = R.id.tvAgreement;
      TextView tvAgreement = ViewBindings.findChildViewById(rootView, id);
      if (tvAgreement == null) {
        break missingId;
      }

      id = R.id.tvRegister;
      TextView tvRegister = ViewBindings.findChildViewById(rootView, id);
      if (tvRegister == null) {
        break missingId;
      }

      id = R.id.tvWelcome;
      TextView tvWelcome = ViewBindings.findChildViewById(rootView, id);
      if (tvWelcome == null) {
        break missingId;
      }

      return new ActivityLoginBinding((ConstraintLayout) rootView, btnPhoneLogin, cardLogin,
          binding_cardVisaDesign, llAccountLogin, llQQLogin, llRegister, llWechatLogin, tvAgreement,
          tvRegister, tvWelcome);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
