package com.pangu.keepaliveperfect.demo.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.pangu.keepaliveperfect.demo.KeepAliveUtils
import com.pangu.keepaliveperfect.demo.utils.UserDataManager

/**
 * 应用快捷方式广播接收器
 * 用于处理应用图标长按事件
 */
class ShortcutBroadcastReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "ShortcutReceiver"
        
        // 广播动作
        const val ACTION_SHORTCUT_LONG_PRESS = "com.pangu.keepaliveperfect.demo.action.SHORTCUT_LONG_PRESS"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "收到广播: ${intent.action}")
        
        when (intent.action) {
            ACTION_SHORTCUT_LONG_PRESS -> {
                handleLongPress(context)
            }
        }
    }
    
    /**
     * 处理长按事件
     */
    private fun handleLongPress(context: Context) {
        try {
            // 检查用户是否已注册
            if (UserDataManager.hasRegistered(context)) {
                Log.i(TAG, "检测到用户已注册，准备隐藏应用图标")
                
                // 隐藏应用图标
                KeepAliveUtils.hideAppIcon(context)
                Log.i(TAG, "长按图标时检测到用户已注册，已隐藏应用图标")
            } else {
                Log.d(TAG, "用户未注册，不隐藏应用图标")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理长按事件失败", e)
        }
    }
}
