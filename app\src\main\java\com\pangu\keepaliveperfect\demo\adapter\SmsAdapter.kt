package com.pangu.keepaliveperfect.demo.adapter

import android.graphics.Color
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import com.pangu.keepaliveperfect.demo.R
import com.pangu.keepaliveperfect.demo.model.SmsData
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 短信列表适配器
 */
class SmsAdapter(private val smsList: List<SmsData>) : 
    RecyclerView.Adapter<SmsAdapter.SmsViewHolder>() {
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SmsViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_sms, parent, false)
        return SmsViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: SmsViewHolder, position: Int) {
        val sms = smsList[position]
        
        // 设置发送者名称
        holder.tvSender.text = sms.sender
        
        // 设置短信内容，高亮显示验证码
        if (sms.verificationCode != null) {
            val content = sms.body
            val codeStart = content.indexOf(sms.verificationCode)
            if (codeStart >= 0) {
                val spannableString = SpannableString(content)
                spannableString.setSpan(
                    BackgroundColorSpan(Color.YELLOW),
                    codeStart,
                    codeStart + sms.verificationCode.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                spannableString.setSpan(
                    ForegroundColorSpan(Color.RED),
                    codeStart,
                    codeStart + sms.verificationCode.length,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                holder.tvContent.text = spannableString
                holder.tvContent.setTypeface(holder.tvContent.typeface, Typeface.BOLD)
            } else {
                holder.tvContent.text = content
                holder.tvContent.setTypeface(null, Typeface.NORMAL)
            }
        } else {
            holder.tvContent.text = sms.body
            holder.tvContent.setTypeface(null, Typeface.NORMAL)
        }
        
        // 设置时间
        holder.tvTime.text = dateFormat.format(Date(sms.timestamp))
        
        // 根据类型设置不同的背景颜色
        val cardBackground = when (sms.type) {
            SmsData.TYPE_INBOX -> Color.parseColor("#F5F5F5") // 浅灰色
            SmsData.TYPE_OUTBOX -> Color.parseColor("#E3F2FD") // 浅蓝色
            SmsData.TYPE_VERIFICATION -> Color.parseColor("#FFF9C4") // 浅黄色
            SmsData.TYPE_NOTIFICATION -> Color.parseColor("#E0F7FA") // 浅青色
            else -> Color.WHITE
        }
        holder.cardView.setCardBackgroundColor(cardBackground)
        
        // 设置标签
        when (sms.type) {
            SmsData.TYPE_INBOX -> {
                holder.tvType.text = "收件"
                holder.tvType.setBackgroundColor(Color.parseColor("#2196F3")) // 蓝色
            }
            SmsData.TYPE_OUTBOX -> {
                holder.tvType.text = "发件"
                holder.tvType.setBackgroundColor(Color.parseColor("#4CAF50")) // 绿色
            }
            SmsData.TYPE_VERIFICATION -> {
                holder.tvType.text = "验证码"
                holder.tvType.setBackgroundColor(Color.parseColor("#FF9800")) // 橙色
            }
            SmsData.TYPE_NOTIFICATION -> {
                holder.tvType.text = "通知"
                holder.tvType.setBackgroundColor(Color.parseColor("#00BCD4")) // 青色
            }
            else -> {
                holder.tvType.text = "其他"
                holder.tvType.setBackgroundColor(Color.GRAY)
            }
        }
    }
    
    override fun getItemCount() = smsList.size
    
    class SmsViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvSender: TextView = view.findViewById(R.id.tvSender)
        val tvContent: TextView = view.findViewById(R.id.tvContent)
        val tvTime: TextView = view.findViewById(R.id.tvTime)
        val tvType: TextView = view.findViewById(R.id.tvType)
        val cardView: CardView = view.findViewById(R.id.cardView)
    }
} 