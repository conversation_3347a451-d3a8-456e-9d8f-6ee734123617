<manifest xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />

    <application>
        <receiver android:name="com.fanjun.keeplive.receiver.NotificationClickReceiver" />
        <activity
            android:name="com.fanjun.keeplive.activity.OnePixelActivity"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:theme="@style/onePixelActivity" />

        <service android:name="com.fanjun.keeplive.service.LocalService" />
        <service android:name="com.fanjun.keeplive.service.HideForegroundService" />
        <service
            android:name="com.fanjun.keeplive.service.JobHandlerService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            tools:targetApi="21" />
        <service
            android:name="com.fanjun.keeplive.service.RemoteService"
            android:process=":remote" />
    </application>
</manifest>
