package com.pangu.keepaliveperfect.demo

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log

/**
 * 守护服务
 * 在独立进程中运行，与主服务互相监视，确保至少有一个服务存活
 */
class DaemonService : Service() {
    private var keepAliveReceiver: BroadcastReceiver? = null
    private val TAG = KeepAliveConfig.TAG

    // 修复：智能模式标志
    private var isSmartMode = false
    private val handler = Handler(Looper.getMainLooper())

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "守护服务已创建")

        try {
            // 启动前台服务
            startForeground(KeepAliveConfig.NOTIFICATION_ID + 1, KeepAliveConfig.createNotification(this))

            // 注册保活广播接收器
            registerKeepAliveReceiver()

            // 提升进程优先级
            KeepAliveUtils.raiseProcessPriority()
        } catch (e: Exception) {
            Log.e(TAG, "守护服务初始化失败", e)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 修复：检查是否为智能模式
        isSmartMode = intent?.getBooleanExtra("smart_mode", false) ?: false

        if (isSmartMode) {
            Log.i(TAG, "🧠 守护服务启动（智能模式）- 降低检查频率")
        } else {
            Log.i(TAG, "守护服务启动（标准模式）")
        }

        // 确保前台通知
        startForeground(KeepAliveConfig.NOTIFICATION_ID + 1, KeepAliveConfig.createNotification(this))

        // 修复：智能模式下启动定时检查
        if (isSmartMode) {
            startSmartModeCheck()
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        Log.i(TAG, "守护服务被销毁，尝试重启")

        // 注销广播接收器
        unregisterKeepAliveReceiver()

        // 重启主服务和自身
        restartServices()

        super.onDestroy()
    }

    /**
     * 注册保活广播接收器
     */
    private fun registerKeepAliveReceiver() {
        try {
            keepAliveReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    when (intent.action) {
                        KeepAliveConfig.ACTION_KEEP_ALIVE -> {
                            // 检查主服务是否存在，不存在则启动
                            if (!KeepAliveUtils.isServiceRunning(context, KeepAliveService::class.java)) {
                                Log.i(TAG, "主服务不在运行，从守护服务重启主服务")
                                KeepAliveService.startServiceSafely(context, KeepAliveService::class.java)
                            }
                        }
                        Intent.ACTION_SCREEN_OFF -> {
                            // 屏幕关闭，启动一像素Activity作为备份
                            Log.i(TAG, "屏幕关闭，守护服务备份启动一像素保活")
                            OnePixelActivity.start(context)
                        }
                        Intent.ACTION_SCREEN_ON -> {
                            // 屏幕点亮
                            Log.i(TAG, "屏幕点亮，守护服务备份关闭一像素Activity")
                            OnePixelActivity.finishSelf()
                        }
                        Intent.ACTION_BOOT_COMPLETED -> {
                            // 开机自启动
                            Log.i(TAG, "系统启动，守护服务启动所有服务")
                            KeepAliveService.startServiceSafely(context, KeepAliveService::class.java)
                        }
                    }
                }
            }

            val filter = IntentFilter()
            filter.addAction(KeepAliveConfig.ACTION_KEEP_ALIVE)
            filter.addAction(KeepAliveConfig.ACTION_SCREEN_OFF)
            filter.addAction(KeepAliveConfig.ACTION_SCREEN_ON)
            filter.addAction(Intent.ACTION_BOOT_COMPLETED)
            registerReceiver(keepAliveReceiver, filter)
            Log.i(TAG, "守护服务广播接收器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册守护服务广播接收器失败", e)
        }
    }

    /**
     * 注销广播接收器
     */
    private fun unregisterKeepAliveReceiver() {
        try {
            keepAliveReceiver?.let {
                unregisterReceiver(it)
                keepAliveReceiver = null
                Log.i(TAG, "守护服务广播接收器注销成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "注销守护服务广播接收器失败", e)
        }
    }

    /**
     * 重启所有服务
     */
    private fun restartServices() {
        try {
            // 重启守护服务
            KeepAliveService.startServiceSafely(applicationContext, DaemonService::class.java)

            // 重启主服务
            KeepAliveService.startServiceSafely(applicationContext, KeepAliveService::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "重启服务失败", e)
        }
    }

    /**
     * 修复：智能模式检查
     * 每5分钟检查一次主服务状态，发现死亡立即重启
     */
    private fun startSmartModeCheck() {
        handler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    // 检查主服务是否运行
                    if (!KeepAliveUtils.isServiceRunning(this@DaemonService, KeepAliveService::class.java)) {
                        Log.w(TAG, "🚨 守护服务检测到主服务死亡，立即重启")
                        KeepAliveService.startServiceSafely(this@DaemonService, KeepAliveService::class.java)
                    } else {
                        Log.v(TAG, "🧠 守护服务检查：主服务正常运行")
                    }

                    // 继续下一次检查（5分钟后）
                    if (isSmartMode) {
                        handler.postDelayed(this, 300_000L) // 5分钟
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "❌ 守护服务智能检查失败", e)
                    // 即使出错也要继续检查
                    if (isSmartMode) {
                        handler.postDelayed(this, 300_000L)
                    }
                }
            }
        }, 60_000L) // 1分钟后开始第一次检查
    }
}