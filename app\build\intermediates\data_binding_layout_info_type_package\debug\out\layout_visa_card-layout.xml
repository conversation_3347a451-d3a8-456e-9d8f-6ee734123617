<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_visa_card" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\layout_visa_card.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/cardVisaDesign"><Targets><Target id="@+id/cardVisaDesign" tag="layout/layout_visa_card_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="163" endOffset="16"/></Target><Target id="@+id/card_view" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="9" startOffset="4" endLine="162" endOffset="39"/></Target><Target id="@+id/card_outer_glow" view="View"><Expressions/><location startLine="18" startOffset="8" endLine="23" endOffset="38"/></Target><Target id="@+id/background" view="ImageView"><Expressions/><location startLine="30" startOffset="12" endLine="35" endOffset="48"/></Target><Target id="@+id/shine_effect" view="View"><Expressions/><location startLine="38" startOffset="12" endLine="43" endOffset="46"/></Target><Target id="@+id/visa_logo" view="ImageView"><Expressions/><location startLine="46" startOffset="12" endLine="56" endOffset="64"/></Target><Target id="@+id/bank_name" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="71" endOffset="42"/></Target><Target id="@+id/card_number" view="TextView"><Expressions/><location startLine="74" startOffset="12" endLine="84" endOffset="41"/></Target><Target id="@+id/label_holder" view="TextView"><Expressions/><location startLine="87" startOffset="12" endLine="97" endOffset="41"/></Target><Target id="@+id/card_holder" view="TextView"><Expressions/><location startLine="100" startOffset="12" endLine="110" endOffset="41"/></Target><Target id="@+id/label_valid" view="TextView"><Expressions/><location startLine="113" startOffset="12" endLine="124" endOffset="41"/></Target><Target id="@+id/card_valid" view="TextView"><Expressions/><location startLine="127" startOffset="12" endLine="138" endOffset="41"/></Target></Targets></Layout>