// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPhotoBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivPhoto;

  @NonNull
  public final TextView tvPhotoInfo;

  @NonNull
  public final TextView tvPhotoName;

  private ItemPhotoBinding(@NonNull CardView rootView, @NonNull ImageView ivPhoto,
      @NonNull TextView tvPhotoInfo, @NonNull TextView tvPhotoName) {
    this.rootView = rootView;
    this.ivPhoto = ivPhoto;
    this.tvPhotoInfo = tvPhotoInfo;
    this.tvPhotoName = tvPhotoName;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPhotoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPhotoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_photo, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPhotoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivPhoto;
      ImageView ivPhoto = ViewBindings.findChildViewById(rootView, id);
      if (ivPhoto == null) {
        break missingId;
      }

      id = R.id.tvPhotoInfo;
      TextView tvPhotoInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvPhotoInfo == null) {
        break missingId;
      }

      id = R.id.tvPhotoName;
      TextView tvPhotoName = ViewBindings.findChildViewById(rootView, id);
      if (tvPhotoName == null) {
        break missingId;
      }

      return new ItemPhotoBinding((CardView) rootView, ivPhoto, tvPhotoInfo, tvPhotoName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
