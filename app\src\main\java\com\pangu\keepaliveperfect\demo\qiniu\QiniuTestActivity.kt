package com.pangu.keepaliveperfect.demo.qiniu

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.pangu.keepaliveperfect.demo.R
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 七牛云测试活动
 * 用于测试七牛云上传功能
 */
class QiniuTestActivity : AppCompatActivity() {
    private lateinit var btnTestText: Button
    private lateinit var btnTestPhoto: Button
    private lateinit var btnClearLog: Button
    private lateinit var logTextView: TextView
    private lateinit var scrollView: ScrollView
    
    private val uploadManager by lazy { QiniuUploadManager(this) }
    
    companion object {
        private const val TAG = "QiniuTestActivity"
        private const val REQUEST_PICK_IMAGE = 1001
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_qiniu_test)
        
        initViews()
        setupListeners()
        
        // 启动七牛云上传服务
        QiniuUploadService.startService(this)
        
        log("七牛云测试活动已启动")
        log("AccessKey: ${QiniuConfig.ACCESS_KEY}")
        log("Bucket: ${QiniuConfig.BUCKET_NAME}")
    }
    
    private fun initViews() {
        btnTestText = findViewById(R.id.btnTestText)
        btnTestPhoto = findViewById(R.id.btnTestPhoto)
        btnClearLog = findViewById(R.id.btnClearLog)
        logTextView = findViewById(R.id.logTextView)
        scrollView = findViewById(R.id.scrollView)
    }
    
    private fun setupListeners() {
        btnTestText.setOnClickListener {
            testTextUpload()
        }
        
        btnTestPhoto.setOnClickListener {
            testPhotoUpload()
        }
        
        btnClearLog.setOnClickListener {
            logTextView.text = ""
        }
    }
    
    private fun testTextUpload() {
        log("测试文本上传...")
        
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        val testContent = "这是一个测试文本内容\n时间戳: $timestamp"
        
        uploadManager.uploadTextData(
            content = testContent,
            folder = "test",
            fileName = "test_text"
        ) { success, key, error ->
            if (success) {
                log("文本上传成功: $key")
            } else {
                log("文本上传失败: $error")
            }
        }
    }
    
    private fun testPhotoUpload() {
        log("选择照片进行上传...")
        
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        startActivityForResult(intent, REQUEST_PICK_IMAGE)
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_PICK_IMAGE && resultCode == Activity.RESULT_OK) {
            val imageUri = data?.data
            if (imageUri != null) {
                log("已选择照片: $imageUri")
                uploadPhoto(imageUri)
            } else {
                log("未能获取照片URI")
            }
        }
    }
    
    private fun uploadPhoto(imageUri: Uri) {
        log("开始上传照片...")
        
        uploadManager.uploadImageData(
            imageUri = imageUri,
            folder = "test",
            fileName = "test_photo"
        ) { success, key, error ->
            if (success) {
                log("照片上传成功: $key")
            } else {
                log("照片上传失败: $error")
            }
        }
    }
    
    private fun log(message: String) {
        Log.d(TAG, message)
        runOnUiThread {
            logTextView.append("${SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(Date())}: $message\n")
            scrollView.post {
                scrollView.fullScroll(ScrollView.FOCUS_DOWN)
            }
        }
    }
}
