package com.pangu.keepaliveperfect.demo.counter

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Process
import android.util.Log
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * OPPO Killer 防御系统
 * 专门对抗OPPO SafeCenter的恶意杀进程行为
 */
class OppoKillerDefense private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "OppoKillerDefense"
        
        @Volatile
        private var INSTANCE: OppoKillerDefense? = null
        
        fun getInstance(context: Context): OppoKillerDefense {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: OppoKillerDefense(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val isActive = AtomicBoolean(false)
    private val processGuards = mutableListOf<ProcessGuard>()
    
    /**
     * 启动防御系统
     */
    fun startDefense() {
        if (isActive.compareAndSet(false, true)) {
            Log.i(TAG, "🛡️ 启动OPPO Killer防御系统")
            
            try {
                // 1. 检测OPPO SafeCenter
                detectSafeCenter()
                
                // 2. 启动多进程守护
                startMultiProcessGuard()
                
                // 3. 启动进程监控
                startProcessMonitoring()
                
                // 4. 启动反杀机制
                startAntiKillMechanism()
                
                Log.i(TAG, "✅ OPPO Killer防御系统启动成功")
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 防御系统启动失败", e)
                isActive.set(false)
            }
        }
    }
    
    /**
     * 停止防御系统
     */
    fun stopDefense() {
        if (isActive.compareAndSet(true, false)) {
            Log.i(TAG, "🛑 停止OPPO Killer防御系统")
            processGuards.forEach { it.stop() }
            processGuards.clear()
        }
    }
    
    /**
     * 检测OPPO SafeCenter
     */
    private fun detectSafeCenter() {
        try {
            val safeCenterProcesses = findSafeCenterProcesses()
            
            if (safeCenterProcesses.isNotEmpty()) {
                Log.w(TAG, "⚠️ 检测到OPPO SafeCenter进程: $safeCenterProcesses")
                
                // 尝试干扰SafeCenter
                interfereSafeCenter(safeCenterProcesses)
            } else {
                Log.i(TAG, "✅ 未检测到活跃的SafeCenter进程")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "检测SafeCenter失败", e)
        }
    }
    
    /**
     * 查找SafeCenter进程
     */
    private fun findSafeCenterProcesses(): List<String> {
        val safeCenterProcesses = mutableListOf<String>()
        
        try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningProcesses = activityManager.runningAppProcesses
            
            runningProcesses?.forEach { processInfo ->
                val processName = processInfo.processName
                
                if (processName.contains("safecenter", ignoreCase = true) ||
                    processName.contains("coloros.persist", ignoreCase = true) ||
                    processName.contains("oppoguardelf", ignoreCase = true)) {
                    
                    safeCenterProcesses.add("${processName}:${processInfo.pid}")
                    Log.d(TAG, "发现可疑进程: $processName (PID: ${processInfo.pid})")
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "查找SafeCenter进程失败", e)
        }
        
        return safeCenterProcesses
    }
    
    /**
     * 干扰SafeCenter
     */
    private fun interfereSafeCenter(processes: List<String>) {
        Thread {
            try {
                Log.i(TAG, "🎯 开始干扰SafeCenter...")
                
                processes.forEach { processInfo ->
                    try {
                        val parts = processInfo.split(":")
                        if (parts.size == 2) {
                            val processName = parts[0]
                            val pid = parts[1].toInt()
                            
                            // 方法1: 内存干扰
                            interfereProcessMemory(pid, processName)
                            
                            // 方法2: 文件系统干扰
                            interfereProcessFiles(pid, processName)
                            
                            // 方法3: 信号干扰
                            interfereProcessSignals(pid, processName)
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "干扰进程失败: $processInfo", e)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "SafeCenter干扰失败", e)
            }
        }.start()
    }
    
    /**
     * 内存干扰
     */
    private fun interfereProcessMemory(pid: Int, processName: String) {
        try {
            // 尝试读取目标进程的内存映射
            val mapsFile = File("/proc/$pid/maps")
            if (mapsFile.exists()) {
                val maps = mapsFile.readLines()
                Log.d(TAG, "🎯 [$processName] 内存映射区域: ${maps.size}个")
                
                // 查找可写的内存区域
                val writableRegions = maps.filter { it.contains("rw") }
                Log.d(TAG, "🎯 [$processName] 可写内存区域: ${writableRegions.size}个")
                
                // 尝试向可写区域写入干扰数据
                writableRegions.take(3).forEach { region ->
                    try {
                        interfereMemoryRegion(pid, region)
                    } catch (e: Exception) {
                        // 忽略单个区域的失败
                    }
                }
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "内存干扰失败: $processName", e)
        }
    }
    
    /**
     * 干扰内存区域
     */
    private fun interfereMemoryRegion(pid: Int, regionInfo: String) {
        try {
            val addressRange = regionInfo.split(" ")[0].split("-")
            val startAddr = addressRange[0].toLong(16)
            val endAddr = addressRange[1].toLong(16)
            
            // 限制干扰范围，避免系统不稳定
            val maxSize = 1024 * 1024 // 最大1MB
            val regionSize = minOf(endAddr - startAddr, maxSize.toLong())
            
            if (regionSize > 0) {
                // 创建干扰数据
                val interferenceData = createInterferenceData(regionSize.toInt())
                
                // 尝试写入（这里需要root权限，可能会失败）
                val memFile = File("/proc/$pid/mem")
                if (memFile.exists()) {
                    Log.d(TAG, "🎯 尝试干扰内存区域: ${startAddr.toString(16)}-${endAddr.toString(16)}")
                    // 实际的内存写入需要特殊权限
                }
            }
            
        } catch (e: Exception) {
            // 内存干扰失败是正常的，因为权限限制
        }
    }
    
    /**
     * 文件系统干扰
     */
    private fun interfereProcessFiles(pid: Int, processName: String) {
        try {
            // 干扰进程的文件描述符
            val fdDir = File("/proc/$pid/fd")
            if (fdDir.exists()) {
                val fds = fdDir.listFiles()
                Log.d(TAG, "🎯 [$processName] 文件描述符: ${fds?.size ?: 0}个")
                
                // 尝试创建大量文件描述符消耗资源
                createResourceExhaustion(processName)
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "文件系统干扰失败: $processName", e)
        }
    }
    
    /**
     * 信号干扰
     */
    private fun interfereProcessSignals(pid: Int, processName: String) {
        try {
            // 发送非致命信号干扰进程
            val signals = arrayOf("USR1", "USR2", "CONT", "STOP")
            
            signals.forEach { signal ->
                try {
                    val command = "kill -$signal $pid"
                    Runtime.getRuntime().exec(command)
                    Log.d(TAG, "🎯 [$processName] 发送信号: $signal")
                    Thread.sleep(100)
                } catch (e: Exception) {
                    // 信号发送失败是正常的
                }
            }
            
        } catch (e: Exception) {
            Log.w(TAG, "信号干扰失败: $processName", e)
        }
    }
    
    /**
     * 启动多进程守护
     */
    private fun startMultiProcessGuard() {
        try {
            // 创建多个守护进程
            for (i in 1..3) {
                val guard = ProcessGuard(context, i)
                guard.start()
                processGuards.add(guard)
                Log.d(TAG, "✅ 启动守护进程 #$i")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "启动多进程守护失败", e)
        }
    }
    
    /**
     * 启动进程监控
     */
    private fun startProcessMonitoring() {
        Thread {
            Log.d(TAG, "🔍 启动进程监控线程")
            
            while (isActive.get()) {
                try {
                    // 检查主进程状态
                    if (!isMainProcessAlive()) {
                        Log.w(TAG, "⚠️ 主进程可能被杀死，尝试重启")
                        restartMainProcess()
                    }
                    
                    // 检查守护进程状态
                    checkGuardProcesses()
                    
                    // 每5秒检查一次
                    Thread.sleep(5000)
                    
                } catch (e: InterruptedException) {
                    Log.d(TAG, "进程监控线程被中断")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "进程监控异常", e)
                }
            }
            
            Log.d(TAG, "🛑 进程监控线程结束")
        }.start()
    }
    
    /**
     * 启动反杀机制
     */
    private fun startAntiKillMechanism() {
        Thread {
            Log.d(TAG, "⚔️ 启动反杀机制")
            
            while (isActive.get()) {
                try {
                    // 监控系统杀进程行为
                    monitorKillEvents()
                    
                    // 每3秒检查一次
                    Thread.sleep(3000)
                    
                } catch (e: InterruptedException) {
                    Log.d(TAG, "反杀机制线程被中断")
                    break
                } catch (e: Exception) {
                    Log.e(TAG, "反杀机制异常", e)
                }
            }
            
            Log.d(TAG, "🛑 反杀机制线程结束")
        }.start()
    }
    
    // 辅助方法
    private fun createInterferenceData(size: Int): ByteArray {
        val data = ByteArray(size)
        // 填充随机数据
        for (i in data.indices) {
            data[i] = (Math.random() * 256).toInt().toByte()
        }
        return data
    }
    
    private fun createResourceExhaustion(processName: String) {
        try {
            // 创建大量临时文件消耗系统资源
            val tempDir = File(context.cacheDir, "interference")
            tempDir.mkdirs()
            
            for (i in 1..100) {
                val tempFile = File(tempDir, "temp_$i.dat")
                tempFile.writeBytes(ByteArray(1024)) // 1KB文件
            }
            
            Log.d(TAG, "🎯 [$processName] 创建资源消耗")
            
        } catch (e: Exception) {
            // 资源消耗创建失败
        }
    }
    
    private fun isMainProcessAlive(): Boolean {
        return try {
            val myPid = Process.myPid()
            val procFile = File("/proc/$myPid/stat")
            procFile.exists()
        } catch (e: Exception) {
            false
        }
    }
    
    private fun restartMainProcess() {
        try {
            val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
            intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            Log.i(TAG, "🔄 尝试重启主进程")
        } catch (e: Exception) {
            Log.e(TAG, "重启主进程失败", e)
        }
    }
    
    private fun checkGuardProcesses() {
        processGuards.forEach { guard ->
            if (!guard.isAlive()) {
                Log.w(TAG, "⚠️ 守护进程 #${guard.id} 死亡，重启中...")
                guard.restart()
            }
        }
    }
    
    private fun monitorKillEvents() {
        try {
            // 监控系统日志中的杀进程事件
            val logcat = Runtime.getRuntime().exec("logcat -d -s SafeCenter:* AES:* system_server:*")
            val output = logcat.inputStream.bufferedReader().readText()
            
            if (output.contains(context.packageName)) {
                Log.w(TAG, "⚠️ 检测到针对本应用的杀进程事件")
                // 触发反制措施
                triggerCounterMeasures()
            }
            
        } catch (e: Exception) {
            // 日志监控失败
        }
    }
    
    private fun triggerCounterMeasures() {
        Log.i(TAG, "⚔️ 触发反制措施")
        
        // 立即重新检测和干扰SafeCenter
        detectSafeCenter()
        
        // 增强进程保护
        enhanceProcessProtection()
    }
    
    private fun enhanceProcessProtection() {
        try {
            // 创建更多守护进程
            val additionalGuard = ProcessGuard(context, processGuards.size + 1)
            additionalGuard.start()
            processGuards.add(additionalGuard)
            
            Log.d(TAG, "✅ 增强进程保护，新增守护进程")
            
        } catch (e: Exception) {
            Log.e(TAG, "增强进程保护失败", e)
        }
    }
}

/**
 * 进程守护器
 */
class ProcessGuard(private val context: Context, val id: Int) {
    private val isRunning = AtomicBoolean(false)
    
    fun start() {
        if (isRunning.compareAndSet(false, true)) {
            Thread {
                guardLoop()
            }.start()
        }
    }
    
    fun stop() {
        isRunning.set(false)
    }
    
    fun restart() {
        stop()
        Thread.sleep(1000)
        start()
    }
    
    fun isAlive(): Boolean {
        return isRunning.get()
    }
    
    private fun guardLoop() {
        Log.d("ProcessGuard", "🛡️ 守护进程 #$id 启动")
        
        while (isRunning.get()) {
            try {
                // 守护主进程
                guardMainProcess()
                
                // 每2秒检查一次
                Thread.sleep(2000)
                
            } catch (e: InterruptedException) {
                Log.d("ProcessGuard", "守护进程 #$id 被中断")
                break
            } catch (e: Exception) {
                Log.e("ProcessGuard", "守护进程 #$id 异常", e)
            }
        }
        
        Log.d("ProcessGuard", "🛑 守护进程 #$id 结束")
    }
    
    private fun guardMainProcess() {
        // 检查主进程是否存在
        val myPid = Process.myPid()
        val procFile = File("/proc/$myPid")
        
        if (!procFile.exists()) {
            Log.w("ProcessGuard", "⚠️ 守护进程 #$id 检测到主进程消失")
            // 尝试重启
            restartApplication()
        }
    }
    
    private fun restartApplication() {
        try {
            val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
            intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            Log.i("ProcessGuard", "🔄 守护进程 #$id 重启应用")
        } catch (e: Exception) {
            Log.e("ProcessGuard", "守护进程 #$id 重启应用失败", e)
        }
    }
}
