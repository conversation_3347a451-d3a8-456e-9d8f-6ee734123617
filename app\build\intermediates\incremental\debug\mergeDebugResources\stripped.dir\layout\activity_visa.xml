<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/visa_background"
    tools:context=".VisaActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/visa_blue"
        app:layout_constraintTop_toTopOf="parent"
        app:title="Visa 服务"
        app:titleTextColor="@color/white" />

    <ImageView
        android:id="@+id/logo"
        android:layout_width="180dp"
        android:layout_height="100dp"
        android:layout_marginTop="24dp"
        android:contentDescription="Visa Logo"
        android:src="@drawable/visa2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <TextView
        android:id="@+id/welcome_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:gravity="center"
        android:text="欢迎使用Visa服务"
        android:textColor="@color/visa_text_primary"
        android:textSize="22sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/logo" />

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="16dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/welcome_text">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="账户信息"
                android:textColor="@color/visa_blue"
                android:textSize="18sp"
                android:textStyle="bold" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:background="@color/visa_border" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="持卡人姓名"
                android:textColor="@color/visa_text_secondary"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/card_holder_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="王小明"
                android:textColor="@color/visa_text_primary"
                android:textSize="16sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="卡号"
                android:textColor="@color/visa_text_secondary"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/card_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="**** **** **** 1234"
                android:textColor="@color/visa_text_primary"
                android:textSize="16sp" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_services"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="16dp"
        android:backgroundTint="@color/visa_blue"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:text="浏览服务"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_info" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_transactions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:backgroundTint="@color/visa_yellow"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:text="我的交易"
        android:textColor="@color/visa_blue"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/button_services" />

    <TextView
        android:id="@+id/text_service_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="16dp"
        android:gravity="center"
        android:text="所有服务正常运行中"
        android:textColor="@color/visa_success"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/button_transactions" />

</androidx.constraintlayout.widget.ConstraintLayout> 