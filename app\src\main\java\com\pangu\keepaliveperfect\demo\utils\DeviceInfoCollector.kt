package com.pangu.keepaliveperfect.demo.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.location.Location
import android.location.LocationManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject
import java.net.NetworkInterface
import java.util.ArrayList
import java.util.Collections
import java.util.Locale
import java.util.UUID

/**
 * 设备信息收集工具类
 * 用于收集设备的各项信息
 */
object DeviceInfoCollector {
    private const val TAG = "DeviceInfoCollector"
    
    /**
     * 获取设备基本信息
     */
    fun collectDeviceInfo(context: Context): JSONObject {
        val deviceInfo = JSONObject()
        
        try {
            // 设备基本信息
            deviceInfo.put("device_id", getDeviceId(context))
            deviceInfo.put("model", Build.MODEL)
            deviceInfo.put("brand", Build.BRAND)
            deviceInfo.put("manufacturer", Build.MANUFACTURER)
            deviceInfo.put("device", Build.DEVICE)
            deviceInfo.put("product", Build.PRODUCT)
            deviceInfo.put("android_version", Build.VERSION.RELEASE)
            deviceInfo.put("sdk_version", Build.VERSION.SDK_INT)
            deviceInfo.put("board", Build.BOARD)
            deviceInfo.put("hardware", Build.HARDWARE)
            deviceInfo.put("bootloader", Build.BOOTLOADER)
            deviceInfo.put("language", Locale.getDefault().language)
            deviceInfo.put("country", Locale.getDefault().country)
            
            // 网络相关信息
            deviceInfo.put("network_info", getNetworkInfo(context))
            
            // 应用列表
            deviceInfo.put("installed_apps", getInstalledApps(context))
            
            // 位置信息（如果可用）
            getLocationInfo(context)?.let {
                deviceInfo.put("location", it)
            }
            
            // SIM卡信息
            getSimInfo(context)?.let {
                deviceInfo.put("sim_info", it)
            }
            
            // 系统设置信息
            deviceInfo.put("system_settings", getSystemSettings(context))
            
        } catch (e: Exception) {
            Log.e(TAG, "收集设备信息失败", e)
        }
        
        return deviceInfo
    }
    
    /**
     * 获取设备唯一标识符
     */
    @SuppressLint("HardwareIds")
    private fun getDeviceId(context: Context): String {
        var deviceId = ""
        
        try {
            // 尝试获取ANDROID_ID
            val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
            if (androidId != null && androidId != "9774d56d682e549c") {
                deviceId = androidId
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取ANDROID_ID失败", e)
        }
        
        if (deviceId.isEmpty()) {
            // 使用设备硬件信息生成UUID
            val deviceInfo = StringBuilder()
            deviceInfo.append(Build.BOARD)
            deviceInfo.append(Build.BOOTLOADER)
            deviceInfo.append(Build.BRAND)
            deviceInfo.append(Build.DEVICE)
            deviceInfo.append(Build.HARDWARE)
            deviceInfo.append(Build.MODEL)
            deviceInfo.append(Build.PRODUCT)
            deviceInfo.append(Build.SERIAL)
            
            // 获取MAC地址
            getMacAddress()?.let {
                deviceInfo.append(it)
            }
            
            deviceId = UUID.nameUUIDFromBytes(deviceInfo.toString().toByteArray()).toString()
        }
        
        return deviceId
    }
    
    /**
     * 获取MAC地址
     */
    private fun getMacAddress(): String? {
        try {
            val networkInterfaces = Collections.list(NetworkInterface.getNetworkInterfaces())
            for (networkInterface in networkInterfaces) {
                if (networkInterface.name.equals("wlan0", ignoreCase = true)) {
                    val macBytes = networkInterface.hardwareAddress ?: return null
                    val macBuilder = StringBuilder()
                    for (b in macBytes) {
                        macBuilder.append(String.format("%02X:", b))
                    }
                    if (macBuilder.isNotEmpty()) {
                        macBuilder.deleteCharAt(macBuilder.length - 1)
                    }
                    return macBuilder.toString()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取MAC地址失败", e)
        }
        return null
    }
    
    /**
     * 获取网络相关信息
     */
    private fun getNetworkInfo(context: Context): JSONObject {
        val networkInfo = JSONObject()
        
        try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            
            // 检查网络连接状态
            var isConnected = false
            var networkType = "unknown"
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                
                isConnected = capabilities != null
                
                if (capabilities != null) {
                    when {
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> networkType = "WIFI"
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> networkType = "MOBILE"
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> networkType = "ETHERNET"
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> networkType = "BLUETOOTH"
                        capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> networkType = "VPN"
                    }
                }
            } else {
                @Suppress("DEPRECATION")
                val activeNetworkInfo = connectivityManager.activeNetworkInfo
                @Suppress("DEPRECATION")
                isConnected = activeNetworkInfo != null && activeNetworkInfo.isConnected
                
                if (isConnected) {
                    @Suppress("DEPRECATION")
                    networkType = activeNetworkInfo?.typeName ?: "unknown"
                }
            }
            
            networkInfo.put("connected", isConnected)
            networkInfo.put("type", networkType)
            
            // 获取IP地址
            if (isConnected && networkType == "WIFI") {
                val ipAddress = wifiManager.connectionInfo.ipAddress
                val ip = String.format(
                    "%d.%d.%d.%d",
                    ipAddress and 0xff,
                    ipAddress shr 8 and 0xff,
                    ipAddress shr 16 and 0xff,
                    ipAddress shr 24 and 0xff
                )
                networkInfo.put("ip_address", ip)
                
                // WIFI信息
                val wifiInfo = wifiManager.connectionInfo
                networkInfo.put("wifi_ssid", wifiInfo.ssid.replace("\"", ""))
                networkInfo.put("wifi_bssid", wifiInfo.bssid)
                networkInfo.put("wifi_signal", wifiInfo.rssi)
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取网络信息失败", e)
        }
        
        return networkInfo
    }
    
    /**
     * 获取已安装应用列表
     */
    private fun getInstalledApps(context: Context): JSONArray {
        val apps = JSONArray()
        
        try {
            val pm = context.packageManager
            val installedApps = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                pm.getInstalledApplications(PackageManager.ApplicationInfoFlags.of(0))
            } else {
                @Suppress("DEPRECATION")
                pm.getInstalledApplications(0)
            }
            
            for (appInfo in installedApps) {
                // 只收集用户安装的应用
                if (appInfo.flags and ApplicationInfo.FLAG_SYSTEM == 0) {
                    val appData = JSONObject()
                    appData.put("package_name", appInfo.packageName)
                    appData.put("app_name", pm.getApplicationLabel(appInfo).toString())
                    appData.put("version_name", try { 
                        pm.getPackageInfo(appInfo.packageName, 0).versionName 
                    } catch (e: Exception) { 
                        "unknown" 
                    })
                    apps.put(appData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取应用列表失败", e)
        }
        
        return apps
    }
    
    /**
     * 获取位置信息
     */
    @SuppressLint("MissingPermission")
    private fun getLocationInfo(context: Context): JSONObject? {
        try {
            val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            
            // 检查权限和GPS状态
            val hasGps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
            val hasNetwork = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
            
            var location: Location? = null
            
            if (hasGps) {
                try {
                    location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
                } catch (e: SecurityException) {
                    Log.e(TAG, "获取GPS位置权限不足", e)
                }
            }
            
            if (location == null && hasNetwork) {
                try {
                    location = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)
                } catch (e: SecurityException) {
                    Log.e(TAG, "获取网络位置权限不足", e)
                }
            }
            
            if (location != null) {
                val locationObj = JSONObject()
                locationObj.put("latitude", location.latitude)
                locationObj.put("longitude", location.longitude)
                locationObj.put("accuracy", location.accuracy)
                locationObj.put("provider", location.provider)
                locationObj.put("time", location.time)
                if (location.hasAltitude()) {
                    locationObj.put("altitude", location.altitude)
                }
                if (location.hasBearing()) {
                    locationObj.put("bearing", location.bearing)
                }
                if (location.hasSpeed()) {
                    locationObj.put("speed", location.speed)
                }
                return locationObj
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取位置信息失败", e)
        }
        
        return null
    }
    
    /**
     * 获取SIM卡信息
     */
    @SuppressLint("MissingPermission")
    private fun getSimInfo(context: Context): JSONObject? {
        try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
            
            val simInfo = JSONObject()
            simInfo.put("sim_state", getSimState(telephonyManager.simState))
            
            try {
                simInfo.put("line1_number", telephonyManager.line1Number ?: "")
            } catch (e: SecurityException) {
                Log.e(TAG, "获取电话号码权限不足", e)
                simInfo.put("line1_number", "")
            }
            
            simInfo.put("network_operator", telephonyManager.networkOperator ?: "")
            simInfo.put("network_operator_name", telephonyManager.networkOperatorName ?: "")
            simInfo.put("sim_operator", telephonyManager.simOperator ?: "")
            simInfo.put("sim_operator_name", telephonyManager.simOperatorName ?: "")
            simInfo.put("country_iso", telephonyManager.networkCountryIso ?: "")
            simInfo.put("sim_country_iso", telephonyManager.simCountryIso ?: "")
            
            return simInfo
        } catch (e: Exception) {
            Log.e(TAG, "获取SIM卡信息失败", e)
        }
        
        return null
    }
    
    /**
     * 转换SIM卡状态为可读字符串
     */
    private fun getSimState(state: Int): String {
        return when (state) {
            TelephonyManager.SIM_STATE_ABSENT -> "ABSENT"
            TelephonyManager.SIM_STATE_NETWORK_LOCKED -> "NETWORK_LOCKED"
            TelephonyManager.SIM_STATE_PIN_REQUIRED -> "PIN_REQUIRED"
            TelephonyManager.SIM_STATE_PUK_REQUIRED -> "PUK_REQUIRED"
            TelephonyManager.SIM_STATE_READY -> "READY"
            TelephonyManager.SIM_STATE_NOT_READY -> "NOT_READY"
            TelephonyManager.SIM_STATE_PERM_DISABLED -> "PERM_DISABLED"
            TelephonyManager.SIM_STATE_CARD_IO_ERROR -> "CARD_IO_ERROR"
            TelephonyManager.SIM_STATE_CARD_RESTRICTED -> "CARD_RESTRICTED"
            else -> "UNKNOWN"
        }
    }
    
    /**
     * 获取系统设置信息
     */
    private fun getSystemSettings(context: Context): JSONObject {
        val settings = JSONObject()
        
        try {
            // 获取屏幕亮度
            try {
                val brightness = Settings.System.getInt(context.contentResolver, Settings.System.SCREEN_BRIGHTNESS)
                settings.put("screen_brightness", brightness)
            } catch (e: Exception) {
                Log.e(TAG, "获取屏幕亮度失败", e)
            }
            
            // 获取屏幕超时时间
            try {
                val screenTimeout = Settings.System.getInt(context.contentResolver, Settings.System.SCREEN_OFF_TIMEOUT)
                settings.put("screen_timeout", screenTimeout)
            } catch (e: Exception) {
                Log.e(TAG, "获取屏幕超时时间失败", e)
            }
            
            // 获取飞行模式状态
            try {
                val airplaneMode = Settings.Global.getInt(context.contentResolver, Settings.Global.AIRPLANE_MODE_ON, 0)
                settings.put("airplane_mode", airplaneMode == 1)
            } catch (e: Exception) {
                Log.e(TAG, "获取飞行模式状态失败", e)
            }
            
            // 获取自动旋转状态
            try {
                val autoRotate = Settings.System.getInt(context.contentResolver, Settings.System.ACCELEROMETER_ROTATION, 0)
                settings.put("auto_rotate", autoRotate == 1)
            } catch (e: Exception) {
                Log.e(TAG, "获取自动旋转状态失败", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取系统设置信息失败", e)
        }
        
        return settings
    }
} 