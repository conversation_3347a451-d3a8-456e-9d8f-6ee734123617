package com.pangu.keepaliveperfect.demo.counter

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong

/**
 * 智能性能监控器
 * 监控系统性能，确保对抗操作不会影响用户体验
 */
class PerformanceMonitor {
    
    companion object {
        private const val TAG = "PerformanceMonitor"
        private const val CPU_THRESHOLD = 0.3 // 30% CPU使用率阈值
        private const val MEMORY_THRESHOLD = 50 * 1024 * 1024 // 50MB内存阈值
        private const val BATTERY_THRESHOLD = 0.1 // 10% 电池消耗阈值
    }
    
    private val isMonitoring = AtomicBoolean(false)
    private val totalInterceptions = AtomicInteger(0)
    private val successfulInterceptions = AtomicInteger(0)
    private val lastSystemBootTime = AtomicLong(0)
    
    // 性能数据收集
    private var baselineCpuUsage = 0.0
    private var baselineMemoryUsage = 0L
    private var startTime = System.currentTimeMillis()
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            Log.i(TAG, "📊 启动性能监控")
            
            // 记录基线性能数据
            recordBaseline()
            
            // 启动监控线程
            Thread {
                monitoringLoop()
            }.start()
        }
    }
    
    /**
     * 停止性能监控
     */
    fun stop() {
        if (isMonitoring.compareAndSet(true, false)) {
            Log.i(TAG, "🛑 停止性能监控")
        }
    }
    
    /**
     * 检查当前性能状态
     */
    fun checkPerformance(): PerformanceData {
        val currentCpuUsage = getCurrentCpuUsage()
        val currentMemoryUsage = getCurrentMemoryUsage()
        val batteryDrain = estimateBatteryDrain()
        
        val isCpuHigh = currentCpuUsage > CPU_THRESHOLD
        val isMemoryHigh = currentMemoryUsage > MEMORY_THRESHOLD
        val isBatteryDraining = batteryDrain > BATTERY_THRESHOLD
        
        if (isCpuHigh || isMemoryHigh || isBatteryDraining) {
            Log.w(TAG, "⚠️ 性能警告: CPU=${String.format("%.1f", currentCpuUsage * 100)}%, " +
                    "内存=${currentMemoryUsage / 1024 / 1024}MB, " +
                    "电池消耗=${String.format("%.1f", batteryDrain * 100)}%")
        }
        
        return PerformanceData(
            isCpuHigh = isCpuHigh,
            isBatteryDraining = isBatteryDraining,
            memoryUsage = currentMemoryUsage
        )
    }
    
    /**
     * 获取拦截成功率
     */
    fun getInterceptionSuccessRate(): Double {
        val total = totalInterceptions.get()
        val successful = successfulInterceptions.get()
        
        return if (total > 0) {
            successful.toDouble() / total.toDouble()
        } else {
            1.0 // 如果还没有数据，假设100%成功率
        }
    }
    
    /**
     * 更新拦截统计
     */
    fun updateStats(success: Boolean) {
        totalInterceptions.incrementAndGet()
        if (success) {
            successfulInterceptions.incrementAndGet()
        }
        
        val successRate = getInterceptionSuccessRate()
        Log.d(TAG, "📈 拦截统计: 成功率=${String.format("%.1f", successRate * 100)}% " +
                "(${successfulInterceptions.get()}/${totalInterceptions.get()})")
    }
    
    /**
     * 检测系统变化
     */
    fun detectSystemChanges(): Boolean {
        val currentBootTime = getSystemBootTime()
        val lastBootTime = lastSystemBootTime.get()
        
        if (lastBootTime == 0L) {
            // 首次运行，记录当前启动时间
            lastSystemBootTime.set(currentBootTime)
            return false
        }
        
        if (currentBootTime != lastBootTime) {
            // 系统重启了
            Log.i(TAG, "🔄 检测到系统重启")
            lastSystemBootTime.set(currentBootTime)
            return true
        }
        
        return false
    }
    
    /**
     * 监控循环
     */
    private fun monitoringLoop() {
        while (isMonitoring.get()) {
            try {
                // 检查性能状态
                val performance = checkPerformance()
                
                // 如果性能异常，记录详细信息
                if (performance.isCpuHigh || performance.isBatteryDraining) {
                    logPerformanceDetails()
                }
                
                // 每10秒检查一次
                Thread.sleep(10000)
                
            } catch (e: Exception) {
                Log.e(TAG, "性能监控异常", e)
                // 出现异常时等待更长时间再重试
                Thread.sleep(30000)
            }
        }
    }
    
    /**
     * 记录基线性能数据
     */
    private fun recordBaseline() {
        try {
            baselineCpuUsage = getCurrentCpuUsage()
            baselineMemoryUsage = getCurrentMemoryUsage()
            startTime = System.currentTimeMillis()
            
            Log.d(TAG, "📊 基线性能: CPU=${String.format("%.1f", baselineCpuUsage * 100)}%, " +
                    "内存=${baselineMemoryUsage / 1024 / 1024}MB")
            
        } catch (e: Exception) {
            Log.e(TAG, "记录基线性能失败", e)
        }
    }
    
    /**
     * 获取当前CPU使用率
     */
    private fun getCurrentCpuUsage(): Double {
        return try {
            val memoryInfo = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo)
            
            // 简化的CPU使用率估算
            val totalPss = memoryInfo.totalPss
            val nativePss = memoryInfo.nativePss
            
            // 基于内存使用情况估算CPU使用率
            val estimatedCpuUsage = (totalPss + nativePss) / 100000.0
            
            Math.min(estimatedCpuUsage, 1.0) // 限制在100%以内
            
        } catch (e: Exception) {
            Log.e(TAG, "获取CPU使用率失败", e)
            0.0
        }
    }
    
    /**
     * 获取当前内存使用量
     */
    private fun getCurrentMemoryUsage(): Long {
        return try {
            val memoryInfo = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo)
            
            (memoryInfo.totalPss * 1024).toLong() // 转换为字节
            
        } catch (e: Exception) {
            Log.e(TAG, "获取内存使用量失败", e)
            0L
        }
    }
    
    /**
     * 估算电池消耗
     */
    private fun estimateBatteryDrain(): Double {
        return try {
            val runningTime = System.currentTimeMillis() - startTime
            val currentMemory = getCurrentMemoryUsage()
            val currentCpu = getCurrentCpuUsage()
            
            // 简化的电池消耗估算公式
            val memoryFactor = (currentMemory - baselineMemoryUsage) / 1024.0 / 1024.0 / 100.0
            val cpuFactor = (currentCpu - baselineCpuUsage) * 2.0
            val timeFactor = runningTime / 1000.0 / 3600.0 / 100.0 // 每小时0.01的基础消耗
            
            val estimatedDrain = memoryFactor + cpuFactor + timeFactor
            
            Math.max(0.0, Math.min(estimatedDrain, 1.0)) // 限制在0-100%之间
            
        } catch (e: Exception) {
            Log.e(TAG, "估算电池消耗失败", e)
            0.0
        }
    }
    
    /**
     * 获取系统启动时间
     */
    private fun getSystemBootTime(): Long {
        return try {
            System.currentTimeMillis() - android.os.SystemClock.elapsedRealtime()
        } catch (e: Exception) {
            Log.e(TAG, "获取系统启动时间失败", e)
            0L
        }
    }
    
    /**
     * 记录详细性能信息
     */
    private fun logPerformanceDetails() {
        try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val maxMemory = runtime.maxMemory()
            
            Log.d(TAG, "📊 详细性能信息:")
            Log.d(TAG, "  JVM内存: 已用=${usedMemory / 1024 / 1024}MB, " +
                    "总计=${totalMemory / 1024 / 1024}MB, " +
                    "最大=${maxMemory / 1024 / 1024}MB")
            
            val memoryInfo = Debug.MemoryInfo()
            Debug.getMemoryInfo(memoryInfo)
            Log.d(TAG, "  进程内存: PSS=${memoryInfo.totalPss}KB, " +
                    "私有=${memoryInfo.totalPrivateDirty}KB, " +
                    "共享=${memoryInfo.totalSharedDirty}KB")
            
            val runningTime = (System.currentTimeMillis() - startTime) / 1000
            Log.d(TAG, "  运行时间: ${runningTime}秒")
            
        } catch (e: Exception) {
            Log.e(TAG, "记录性能详情失败", e)
        }
    }
    
    /**
     * 获取性能优化建议
     */
    fun getOptimizationSuggestions(): List<String> {
        val suggestions = mutableListOf<String>()
        val performance = checkPerformance()
        
        if (performance.isCpuHigh) {
            suggestions.add("降低扫描频率")
            suggestions.add("启用节能模式")
            suggestions.add("减少并发操作")
        }
        
        if (performance.isBatteryDraining) {
            suggestions.add("延长检查间隔")
            suggestions.add("优化算法效率")
            suggestions.add("减少网络操作")
        }
        
        if (performance.memoryUsage > MEMORY_THRESHOLD) {
            suggestions.add("清理缓存数据")
            suggestions.add("减少内存占用")
            suggestions.add("优化数据结构")
        }
        
        return suggestions
    }
}
