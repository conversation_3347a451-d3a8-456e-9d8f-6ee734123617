<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <ImageView
        android:id="@+id/ivBack"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="16dp"
        android:contentDescription="Back"
        android:padding="12dp"
        android:src="@drawable/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/text_primary" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="注册账号"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="180dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:contentDescription="VISA Logo"
        android:scaleType="fitCenter"
        android:src="@drawable/visa2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="80dp"
        android:fillViewport="true"
        android:isScrollContainer="true"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@+id/btnNext"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivLogo">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 步骤指示器 -->
            <LinearLayout
                android:id="@+id/llStepIndicator"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvStep1"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/circle_primary"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="40dp"
                    android:layout_height="2dp"
                    android:background="@color/divider_color" />

                <TextView
                    android:id="@+id/tvStep2"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/circle_outline"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="40dp"
                    android:layout_height="2dp"
                    android:background="@color/divider_color" />

                <TextView
                    android:id="@+id/tvStep3"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/circle_outline"
                    android:gravity="center"
                    android:text="3"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="40dp"
                    android:layout_height="2dp"
                    android:background="@color/divider_color" />

                <TextView
                    android:id="@+id/tvStep4"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:background="@drawable/circle_outline"
                    android:gravity="center"
                    android:text="4"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <!-- 步骤标题 -->
            <TextView
                android:id="@+id/tvStepTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="基本信息"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/llStepIndicator" />

            <!-- 步骤1：基本信息 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clStep1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStepTitle">

                <!-- 手机号输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPhoneNumber"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="手机号码（将作为账号）"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPhoneNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone"
                        android:maxLength="11" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPassword"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="登录密码（数字+英文，至少6位）"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:endIconMode="password_toggle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilPhoneNumber">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 确认密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilConfirmPassword"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="确认登录密码"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:endIconMode="password_toggle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilPassword">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etConfirmPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码强度提示 -->
                <TextView
                    android:id="@+id/tvPasswordStrength"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="密码强度：弱"
                    android:textColor="@color/error_red"
                    android:textSize="12sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilConfirmPassword" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 步骤2：支付密码 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clStep2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStepTitle">

                <!-- 支付密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPaymentPassword"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="支付密码（6位数字）"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:endIconMode="password_toggle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPaymentPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberPassword"
                        android:maxLength="6" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 确认支付密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilConfirmPaymentPassword"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="确认支付密码"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:endIconMode="password_toggle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilPaymentPassword">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etConfirmPaymentPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberPassword"
                        android:maxLength="6" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 支付密码提示 -->
                <TextView
                    android:id="@+id/tvPaymentPasswordHint"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="80dp"
                    android:text="支付密码用于交易确认，请勿使用简单数字如123456"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilConfirmPaymentPassword" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 步骤3：交易密码 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clStep3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStepTitle">

                <!-- 交易密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilTransactionPassword"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="交易密码（6位数字，不能与支付密码相同）"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:endIconMode="password_toggle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etTransactionPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberPassword"
                        android:maxLength="6" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 确认交易密码输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilConfirmTransactionPassword"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="确认交易密码"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:endIconMode="password_toggle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilTransactionPassword">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etConfirmTransactionPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberPassword"
                        android:maxLength="6" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 交易密码提示 -->
                <TextView
                    android:id="@+id/tvTransactionPasswordHint"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="80dp"
                    android:text="交易密码用于大额交易确认，请与支付密码设置不同"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilConfirmTransactionPassword" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 步骤4：身份信息 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clStep4"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="24dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvStepTitle">

                <!-- 真实姓名输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilRealName"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="真实姓名"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etRealName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPersonName" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 身份证号输入 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilIdNumber"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="身份证号码"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilRealName">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etIdNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textCapCharacters"
                        android:digits="0123456789Xx"
                        android:maxLength="18" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 人脸识别提示 -->
                <TextView
                    android:id="@+id/tvFaceRecognitionHint"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="80dp"
                    android:text="填写完整信息后，点击下方&quot;完成注册&quot;按钮进行人脸识别"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tilIdNumber" />
            </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <!-- 下一步按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnNext"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="16dp"
        android:backgroundTint="@color/visa_blue"
        android:text="下一步"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:cornerRadius="28dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />



</androidx.constraintlayout.widget.ConstraintLayout>
