package com.pangu.keepaliveperfect.demo.visa.model

/**
 * 交易数据模型
 */
data class Transaction(
    val merchantName: String,  // 商户名称/描述
    val amount: Float,         // 交易金额（正数为收入，负数为支出）
    val time: String,          // 交易时间
    val type: String = "",     // 交易类型：transfer, payment, bill
    val status: String = "",   // 交易状态：success, failed, pending
    val details: Map<String, String> = emptyMap() // 交易详情
)