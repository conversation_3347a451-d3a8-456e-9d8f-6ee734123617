{"logs": [{"outputFile": "com.pangu.keepaliveperfect.demo.app-mergeDebugResources-47:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\376ccaceccb8c8d200dd37c294afb78a\\transformed\\preference-1.2.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,262,341,488,657,737", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "172,257,336,483,652,732,810"}, "to": {"startLines": "59,61,114,116,119,120,121", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5968,6104,10131,10287,10615,10784,10864", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "6035,6184,10205,10429,10779,10859,10937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38bdbb3aec41791523ad0d9573b07666\\transformed\\core-1.9.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "118", "startColumns": "4", "startOffsets": "10514", "endColumns": "100", "endOffsets": "10610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e0e92451cb7aee5ff8934f376f578f88\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,10434", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,10509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ae5003da7b262492451d61e3e75a684\\transformed\\jetified-play-services-basement-18.0.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4714", "endColumns": "150", "endOffsets": "4860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\79230ecb7deb426f90957f200c266d44\\transformed\\jetified-play-services-base-18.0.1\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3703,3810,3983,4113,4222,4369,4498,4611,4865,5027,5136,5309,5441,5594,5755,5820,5886", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "3805,3978,4108,4217,4364,4493,4606,4709,5022,5131,5304,5436,5589,5750,5815,5881,5963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fdd3b69b66e6b65f494e6d96e162c073\\transformed\\material-1.9.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1055,1147,1216,1275,1360,1423,1485,1543,1607,1668,1722,1836,1894,1954,2008,2078,2205,2286,2365,2500,2576,2653,2737,2792,2847,2913,2982,3059,3145,3213,3289,3359,3424,3519,3592,3686,3779,3853,3922,4016,4072,4139,4223,4311,4373,4437,4500,4597,4692,4783,4879,4938,4997", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,83,54,54,65,68,76,85,67,75,69,64,94,72,93,92,73,68,93,55,66,83,87,61,63,62,96,94,90,95,58,58,76", "endOffsets": "258,335,414,495,594,683,791,903,986,1050,1142,1211,1270,1355,1418,1480,1538,1602,1663,1717,1831,1889,1949,2003,2073,2200,2281,2360,2495,2571,2648,2732,2787,2842,2908,2977,3054,3140,3208,3284,3354,3419,3514,3587,3681,3774,3848,3917,4011,4067,4134,4218,4306,4368,4432,4495,4592,4687,4778,4874,4933,4992,5069"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2975,3052,3131,3212,3311,3400,3508,3620,6040,6189,6281,6350,6409,6494,6557,6619,6677,6741,6802,6856,6970,7028,7088,7142,7212,7339,7420,7499,7634,7710,7787,7871,7926,7981,8047,8116,8193,8279,8347,8423,8493,8558,8653,8726,8820,8913,8987,9056,9150,9206,9273,9357,9445,9507,9571,9634,9731,9826,9917,10013,10072,10210", "endLines": "5,33,34,35,36,37,38,39,40,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,83,54,54,65,68,76,85,67,75,69,64,94,72,93,92,73,68,93,55,66,83,87,61,63,62,96,94,90,95,58,58,76", "endOffsets": "308,3047,3126,3207,3306,3395,3503,3615,3698,6099,6276,6345,6404,6489,6552,6614,6672,6736,6797,6851,6965,7023,7083,7137,7207,7334,7415,7494,7629,7705,7782,7866,7921,7976,8042,8111,8188,8274,8342,8418,8488,8553,8648,8721,8815,8908,8982,9051,9145,9201,9268,9352,9440,9502,9566,9629,9726,9821,9912,10008,10067,10126,10282"}}]}]}