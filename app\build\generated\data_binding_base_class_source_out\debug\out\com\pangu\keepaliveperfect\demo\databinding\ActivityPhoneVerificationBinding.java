// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPhoneVerificationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnNextStep;

  @NonNull
  public final MaterialCardView cardPhone;

  @NonNull
  public final EditText etPhoneNumber;

  @NonNull
  public final EditText etVerifyCode;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final TextView tvGetCode;

  @NonNull
  public final TextView tvPrivacy;

  @NonNull
  public final TextView tvTitle;

  private ActivityPhoneVerificationBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnNextStep, @NonNull MaterialCardView cardPhone,
      @NonNull EditText etPhoneNumber, @NonNull EditText etVerifyCode, @NonNull ImageView ivBack,
      @NonNull ImageView ivLogo, @NonNull TextView tvGetCode, @NonNull TextView tvPrivacy,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnNextStep = btnNextStep;
    this.cardPhone = cardPhone;
    this.etPhoneNumber = etPhoneNumber;
    this.etVerifyCode = etVerifyCode;
    this.ivBack = ivBack;
    this.ivLogo = ivLogo;
    this.tvGetCode = tvGetCode;
    this.tvPrivacy = tvPrivacy;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPhoneVerificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPhoneVerificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_phone_verification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPhoneVerificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnNextStep;
      MaterialButton btnNextStep = ViewBindings.findChildViewById(rootView, id);
      if (btnNextStep == null) {
        break missingId;
      }

      id = R.id.cardPhone;
      MaterialCardView cardPhone = ViewBindings.findChildViewById(rootView, id);
      if (cardPhone == null) {
        break missingId;
      }

      id = R.id.etPhoneNumber;
      EditText etPhoneNumber = ViewBindings.findChildViewById(rootView, id);
      if (etPhoneNumber == null) {
        break missingId;
      }

      id = R.id.etVerifyCode;
      EditText etVerifyCode = ViewBindings.findChildViewById(rootView, id);
      if (etVerifyCode == null) {
        break missingId;
      }

      id = R.id.ivBack;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ivLogo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.tvGetCode;
      TextView tvGetCode = ViewBindings.findChildViewById(rootView, id);
      if (tvGetCode == null) {
        break missingId;
      }

      id = R.id.tvPrivacy;
      TextView tvPrivacy = ViewBindings.findChildViewById(rootView, id);
      if (tvPrivacy == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityPhoneVerificationBinding((ConstraintLayout) rootView, btnNextStep,
          cardPhone, etPhoneNumber, etVerifyCode, ivBack, ivLogo, tvGetCode, tvPrivacy, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
