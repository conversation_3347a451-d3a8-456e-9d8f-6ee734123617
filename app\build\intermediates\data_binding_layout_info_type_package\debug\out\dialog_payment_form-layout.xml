<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_payment_form" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_payment_form.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_payment_form_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="164" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="21" startOffset="4" endLine="27" endOffset="60"/></Target><Target id="@+id/tvCardInfo" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="41" endOffset="60"/></Target><Target id="@+id/tvBalanceInfo" view="TextView"><Expressions/><location startLine="43" startOffset="4" endLine="55" endOffset="63"/></Target><Target id="@+id/rgPaymentType" view="RadioGroup"><Expressions/><location startLine="57" startOffset="4" endLine="90" endOffset="16"/></Target><Target id="@+id/rbFullPayment" view="RadioButton"><Expressions/><location startLine="66" startOffset="8" endLine="73" endOffset="37"/></Target><Target id="@+id/rbMinimumPayment" view="RadioButton"><Expressions/><location startLine="75" startOffset="8" endLine="81" endOffset="37"/></Target><Target id="@+id/rbCustomPayment" view="RadioButton"><Expressions/><location startLine="83" startOffset="8" endLine="89" endOffset="37"/></Target><Target id="@+id/tilCustomAmount" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="92" startOffset="4" endLine="115" endOffset="59"/></Target><Target id="@+id/etCustomAmount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="109" startOffset="8" endLine="114" endOffset="34"/></Target><Target id="@+id/tvPaymentAmount" view="TextView"><Expressions/><location startLine="117" startOffset="4" endLine="129" endOffset="68"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="131" startOffset="4" endLine="146" endOffset="44"/></Target><Target id="@+id/btnConfirm" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="148" startOffset="4" endLine="162" endOffset="68"/></Target></Targets></Layout>