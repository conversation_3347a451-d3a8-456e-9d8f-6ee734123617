<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_face_recognition" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_face_recognition.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_face_recognition_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="186" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/ivClose" view="ImageView"><Expressions/><location startLine="22" startOffset="4" endLine="31" endOffset="57"/></Target><Target id="@+id/faceFrameContainer" view="FrameLayout"><Expressions/><location startLine="34" startOffset="4" endLine="153" endOffset="17"/></Target><Target id="@+id/previewContainer" view="com.pangu.keepaliveperfect.demo.visa.CircularPreviewContainer"><Expressions/><location startLine="45" startOffset="8" endLine="60" endOffset="71"/></Target><Target id="@+id/previewView" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="54" startOffset="12" endLine="58" endOffset="65"/></Target><Target id="@+id/circularMask" view="View"><Expressions/><location startLine="63" startOffset="8" endLine="67" endOffset="58"/></Target><Target id="@+id/faceDetectionBox" view="View"><Expressions/><location startLine="70" startOffset="8" endLine="75" endOffset="72"/></Target><Target id="@+id/cornerTopLeft" view="View"><Expressions/><location startLine="78" startOffset="8" endLine="85" endOffset="42"/></Target><Target id="@+id/verticalTopLeft" view="View"><Expressions/><location startLine="87" startOffset="8" endLine="94" endOffset="42"/></Target><Target id="@+id/cornerTopRight" view="View"><Expressions/><location startLine="97" startOffset="8" endLine="104" endOffset="42"/></Target><Target id="@+id/verticalTopRight" view="View"><Expressions/><location startLine="106" startOffset="8" endLine="113" endOffset="42"/></Target><Target id="@+id/cornerBottomLeft" view="View"><Expressions/><location startLine="116" startOffset="8" endLine="123" endOffset="42"/></Target><Target id="@+id/verticalBottomLeft" view="View"><Expressions/><location startLine="125" startOffset="8" endLine="132" endOffset="42"/></Target><Target id="@+id/cornerBottomRight" view="View"><Expressions/><location startLine="135" startOffset="8" endLine="142" endOffset="42"/></Target><Target id="@+id/verticalBottomRight" view="View"><Expressions/><location startLine="144" startOffset="8" endLine="151" endOffset="42"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="158" startOffset="4" endLine="168" endOffset="71"/></Target><Target id="@+id/tvTip" view="TextView"><Expressions/><location startLine="171" startOffset="4" endLine="184" endOffset="55"/></Target></Targets></Layout>