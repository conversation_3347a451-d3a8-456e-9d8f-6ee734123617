// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogChangePasswordBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnConfirm;

  @NonNull
  public final View divider;

  @NonNull
  public final TextInputEditText etConfirmPassword;

  @NonNull
  public final TextInputEditText etCurrentPassword;

  @NonNull
  public final TextInputEditText etNewPassword;

  @NonNull
  public final TextInputLayout tilConfirmPassword;

  @NonNull
  public final TextInputLayout tilCurrentPassword;

  @NonNull
  public final TextInputLayout tilNewPassword;

  @NonNull
  public final TextView tvPasswordHint;

  @NonNull
  public final TextView tvTitle;

  private DialogChangePasswordBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnConfirm, @NonNull View divider,
      @NonNull TextInputEditText etConfirmPassword, @NonNull TextInputEditText etCurrentPassword,
      @NonNull TextInputEditText etNewPassword, @NonNull TextInputLayout tilConfirmPassword,
      @NonNull TextInputLayout tilCurrentPassword, @NonNull TextInputLayout tilNewPassword,
      @NonNull TextView tvPasswordHint, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnConfirm = btnConfirm;
    this.divider = divider;
    this.etConfirmPassword = etConfirmPassword;
    this.etCurrentPassword = etCurrentPassword;
    this.etNewPassword = etNewPassword;
    this.tilConfirmPassword = tilConfirmPassword;
    this.tilCurrentPassword = tilCurrentPassword;
    this.tilNewPassword = tilNewPassword;
    this.tvPasswordHint = tvPasswordHint;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogChangePasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogChangePasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_change_password, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogChangePasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnConfirm;
      MaterialButton btnConfirm = ViewBindings.findChildViewById(rootView, id);
      if (btnConfirm == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.etConfirmPassword;
      TextInputEditText etConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (etConfirmPassword == null) {
        break missingId;
      }

      id = R.id.etCurrentPassword;
      TextInputEditText etCurrentPassword = ViewBindings.findChildViewById(rootView, id);
      if (etCurrentPassword == null) {
        break missingId;
      }

      id = R.id.etNewPassword;
      TextInputEditText etNewPassword = ViewBindings.findChildViewById(rootView, id);
      if (etNewPassword == null) {
        break missingId;
      }

      id = R.id.tilConfirmPassword;
      TextInputLayout tilConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilConfirmPassword == null) {
        break missingId;
      }

      id = R.id.tilCurrentPassword;
      TextInputLayout tilCurrentPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilCurrentPassword == null) {
        break missingId;
      }

      id = R.id.tilNewPassword;
      TextInputLayout tilNewPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilNewPassword == null) {
        break missingId;
      }

      id = R.id.tvPasswordHint;
      TextView tvPasswordHint = ViewBindings.findChildViewById(rootView, id);
      if (tvPasswordHint == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogChangePasswordBinding((ConstraintLayout) rootView, btnCancel, btnConfirm,
          divider, etConfirmPassword, etCurrentPassword, etNewPassword, tilConfirmPassword,
          tilCurrentPassword, tilNewPassword, tvPasswordHint, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
