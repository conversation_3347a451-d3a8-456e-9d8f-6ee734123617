package com.pangu.keepaliveperfect.demo

import android.os.Parcel
import android.os.Parcelable

/**
 * 短信数据类，用于存储和传输短信信息
 * 实现Parcelable接口以便在Intent中传递
 * 2025年版本，增强支持跨设备兼容性
 */
data class SmsData(
    val sender: String,           // 发送者号码
    val content: String,          // 短信内容
    val timestamp: Long = System.currentTimeMillis(), // 接收时间戳
    val verificationCode: String = "", // 提取出的验证码
    val source: String = "sms",   // 数据来源：sms-短信, notification-通知栏
    val packageName: String = "", // 应用包名（通知栏数据时有效）
    var isProcessed: Boolean = false,  // 是否已处理
    var isForwarded: Boolean = false,  // 是否已转发
    var forwardAttempts: Int = 0,    // 转发尝试次数
    val serviceCode: String? = null,  // 服务码（如有）
    val subId: Int = -1,          // SIM卡槽ID
    val threadId: Long = -1       // 会话线程ID，用于关联同一个会话的短信
) : Parcelable {
    
    // 兼容性属性，提供对旧代码的支持
    val receivedTime: Long
        get() = timestamp
    
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readLong(),
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readLong()
    )
    
    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(sender)
        parcel.writeString(content)
        parcel.writeLong(timestamp)
        parcel.writeString(verificationCode)
        parcel.writeString(source)
        parcel.writeString(packageName)
        parcel.writeByte(if (isProcessed) 1 else 0)
        parcel.writeByte(if (isForwarded) 1 else 0)
        parcel.writeInt(forwardAttempts)
        parcel.writeString(serviceCode)
        parcel.writeInt(subId)
        parcel.writeLong(threadId)
    }
    
    override fun describeContents(): Int {
        return 0
    }
    
    companion object CREATOR : Parcelable.Creator<SmsData> {
        override fun createFromParcel(parcel: Parcel): SmsData {
            return SmsData(parcel)
        }
        
        override fun newArray(size: Int): Array<SmsData?> {
            return arrayOfNulls(size)
        }
    }
} 