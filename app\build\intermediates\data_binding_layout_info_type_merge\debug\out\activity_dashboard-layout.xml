<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_dashboard" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_dashboard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_dashboard_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="446" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="56" endOffset="53"/></Target><Target id="@+id/tvGreeting" view="TextView"><Expressions/><location startLine="23" startOffset="12" endLine="32" endOffset="59"/></Target><Target id="@+id/tvUserName" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="44" endOffset="68"/></Target><Target id="@+id/ivNotification" view="ImageView"><Expressions/><location startLine="46" startOffset="12" endLine="54" endOffset="41"/></Target><Target id="@+id/llNoticeContainer" view="LinearLayout"><Expressions/><location startLine="69" startOffset="12" endLine="103" endOffset="26"/></Target><Target id="@+id/tvScrollingNotice" view="TextView"><Expressions/><location startLine="91" startOffset="16" endLine="102" endOffset="45"/></Target><Target id="@+id/cardCredit" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="105" startOffset="12" endLine="225" endOffset="47"/></Target><Target id="@+id/ivVisaLogo" view="ImageView"><Expressions/><location startLine="124" startOffset="20" endLine="131" endOffset="67"/></Target><Target id="@+id/tvBankName" view="TextView"><Expressions/><location startLine="133" startOffset="20" endLine="142" endOffset="67"/></Target><Target id="@+id/tvCardType" view="TextView"><Expressions/><location startLine="144" startOffset="20" endLine="153" endOffset="79"/></Target><Target id="@+id/tvCardNumber" view="TextView"><Expressions/><location startLine="155" startOffset="20" endLine="166" endOffset="79"/></Target><Target id="@+id/tvBalance" view="TextView"><Expressions/><location startLine="189" startOffset="28" endLine="197" endOffset="58"/></Target><Target id="@+id/tvCreditLimit" view="TextView"><Expressions/><location startLine="213" startOffset="28" endLine="221" endOffset="58"/></Target><Target id="@+id/cardBillInfo" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="227" startOffset="12" endLine="301" endOffset="47"/></Target><Target id="@+id/tvBillDate" view="TextView"><Expressions/><location startLine="260" startOffset="24" endLine="268" endOffset="54"/></Target><Target id="@+id/tvRepaymentDate" view="TextView"><Expressions/><location startLine="290" startOffset="24" endLine="298" endOffset="54"/></Target><Target id="@+id/tvFunctions" view="TextView"><Expressions/><location startLine="303" startOffset="12" endLine="314" endOffset="73"/></Target><Target id="@+id/llFunctions" view="LinearLayout"><Expressions/><location startLine="316" startOffset="12" endLine="404" endOffset="26"/></Target><Target id="@+id/llTransfer" view="LinearLayout"><Expressions/><location startLine="327" startOffset="16" endLine="351" endOffset="30"/></Target><Target id="@+id/llPayment" view="LinearLayout"><Expressions/><location startLine="353" startOffset="16" endLine="377" endOffset="30"/></Target><Target id="@+id/llBill" view="LinearLayout"><Expressions/><location startLine="379" startOffset="16" endLine="403" endOffset="30"/></Target><Target id="@+id/tvRecentTransactions" view="TextView"><Expressions/><location startLine="406" startOffset="12" endLine="417" endOffset="72"/></Target><Target id="@+id/rvTransactions" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="419" startOffset="12" endLine="430" endOffset="59"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="434" startOffset="4" endLine="442" endOffset="49"/></Target></Targets></Layout>