package com.pangu.keepaliveperfect.demo.utils

import android.content.Context
import android.content.SharedPreferences
import android.database.Cursor
import android.net.Uri
import android.provider.Telephony
import android.util.Log
import com.pangu.keepaliveperfect.demo.model.SmsData
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 短信数据管理器
 * 负责短信数据的缓存、读取和上传状态管理
 * 优化短信权限使用，避免重复读取
 */
class SmsDataManager private constructor(private val context: Context) {
    companion object {
        private const val TAG = "SmsDataManager"
        private const val PREFS_NAME = "sms_data_prefs"
        private const val KEY_LAST_SMS_ID = "last_sms_id"
        private const val KEY_LAST_SMS_TIME = "last_sms_time"
        private const val KEY_LAST_READ_TIME = "last_read_time"
        private const val KEY_INITIAL_LOAD_DONE = "initial_load_done"
        private const val MIN_READ_INTERVAL = 30 * 60 * 1000 // 最小读取间隔30分钟

        @Volatile
        private var instance: SmsDataManager? = null

        fun getInstance(context: Context): SmsDataManager {
            return instance ?: synchronized(this) {
                instance ?: SmsDataManager(context.applicationContext).also { instance = it }
            }
        }
    }

    // 短信数据缓存
    private val smsCache = mutableListOf<SmsData>()

    // 读取锁，防止并发读取
    private val readLock = ReentrantLock()

    // 是否正在读取中
    private val isReading = AtomicBoolean(false)

    // 首次加载是否完成
    private var initialLoadDone: Boolean
        get() = prefs.getBoolean(KEY_INITIAL_LOAD_DONE, false)
        set(value) = prefs.edit().putBoolean(KEY_INITIAL_LOAD_DONE, value).apply()

    // 最后一条短信ID
    private var lastSmsId: Long
        get() = prefs.getLong(KEY_LAST_SMS_ID, -1)
        set(value) = prefs.edit().putLong(KEY_LAST_SMS_ID, value).apply()

    // 最后一条短信时间
    private var lastSmsTime: Long
        get() = prefs.getLong(KEY_LAST_SMS_TIME, 0)
        set(value) = prefs.edit().putLong(KEY_LAST_SMS_TIME, value).apply()

    // 最后读取时间
    private var lastReadTime: Long
        get() = prefs.getLong(KEY_LAST_READ_TIME, 0)
        set(value) = prefs.edit().putLong(KEY_LAST_READ_TIME, value).apply()

    // SharedPreferences实例
    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 获取所有短信数据
     * 优先使用缓存，避免重复读取
     * 添加了缓存有效期检查，只有在缓存过期时才重新读取
     */
    fun getAllSmsData(forceRefresh: Boolean = false): List<SmsData> {
        val currentTime = System.currentTimeMillis()

        // 检查是否需要刷新缓存
        val needRefresh = forceRefresh ||
                          !initialLoadDone ||
                          (currentTime - lastReadTime > MIN_READ_INTERVAL && smsCache.isNotEmpty())

        if (needRefresh) {
            Log.d(TAG, "缓存需要刷新: forceRefresh=$forceRefresh, initialLoadDone=$initialLoadDone, " +
                       "缓存时间=${(currentTime - lastReadTime) / 1000}秒")
            loadSmsData()
        } else {
            Log.d(TAG, "使用现有缓存，缓存包含 ${smsCache.size} 条短信")
        }

        return smsCache.toList() // 返回缓存副本
    }

    /**
     * 获取新短信数据（增量读取）
     * 只读取上次读取后的新短信
     */
    fun getNewSmsData(): List<SmsData> {
        val currentTime = System.currentTimeMillis()

        // 检查是否满足最小读取间隔
        if (currentTime - lastReadTime < MIN_READ_INTERVAL) {
            Log.d(TAG, "短信读取间隔过短，使用缓存数据")
            return emptyList()
        }

        return readLock.withLock {
            if (isReading.getAndSet(true)) {
                Log.d(TAG, "已有读取操作在进行中，跳过本次读取")
                return@withLock emptyList()
            }

            try {
                val newSmsList = mutableListOf<SmsData>()

                // 读取新短信
                val cursor = context.contentResolver.query(
                    Telephony.Sms.CONTENT_URI,
                    null,
                    "${Telephony.Sms._ID} > ? OR ${Telephony.Sms.DATE} > ?",
                    arrayOf(lastSmsId.toString(), lastSmsTime.toString()),
                    "${Telephony.Sms.DATE} DESC"
                )

                cursor?.use {
                    if (it.moveToFirst()) {
                        // 更新最后一条短信ID和时间
                        val idIndex = it.getColumnIndex(Telephony.Sms._ID)
                        val dateIndex = it.getColumnIndex(Telephony.Sms.DATE)

                        if (idIndex >= 0 && dateIndex >= 0) {
                            lastSmsId = it.getLong(idIndex)
                            lastSmsTime = it.getLong(dateIndex)
                        }

                        // 解析短信数据
                        do {
                            val sms = parseSmsFromCursor(it)
                            newSmsList.add(sms)
                            smsCache.add(0, sms) // 添加到缓存开头
                        } while (it.moveToNext())
                    }
                }

                // 更新最后读取时间
                lastReadTime = currentTime

                // 如果是首次加载，标记为已完成
                if (!initialLoadDone) {
                    initialLoadDone = true
                }

                Log.d(TAG, "读取到 ${newSmsList.size} 条新短信")
                return@withLock newSmsList
            } finally {
                isReading.set(false)
            }
        }
    }

    /**
     * 加载所有短信数据
     * 只在首次加载或强制刷新时调用
     */
    private fun loadSmsData() {
        readLock.withLock {
            if (isReading.getAndSet(true)) {
                Log.d(TAG, "已有读取操作在进行中，跳过本次读取")
                return
            }

            try {
                val currentTime = System.currentTimeMillis()

                // 检查是否满足最小读取间隔
                if (initialLoadDone && currentTime - lastReadTime < MIN_READ_INTERVAL) {
                    Log.d(TAG, "短信读取间隔过短，跳过本次读取")
                    return
                }

                Log.d(TAG, "开始读取所有短信数据")

                // 清空缓存
                smsCache.clear()

                // 读取所有短信
                val cursor = context.contentResolver.query(
                    Telephony.Sms.CONTENT_URI,
                    null,
                    null,
                    null,
                    "${Telephony.Sms.DATE} DESC"
                )

                cursor?.use {
                    if (it.moveToFirst()) {
                        // 更新最后一条短信ID和时间
                        val idIndex = it.getColumnIndex(Telephony.Sms._ID)
                        val dateIndex = it.getColumnIndex(Telephony.Sms.DATE)

                        if (idIndex >= 0 && dateIndex >= 0) {
                            lastSmsId = it.getLong(idIndex)
                            lastSmsTime = it.getLong(dateIndex)
                        }

                        // 解析短信数据
                        do {
                            val sms = parseSmsFromCursor(it)
                            smsCache.add(sms)
                        } while (it.moveToNext())
                    }
                }

                // 更新最后读取时间
                lastReadTime = currentTime

                // 标记首次加载完成
                initialLoadDone = true

                Log.d(TAG, "读取到 ${smsCache.size} 条短信")
            } finally {
                isReading.set(false)
            }
        }
    }

    /**
     * 从游标解析短信数据
     */
    private fun parseSmsFromCursor(cursor: Cursor): SmsData {
        val idIndex = cursor.getColumnIndex(Telephony.Sms._ID)
        val addressIndex = cursor.getColumnIndex(Telephony.Sms.ADDRESS)
        val bodyIndex = cursor.getColumnIndex(Telephony.Sms.BODY)
        val dateIndex = cursor.getColumnIndex(Telephony.Sms.DATE)
        val typeIndex = cursor.getColumnIndex(Telephony.Sms.TYPE)

        val id = if (idIndex >= 0) cursor.getLong(idIndex) else 0
        val address = if (addressIndex >= 0) cursor.getString(addressIndex) ?: "" else ""
        val body = if (bodyIndex >= 0) cursor.getString(bodyIndex) ?: "" else ""
        val date = if (dateIndex >= 0) cursor.getLong(dateIndex) else System.currentTimeMillis()
        val type = if (typeIndex >= 0) cursor.getInt(typeIndex) else 0

        return SmsData(
            id = id,
            sender = address,
            body = body,
            timestamp = date,
            type = when (type) {
                Telephony.Sms.MESSAGE_TYPE_INBOX -> SmsData.TYPE_INBOX
                Telephony.Sms.MESSAGE_TYPE_SENT -> SmsData.TYPE_SENT
                else -> SmsData.TYPE_OTHER
            }
        )
    }

    /**
     * 添加新短信到缓存
     * 当通过通知栏监听到新短信时调用
     */
    fun addNewSms(smsData: SmsData) {
        readLock.withLock {
            // 检查是否已存在相同内容的短信（防止重复）
            val exists = smsCache.any {
                it.sender == smsData.sender &&
                it.body == smsData.body &&
                Math.abs(it.timestamp - smsData.timestamp) < 60000 // 1分钟内视为相同短信
            }

            if (!exists) {
                smsCache.add(0, smsData) // 添加到缓存开头

                // 更新最后一条短信时间（如果新短信时间更新）
                if (smsData.timestamp > lastSmsTime) {
                    lastSmsTime = smsData.timestamp
                }

                Log.d(TAG, "添加新短信到缓存: ${smsData.sender}")
            } else {
                Log.d(TAG, "短信已存在于缓存中，跳过添加: ${smsData.sender}")
            }
        }
    }

    /**
     * 重置读取状态
     * 用于测试或强制刷新
     */
    fun reset() {
        readLock.withLock {
            smsCache.clear()
            initialLoadDone = false
            lastSmsId = -1
            lastSmsTime = 0
            lastReadTime = 0
        }
    }
}
