<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_phone_verification" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_phone_verification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_phone_verification_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="177" endOffset="51"/></Target><Target id="@+id/ivBack" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="40"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="31" endOffset="51"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="33" startOffset="4" endLine="43" endOffset="60"/></Target><Target id="@+id/cardPhone" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="45" startOffset="4" endLine="161" endOffset="55"/></Target><Target id="@+id/etPhoneNumber" view="EditText"><Expressions/><location startLine="95" startOffset="16" endLine="106" endOffset="45"/></Target><Target id="@+id/etVerifyCode" view="EditText"><Expressions/><location startLine="121" startOffset="16" endLine="132" endOffset="45"/></Target><Target id="@+id/tvGetCode" view="TextView"><Expressions/><location startLine="134" startOffset="16" endLine="142" endOffset="46"/></Target><Target id="@+id/btnNextStep" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="150" startOffset="12" endLine="159" endOffset="41"/></Target><Target id="@+id/tvPrivacy" view="TextView"><Expressions/><location startLine="163" startOffset="4" endLine="173" endOffset="55"/></Target></Targets></Layout>