# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 47ms
  [gap of 15ms]
create_cxx_tasks completed in 62ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 73ms]
  create-initial-cxx-model completed in 84ms
create_cxx_tasks completed in 92ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 50ms
  [gap of 18ms]
create_cxx_tasks completed in 68ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 50ms
create_cxx_tasks completed in 53ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 48ms
  [gap of 15ms]
create_cxx_tasks completed in 63ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 48ms
create_cxx_tasks completed in 50ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 53ms
  [gap of 17ms]
create_cxx_tasks completed in 70ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 73ms
create_cxx_tasks completed in 78ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 47ms]
    create-ARM64_V8A-model 10ms
    [gap of 14ms]
  create-initial-cxx-model completed in 71ms
  [gap of 28ms]
create_cxx_tasks completed in 99ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 43ms
create_cxx_tasks completed in 45ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 44ms
  [gap of 11ms]
create_cxx_tasks completed in 55ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 44ms
  [gap of 13ms]
create_cxx_tasks completed in 57ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 45ms
  [gap of 13ms]
create_cxx_tasks completed in 59ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 41ms
  [gap of 15ms]
create_cxx_tasks completed in 56ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 44ms
  [gap of 14ms]
create_cxx_tasks completed in 58ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 45ms
  [gap of 16ms]
create_cxx_tasks completed in 61ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 42ms
    [gap of 70ms]
  create-initial-cxx-model completed in 113ms
create_cxx_tasks completed in 115ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 54ms
  [gap of 20ms]
create_cxx_tasks completed in 75ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 40ms
  [gap of 10ms]
create_cxx_tasks completed in 51ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 53ms
  [gap of 20ms]
create_cxx_tasks completed in 74ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 58ms
  [gap of 18ms]
create_cxx_tasks completed in 76ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 45ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 21ms]
  create-initial-cxx-model completed in 77ms
create_cxx_tasks completed in 82ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 13ms
    [gap of 31ms]
    create-module-model 13ms
    [gap of 39ms]
  create-initial-cxx-model completed in 96ms
create_cxx_tasks completed in 103ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 44ms
  [gap of 13ms]
create_cxx_tasks completed in 57ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 41ms
  [gap of 15ms]
create_cxx_tasks completed in 56ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 55ms
create_cxx_tasks completed in 58ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
  [gap of 14ms]
create_cxx_tasks completed in 53ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
create_cxx_tasks completed in 41ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 51ms]
  create-initial-cxx-model completed in 61ms
  [gap of 15ms]
create_cxx_tasks completed in 76ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 44ms]
  create-initial-cxx-model completed in 54ms
  [gap of 17ms]
create_cxx_tasks completed in 71ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 20ms]
    create-X86-model 10ms
    [gap of 44ms]
  create-initial-cxx-model completed in 74ms
  [gap of 12ms]
create_cxx_tasks completed in 86ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 45ms
  [gap of 16ms]
create_cxx_tasks completed in 62ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 60ms]
  create-initial-cxx-model completed in 70ms
create_cxx_tasks completed in 74ms

