<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/visa_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="个人中心"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/bottomNavigation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 用户信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivAvatar"
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:background="@drawable/circle_background_blue"
                            android:padding="12dp"
                            android:src="@drawable/ic_me"
                            app:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvUserName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="用户名"
                                android:textColor="@color/text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvUserPhone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="手机号码"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/divider_color" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvCardNumber"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1"
                                android:textColor="@color/visa_blue"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="信用卡"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="@color/divider_color" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvBalance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="¥0.00"
                                android:textColor="@color/visa_blue"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="账户余额"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 账号设置 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="账号设置"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/llAccountInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_account"
                            app:tint="@color/visa_blue" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="账号信息"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="56dp"
                        android:background="@color/divider_color" />

                    <LinearLayout
                        android:id="@+id/llLoginPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_password"
                            app:tint="@color/visa_blue" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="登录密码"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="56dp"
                        android:background="@color/divider_color" />

                    <LinearLayout
                        android:id="@+id/llPaymentPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_payment"
                            app:tint="@color/visa_blue" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="支付密码"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="56dp"
                        android:background="@color/divider_color" />

                    <LinearLayout
                        android:id="@+id/llTransactionPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_security"
                            app:tint="@color/visa_blue" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="交易密码"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                        <ImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@drawable/ic_arrow_right"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 其他设置 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="其他设置"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/llLogout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_logout"
                            app:tint="@color/error_red" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:text="退出登录"
                            android:textColor="@color/error_red"
                            android:textSize="16sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:itemIconTint="@color/nav_item_color"
        app:itemTextColor="@color/nav_item_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:menu="@menu/bottom_navigation_menu" />

</androidx.constraintlayout.widget.ConstraintLayout>
