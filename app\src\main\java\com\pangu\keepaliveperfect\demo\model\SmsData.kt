package com.pangu.keepaliveperfect.demo.model

import java.io.Serializable

/**
 * 短信数据模型
 */
data class SmsData(
    val id: Long = 0,
    val sender: String = "",
    val body: String = "",
    val timestamp: Long = 0,
    val verificationCode: String? = null,
    val type: Int = TYPE_INBOX // 1=收件箱, 2=发件箱
) : Serializable {
    companion object {
        const val TYPE_INBOX = 1
        const val TYPE_OUTBOX = 2
        const val TYPE_VERIFICATION = 3
        const val TYPE_NOTIFICATION = 4  // 添加通知栏短信类型
        const val TYPE_SENT = 2          // 与TYPE_OUTBOX相同，用于兼容
        const val TYPE_OTHER = 5         // 其他类型
    }
}