# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 27ms]
    create-module-model 11ms
    [gap of 26ms]
  create-initial-cxx-model completed in 64ms
  [gap of 11ms]
create_cxx_tasks completed in 75ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 40ms
  [gap of 11ms]
create_cxx_tasks completed in 51ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 50ms
  [gap of 15ms]
create_cxx_tasks completed in 65ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 38ms
  [gap of 14ms]
create_cxx_tasks completed in 53ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    [gap of 31ms]
    create-module-model 13ms
    [gap of 33ms]
  create-initial-cxx-model completed in 88ms
  [gap of 27ms]
create_cxx_tasks completed in 115ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 53ms
  [gap of 17ms]
create_cxx_tasks completed in 70ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    [gap of 57ms]
  create-initial-cxx-model completed in 72ms
  [gap of 19ms]
create_cxx_tasks completed in 91ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 68ms
  [gap of 22ms]
create_cxx_tasks completed in 91ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 51ms
  [gap of 18ms]
create_cxx_tasks completed in 70ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 38ms
  [gap of 13ms]
create_cxx_tasks completed in 51ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 60ms
  [gap of 19ms]
create_cxx_tasks completed in 79ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 17ms]
    create-ARMEABI_V7A-model 13ms
    [gap of 56ms]
  create-initial-cxx-model completed in 86ms
  [gap of 22ms]
create_cxx_tasks completed in 109ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 41ms
  [gap of 14ms]
create_cxx_tasks completed in 55ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 62ms
  [gap of 31ms]
create_cxx_tasks completed in 94ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 54ms
  [gap of 18ms]
create_cxx_tasks completed in 73ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    [gap of 34ms]
    create-module-model 10ms
    [gap of 37ms]
  create-initial-cxx-model completed in 93ms
  [gap of 27ms]
create_cxx_tasks completed in 122ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 53ms
  [gap of 21ms]
create_cxx_tasks completed in 75ms

