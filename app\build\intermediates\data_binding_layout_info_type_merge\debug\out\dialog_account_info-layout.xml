<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_account_info" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\dialog_account_info.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_account_info_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="163" endOffset="51"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="51"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="21" startOffset="4" endLine="27" endOffset="60"/></Target><Target id="@+id/tvUsername" view="TextView"><Expressions/><location startLine="51" startOffset="12" endLine="57" endOffset="41"/></Target><Target id="@+id/tvPhone" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="79" endOffset="41"/></Target><Target id="@+id/tvRealName" view="TextView"><Expressions/><location startLine="95" startOffset="12" endLine="101" endOffset="41"/></Target><Target id="@+id/tvIdCard" view="TextView"><Expressions/><location startLine="117" startOffset="12" endLine="123" endOffset="41"/></Target><Target id="@+id/tvRegisterTime" view="TextView"><Expressions/><location startLine="139" startOffset="12" endLine="145" endOffset="41"/></Target><Target id="@+id/btnClose" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="150" startOffset="4" endLine="161" endOffset="65"/></Target></Targets></Layout>