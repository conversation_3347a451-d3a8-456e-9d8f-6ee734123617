<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="96" endOffset="14"/></Target><Target id="@+id/statusTextView" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="28" endOffset="26"/></Target><Target id="@+id/startButton" view="Button"><Expressions/><location startLine="30" startOffset="4" endLine="36" endOffset="26"/></Target><Target id="@+id/stopButton" view="Button"><Expressions/><location startLine="38" startOffset="4" endLine="44" endOffset="26"/></Target><Target id="@+id/hideButton" view="Button"><Expressions/><location startLine="46" startOffset="4" endLine="52" endOffset="26"/></Target><Target id="@+id/btnStartService" view="Button"><Expressions/><location startLine="54" startOffset="4" endLine="60" endOffset="26"/></Target><Target id="@+id/btnStopService" view="Button"><Expressions/><location startLine="62" startOffset="4" endLine="68" endOffset="26"/></Target><Target id="@+id/viewLogsButton" view="Button"><Expressions/><location startLine="70" startOffset="4" endLine="76" endOffset="26"/></Target><Target id="@+id/deviceInfoButton" view="Button"><Expressions/><location startLine="78" startOffset="4" endLine="84" endOffset="26"/></Target></Targets></Layout>