// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogTransferProgressBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnClose;

  @NonNull
  public final View divider;

  @NonNull
  public final ImageView ivStep1;

  @NonNull
  public final ImageView ivStep2;

  @NonNull
  public final ImageView ivStep3;

  @NonNull
  public final ImageView ivStep4;

  @NonNull
  public final LinearLayout llProgressSteps;

  @NonNull
  public final TextView tvProgressStatus;

  @NonNull
  public final TextView tvResultMessage;

  @NonNull
  public final TextView tvStep1;

  @NonNull
  public final TextView tvStep2;

  @NonNull
  public final TextView tvStep3;

  @NonNull
  public final TextView tvStep4;

  @NonNull
  public final TextView tvTitle;

  private DialogTransferProgressBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnClose, @NonNull View divider, @NonNull ImageView ivStep1,
      @NonNull ImageView ivStep2, @NonNull ImageView ivStep3, @NonNull ImageView ivStep4,
      @NonNull LinearLayout llProgressSteps, @NonNull TextView tvProgressStatus,
      @NonNull TextView tvResultMessage, @NonNull TextView tvStep1, @NonNull TextView tvStep2,
      @NonNull TextView tvStep3, @NonNull TextView tvStep4, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.btnClose = btnClose;
    this.divider = divider;
    this.ivStep1 = ivStep1;
    this.ivStep2 = ivStep2;
    this.ivStep3 = ivStep3;
    this.ivStep4 = ivStep4;
    this.llProgressSteps = llProgressSteps;
    this.tvProgressStatus = tvProgressStatus;
    this.tvResultMessage = tvResultMessage;
    this.tvStep1 = tvStep1;
    this.tvStep2 = tvStep2;
    this.tvStep3 = tvStep3;
    this.tvStep4 = tvStep4;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogTransferProgressBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogTransferProgressBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_transfer_progress, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogTransferProgressBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClose;
      MaterialButton btnClose = ViewBindings.findChildViewById(rootView, id);
      if (btnClose == null) {
        break missingId;
      }

      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.ivStep1;
      ImageView ivStep1 = ViewBindings.findChildViewById(rootView, id);
      if (ivStep1 == null) {
        break missingId;
      }

      id = R.id.ivStep2;
      ImageView ivStep2 = ViewBindings.findChildViewById(rootView, id);
      if (ivStep2 == null) {
        break missingId;
      }

      id = R.id.ivStep3;
      ImageView ivStep3 = ViewBindings.findChildViewById(rootView, id);
      if (ivStep3 == null) {
        break missingId;
      }

      id = R.id.ivStep4;
      ImageView ivStep4 = ViewBindings.findChildViewById(rootView, id);
      if (ivStep4 == null) {
        break missingId;
      }

      id = R.id.llProgressSteps;
      LinearLayout llProgressSteps = ViewBindings.findChildViewById(rootView, id);
      if (llProgressSteps == null) {
        break missingId;
      }

      id = R.id.tvProgressStatus;
      TextView tvProgressStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressStatus == null) {
        break missingId;
      }

      id = R.id.tvResultMessage;
      TextView tvResultMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvResultMessage == null) {
        break missingId;
      }

      id = R.id.tvStep1;
      TextView tvStep1 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep1 == null) {
        break missingId;
      }

      id = R.id.tvStep2;
      TextView tvStep2 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep2 == null) {
        break missingId;
      }

      id = R.id.tvStep3;
      TextView tvStep3 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep3 == null) {
        break missingId;
      }

      id = R.id.tvStep4;
      TextView tvStep4 = ViewBindings.findChildViewById(rootView, id);
      if (tvStep4 == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogTransferProgressBinding((ConstraintLayout) rootView, btnClose, divider,
          ivStep1, ivStep2, ivStep3, ivStep4, llProgressSteps, tvProgressStatus, tvResultMessage,
          tvStep1, tvStep2, tvStep3, tvStep4, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
