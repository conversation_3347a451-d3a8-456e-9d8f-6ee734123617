<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_account_login" modulePackage="com.pangu.keepaliveperfect.demo" filePath="app\src\main\res\layout\activity_account_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_account_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="253" endOffset="51"/></Target><Target id="@+id/ivBack" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="18" endOffset="40"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="31" endOffset="51"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="33" startOffset="4" endLine="43" endOffset="60"/></Target><Target id="@+id/cardLogin" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="45" startOffset="4" endLine="219" endOffset="55"/></Target><Target id="@+id/etUsername" view="EditText"><Expressions/><location startLine="88" startOffset="16" endLine="99" endOffset="45"/></Target><Target id="@+id/etPassword" view="EditText"><Expressions/><location startLine="122" startOffset="16" endLine="133" endOffset="45"/></Target><Target id="@+id/ivTogglePassword" view="ImageView"><Expressions/><location startLine="135" startOffset="16" endLine="141" endOffset="54"/></Target><Target id="@+id/tvForgotPassword" view="TextView"><Expressions/><location startLine="150" startOffset="12" endLine="158" endOffset="41"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="161" startOffset="12" endLine="170" endOffset="41"/></Target><Target id="@+id/ivPhoneLogin" view="ImageView"><Expressions/><location startLine="189" startOffset="16" endLine="197" endOffset="54"/></Target><Target id="@+id/ivWechatLogin" view="ImageView"><Expressions/><location startLine="199" startOffset="16" endLine="207" endOffset="52"/></Target><Target id="@+id/ivQQLogin" view="ImageView"><Expressions/><location startLine="209" startOffset="16" endLine="216" endOffset="53"/></Target><Target id="@+id/llRegister" view="LinearLayout"><Expressions/><location startLine="222" startOffset="4" endLine="249" endOffset="18"/></Target><Target id="@+id/tvRegister" view="TextView"><Expressions/><location startLine="240" startOffset="8" endLine="248" endOffset="38"/></Target></Targets></Layout>