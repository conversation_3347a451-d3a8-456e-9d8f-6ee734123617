# For more information about using CMakeLists with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.
cmake_minimum_required(VERSION 3.10.2)

# Declares and names the project.
project("immortal_guardian")

# Enable C++14 features (more compatible)
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add compile flags for optimization and security
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O2 -fPIC")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O2 -fPIC")

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
add_library(
        basic_native
        SHARED

        # Only basic native functionality
        native-lib.cpp
)

# Find required libraries
find_library(log-lib log)

# Link libraries
target_link_libraries(
        basic_native
        ${log-lib}
)
