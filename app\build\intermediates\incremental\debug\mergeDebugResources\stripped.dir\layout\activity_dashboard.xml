<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/visa_blue"
        app:elevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="16dp"
            android:paddingTop="48dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/tvGreeting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dashboard_greeting"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="用户"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@+id/tvGreeting"
                app:layout_constraintTop_toTopOf="@+id/tvGreeting" />

            <ImageView
                android:id="@+id/ivNotification"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="Notifications"
                android:src="@drawable/ic_notification"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 滚动提示信息容器 -->
            <LinearLayout
                android:id="@+id/llNoticeContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/bg_rounded_light"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_notification"
                    app:tint="@color/visa_blue" />

                <TextView
                    android:id="@+id/tvScrollingNotice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:textColor="@color/visa_blue"
                    android:textSize="14sp" />
            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:id="@+id/cardCredit"
                android:layout_width="0dp"
                android:layout_height="200dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                app:cardBackgroundColor="@color/card_background"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/llNoticeContainer">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="16dp">

                    <ImageView
                        android:id="@+id/ivVisaLogo"
                        android:layout_width="100dp"
                        android:layout_height="60dp"
                        android:contentDescription="VISA Logo"
                        android:src="@drawable/visa2"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvBankName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="中国银行"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvCardType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="信用卡"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvBankName" />

                    <TextView
                        android:id="@+id/tvCardNumber"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:fontFamily="monospace"
                        android:text="**** **** **** 1234"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvCardType" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/card_balance"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tvBalance"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="¥ 10,342.50"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/credit_limit"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tvCreditLimit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="¥ 50,000.00"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </LinearLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cardBillInfo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:cardBackgroundColor="@color/surface"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cardCredit">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/bill_date"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/tvBillDate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="每月5日"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="match_parent"
                        android:background="@color/divider_color" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/repayment_date"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/tvRepaymentDate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="每月25日"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/tvFunctions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:text="常用功能"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/cardBillInfo" />

            <LinearLayout
                android:id="@+id/llFunctions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="horizontal"
                android:padding="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvFunctions">

                <LinearLayout
                    android:id="@+id/llTransfer"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="@drawable/circle_background_blue"
                        android:contentDescription="Transfer"
                        android:padding="12dp"
                        android:src="@drawable/ic_transfer"
                        app:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/transfer"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llPayment"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="@drawable/circle_background_gold"
                        android:contentDescription="Payment"
                        android:padding="12dp"
                        android:src="@drawable/ic_payment"
                        app:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/payment"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/llBill"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="@drawable/circle_background_red"
                        android:contentDescription="Bill"
                        android:padding="12dp"
                        android:src="@drawable/ic_bill"
                        app:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/bill"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tvRecentTransactions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:text="近期交易"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/llFunctions" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvTransactions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:nestedScrollingEnabled="false"
                android:paddingBottom="80dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvRecentTransactions"
                tools:itemCount="3"
                tools:listitem="@layout/item_transaction" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/text_primary"
        app:itemTextColor="@color/text_primary"
        app:menu="@menu/bottom_navigation_menu" />



</androidx.coordinatorlayout.widget.CoordinatorLayout>