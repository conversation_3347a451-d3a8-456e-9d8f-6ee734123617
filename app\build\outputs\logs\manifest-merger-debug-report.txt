-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:405:9-414:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:409:13-31
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:407:13-68
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:408:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:406:13-67
manifest
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\a0bdbb46d5130446a32accda8a0f5f81\transformed\jetified-viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\89776205eeab5a560994ed21c34c090c\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd3b69b66e6b65f494e6d96e162c073\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e8fd734ebd1d3931605923d464e80899\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb15e9debdc6dc0d32c0311da9fb1a3a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f02f5bdf0f745e19621751fc73ad7a7\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\376ccaceccb8c8d200dd37c294afb78a\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\97844f3dc7e2ef7ecd9c0f1aee0a5245\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e0e92451cb7aee5ff8934f376f578f88\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfcf253a2a872ddc34a6c991b049ba13\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b9c3f4b305dfd2128591f5744bf7ca7\transformed\jetified-glide-4.15.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\113ac73f0569749bbc0f903df5106eb8\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.mlkit:face-detection:16.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.mlkit:vision-interfaces:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a01abb3db1326634748cf0671d7f20\transformed\jetified-vision-interfaces-16.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\834651666f124d80cef7289047dba2ba\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4b605ed60a1172a84ddfed24abb6cff0\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a735e2fa524b3248371761c0991ff20\transformed\jetified-activity-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d23cf247353db7fb3bceaff70915c467\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7770345ab7573bf3d7513fc6b353bbb6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0936f4b13e18f91d914662ef6fbc9cd3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98a0c2fb4ce964adbf10229b173786c4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\7fdc673b955968f412ee6d417525d079\transformed\jetified-lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ad63aa82f7e97ddab2fd6f7e1a5e29b\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6402e68facfd9b21ac33052eadb60e2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d27a9ec17804b8e915a4fc0fc9ca97a8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\c9c419aaf46c2224cc974475496db6fa\transformed\jetified-lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\22467e2b2b3c344ecaab554f435872f6\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\25b97fe0d4ffa4500cbaa53bbb06c8de\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\847d0b955d71c8093b748f33f91c9d7e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a35e2a4a566ce95e3420de6dd6309c8f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\02cc149499fb5303e82cc081ee85896e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7abfad9d7fb329a0b5368839949b919a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2f3f7b793ed3a1dd68f0df4baee740d\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2052975e6a22dfce107aef429231dbf\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\70d0acfd0497a81977a1451649aedb4a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9074eb03053974ac06b8f0a0431fbce6\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f8d5cec48544953de88d1124ee6b37c\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.getActivity:XXPermissions:21.2] C:\Users\<USER>\.gradle\caches\transforms-3\db21868e878961f80e1bf1e6b3c4bb47\transformed\jetified-XXPermissions-21.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cb07a95702d8f86e36fd043797c986c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bb991045850cde0873e5278df30156f\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1f359fb76d5dc851c8490048a9c953c\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4dd0da825ab399ca1452308e8fc393\transformed\jetified-savedstate-ktx-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\336dda9f6c7783dbe54a5c3c728af833\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c265f30dde446d413fd34f3c4825bbc\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2662ef64f13472442dc0a3ea192d8a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\0473a21f593252778fa34463ff3ed85b\transformed\jetified-gifdecoder-4.15.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\78e9bf5a718bbe397c674e62dfc92144\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e416db03f4cc6de18bcdda8f215c5d0\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e3969449c9734ebc2910e977530bb22\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bc99d1575cad79cb8f0b4f422e37a58\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\99a1099a6c8439184d405d484621d66a\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\db566a429e900fb753de1770e40e3c29\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c11f764ac9b8df9caebdc0141d7ee9\transformed\jetified-lifecycle-livedata-core-ktx-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\a780c33170532e310c1c5655f4882f5b\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\61c50e78abc8a4fe196ada5624527142\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4503eaba342e6e637bf8397f4eb4bc0a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb51cd6b7418f7a8032edfa60064527d\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e473e3fc00a6490438ea15bad264293\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36f91bfcaafff320d0f346783ae7e5ae\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aaa576556460f2c64b030835b315b09\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c281a44e0783d9af63e36c735c6591b5\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8006cf1fbfbdd0536175b61bc60600e\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.conscrypt:conscrypt-android:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e038ffa879db1937b2ff34b350a5080f\transformed\jetified-conscrypt-android-2.2.1\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\cb2be6867e92175dbb7759f29a68311e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
	package
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:4:5-46
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:1-416:12
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:8:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:9:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:5-95
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:12:22-92
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:5-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:15:22-67
uses-permission#android.permission.READ_SMS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.SEND_SMS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:17:22-64
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:20:22-72
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:21:22-74
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:24:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:5-26:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:26:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:25:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:28:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:29:22-72
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:5-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:32:22-62
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:5-85
	android:required
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:58-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:33:19-57
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:5-37:53
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:37:9-50
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:36:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:5-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:40:22-84
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:43:22-74
uses-permission#com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:5-86
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:47:22-83
uses-permission#com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:48:22-82
uses-permission#com.vivo.notification.permission.BADGE_ICON
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:5-83
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:49:22-80
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:5-93
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:52:22-90
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:55:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:56:22-73
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:59:22-75
uses-permission#android.permission.BIND_DEVICE_ADMIN
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:60:22-73
uses-permission#android.permission.GET_ACCOUNTS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:61:22-68
uses-permission#android.permission.MANAGE_ACCOUNTS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:62:22-71
uses-permission#android.permission.AUTHENTICATE_ACCOUNTS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:63:22-77
uses-permission#android.permission.USE_CREDENTIALS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:64:22-71
uses-permission#android.permission.WRITE_SYNC_SETTINGS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:65:22-75
uses-permission#android.permission.READ_SYNC_SETTINGS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:66:22-74
uses-permission#android.permission.READ_SYNC_STATS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:67:22-71
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:68:22-82
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:69:22-63
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:70:22-72
uses-permission#android.permission.EXPAND_STATUS_BAR
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:71:22-73
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:72:22-77
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:5-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:73:22-81
uses-permission#android.permission.RESTART_PACKAGES
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:74:22-72
uses-permission#android.permission.GET_TASKS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:75:22-65
uses-permission#android.permission.REORDER_TASKS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:76:22-69
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:5-83
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:77:22-80
uses-permission#android.permission.INSTALL_PACKAGES
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:78:22-72
uses-permission#android.permission.DELETE_PACKAGES
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:79:22-71
uses-permission#android.permission.CLEAR_APP_CACHE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:80:22-71
uses-permission#android.permission.CLEAR_APP_USER_DATA
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:81:22-75
uses-permission#android.permission.WRITE_SECURE_SETTINGS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:82:22-77
uses-permission#android.permission.WRITE_SETTINGS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:83:22-70
uses-permission#android.permission.CHANGE_CONFIGURATION
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:84:22-76
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:5-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:85:22-81
uses-permission#android.permission.ACCESS_SUPERUSER
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:86:22-72
uses-permission#miui.permission.USE_INTERNAL_GENERAL_API
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:89:22-77
uses-permission#com.huawei.permission.external_app_settings.USE_COMPONENT
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:5-97
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:90:22-94
uses-permission#oppo.permission.OPPO_COMPONENT_SAFE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:91:22-72
uses-permission#com.vivo.permissionmanager.permission.ACCESS_COMPONENT_SAFE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:5-99
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:92:22-96
uses-permission#android.permission.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:95:22-73
uses-permission#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:96:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:99:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:100:22-76
uses-permission#android.permission.READ_CONTACTS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:103:22-69
uses-permission#android.permission.WRITE_CONTACTS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:104:22-70
uses-permission#android.permission.READ_CALL_LOG
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:5-72
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:107:22-69
uses-permission#android.permission.WRITE_CALL_LOG
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:108:22-70
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:111:22-68
application
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:113:5-415:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd3b69b66e6b65f494e6d96e162c073\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd3b69b66e6b65f494e6d96e162c073\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\97844f3dc7e2ef7ecd9c0f1aee0a5245\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\97844f3dc7e2ef7ecd9c0f1aee0a5245\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b9c3f4b305dfd2128591f5744bf7ca7\transformed\jetified-glide-4.15.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b9c3f4b305dfd2128591f5744bf7ca7\transformed\jetified-glide-4.15.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\834651666f124d80cef7289047dba2ba\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\834651666f124d80cef7289047dba2ba\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2662ef64f13472442dc0a3ea192d8a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2662ef64f13472442dc0a3ea192d8a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\0473a21f593252778fa34463ff3ed85b\transformed\jetified-gifdecoder-4.15.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\0473a21f593252778fa34463ff3ed85b\transformed\jetified-gifdecoder-4.15.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [org.conscrypt:conscrypt-android:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e038ffa879db1937b2ff34b350a5080f\transformed\jetified-conscrypt-android-2.2.1\AndroidManifest.xml:11:5-20
MERGED from [org.conscrypt:conscrypt-android:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e038ffa879db1937b2ff34b350a5080f\transformed\jetified-conscrypt-android-2.2.1\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\cb2be6867e92175dbb7759f29a68311e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\cb2be6867e92175dbb7759f29a68311e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:123:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:119:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:117:9-46
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:118:9-47
	android:icon
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:116:9-42
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:115:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:120:9-51
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:121:9-39
	tools:replace
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:124:9-38
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:122:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:114:9-45
activity#com.pangu.keepaliveperfect.demo.visa.LoginActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:127:9-136:20
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:129:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:130:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:131:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:128:13-47
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:132:13-135:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:133:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:134:27-74
activity#com.pangu.keepaliveperfect.demo.visa.PhoneVerificationActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:138:9-141:48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:140:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:141:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:139:13-59
activity#com.pangu.keepaliveperfect.demo.visa.AccountLoginActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:143:9-146:48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:145:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:146:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:144:13-54
activity#com.pangu.keepaliveperfect.demo.visa.WechatLoginActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:148:9-151:48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:150:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:151:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:149:13-53
activity#com.pangu.keepaliveperfect.demo.visa.QQLoginActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:153:9-156:48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:155:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:156:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:154:13-49
activity#com.pangu.keepaliveperfect.demo.visa.RegisterActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:158:9-162:76
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:162:13-73
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:160:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:161:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:159:13-50
activity#com.pangu.keepaliveperfect.demo.visa.HideIconActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:165:9-170:40
	android:excludeFromRecents
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:169:13-46
	android:noHistory
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:170:13-37
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:167:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:168:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:166:13-50
activity#com.pangu.keepaliveperfect.demo.visa.DashboardActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:172:9-175:48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:174:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:175:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:173:13-51
activity#com.pangu.keepaliveperfect.demo.visa.ProfileActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:177:9-180:48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:179:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:180:13-45
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:178:13-49
activity#com.pangu.keepaliveperfect.demo.SimplePermissionActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:183:9-187:20
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:185:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:186:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:184:13-53
activity#com.pangu.keepaliveperfect.demo.LogsActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:190:9-192:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:192:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:191:13-41
activity#com.pangu.keepaliveperfect.demo.MainActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:195:9-206:20
	android:excludeFromRecents
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:201:13-46
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:199:13-48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:197:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:198:13-56
	android:taskAffinity
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:200:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:196:13-41
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:202:13-205:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:203:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:204:27-73
activity#com.pangu.keepaliveperfect.demo.OnePixelActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:209:9-215:20
	android:excludeFromRecents
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:211:13-46
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:213:13-48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:212:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:214:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:210:13-45
activity#com.pangu.keepaliveperfect.demo.DeviceInfoActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:218:9-220:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:220:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:219:13-47
service#com.pangu.keepaliveperfect.demo.KeepAliveService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:223:9-229:19
	android:process
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:228:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:225:13-37
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:227:13-43
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:226:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:224:13-45
service#com.pangu.keepaliveperfect.demo.service.DaemonService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:232:9-238:19
	android:process
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:237:13-46
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:234:13-37
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:236:13-43
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:235:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:233:13-50
service#com.pangu.keepaliveperfect.demo.service.IndependentGuardianService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:241:9-249:19
	android:process
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:243:13-40
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:245:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:246:13-37
	android:priority
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:248:13-36
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:244:13-43
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:247:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:242:13-63
service#com.pangu.keepaliveperfect.demo.KeepAliveJobService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:254:9-260:19
	android:process
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:259:13-43
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:257:13-37
	android:permission
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:256:13-69
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:258:13-43
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:255:13-48
service#com.pangu.keepaliveperfect.demo.account.AuthenticatorService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:263:9-273:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:265:13-37
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:266:13-43
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:264:13-57
intent-filter#action:name:android.accounts.AccountAuthenticator
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:267:13-269:29
action#android.accounts.AccountAuthenticator
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:268:17-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:268:25-77
meta-data#android.accounts.AccountAuthenticator
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:270:13-272:57
	android:resource
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:272:17-54
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:271:17-69
service#com.pangu.keepaliveperfect.demo.account.SyncService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:276:9-287:19
	android:process
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:280:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:278:13-37
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:279:13-43
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:277:13-48
intent-filter#action:name:android.content.SyncAdapter
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:281:13-283:29
action#android.content.SyncAdapter
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:282:17-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:282:25-67
meta-data#android.content.SyncAdapter
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:284:13-286:55
	android:resource
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:286:17-52
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:285:17-59
provider#com.pangu.keepaliveperfect.demo.account.StubProvider
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:290:9-294:39
	android:authorities
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:292:13-75
	android:syncable
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:294:13-36
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:293:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:291:13-49
receiver#com.pangu.keepaliveperfect.demo.BootReceiver
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:297:9-307:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:300:13-36
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:299:13-43
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:298:13-41
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:com.htc.intent.action.QUICKBOOT_POWERON+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:301:13-306:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:17-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:302:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:303:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:303:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:304:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:304:25-79
receiver#com.pangu.keepaliveperfect.demo.receiver.PackageReceiver
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:310:9-319:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:312:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:311:13-53
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_ADDED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:313:13-318:29
action#android.intent.action.PACKAGE_ADDED
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:314:25-75
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:17-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:315:25-78
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:316:17-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:316:25-81
data
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:317:17-50
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:317:23-47
receiver#com.pangu.keepaliveperfect.demo.SmsReceiver
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:322:9-330:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:324:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:326:13-66
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:325:13-43
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:323:13-40
intent-filter#action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:13-329:29
	android:priority
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:327:28-50
action#android.provider.Telephony.SMS_RECEIVED
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:328:25-79
receiver#com.pangu.keepaliveperfect.demo.receiver.ServiceRestartReceiver
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:333:9-341:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:335:13-37
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:336:13-43
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:334:13-60
intent-filter#action:name:com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE+action:name:com.pangu.keepaliveperfect.demo.action.START_SERVICE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:337:13-340:29
action#com.pangu.keepaliveperfect.demo.action.START_SERVICE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:17-95
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:338:25-92
action#com.pangu.keepaliveperfect.demo.action.RESTART_SERVICE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:17-97
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:339:25-94
receiver#com.pangu.keepaliveperfect.demo.receiver.VendorReceiver
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:344:9-358:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:346:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:345:13-52
intent-filter#action:name:com.coloros.mcssdk.action.RECEIVE_MCS_MESSAGE+action:name:com.heytap.mcssdk.action.RECEIVE_MCS_MESSAGE+action:name:com.huawei.android.push.intent.RECEIVE+action:name:com.vivo.pushclient.action.RECEIVE+action:name:com.xiaomi.mipush.RECEIVE_MESSAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:347:13-357:29
action#com.xiaomi.mipush.RECEIVE_MESSAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:349:25-73
action#com.huawei.android.push.intent.RECEIVE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:351:17-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:351:25-78
action#com.coloros.mcssdk.action.RECEIVE_MCS_MESSAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:353:17-88
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:353:25-85
action#com.heytap.mcssdk.action.RECEIVE_MCS_MESSAGE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:354:17-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:354:25-84
action#com.vivo.pushclient.action.RECEIVE
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:356:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:356:25-74
receiver#com.pangu.keepaliveperfect.demo.receiver.ShortcutBroadcastReceiver
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:361:9-368:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:363:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:362:13-63
intent-filter#action:name:com.pangu.keepaliveperfect.demo.action.SHORTCUT_LONG_PRESS+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:364:13-367:29
action#com.pangu.keepaliveperfect.demo.action.SHORTCUT_LONG_PRESS
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:17-101
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:365:25-98
service#com.pangu.keepaliveperfect.demo.utils.NotificationAccessHelper$UniversalNotificationListener
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:371:9-382:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:373:13-37
	android:permission
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:374:13-87
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:375:13-43
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:372:13-89
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:376:13-378:29
action#android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:377:17-99
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:377:25-96
meta-data#android.service.notification.default_filter_types
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:379:13-381:73
	android:value
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:381:17-70
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:380:17-81
service#com.pangu.keepaliveperfect.demo.qiniu.QiniuUploadService
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:385:9-390:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:388:13-37
	android:directBootAware
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:387:13-43
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:389:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:386:13-53
activity#com.pangu.keepaliveperfect.demo.qiniu.QiniuTestActivity
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:393:9-396:37
	android:label
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:396:13-34
	android:exported
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:395:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:394:13-52
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:400:9-402:69
	android:resource
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:402:13-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:401:13-83
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\a0bdbb46d5130446a32accda8a0f5f81\transformed\jetified-viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\a0bdbb46d5130446a32accda8a0f5f81\transformed\jetified-viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\89776205eeab5a560994ed21c34c090c\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\89776205eeab5a560994ed21c34c090c\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd3b69b66e6b65f494e6d96e162c073\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd3b69b66e6b65f494e6d96e162c073\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e8fd734ebd1d3931605923d464e80899\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\e8fd734ebd1d3931605923d464e80899\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb15e9debdc6dc0d32c0311da9fb1a3a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb15e9debdc6dc0d32c0311da9fb1a3a\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f02f5bdf0f745e19621751fc73ad7a7\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7f02f5bdf0f745e19621751fc73ad7a7\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\376ccaceccb8c8d200dd37c294afb78a\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\376ccaceccb8c8d200dd37c294afb78a\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\97844f3dc7e2ef7ecd9c0f1aee0a5245\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\97844f3dc7e2ef7ecd9c0f1aee0a5245\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e0e92451cb7aee5ff8934f376f578f88\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e0e92451cb7aee5ff8934f376f578f88\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfcf253a2a872ddc34a6c991b049ba13\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\dfcf253a2a872ddc34a6c991b049ba13\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b9c3f4b305dfd2128591f5744bf7ca7\transformed\jetified-glide-4.15.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b9c3f4b305dfd2128591f5744bf7ca7\transformed\jetified-glide-4.15.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\113ac73f0569749bbc0f903df5106eb8\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\113ac73f0569749bbc0f903df5106eb8\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.mlkit:face-detection:16.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:face-detection:16.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\ee74b9895a8e480ae9aad53149f13b7a\transformed\jetified-face-detection-16.1.5\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.mlkit:vision-interfaces:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a01abb3db1326634748cf0671d7f20\transformed\jetified-vision-interfaces-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08a01abb3db1326634748cf0671d7f20\transformed\jetified-vision-interfaces-16.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\834651666f124d80cef7289047dba2ba\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\834651666f124d80cef7289047dba2ba\transformed\jetified-play-services-tasks-18.0.1\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4b605ed60a1172a84ddfed24abb6cff0\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\4b605ed60a1172a84ddfed24abb6cff0\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a735e2fa524b3248371761c0991ff20\transformed\jetified-activity-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a735e2fa524b3248371761c0991ff20\transformed\jetified-activity-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d23cf247353db7fb3bceaff70915c467\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\d23cf247353db7fb3bceaff70915c467\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7770345ab7573bf3d7513fc6b353bbb6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7770345ab7573bf3d7513fc6b353bbb6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0936f4b13e18f91d914662ef6fbc9cd3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0936f4b13e18f91d914662ef6fbc9cd3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98a0c2fb4ce964adbf10229b173786c4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98a0c2fb4ce964adbf10229b173786c4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\7fdc673b955968f412ee6d417525d079\transformed\jetified-lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\7fdc673b955968f412ee6d417525d079\transformed\jetified-lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ad63aa82f7e97ddab2fd6f7e1a5e29b\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ad63aa82f7e97ddab2fd6f7e1a5e29b\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6402e68facfd9b21ac33052eadb60e2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6402e68facfd9b21ac33052eadb60e2b\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d27a9ec17804b8e915a4fc0fc9ca97a8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d27a9ec17804b8e915a4fc0fc9ca97a8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\c9c419aaf46c2224cc974475496db6fa\transformed\jetified-lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\c9c419aaf46c2224cc974475496db6fa\transformed\jetified-lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\22467e2b2b3c344ecaab554f435872f6\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\22467e2b2b3c344ecaab554f435872f6\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\25b97fe0d4ffa4500cbaa53bbb06c8de\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\25b97fe0d4ffa4500cbaa53bbb06c8de\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\847d0b955d71c8093b748f33f91c9d7e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\847d0b955d71c8093b748f33f91c9d7e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a35e2a4a566ce95e3420de6dd6309c8f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a35e2a4a566ce95e3420de6dd6309c8f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\02cc149499fb5303e82cc081ee85896e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\02cc149499fb5303e82cc081ee85896e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7abfad9d7fb329a0b5368839949b919a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7abfad9d7fb329a0b5368839949b919a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2f3f7b793ed3a1dd68f0df4baee740d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f2f3f7b793ed3a1dd68f0df4baee740d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2052975e6a22dfce107aef429231dbf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a2052975e6a22dfce107aef429231dbf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\70d0acfd0497a81977a1451649aedb4a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\70d0acfd0497a81977a1451649aedb4a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9074eb03053974ac06b8f0a0431fbce6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9074eb03053974ac06b8f0a0431fbce6\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f8d5cec48544953de88d1124ee6b37c\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\2f8d5cec48544953de88d1124ee6b37c\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.getActivity:XXPermissions:21.2] C:\Users\<USER>\.gradle\caches\transforms-3\db21868e878961f80e1bf1e6b3c4bb47\transformed\jetified-XXPermissions-21.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.getActivity:XXPermissions:21.2] C:\Users\<USER>\.gradle\caches\transforms-3\db21868e878961f80e1bf1e6b3c4bb47\transformed\jetified-XXPermissions-21.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1ec1a1ad6d22df9da9406b888b5c300\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cb07a95702d8f86e36fd043797c986c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cb07a95702d8f86e36fd043797c986c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bb991045850cde0873e5278df30156f\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2bb991045850cde0873e5278df30156f\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1f359fb76d5dc851c8490048a9c953c\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e1f359fb76d5dc851c8490048a9c953c\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4dd0da825ab399ca1452308e8fc393\transformed\jetified-savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab4dd0da825ab399ca1452308e8fc393\transformed\jetified-savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\336dda9f6c7783dbe54a5c3c728af833\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\336dda9f6c7783dbe54a5c3c728af833\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c265f30dde446d413fd34f3c4825bbc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3c265f30dde446d413fd34f3c4825bbc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2662ef64f13472442dc0a3ea192d8a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c2662ef64f13472442dc0a3ea192d8a0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\0473a21f593252778fa34463ff3ed85b\transformed\jetified-gifdecoder-4.15.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.0] C:\Users\<USER>\.gradle\caches\transforms-3\0473a21f593252778fa34463ff3ed85b\transformed\jetified-gifdecoder-4.15.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\78e9bf5a718bbe397c674e62dfc92144\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\78e9bf5a718bbe397c674e62dfc92144\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e416db03f4cc6de18bcdda8f215c5d0\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e416db03f4cc6de18bcdda8f215c5d0\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e3969449c9734ebc2910e977530bb22\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2e3969449c9734ebc2910e977530bb22\transformed\jetified-transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bc99d1575cad79cb8f0b4f422e37a58\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bc99d1575cad79cb8f0b4f422e37a58\transformed\jetified-firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\99a1099a6c8439184d405d484621d66a\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\99a1099a6c8439184d405d484621d66a\transformed\jetified-firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\db566a429e900fb753de1770e40e3c29\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\db566a429e900fb753de1770e40e3c29\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c11f764ac9b8df9caebdc0141d7ee9\transformed\jetified-lifecycle-livedata-core-ktx-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f2c11f764ac9b8df9caebdc0141d7ee9\transformed\jetified-lifecycle-livedata-core-ktx-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\a780c33170532e310c1c5655f4882f5b\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\a780c33170532e310c1c5655f4882f5b\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\61c50e78abc8a4fe196ada5624527142\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\61c50e78abc8a4fe196ada5624527142\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4503eaba342e6e637bf8397f4eb4bc0a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4503eaba342e6e637bf8397f4eb4bc0a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb51cd6b7418f7a8032edfa60064527d\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\fb51cd6b7418f7a8032edfa60064527d\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e473e3fc00a6490438ea15bad264293\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e473e3fc00a6490438ea15bad264293\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36f91bfcaafff320d0f346783ae7e5ae\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\36f91bfcaafff320d0f346783ae7e5ae\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aaa576556460f2c64b030835b315b09\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8aaa576556460f2c64b030835b315b09\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c281a44e0783d9af63e36c735c6591b5\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c281a44e0783d9af63e36c735c6591b5\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8006cf1fbfbdd0536175b61bc60600e\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d8006cf1fbfbdd0536175b61bc60600e\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [org.conscrypt:conscrypt-android:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e038ffa879db1937b2ff34b350a5080f\transformed\jetified-conscrypt-android-2.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [org.conscrypt:conscrypt-android:2.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e038ffa879db1937b2ff34b350a5080f\transformed\jetified-conscrypt-android-2.2.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\cb2be6867e92175dbb7759f29a68311e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\cb2be6867e92175dbb7759f29a68311e\transformed\jetified-image-1.0.0-beta1\AndroidManifest.xml:5:5-44
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:410:13-413:39
REJECTED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:413:17-36
	android:value
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:412:17-49
	android:name
		ADDED from C:\Users\<USER>\Desktop\5.22MM 9999\app\src\main\AndroidManifest.xml:411:17-68
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-face-detection:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb4a3eb37aed140bdef5bbda7349814c\transformed\jetified-play-services-mlkit-face-detection-17.0.1\AndroidManifest.xml:13:17-114
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d1265fea07d5fe6db55b104b4b9ee5e\transformed\jetified-vision-common-17.1.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8628bbc7691862ad9d5d2e56da6796a5\transformed\jetified-common-18.1.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\79230ecb7deb426f90957f200c266d44\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ae5003da7b262492451d61e3e75a684\transformed\jetified-play-services-basement-18.0.0\AndroidManifest.xml:22:13-58
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cfec3a8b05d7baa4efaedb9c3e35ea5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\7767cfc8b362984194d74d8bc32ab965\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d636d80b4a99f9af79777408483d3f77\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\31c5d19b0f3ae511c594364d1f92e25c\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\7f82fa51baad483287077af0401414c2\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f3804a02bcdb9d6291362ac51fbac80\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.pangu.keepaliveperfect.demo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\38bdbb3aec41791523ad0d9573b07666\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\593279399413e0d5cb95dae712fc58fa\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\319477bc9565e057970b04bc7b06891b\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\44582400701042f7f8c5cccde3980d01\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
