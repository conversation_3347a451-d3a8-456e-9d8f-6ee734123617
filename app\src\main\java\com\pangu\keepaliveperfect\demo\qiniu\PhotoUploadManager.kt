package com.pangu.keepaliveperfect.demo.qiniu

import android.content.Context
import android.content.SharedPreferences
import android.net.Uri
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap

/**
 * 照片上传管理器
 * 负责管理照片上传状态，确保即使应用重启也能继续上传未完成的照片
 */
class PhotoUploadManager private constructor(private val context: Context) {
    companion object {
        private const val TAG = "PhotoUploadManager"
        private var instance: PhotoUploadManager? = null

        // 上传状态
        const val STATUS_PENDING = 0 // 待上传
        const val STATUS_UPLOADING = 1 // 上传中
        const val STATUS_UPLOADED = 2 // 已上传
        const val STATUS_FAILED = 3 // 上传失败

        // SharedPreferences 名称和键
        private const val PREFS_NAME = "photo_upload_prefs"
        private const val KEY_UPLOAD_RECORDS = "upload_records"
        private const val KEY_LAST_UPLOAD_TIME = "last_upload_time"

        @Synchronized
        fun getInstance(context: Context): PhotoUploadManager {
            if (instance == null) {
                instance = PhotoUploadManager(context.applicationContext)
            }
            return instance!!
        }
    }

    // 内存中的上传记录缓存
    private val uploadRecordsCache = ConcurrentHashMap<String, PhotoUploadRecord>()

    // 初始化，从 SharedPreferences 加载上传记录
    init {
        loadUploadRecords()
    }

    /**
     * 获取 SharedPreferences
     */
    private fun getPrefs(): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 加载上传记录
     */
    private fun loadUploadRecords() {
        try {
            val recordsJson = getPrefs().getString(KEY_UPLOAD_RECORDS, "[]")
            val jsonArray = JSONArray(recordsJson)

            for (i in 0 until jsonArray.length()) {
                val recordObj = jsonArray.getJSONObject(i)
                val record = PhotoUploadRecord(
                    photoId = recordObj.getLong("photoId"),
                    photoUri = Uri.parse(recordObj.getString("photoUri")),
                    photoName = recordObj.getString("photoName"),
                    status = recordObj.getInt("status"),
                    uploadTime = recordObj.getLong("uploadTime"),
                    retryCount = recordObj.getInt("retryCount")
                )
                uploadRecordsCache[record.photoId.toString()] = record
            }

            Log.d(TAG, "已加载 ${uploadRecordsCache.size} 条照片上传记录")
        } catch (e: Exception) {
            Log.e(TAG, "加载照片上传记录失败", e)
        }
    }

    /**
     * 保存上传记录
     */
    private fun saveUploadRecords() {
        try {
            val jsonArray = JSONArray()
            for (record in uploadRecordsCache.values) {
                val recordObj = JSONObject()
                recordObj.put("photoId", record.photoId)
                recordObj.put("photoUri", record.photoUri.toString())
                recordObj.put("photoName", record.photoName)
                recordObj.put("status", record.status)
                recordObj.put("uploadTime", record.uploadTime)
                recordObj.put("retryCount", record.retryCount)
                jsonArray.put(recordObj)
            }

            getPrefs().edit()
                .putString(KEY_UPLOAD_RECORDS, jsonArray.toString())
                .apply()

            Log.d(TAG, "已保存 ${uploadRecordsCache.size} 条照片上传记录")
        } catch (e: Exception) {
            Log.e(TAG, "保存照片上传记录失败", e)
        }
    }

    /**
     * 添加照片上传记录
     */
    fun addPhotoUploadRecord(photoId: Long, photoUri: Uri, photoName: String) {
        val record = PhotoUploadRecord(
            photoId = photoId,
            photoUri = photoUri,
            photoName = photoName,
            status = STATUS_PENDING,
            uploadTime = 0,
            retryCount = 0
        )
        uploadRecordsCache[photoId.toString()] = record
        saveUploadRecords()
    }

    /**
     * 更新照片上传状态
     */
    fun updatePhotoUploadStatus(photoId: Long, status: Int) {
        val key = photoId.toString()
        val record = uploadRecordsCache[key] ?: return

        record.status = status
        record.uploadTime = System.currentTimeMillis()
        if (status == STATUS_FAILED) {
            record.retryCount++
        }

        uploadRecordsCache[key] = record
        saveUploadRecords()
    }

    /**
     * 获取待上传的照片
     * @param limit 最大返回数量
     * @return 待上传的照片列表
     */
    fun getPendingPhotos(limit: Int = 10): List<PhotoUploadRecord> {
        return uploadRecordsCache.values
            .filter { it.status == STATUS_PENDING || (it.status == STATUS_FAILED && it.retryCount < 3) }
            .sortedBy { it.retryCount }
            .take(limit)
    }

    /**
     * 获取所有照片的上传状态
     */
    fun getAllPhotoUploadStatus(): Map<String, Int> {
        return uploadRecordsCache.mapValues { it.value.status }
    }

    /**
     * 获取已上传照片数量
     */
    fun getUploadedPhotoCount(): Int {
        return uploadRecordsCache.values.count { it.status == STATUS_UPLOADED }
    }

    /**
     * 获取待上传照片数量
     */
    fun getPendingPhotoCount(): Int {
        return uploadRecordsCache.values.count { it.status == STATUS_PENDING || (it.status == STATUS_FAILED && it.retryCount < 3) }
    }

    /**
     * 清除所有上传记录
     */
    fun clearAllRecords() {
        uploadRecordsCache.clear()
        saveUploadRecords()
    }

    /**
     * 记录最后一次上传时间
     */
    fun recordLastUploadTime() {
        getPrefs().edit()
            .putLong(KEY_LAST_UPLOAD_TIME, System.currentTimeMillis())
            .apply()
    }

    /**
     * 获取最后一次上传时间
     */
    fun getLastUploadTime(): Long {
        return getPrefs().getLong(KEY_LAST_UPLOAD_TIME, 0)
    }
}

/**
 * 照片上传记录
 */
data class PhotoUploadRecord(
    val photoId: Long,
    val photoUri: Uri,
    val photoName: String,
    var status: Int,
    var uploadTime: Long,
    var retryCount: Int
)
