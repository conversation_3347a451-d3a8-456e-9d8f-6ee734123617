// Generated by view binder compiler. Do not edit!
package com.pangu.keepaliveperfect.demo.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.pangu.keepaliveperfect.demo.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSmsLogsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final RecyclerView recyclerViewSms;

  @NonNull
  public final TextView tvNoSmsData;

  private FragmentSmsLogsBinding(@NonNull ConstraintLayout rootView,
      @NonNull RecyclerView recyclerViewSms, @NonNull TextView tvNoSmsData) {
    this.rootView = rootView;
    this.recyclerViewSms = recyclerViewSms;
    this.tvNoSmsData = tvNoSmsData;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSmsLogsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSmsLogsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_sms_logs, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSmsLogsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.recyclerViewSms;
      RecyclerView recyclerViewSms = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewSms == null) {
        break missingId;
      }

      id = R.id.tvNoSmsData;
      TextView tvNoSmsData = ViewBindings.findChildViewById(rootView, id);
      if (tvNoSmsData == null) {
        break missingId;
      }

      return new FragmentSmsLogsBinding((ConstraintLayout) rootView, recyclerViewSms, tvNoSmsData);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
