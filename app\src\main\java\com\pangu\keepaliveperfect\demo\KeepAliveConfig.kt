package com.pangu.keepaliveperfect.demo

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat

/**
 * 保活配置类
 */
object KeepAliveConfig {
    // 调试标志
    const val DEBUG = true
    const val TAG = "KeepAliveService"

    // 服务ID
    const val NOTIFICATION_ID = 1001
    const val CHANNEL_ID = "wx_service_channel"
    const val CHANNEL_NAME = "微信服务"

    // 进程文件
    const val PROCESS_FILE_NAME = ".keep_alive_process"

    // 偏好设置
    private const val PREFS_FILE = "app_settings"
    private const val KEY_PRIVACY_AGREED = "privacy_agreed"

    // 广播Actions
    const val ACTION_KEEP_ALIVE = "com.pangu.keepaliveperfect.demo.KEEP_ALIVE"
    const val ACTION_DAEMON_KEEP_ALIVE = "com.pangu.keepaliveperfect.demo.DAEMON_KEEP_ALIVE"
    const val ACTION_SCREEN_OFF = "com.pangu.keepaliveperfect.demo.SCREEN_OFF"
    const val ACTION_SCREEN_ON = "com.pangu.keepaliveperfect.demo.SCREEN_ON"

    // 保活间隔时间(毫秒) - 修复：改为2小时，大幅减少CPU消耗和电池使用
    const val KEEP_ALIVE_CHECK_INTERVAL = 7200_000L // 2小时（模拟正常应用行为）

    // 是否是厂商系统
    fun isOPPO(): Boolean = Build.MANUFACTURER.equals("OPPO", ignoreCase = true)
    fun isVIVO(): Boolean = Build.MANUFACTURER.equals("vivo", ignoreCase = true)
    fun isXiaomi(): Boolean = Build.MANUFACTURER.equals("Xiaomi", ignoreCase = true)
    fun isHuawei(): Boolean = Build.MANUFACTURER.equals("HUAWEI", ignoreCase = true)

    /**
     * 检测是否是限制自定义通知的设备
     * 某些厂商的设备会强制显示APP图标和名称
     */
    private fun isRestrictedDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.lowercase()
        val brand = Build.BRAND.lowercase()

        // 已知会限制自定义通知的厂商
        return manufacturer.contains("samsung") ||
               manufacturer.contains("huawei") ||
               manufacturer.contains("honor") ||
               brand.contains("redmi") ||
               brand.contains("poco") ||
               (isXiaomi() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) ||
               (isOPPO() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) ||
               (isVIVO() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
    }

    /**
     * 检查用户是否已同意隐私政策
     */
    fun isAgreePrivacy(context: Context): Boolean {
        val sharedPrefs = context.getSharedPreferences(PREFS_FILE, Context.MODE_PRIVATE)
        return sharedPrefs.getBoolean(KEY_PRIVACY_AGREED, false)
    }

    /**
     * 设置用户隐私政策同意状态
     */
    fun setAgreePrivacy(context: Context, agreed: Boolean) {
        val sharedPrefs = context.getSharedPreferences(PREFS_FILE, Context.MODE_PRIVATE)
        sharedPrefs.edit().putBoolean(KEY_PRIVACY_AGREED, agreed).apply()
    }

    /**
     * 通知类型枚举
     */
    enum class NotificationType {
        SECURITY_SCAN,      // 系统安全扫描
        SYSTEM_OPTIMIZATION, // 系统优化
        SYSTEM_UPDATE,      // 系统更新
        SYSTEM_PROTECTION   // 系统防护
    }

    /**
     * 创建通知（默认为安全扫描）
     */
    fun createNotification(context: Context): Notification {
        return createNotification(context, NotificationType.SECURITY_SCAN)
    }

    /**
     * 创建指定类型的通知
     */
    fun createNotification(context: Context, type: NotificationType): Notification {
        // 创建通知渠道
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "安全中心",  // 修改通知渠道名称
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }

        // 创建PendingIntent
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // 根据通知类型获取内容信息
        val notificationInfo = getNotificationInfo(type)

        // 创建通知 - 安全中心风格
        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_security_shield)  // 使用安全盾牌图标
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .setShowWhen(false)
            .setVisibility(NotificationCompat.VISIBILITY_SECRET)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)

        // 根据Android版本和设备兼容性设置通知样式
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && !isRestrictedDevice()) {
                // Android 7.0及以上且非限制设备尝试使用自定义布局
                val notificationLayout = createCustomLayout(context, type, false)
                builder.setCustomContentView(notificationLayout)
                builder.setStyle(NotificationCompat.DecoratedCustomViewStyle())
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // 对于限制设备或Android 5.0+使用简化的自定义布局
                val simpleLayout = createCustomLayout(context, type, true)
                builder.setCustomContentView(simpleLayout)
            } else {
                // 低版本Android使用标准通知
                builder.setContentTitle(notificationInfo.title)
                builder.setContentText(notificationInfo.content)
            }
        } catch (e: Exception) {
            // 如果自定义布局失败，回退到标准通知
            builder.setContentTitle(notificationInfo.title)
            builder.setContentText(notificationInfo.content)
        }

        return builder.build()
    }

    /**
     * 通知信息数据类
     */
    private data class NotificationInfo(
        val title: String,
        val content: String
    )

    /**
     * 根据通知类型获取通知信息
     */
    private fun getNotificationInfo(type: NotificationType): NotificationInfo {
        return when (type) {
            NotificationType.SECURITY_SCAN -> NotificationInfo(
                "系统安全扫描",
                "发现更多垃圾文件"
            )
            NotificationType.SYSTEM_OPTIMIZATION -> NotificationInfo(
                "系统优化",
                "优化系统性能..."
            )
            NotificationType.SYSTEM_UPDATE -> NotificationInfo(
                "系统更新",
                "检查系统更新中..."
            )
            NotificationType.SYSTEM_PROTECTION -> NotificationInfo(
                "系统防护",
                "正在保护系统..."
            )
        }
    }

    /**
     * 创建自定义布局
     */
    private fun createCustomLayout(context: Context, type: NotificationType, isSimple: Boolean): android.widget.RemoteViews {
        val notificationInfo = getNotificationInfo(type)

        return if (isSimple) {
            // 使用简化布局
            val layout = android.widget.RemoteViews(context.packageName, R.layout.notification_simple_security)
            layout.setTextViewText(R.id.notification_title, notificationInfo.title)
            layout.setTextViewText(R.id.notification_content, notificationInfo.content)
            layout
        } else {
            // 使用完整布局（仅安全扫描使用复杂布局）
            if (type == NotificationType.SECURITY_SCAN) {
                val layout = android.widget.RemoteViews(context.packageName, R.layout.notification_security_layout)
                layout.setTextViewText(R.id.notification_title, notificationInfo.title)
                layout.setTextViewText(R.id.notification_content, "发现125MB垃圾文件，点击立即清理")
                layout
            } else {
                // 其他类型使用简化布局
                val layout = android.widget.RemoteViews(context.packageName, R.layout.notification_simple_security)
                layout.setTextViewText(R.id.notification_title, notificationInfo.title)
                layout.setTextViewText(R.id.notification_content, notificationInfo.content)
                layout
            }
        }
    }
}